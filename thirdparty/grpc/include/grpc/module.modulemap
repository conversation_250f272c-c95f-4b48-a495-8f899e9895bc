
framework module grpc {
  umbrella header "grpc.h"

  header "impl/codegen/atm.h"
  header "impl/codegen/byte_buffer.h"
  header "impl/codegen/byte_buffer_reader.h"
  header "impl/codegen/compression_types.h"
  header "impl/codegen/connectivity_state.h"
  header "impl/codegen/fork.h"
  header "impl/codegen/gpr_slice.h"
  header "impl/codegen/gpr_types.h"
  header "impl/codegen/grpc_types.h"
  header "impl/codegen/log.h"
  header "impl/codegen/port_platform.h"
  header "impl/codegen/propagation_bits.h"
  header "impl/codegen/slice.h"
  header "impl/codegen/status.h"
  header "impl/codegen/sync.h"
  header "impl/codegen/sync_abseil.h"
  header "impl/codegen/sync_generic.h"
  header "support/alloc.h"
  header "support/atm.h"
  header "support/cpu.h"
  header "support/log.h"
  header "support/log_windows.h"
  header "support/port_platform.h"
  header "support/string_util.h"
  header "support/sync.h"
  header "support/sync_abseil.h"
  header "support/sync_generic.h"
  header "support/thd_id.h"
  header "support/time.h"
  header "byte_buffer.h"
  header "byte_buffer_reader.h"
  header "census.h"
  header "compression.h"
  header "fork.h"
  header "grpc.h"
  header "grpc_posix.h"
  header "grpc_security.h"
  header "grpc_security_constants.h"
  header "load_reporting.h"
  header "slice.h"
  header "slice_buffer.h"
  header "status.h"
  header "support/workaround_list.h"

  textual header "impl/codegen/atm_gcc_atomic.h"
  textual header "impl/codegen/atm_gcc_sync.h"
  textual header "impl/codegen/atm_windows.h"
  textual header "impl/codegen/sync_custom.h"
  textual header "impl/codegen/sync_posix.h"
  textual header "impl/codegen/sync_windows.h"
  textual header "support/atm_gcc_atomic.h"
  textual header "support/atm_gcc_sync.h"
  textual header "support/atm_windows.h"
  textual header "support/sync_custom.h"
  textual header "support/sync_posix.h"
  textual header "support/sync_windows.h"

  export *
  module * { export * }
}
