/*
 * Copyright 2002, LifeLine Networks BV (www.lifeline.nl). All rights reserved.
 * Copyright 2002, <PERSON><PERSON><PERSON><PERSON>. All rights reserved.
 *
 * See the COPYING file for the terms of usage and distribution.
 */

#ifndef _INCLUDE_LOG4CPP_CONFIG_OPENVMS_H
#define _INCLUDE_LOG4CPP_CONFIG_OPENVMS_H 1
 
/* include/log4cpp/config.h. Generated automatically at end of configure. */
/* include/config.h.  Generated automatically by configure.  */
/* include/config.h.in.  Generated automatically from configure.in by autoheader.  */

/* Define if you have the <dlfcn.h> header file. */
#ifndef LOG4CPP_HAVE_DLFCN_H 
#define LOG4CPP_HAVE_DLFCN_H  1 
#endif

/* Define if you have the `ftime' function. */
#ifndef LOG4CPP_HAVE_FTIME 
#define LOG4CPP_HAVE_FTIME  1 
#endif

/* Define if you have the `gettimeofday' function. */
#ifndef LOG4CPP_HAVE_GETTIMEOFDAY 
#define LOG4CPP_HAVE_GETTIMEOFDAY  1 
#endif

/* define if the compiler has int64_t */
#ifndef LOG4CPP_HAVE_INT64_T 
#define LOG4CPP_HAVE_INT64_T   
#include <inttypes.h>
#endif

/* Define if you have the <io.h> header file. */
/* #undef LOG4CPP_HAVE_IO_H */

/* Define if you have the `idsa' library (-lidsa). */
/* #undef LOG4CPP_HAVE_LIBIDSA */

/* define if the compiler implements namespaces */
#ifndef LOG4CPP_HAVE_NAMESPACES 
#define LOG4CPP_HAVE_NAMESPACES   
#endif

/* define if the C library has snprintf */
/* #undef LOG4CPP_HAVE_SNPRINTF */  

/* define if the compiler has stringstream */
#ifndef LOG4CPP_HAVE_SSTREAM 
#define LOG4CPP_HAVE_SSTREAM   
#endif

/* Define if you have the `syslog' function. */
/* #undef LOG4CPP_HAVE_SYSLOG */

/* Define if you have the <unistd.h> header file. */
#ifndef LOG4CPP_HAVE_UNISTD_H 
#define LOG4CPP_HAVE_UNISTD_H  1 
#endif

/* Name of package */
#ifndef LOG4CPP_PACKAGE 
#define LOG4CPP_PACKAGE  "log4cpp" 
#endif

/* Version number of package */
#ifndef LOG4CPP_VERSION 
#define LOG4CPP_VERSION  "1.0" 
#endif
 
/* _INCLUDE_LOG4CPP_CONFIG_OPENVMS_H */
#endif
