/**
 * Autogenerated by Thrift Compiler (0.11.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
#ifndef AggregationValidator_H
#define AggregationValidator_H

#include <thrift/TDispatchProcessor.h>
#include <thrift/async/TConcurrentClientSyncInfo.h>
#include "aggregation_validator_types.h"

namespace jaegertracing { namespace thrift {

#ifdef _MSC_VER
  #pragma warning( push )
  #pragma warning (disable : 4250 ) //inheriting methods via dominance 
#endif

class AggregationValidatorIf {
 public:
  virtual ~AggregationValidatorIf() {}
  virtual void validateTrace(ValidateTraceResponse& _return, const std::string& traceId) = 0;
};

class AggregationValidatorIfFactory {
 public:
  typedef AggregationValidatorIf Handler;

  virtual ~AggregationValidatorIfFactory() {}

  virtual AggregationValidatorIf* getHandler(const ::apache::thrift::TConnectionInfo& connInfo) = 0;
  virtual void releaseHandler(AggregationValidatorIf* /* handler */) = 0;
};

class AggregationValidatorIfSingletonFactory : virtual public AggregationValidatorIfFactory {
 public:
  AggregationValidatorIfSingletonFactory(const ::apache::thrift::stdcxx::shared_ptr<AggregationValidatorIf>& iface) : iface_(iface) {}
  virtual ~AggregationValidatorIfSingletonFactory() {}

  virtual AggregationValidatorIf* getHandler(const ::apache::thrift::TConnectionInfo&) {
    return iface_.get();
  }
  virtual void releaseHandler(AggregationValidatorIf* /* handler */) {}

 protected:
  ::apache::thrift::stdcxx::shared_ptr<AggregationValidatorIf> iface_;
};

class AggregationValidatorNull : virtual public AggregationValidatorIf {
 public:
  virtual ~AggregationValidatorNull() {}
  void validateTrace(ValidateTraceResponse& /* _return */, const std::string& /* traceId */) {
    return;
  }
};


class AggregationValidator_validateTrace_args {
 public:

  AggregationValidator_validateTrace_args(const AggregationValidator_validateTrace_args&);
  AggregationValidator_validateTrace_args& operator=(const AggregationValidator_validateTrace_args&);
  AggregationValidator_validateTrace_args() : traceId() {
  }

  virtual ~AggregationValidator_validateTrace_args() throw();
  std::string traceId;

  void __set_traceId(const std::string& val);

  bool operator == (const AggregationValidator_validateTrace_args & rhs) const
  {
    if (!(traceId == rhs.traceId))
      return false;
    return true;
  }
  bool operator != (const AggregationValidator_validateTrace_args &rhs) const {
    return !(*this == rhs);
  }

  bool operator < (const AggregationValidator_validateTrace_args & ) const;

  uint32_t read(::apache::thrift::protocol::TProtocol* iprot);
  uint32_t write(::apache::thrift::protocol::TProtocol* oprot) const;

};


class AggregationValidator_validateTrace_pargs {
 public:


  virtual ~AggregationValidator_validateTrace_pargs() throw();
  const std::string* traceId;

  uint32_t write(::apache::thrift::protocol::TProtocol* oprot) const;

};

typedef struct _AggregationValidator_validateTrace_result__isset {
  _AggregationValidator_validateTrace_result__isset() : success(false) {}
  bool success :1;
} _AggregationValidator_validateTrace_result__isset;

class AggregationValidator_validateTrace_result {
 public:

  AggregationValidator_validateTrace_result(const AggregationValidator_validateTrace_result&);
  AggregationValidator_validateTrace_result& operator=(const AggregationValidator_validateTrace_result&);
  AggregationValidator_validateTrace_result() {
  }

  virtual ~AggregationValidator_validateTrace_result() throw();
  ValidateTraceResponse success;

  _AggregationValidator_validateTrace_result__isset __isset;

  void __set_success(const ValidateTraceResponse& val);

  bool operator == (const AggregationValidator_validateTrace_result & rhs) const
  {
    if (!(success == rhs.success))
      return false;
    return true;
  }
  bool operator != (const AggregationValidator_validateTrace_result &rhs) const {
    return !(*this == rhs);
  }

  bool operator < (const AggregationValidator_validateTrace_result & ) const;

  uint32_t read(::apache::thrift::protocol::TProtocol* iprot);
  uint32_t write(::apache::thrift::protocol::TProtocol* oprot) const;

};

typedef struct _AggregationValidator_validateTrace_presult__isset {
  _AggregationValidator_validateTrace_presult__isset() : success(false) {}
  bool success :1;
} _AggregationValidator_validateTrace_presult__isset;

class AggregationValidator_validateTrace_presult {
 public:


  virtual ~AggregationValidator_validateTrace_presult() throw();
  ValidateTraceResponse* success;

  _AggregationValidator_validateTrace_presult__isset __isset;

  uint32_t read(::apache::thrift::protocol::TProtocol* iprot);

};

class AggregationValidatorClient : virtual public AggregationValidatorIf {
 public:
  AggregationValidatorClient(apache::thrift::stdcxx::shared_ptr< ::apache::thrift::protocol::TProtocol> prot) {
    setProtocol(prot);
  }
  AggregationValidatorClient(apache::thrift::stdcxx::shared_ptr< ::apache::thrift::protocol::TProtocol> iprot, apache::thrift::stdcxx::shared_ptr< ::apache::thrift::protocol::TProtocol> oprot) {
    setProtocol(iprot,oprot);
  }
 private:
  void setProtocol(apache::thrift::stdcxx::shared_ptr< ::apache::thrift::protocol::TProtocol> prot) {
  setProtocol(prot,prot);
  }
  void setProtocol(apache::thrift::stdcxx::shared_ptr< ::apache::thrift::protocol::TProtocol> iprot, apache::thrift::stdcxx::shared_ptr< ::apache::thrift::protocol::TProtocol> oprot) {
    piprot_=iprot;
    poprot_=oprot;
    iprot_ = iprot.get();
    oprot_ = oprot.get();
  }
 public:
  apache::thrift::stdcxx::shared_ptr< ::apache::thrift::protocol::TProtocol> getInputProtocol() {
    return piprot_;
  }
  apache::thrift::stdcxx::shared_ptr< ::apache::thrift::protocol::TProtocol> getOutputProtocol() {
    return poprot_;
  }
  void validateTrace(ValidateTraceResponse& _return, const std::string& traceId);
  void send_validateTrace(const std::string& traceId);
  void recv_validateTrace(ValidateTraceResponse& _return);
 protected:
  apache::thrift::stdcxx::shared_ptr< ::apache::thrift::protocol::TProtocol> piprot_;
  apache::thrift::stdcxx::shared_ptr< ::apache::thrift::protocol::TProtocol> poprot_;
  ::apache::thrift::protocol::TProtocol* iprot_;
  ::apache::thrift::protocol::TProtocol* oprot_;
};

class AggregationValidatorProcessor : public ::apache::thrift::TDispatchProcessor {
 protected:
  ::apache::thrift::stdcxx::shared_ptr<AggregationValidatorIf> iface_;
  virtual bool dispatchCall(::apache::thrift::protocol::TProtocol* iprot, ::apache::thrift::protocol::TProtocol* oprot, const std::string& fname, int32_t seqid, void* callContext);
 private:
  typedef  void (AggregationValidatorProcessor::*ProcessFunction)(int32_t, ::apache::thrift::protocol::TProtocol*, ::apache::thrift::protocol::TProtocol*, void*);
  typedef std::map<std::string, ProcessFunction> ProcessMap;
  ProcessMap processMap_;
  void process_validateTrace(int32_t seqid, ::apache::thrift::protocol::TProtocol* iprot, ::apache::thrift::protocol::TProtocol* oprot, void* callContext);
 public:
  AggregationValidatorProcessor(::apache::thrift::stdcxx::shared_ptr<AggregationValidatorIf> iface) :
    iface_(iface) {
    processMap_["validateTrace"] = &AggregationValidatorProcessor::process_validateTrace;
  }

  virtual ~AggregationValidatorProcessor() {}
};

class AggregationValidatorProcessorFactory : public ::apache::thrift::TProcessorFactory {
 public:
  AggregationValidatorProcessorFactory(const ::apache::thrift::stdcxx::shared_ptr< AggregationValidatorIfFactory >& handlerFactory) :
      handlerFactory_(handlerFactory) {}

  ::apache::thrift::stdcxx::shared_ptr< ::apache::thrift::TProcessor > getProcessor(const ::apache::thrift::TConnectionInfo& connInfo);

 protected:
  ::apache::thrift::stdcxx::shared_ptr< AggregationValidatorIfFactory > handlerFactory_;
};

class AggregationValidatorMultiface : virtual public AggregationValidatorIf {
 public:
  AggregationValidatorMultiface(std::vector<apache::thrift::stdcxx::shared_ptr<AggregationValidatorIf> >& ifaces) : ifaces_(ifaces) {
  }
  virtual ~AggregationValidatorMultiface() {}
 protected:
  std::vector<apache::thrift::stdcxx::shared_ptr<AggregationValidatorIf> > ifaces_;
  AggregationValidatorMultiface() {}
  void add(::apache::thrift::stdcxx::shared_ptr<AggregationValidatorIf> iface) {
    ifaces_.push_back(iface);
  }
 public:
  void validateTrace(ValidateTraceResponse& _return, const std::string& traceId) {
    size_t sz = ifaces_.size();
    size_t i = 0;
    for (; i < (sz - 1); ++i) {
      ifaces_[i]->validateTrace(_return, traceId);
    }
    ifaces_[i]->validateTrace(_return, traceId);
    return;
  }

};

// The 'concurrent' client is a thread safe client that correctly handles
// out of order responses.  It is slower than the regular client, so should
// only be used when you need to share a connection among multiple threads
class AggregationValidatorConcurrentClient : virtual public AggregationValidatorIf {
 public:
  AggregationValidatorConcurrentClient(apache::thrift::stdcxx::shared_ptr< ::apache::thrift::protocol::TProtocol> prot) {
    setProtocol(prot);
  }
  AggregationValidatorConcurrentClient(apache::thrift::stdcxx::shared_ptr< ::apache::thrift::protocol::TProtocol> iprot, apache::thrift::stdcxx::shared_ptr< ::apache::thrift::protocol::TProtocol> oprot) {
    setProtocol(iprot,oprot);
  }
 private:
  void setProtocol(apache::thrift::stdcxx::shared_ptr< ::apache::thrift::protocol::TProtocol> prot) {
  setProtocol(prot,prot);
  }
  void setProtocol(apache::thrift::stdcxx::shared_ptr< ::apache::thrift::protocol::TProtocol> iprot, apache::thrift::stdcxx::shared_ptr< ::apache::thrift::protocol::TProtocol> oprot) {
    piprot_=iprot;
    poprot_=oprot;
    iprot_ = iprot.get();
    oprot_ = oprot.get();
  }
 public:
  apache::thrift::stdcxx::shared_ptr< ::apache::thrift::protocol::TProtocol> getInputProtocol() {
    return piprot_;
  }
  apache::thrift::stdcxx::shared_ptr< ::apache::thrift::protocol::TProtocol> getOutputProtocol() {
    return poprot_;
  }
  void validateTrace(ValidateTraceResponse& _return, const std::string& traceId);
  int32_t send_validateTrace(const std::string& traceId);
  void recv_validateTrace(ValidateTraceResponse& _return, const int32_t seqid);
 protected:
  apache::thrift::stdcxx::shared_ptr< ::apache::thrift::protocol::TProtocol> piprot_;
  apache::thrift::stdcxx::shared_ptr< ::apache::thrift::protocol::TProtocol> poprot_;
  ::apache::thrift::protocol::TProtocol* iprot_;
  ::apache::thrift::protocol::TProtocol* oprot_;
  ::apache::thrift::async::TConcurrentClientSyncInfo sync_;
};

#ifdef _MSC_VER
  #pragma warning( pop )
#endif

}} // namespace

#endif
