/**
 * Autogenerated by <PERSON>hrift Compiler (0.11.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
#ifndef dependency_TYPES_H
#define dependency_TYPES_H

#include <iosfwd>

#include <thrift/Thrift.h>
#include <thrift/TApplicationException.h>
#include <thrift/TBase.h>
#include <thrift/protocol/TProtocol.h>
#include <thrift/transport/TTransport.h>

#include <thrift/stdcxx.h>


namespace jaegertracing { namespace thrift {

class DependencyLink;

class Dependencies;


class DependencyLink : public virtual ::apache::thrift::TBase {
 public:

  DependencyLink(const DependencyLink&);
  DependencyLink& operator=(const DependencyLink&);
  DependencyLink() : parent(), child(), callCount(0) {
  }

  virtual ~DependencyLink() throw();
  std::string parent;
  std::string child;
  int64_t callCount;

  void __set_parent(const std::string& val);

  void __set_child(const std::string& val);

  void __set_callCount(const int64_t val);

  bool operator == (const DependencyLink & rhs) const
  {
    if (!(parent == rhs.parent))
      return false;
    if (!(child == rhs.child))
      return false;
    if (!(callCount == rhs.callCount))
      return false;
    return true;
  }
  bool operator != (const DependencyLink &rhs) const {
    return !(*this == rhs);
  }

  bool operator < (const DependencyLink & ) const;

  uint32_t read(::apache::thrift::protocol::TProtocol* iprot);
  uint32_t write(::apache::thrift::protocol::TProtocol* oprot) const;

  virtual void printTo(std::ostream& out) const;
};

void swap(DependencyLink &a, DependencyLink &b);

std::ostream& operator<<(std::ostream& out, const DependencyLink& obj);


class Dependencies : public virtual ::apache::thrift::TBase {
 public:

  Dependencies(const Dependencies&);
  Dependencies& operator=(const Dependencies&);
  Dependencies() {
  }

  virtual ~Dependencies() throw();
  std::vector<DependencyLink>  links;

  void __set_links(const std::vector<DependencyLink> & val);

  bool operator == (const Dependencies & rhs) const
  {
    if (!(links == rhs.links))
      return false;
    return true;
  }
  bool operator != (const Dependencies &rhs) const {
    return !(*this == rhs);
  }

  bool operator < (const Dependencies & ) const;

  uint32_t read(::apache::thrift::protocol::TProtocol* iprot);
  uint32_t write(::apache::thrift::protocol::TProtocol* oprot) const;

  virtual void printTo(std::ostream& out) const;
};

void swap(Dependencies &a, Dependencies &b);

std::ostream& operator<<(std::ostream& out, const Dependencies& obj);

}} // namespace

#endif
