/*=============================================================================
    Copyright (c) 1998-2003 <PERSON>
    Copyright (c) 2001-2003 <PERSON>
    Copyright (c) 2001-2003 <PERSON><PERSON>t <PERSON>
    Copyright (c) 2002-2003 <PERSON>
    Copyright (c) 2002 Juan <PERSON>
    Copyright (c) 2002 Raghavendra Satish
    Copyright (c) 2002 <PERSON>hl
    http://spirit.sourceforge.net/

  Distributed under the Boost Software License, Version 1.0. (See accompanying
  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
=============================================================================*/
#if !defined(BOOST_SPIRIT_UTILITY_MAIN_HPP)
#define BOOST_SPIRIT_UTILITY_MAIN_HPP

#include <boost/spirit/home/<USER>/version.hpp>

///////////////////////////////////////////////////////////////////////////////
//
//  Master header for Spirit.Utilities
//
///////////////////////////////////////////////////////////////////////////////

// Utility.Parsers
#include <boost/spirit/home/<USER>/utility/chset.hpp>
#include <boost/spirit/home/<USER>/utility/chset_operators.hpp>
#include <boost/spirit/home/<USER>/utility/escape_char.hpp>
#include <boost/spirit/home/<USER>/utility/functor_parser.hpp>
#include <boost/spirit/home/<USER>/utility/loops.hpp>
#include <boost/spirit/home/<USER>/utility/confix.hpp>
#include <boost/spirit/home/<USER>/utility/lists.hpp>
#include <boost/spirit/home/<USER>/utility/distinct.hpp>

// Utility.Support
#include <boost/spirit/home/<USER>/utility/flush_multi_pass.hpp>
#ifdef BOOST_SPIRIT_THREADSAFE
#include <boost/spirit/home/<USER>/utility/scoped_lock.hpp>
#endif


#endif // !defined(BOOST_SPIRIT_UTILITY_MAIN_HPP)
