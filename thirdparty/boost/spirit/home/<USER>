/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(BOOST_SPIRIT_SUPPORT_SEPTEMBER_26_2008_0340AM)
#define BOOST_SPIRIT_SUPPORT_SEPTEMBER_26_2008_0340AM

#if defined(_MSC_VER)
#pragma once
#endif

#include<boost/spirit/home/<USER>/assert_msg.hpp>
#include<boost/spirit/home/<USER>/action_dispatch.hpp>
#include<boost/spirit/home/<USER>/argument.hpp>
#include<boost/spirit/home/<USER>/attributes.hpp>
#include<boost/spirit/home/<USER>/char_class.hpp>
#include<boost/spirit/home/<USER>/common_terminals.hpp>
#include<boost/spirit/home/<USER>/container.hpp>
#include<boost/spirit/home/<USER>/context.hpp>
#include<boost/spirit/home/<USER>/info.hpp>
#include<boost/spirit/home/<USER>/lazy.hpp>
#include<boost/spirit/home/<USER>/make_component.hpp>
#include<boost/spirit/home/<USER>/meta_compiler.hpp>
#include<boost/spirit/home/<USER>/modify.hpp>
#include<boost/spirit/home/<USER>/sequence_base_id.hpp>
#include<boost/spirit/home/<USER>/string_traits.hpp>
#include<boost/spirit/home/<USER>/terminal.hpp>
#include<boost/spirit/home/<USER>/unused.hpp>
#include<boost/spirit/home/<USER>/utf8.hpp>

#endif

