/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(BOOST_SPIRIT_DIRECTIVE_FEBRUARY_05_2007_0313PM)
#define BOOST_SPIRIT_DIRECTIVE_FEBRUARY_05_2007_0313PM

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/home/<USER>/directive/as.hpp>
#include <boost/spirit/home/<USER>/directive/encoding.hpp>
#include <boost/spirit/home/<USER>/directive/hold.hpp>
#include <boost/spirit/home/<USER>/directive/lexeme.hpp>
#include <boost/spirit/home/<USER>/directive/no_skip.hpp>
#include <boost/spirit/home/<USER>/directive/matches.hpp>
#include <boost/spirit/home/<USER>/directive/no_case.hpp>
#include <boost/spirit/home/<USER>/directive/omit.hpp>
#include <boost/spirit/home/<USER>/directive/raw.hpp>
#include <boost/spirit/home/<USER>/directive/repeat.hpp>
#include <boost/spirit/home/<USER>/directive/skip.hpp>
#include <boost/spirit/home/<USER>/directive/expect.hpp>

#endif
