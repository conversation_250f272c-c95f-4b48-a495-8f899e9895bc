/*=============================================================================
    Copyright (c) 2001-2003 <PERSON>
    Copyright (c) 2003 Giovanni Bajo
    http://spirit.sourceforge.net/

  Distributed under the Boost Software License, Version 1.0. (See accompanying
  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
=============================================================================*/
#if !defined(BOOST_SPIRIT_ITERATOR_MAIN_HPP)
#define BOOST_SPIRIT_ITERATOR_MAIN_HPP

#include <boost/spirit/home/<USER>/version.hpp>

///////////////////////////////////////////////////////////////////////////////
//
//  Master header for Spirit.Iterators
//
///////////////////////////////////////////////////////////////////////////////

#include <boost/spirit/home/<USER>/iterator/file_iterator.hpp>
#include <boost/spirit/home/<USER>/iterator/fixed_size_queue.hpp>
#include <boost/spirit/home/<USER>/iterator/position_iterator.hpp>
#include <boost/spirit/home/<USER>/iterator/multi_pass.hpp>

#endif // !defined(BOOST_SPIRIT_ITERATOR_MAIN_HPP)
