/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(BOOST_SPIRIT_NONTERMINAL_FEBRUARY_12_2007_1018AM)
#define BOOST_SPIRIT_NONTERMINAL_FEBRUARY_12_2007_1018AM

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/home/<USER>/nonterminal/rule.hpp>
#include <boost/spirit/home/<USER>/nonterminal/grammar.hpp>
#include <boost/spirit/home/<USER>/nonterminal/error_handler.hpp>
#include <boost/spirit/home/<USER>/nonterminal/debug_handler.hpp>
#include <boost/spirit/home/<USER>/nonterminal/simple_trace.hpp>
#include <boost/spirit/home/<USER>/nonterminal/success_handler.hpp>

#endif
