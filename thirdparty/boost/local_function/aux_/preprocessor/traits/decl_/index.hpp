
// Copyright (C) 2009-2012 <PERSON>
// Distributed under the Boost Software License, Version 1.0
// (see accompanying file LICENSE_1_0.txt or a copy at
// http://www.boost.org/LICENSE_1_0.txt)
// Home at http://www.boost.org/libs/local_function

#ifndef BOOST_LOCAL_FUNCTION_AUX_PP_DECL_TRAITS_INDEX_HPP_
#define BOOST_LOCAL_FUNCTION_AUX_PP_DECL_TRAITS_INDEX_HPP_

#define BOOST_LOCAL_FUNCTION_AUX_PP_DECL_TRAITS_INDEX_RETURNS               0
#define BOOST_LOCAL_FUNCTION_AUX_PP_DECL_TRAITS_INDEX_PARAMS                1
#define BOOST_LOCAL_FUNCTION_AUX_PP_DECL_TRAITS_INDEX_CONST_BINDS           2
#define BOOST_LOCAL_FUNCTION_AUX_PP_DECL_TRAITS_INDEX_CONST_BIND_THIS_TYPES 3
#define BOOST_LOCAL_FUNCTION_AUX_PP_DECL_TRAITS_INDEX_BINDS                 4
#define BOOST_LOCAL_FUNCTION_AUX_PP_DECL_TRAITS_INDEX_BIND_THIS_TYPES       5
#define BOOST_LOCAL_FUNCTION_AUX_PP_DECL_TRAITS_INDEX_ERROR                 6
#define BOOST_LOCAL_FUNCTION_AUX_PP_DECL_TRAITS_INDEX_MAX                   7

#endif // #include guard

