#ifndef BOOST_METAPARSE_V1_IMPL_AT_C_HPP
#define BOOST_METAPARSE_V1_IMPL_AT_C_HPP

// Copyright <PERSON> (<EMAIL>)  2013.
// Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//          http://www.boost.org/LICENSE_1_0.txt)

#include <boost/metaparse/config.hpp>

#if BOOST_METAPARSE_STD >= 2011
#  include <boost/metaparse/v1/cpp11/impl/at_c.hpp>
#else
#  include <boost/metaparse/v1/cpp98/impl/at_c.hpp>
#endif

#endif

