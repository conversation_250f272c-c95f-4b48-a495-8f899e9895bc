// Boost.Range library
//
//  Copyright <PERSON><PERSON> 2003-2006. Use, modification and
//  distribution is subject to the Boost Software License, Version
//  1.0. (See accompanying file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt)
//
// For more information, see http://www.boost.org/libs/range/
//

#ifndef BOOST_RANGE_FUNCTIONS_HPP
#define BOOST_RANGE_FUNCTIONS_HPP

#if defined(_MSC_VER)
# pragma once
#endif

#include <boost/range/begin.hpp>
#include <boost/range/end.hpp>
#include <boost/range/size.hpp>
#include <boost/range/distance.hpp>
#include <boost/range/empty.hpp>
#include <boost/range/rbegin.hpp>
#include <boost/range/rend.hpp>

#endif

