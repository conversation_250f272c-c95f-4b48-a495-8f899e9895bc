// (C) Copyright 2008 CodeRage, LLC (turkanis at coderage dot com)
// (C) Copyright 2005-2007 <PERSON>
// Distributed under the Boost Software License, Version 1.0. (See accompanying
// file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt.)

// See http://www.boost.org/libs/iostreams for documentation.

#ifndef BOOST_IOSTREAMS_OPERATIONS_HPP_INCLUDED
#define BOOST_IOSTREAMS_OPERATIONS_HPP_INCLUDED

#if defined(_MSC_VER)
# pragma once
#endif

#include <boost/iostreams/operations_fwd.hpp>
#include <boost/iostreams/close.hpp>
#include <boost/iostreams/flush.hpp>
#include <boost/iostreams/imbue.hpp>
#include <boost/iostreams/input_sequence.hpp>
#include <boost/iostreams/optimal_buffer_size.hpp>
#include <boost/iostreams/output_sequence.hpp>
#include <boost/iostreams/read.hpp>
#include <boost/iostreams/seek.hpp>
#include <boost/iostreams/write.hpp>

#endif // #ifndef BOOST_IOSTREAMS_OPERATIONS_HPP_INCLUDED
