# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* Revised by <PERSON> (2020) */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_FACILITIES_INTERCEPT_HPP
# define BOOST_PREPROCESSOR_FACILITIES_INTERCEPT_HPP
#
# include <boost/preprocessor/config/config.hpp>
#
# if ~BOOST_PP_CONFIG_FLAGS() & BOOST_PP_CONFIG_STRICT()
#
# /* BOOST_PP_INTERCEPT */
#
# define BOOST_PP_INTERCEPT BOOST_PP_INTERCEPT_
#
# define BOOST_PP_INTERCEPT_0
# define BOOST_PP_INTERCEPT_1
# define BOOST_PP_INTERCEPT_2
# define BOOST_PP_INTERCEPT_3
# define BOOST_PP_INTERCEPT_4
# define BOOST_PP_INTERCEPT_5
# define BOOST_PP_INTERCEPT_6
# define BOOST_PP_INTERCEPT_7
# define BOOST_PP_INTERCEPT_8
# define BOOST_PP_INTERCEPT_9
# define BOOST_PP_INTERCEPT_10
# define BOOST_PP_INTERCEPT_11
# define BOOST_PP_INTERCEPT_12
# define BOOST_PP_INTERCEPT_13
# define BOOST_PP_INTERCEPT_14
# define BOOST_PP_INTERCEPT_15
# define BOOST_PP_INTERCEPT_16
# define BOOST_PP_INTERCEPT_17
# define BOOST_PP_INTERCEPT_18
# define BOOST_PP_INTERCEPT_19
# define BOOST_PP_INTERCEPT_20
# define BOOST_PP_INTERCEPT_21
# define BOOST_PP_INTERCEPT_22
# define BOOST_PP_INTERCEPT_23
# define BOOST_PP_INTERCEPT_24
# define BOOST_PP_INTERCEPT_25
# define BOOST_PP_INTERCEPT_26
# define BOOST_PP_INTERCEPT_27
# define BOOST_PP_INTERCEPT_28
# define BOOST_PP_INTERCEPT_29
# define BOOST_PP_INTERCEPT_30
# define BOOST_PP_INTERCEPT_31
# define BOOST_PP_INTERCEPT_32
# define BOOST_PP_INTERCEPT_33
# define BOOST_PP_INTERCEPT_34
# define BOOST_PP_INTERCEPT_35
# define BOOST_PP_INTERCEPT_36
# define BOOST_PP_INTERCEPT_37
# define BOOST_PP_INTERCEPT_38
# define BOOST_PP_INTERCEPT_39
# define BOOST_PP_INTERCEPT_40
# define BOOST_PP_INTERCEPT_41
# define BOOST_PP_INTERCEPT_42
# define BOOST_PP_INTERCEPT_43
# define BOOST_PP_INTERCEPT_44
# define BOOST_PP_INTERCEPT_45
# define BOOST_PP_INTERCEPT_46
# define BOOST_PP_INTERCEPT_47
# define BOOST_PP_INTERCEPT_48
# define BOOST_PP_INTERCEPT_49
# define BOOST_PP_INTERCEPT_50
# define BOOST_PP_INTERCEPT_51
# define BOOST_PP_INTERCEPT_52
# define BOOST_PP_INTERCEPT_53
# define BOOST_PP_INTERCEPT_54
# define BOOST_PP_INTERCEPT_55
# define BOOST_PP_INTERCEPT_56
# define BOOST_PP_INTERCEPT_57
# define BOOST_PP_INTERCEPT_58
# define BOOST_PP_INTERCEPT_59
# define BOOST_PP_INTERCEPT_60
# define BOOST_PP_INTERCEPT_61
# define BOOST_PP_INTERCEPT_62
# define BOOST_PP_INTERCEPT_63
# define BOOST_PP_INTERCEPT_64
# define BOOST_PP_INTERCEPT_65
# define BOOST_PP_INTERCEPT_66
# define BOOST_PP_INTERCEPT_67
# define BOOST_PP_INTERCEPT_68
# define BOOST_PP_INTERCEPT_69
# define BOOST_PP_INTERCEPT_70
# define BOOST_PP_INTERCEPT_71
# define BOOST_PP_INTERCEPT_72
# define BOOST_PP_INTERCEPT_73
# define BOOST_PP_INTERCEPT_74
# define BOOST_PP_INTERCEPT_75
# define BOOST_PP_INTERCEPT_76
# define BOOST_PP_INTERCEPT_77
# define BOOST_PP_INTERCEPT_78
# define BOOST_PP_INTERCEPT_79
# define BOOST_PP_INTERCEPT_80
# define BOOST_PP_INTERCEPT_81
# define BOOST_PP_INTERCEPT_82
# define BOOST_PP_INTERCEPT_83
# define BOOST_PP_INTERCEPT_84
# define BOOST_PP_INTERCEPT_85
# define BOOST_PP_INTERCEPT_86
# define BOOST_PP_INTERCEPT_87
# define BOOST_PP_INTERCEPT_88
# define BOOST_PP_INTERCEPT_89
# define BOOST_PP_INTERCEPT_90
# define BOOST_PP_INTERCEPT_91
# define BOOST_PP_INTERCEPT_92
# define BOOST_PP_INTERCEPT_93
# define BOOST_PP_INTERCEPT_94
# define BOOST_PP_INTERCEPT_95
# define BOOST_PP_INTERCEPT_96
# define BOOST_PP_INTERCEPT_97
# define BOOST_PP_INTERCEPT_98
# define BOOST_PP_INTERCEPT_99
# define BOOST_PP_INTERCEPT_100
# define BOOST_PP_INTERCEPT_101
# define BOOST_PP_INTERCEPT_102
# define BOOST_PP_INTERCEPT_103
# define BOOST_PP_INTERCEPT_104
# define BOOST_PP_INTERCEPT_105
# define BOOST_PP_INTERCEPT_106
# define BOOST_PP_INTERCEPT_107
# define BOOST_PP_INTERCEPT_108
# define BOOST_PP_INTERCEPT_109
# define BOOST_PP_INTERCEPT_110
# define BOOST_PP_INTERCEPT_111
# define BOOST_PP_INTERCEPT_112
# define BOOST_PP_INTERCEPT_113
# define BOOST_PP_INTERCEPT_114
# define BOOST_PP_INTERCEPT_115
# define BOOST_PP_INTERCEPT_116
# define BOOST_PP_INTERCEPT_117
# define BOOST_PP_INTERCEPT_118
# define BOOST_PP_INTERCEPT_119
# define BOOST_PP_INTERCEPT_120
# define BOOST_PP_INTERCEPT_121
# define BOOST_PP_INTERCEPT_122
# define BOOST_PP_INTERCEPT_123
# define BOOST_PP_INTERCEPT_124
# define BOOST_PP_INTERCEPT_125
# define BOOST_PP_INTERCEPT_126
# define BOOST_PP_INTERCEPT_127
# define BOOST_PP_INTERCEPT_128
# define BOOST_PP_INTERCEPT_129
# define BOOST_PP_INTERCEPT_130
# define BOOST_PP_INTERCEPT_131
# define BOOST_PP_INTERCEPT_132
# define BOOST_PP_INTERCEPT_133
# define BOOST_PP_INTERCEPT_134
# define BOOST_PP_INTERCEPT_135
# define BOOST_PP_INTERCEPT_136
# define BOOST_PP_INTERCEPT_137
# define BOOST_PP_INTERCEPT_138
# define BOOST_PP_INTERCEPT_139
# define BOOST_PP_INTERCEPT_140
# define BOOST_PP_INTERCEPT_141
# define BOOST_PP_INTERCEPT_142
# define BOOST_PP_INTERCEPT_143
# define BOOST_PP_INTERCEPT_144
# define BOOST_PP_INTERCEPT_145
# define BOOST_PP_INTERCEPT_146
# define BOOST_PP_INTERCEPT_147
# define BOOST_PP_INTERCEPT_148
# define BOOST_PP_INTERCEPT_149
# define BOOST_PP_INTERCEPT_150
# define BOOST_PP_INTERCEPT_151
# define BOOST_PP_INTERCEPT_152
# define BOOST_PP_INTERCEPT_153
# define BOOST_PP_INTERCEPT_154
# define BOOST_PP_INTERCEPT_155
# define BOOST_PP_INTERCEPT_156
# define BOOST_PP_INTERCEPT_157
# define BOOST_PP_INTERCEPT_158
# define BOOST_PP_INTERCEPT_159
# define BOOST_PP_INTERCEPT_160
# define BOOST_PP_INTERCEPT_161
# define BOOST_PP_INTERCEPT_162
# define BOOST_PP_INTERCEPT_163
# define BOOST_PP_INTERCEPT_164
# define BOOST_PP_INTERCEPT_165
# define BOOST_PP_INTERCEPT_166
# define BOOST_PP_INTERCEPT_167
# define BOOST_PP_INTERCEPT_168
# define BOOST_PP_INTERCEPT_169
# define BOOST_PP_INTERCEPT_170
# define BOOST_PP_INTERCEPT_171
# define BOOST_PP_INTERCEPT_172
# define BOOST_PP_INTERCEPT_173
# define BOOST_PP_INTERCEPT_174
# define BOOST_PP_INTERCEPT_175
# define BOOST_PP_INTERCEPT_176
# define BOOST_PP_INTERCEPT_177
# define BOOST_PP_INTERCEPT_178
# define BOOST_PP_INTERCEPT_179
# define BOOST_PP_INTERCEPT_180
# define BOOST_PP_INTERCEPT_181
# define BOOST_PP_INTERCEPT_182
# define BOOST_PP_INTERCEPT_183
# define BOOST_PP_INTERCEPT_184
# define BOOST_PP_INTERCEPT_185
# define BOOST_PP_INTERCEPT_186
# define BOOST_PP_INTERCEPT_187
# define BOOST_PP_INTERCEPT_188
# define BOOST_PP_INTERCEPT_189
# define BOOST_PP_INTERCEPT_190
# define BOOST_PP_INTERCEPT_191
# define BOOST_PP_INTERCEPT_192
# define BOOST_PP_INTERCEPT_193
# define BOOST_PP_INTERCEPT_194
# define BOOST_PP_INTERCEPT_195
# define BOOST_PP_INTERCEPT_196
# define BOOST_PP_INTERCEPT_197
# define BOOST_PP_INTERCEPT_198
# define BOOST_PP_INTERCEPT_199
# define BOOST_PP_INTERCEPT_200
# define BOOST_PP_INTERCEPT_201
# define BOOST_PP_INTERCEPT_202
# define BOOST_PP_INTERCEPT_203
# define BOOST_PP_INTERCEPT_204
# define BOOST_PP_INTERCEPT_205
# define BOOST_PP_INTERCEPT_206
# define BOOST_PP_INTERCEPT_207
# define BOOST_PP_INTERCEPT_208
# define BOOST_PP_INTERCEPT_209
# define BOOST_PP_INTERCEPT_210
# define BOOST_PP_INTERCEPT_211
# define BOOST_PP_INTERCEPT_212
# define BOOST_PP_INTERCEPT_213
# define BOOST_PP_INTERCEPT_214
# define BOOST_PP_INTERCEPT_215
# define BOOST_PP_INTERCEPT_216
# define BOOST_PP_INTERCEPT_217
# define BOOST_PP_INTERCEPT_218
# define BOOST_PP_INTERCEPT_219
# define BOOST_PP_INTERCEPT_220
# define BOOST_PP_INTERCEPT_221
# define BOOST_PP_INTERCEPT_222
# define BOOST_PP_INTERCEPT_223
# define BOOST_PP_INTERCEPT_224
# define BOOST_PP_INTERCEPT_225
# define BOOST_PP_INTERCEPT_226
# define BOOST_PP_INTERCEPT_227
# define BOOST_PP_INTERCEPT_228
# define BOOST_PP_INTERCEPT_229
# define BOOST_PP_INTERCEPT_230
# define BOOST_PP_INTERCEPT_231
# define BOOST_PP_INTERCEPT_232
# define BOOST_PP_INTERCEPT_233
# define BOOST_PP_INTERCEPT_234
# define BOOST_PP_INTERCEPT_235
# define BOOST_PP_INTERCEPT_236
# define BOOST_PP_INTERCEPT_237
# define BOOST_PP_INTERCEPT_238
# define BOOST_PP_INTERCEPT_239
# define BOOST_PP_INTERCEPT_240
# define BOOST_PP_INTERCEPT_241
# define BOOST_PP_INTERCEPT_242
# define BOOST_PP_INTERCEPT_243
# define BOOST_PP_INTERCEPT_244
# define BOOST_PP_INTERCEPT_245
# define BOOST_PP_INTERCEPT_246
# define BOOST_PP_INTERCEPT_247
# define BOOST_PP_INTERCEPT_248
# define BOOST_PP_INTERCEPT_249
# define BOOST_PP_INTERCEPT_250
# define BOOST_PP_INTERCEPT_251
# define BOOST_PP_INTERCEPT_252
# define BOOST_PP_INTERCEPT_253
# define BOOST_PP_INTERCEPT_254
# define BOOST_PP_INTERCEPT_255
# define BOOST_PP_INTERCEPT_256
#
# else
#
# /* BOOST_PP_INTERCEPT */
#
# define BOOST_PP_INTERCEPT BOOST_PP_INTERCEPT_
#
# include <boost/preprocessor/config/limits.hpp>
#
# if BOOST_PP_LIMIT_MAG == 256
# include <boost/preprocessor/facilities/limits/intercept_256.hpp>
# elif BOOST_PP_LIMIT_MAG == 512
# include <boost/preprocessor/facilities/limits/intercept_256.hpp>
# include <boost/preprocessor/facilities/limits/intercept_512.hpp>
# elif BOOST_PP_LIMIT_MAG == 1024
# include <boost/preprocessor/facilities/limits/intercept_256.hpp>
# include <boost/preprocessor/facilities/limits/intercept_512.hpp>
# include <boost/preprocessor/facilities/limits/intercept_1024.hpp>
# else
# error Incorrect value for the BOOST_PP_LIMIT_MAG limit
# endif
#
# endif
#
# endif
