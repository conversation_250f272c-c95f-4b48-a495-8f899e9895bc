/*!
@file
Includes all the headers needed to setup tag-dispatching.

@copyright <PERSON> 2013-2017
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)
 */

#ifndef BOOST_HANA_CORE_DISPATCH_HPP
#define BOOST_HANA_CORE_DISPATCH_HPP

#include <boost/hana/core/tag_of.hpp>
#include <boost/hana/detail/dispatch_if.hpp>
#include <boost/hana/core/default.hpp>
#include <boost/hana/core/when.hpp>

#endif // !BOOST_HANA_CORE_DISPATCH_HPP
