///////////////////////////////////////////////////////////////////////////////
/// \file fusion.hpp
/// Proto callables for things found in the Fusion library
//
//  Copyright 2010 <PERSON>. Distributed under the Boost
//  Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_PROTO_FUNCTIONAL_FUSION_HPP_EAN_11_27_2010
#define BOOST_PROTO_FUNCTIONAL_FUSION_HPP_EAN_11_27_2010

#include <boost/proto/functional/fusion/at.hpp>
#include <boost/proto/functional/fusion/pop_back.hpp>
#include <boost/proto/functional/fusion/pop_front.hpp>
#include <boost/proto/functional/fusion/push_back.hpp>
#include <boost/proto/functional/fusion/push_front.hpp>
#include <boost/proto/functional/fusion/reverse.hpp>

#endif
