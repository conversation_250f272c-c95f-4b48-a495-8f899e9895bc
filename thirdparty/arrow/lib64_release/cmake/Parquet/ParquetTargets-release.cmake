#----------------------------------------------------------------
# Generated CMake target import file for configuration "RELEASE".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "Parquet::parquet_static" for configuration "RELEASE"
set_property(TARGET Parquet::parquet_static APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(Parquet::parquet_static PROPERTIES
  IMPORTED_LINK_INTERFACE_LANGUAGES_RELEASE "CXX"
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib64/libparquet.a"
  )

list(APPEND _IMPORT_CHECK_TARGETS Parquet::parquet_static )
list(APPEND _IMPORT_CHECK_FILES_FOR_Parquet::parquet_static "${_IMPORT_PREFIX}/lib64/libparquet.a" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
