# database source
[db-dev]
mysql_host = 127.0.0.1
mysql_port = 3306
user = root
password = 123456
database = recommend_config
charset = utf8

[db-master]
mysql_host = dbproxy.recommend-config-abc.shein.com
mysql_port = 3336
user = abc_config_rw
password = behC42RgjOK2gTik
database = recommend_config
charset = utf8

[db-test]
mysql_host = *************
mysql_port = 3307
user = abc_test
password = zQ5qf9u3bj0f4tuT
database = recommend_config
charset = utf8

[log]
path = /data/log/
file_name = model_update.log

[model]
download = /data/download/
rank_target_path = /data/tfs/model2/
data_target_path = /data/done/
match_target_path = /data/model/
faiss_target_path = /data/faiss/download/
