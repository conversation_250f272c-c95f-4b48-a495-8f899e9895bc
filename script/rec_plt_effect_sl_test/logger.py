#!/usr/bin/evn python
# _*_ coding:utf-8 _*_
import logging
import logging.handlers

def setup_custom_logger(logName, logLevel, pathName=''):
    if not pathName:
        handler = logging.StreamHandler()
    else:
        # handler = logging.FileHandler(pathName)
        handler = logging.handlers.RotatingFileHandler(pathName, maxBytes=10000000, backupCount=5)
    formatter = logging.Formatter(fmt='%(asctime)s - %(levelname)s - %(module)s - %(message)s')
    handler.setFormatter(formatter)

    logger = logging.getLogger(logName)
    logger.setLevel(level=logLevel)
    logger.addHandler(handler)
    return logger
