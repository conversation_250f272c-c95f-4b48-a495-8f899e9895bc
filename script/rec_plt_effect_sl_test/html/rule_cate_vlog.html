<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>推荐离线评估体系类目校验vlog</title>
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script src="https://cdn.bootcss.com/jquery/3.3.1/jquery.js"></script>
    <link href="https://cdn.bootcss.com/bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.16/css/jquery.dataTables.min.css">
    <script src="https://cdn.bootcss.com/datatables/1.10.16/js/jquery.dataTables.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/css/toastr.min.css" rel="stylesheet"/>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
    <style>
        .content {
            margin: 50px auto;
            border: 1px solid #ccc;
        }

        .operation {
            margin: 10px;
        }

        .operation > button {
            margin: 10px;
        }

        #model_length {
            float: left;
            margin-left: 20px;
        }

        #model_filter {
            float: right;
            margin-right: 20px;
        }

        #model {
            margin: 5px;
        }

        .center-block {
            display: block;
            width: 21%;
            margin: auto;
        }

        /*table {*/
        /*table-layout: fixed;*/
        /*}*/

        .colStyle {
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }
    </style>
	<div>
		<center>历史数据查询</center>
	</div>
</head>

<body>
<section class="content">
    <div class="btn-group operation">
        <button id="btn_edit" type="button" class="btn bg-warning">
            <span class="glyphicon glyphicon-list-alt" aria-hidden="true"></span>查看
        </button>
		<button id="btn_delete" type="button" class="btn bg-danger">
            <span class="glyphicon glyphicon-remove" aria-hidden="true"></span>删除
        </button>
    </div>
    <div class="modal fade" id="editResInfo" role="dialog">
        <div class="modal-dialog">
            <div id="display_detail" style="padding-left:30px;">
                <table class="table">
                    <p style="float: right;" input type="checkbox" class="allcb" data-child="chk" checked/> </p>
                    <tbody>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>站点</th>
                                <th>符合商品数</th>
                                <th>不符合商品数</th>
                            </tr>
                        </thead>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
	<div class="modal fade" id="deleteInfo" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">确认要删除该条数据吗？</h4>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button id="btn_delete_ack" type="button" class="btn btn-danger" data-dismiss="modal">删除</button>
                </div>
            </div>
        </div>
    </div>
    <table id="model" style="table-layout:fixed"
           class="table table-striped table-bordered row-border hover order-column" cellspacing="0" width="100%">
        <thead>
        <tr>
			<th>序号</th>
            <th>推荐场景</th>
            <th>商品id</th>
            <th>商品类目</th>
            <th>推荐商品数</th>
            <th>品类级别</th>
			<th>品类id</th>
            <th>实验参数</th>
            <th>服务名</th>
            <th>时间</th>
        </tr>
        </thead>
        <tbody></tbody>
    </table>
</section>
</body>
<script>
    toastr.options.positionClass = 'toast-bottom-right';
    // window.operateEvents = {
    //  'click .RoleOfedit': function (e, value, row, index) {
    //     alert(row.qxxh);
    //     $("#upload").modal('show');
    //   }
    // };

    var titles = ['推荐场景', '推荐商品数', '品类级别', '品类id', '实验参数', '服务名', '时间']
    $(function () {
        var table = $('#model').DataTable({
            "ajax": {
                "url": "cate_vlog_info",
                "type": "POST",
                contentType: "application/json",
                dataType: "json",
                "data": function (d) {
                    return JSON.stringify(d);
                }
            },
            "pagingType": "full_numbers",
            "bSort": false,
            "language": {
                "sProcessing": "处理中...",
                "sLengthMenu": "显示 _MENU_ 项结果",
                "sZeroRecords": "没有匹配结果",
                "sInfo": "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                "sInfoEmpty": "显示第 0 至 0 项结果，共 0 项",
                "sInfoFiltered": "(由 _MAX_ 项结果过滤)",
                "sInfoPostFix": "",
                "sSearch": "搜索:",
                "sUrl": "",
                "sEmptyTable": "表中数据为空",
                "sLoadingRecords": "载入中...",
                "sInfoThousands": ",",
                "oPaginate": {
                    "sFirst": "首页",
                    "sPrevious": "上页",
                    "sNext": "下页",
                    "sLast": "末页"
                },
                "oAria": {
                    "sSortAscending": ": 以升序排列此列",
                    "sSortDescending": ": 以降序排列此列"
                }
            },
            "columnDefs": [
                {
                    "targets": 0,
                    width: 30,
                    class: 'colStyle',
                    render: paramsMatter,
                },
                {
                    "targets": 1,
                    width: 60,
                    class: 'colStyle',
                    render: paramsMatter,
                },
                {
                    "targets": 2,
                    width: 60,
                    class: 'colStyle',
                    render: paramsMatter,
                },
                {
                    "targets": 3,
                    width: 60,
                    class: 'colStyle',
                    render: paramsMatter,
                },
                {
                    "targets": 4,
                    width: 80,
                    class: 'colStyle',
                    render: paramsMatter,
                },
                {
                    "targets": 5,
                    width: 60,
                    class: 'colStyle',
                    render: paramsMatter,
                },
                {
                    "targets": 6,
                    width: 60,
                    class: 'colStyle',
                    render: paramsMatter,
                },
                {
                    "targets": 7,
                    class: 'colStyle',
                    render: paramsMatter,
                },
                {
                    "targets": 8,
                    class: 'colStyle',
                    render: paramsMatter,
                },
                {
                    "targets": 9,
                    width: 120,
                    //获取日期列的值进行转换
                    render: function (data, type, row, meta) {
                        return changeDateFormat(data)
                    }
                }],
            // "order": [[1, 'asc']]
        });

        $('#model tbody').on('click', 'tr', function () {
            if ($(this).hasClass('selected')) {
                $(this).removeClass('selected');
            }
            else {
                table.$('tr.selected').removeClass('selected');
                $(this).addClass('selected');
            }
        });

        $('#btn_delete').click(function () {
			console.log('delete');
            if (table.rows('.selected').data().length) {
				$("#deleteInfo").modal()
			} else {
                alert('请选择项目');
            }
        });
		
		$('#btn_delete_ack').click(function () {
            var rowData = table.rows('.selected').data()[0];
			console.log(rowData)
			pid = rowData[0]
			$.ajax({
                type: "post",
                url: 'del_cate_vlog',
                async: false, // 使用同步方式
                data: JSON.stringify({
                    id: pid
                }),
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (data) {
                    $('#model').dataTable()._fnAjaxUpdate();
                }
            });
        });
		
        $('#btn_edit').click(function () {
            console.log('edit');
            if (table.rows('.selected').data().length) {
                $("#editResInfo").modal()
                var rowData = table.rows('.selected').data()[0];
                console.log(rowData)
                display_detail(rowData[10])
            } else {
                alert('请选择项目');
            }
        });
    })

    //转换日期格式(时间戳转换为datetime格式)
    function changeDateFormat(cellval) {
        var dateVal = cellval + "";
        if (cellval != null) {
            var date = new Date(parseInt(dateVal.replace("/Date(", "").replace(")/", ""), 10));
            var month = date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1;
            var currentDate = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
            var hours = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
            var minutes = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
            var seconds = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
            return date.getFullYear() + "-" + month + "-" + currentDate + " " + hours + ":" + minutes + ":" + seconds;
        }
    }

    function paramsMatter(data, type, row, meta) {
        var values = data;
        var span = document.createElement('span');
        span.setAttribute('title', values);
        span.innerHTML = data;
        return span.outerHTML;
    }

    function display_detail(data) {
        console.log('display_detail')
        console.log(data);
        var obj=eval(data)
        var out_data = "<tr>"
        for (var i in obj) {
            out_data += "<tr>"
            out_data += "<td>" + i  + "</td>"
            out_data += "<td>" + obj[i]["site_uid"]  + "</td>"
            out_data += "<td>" + obj[i]["right_num"]  + "</td>"
            out_data += "<td>" + obj[i]["error_num"]  + "</td>"
            out_data += "</tr>"
        }
        out_data += "</tr>"
        console.log('out_data');
        console.log(out_data);
        $('#display_detail tbody').html(out_data);
        $('#display_detail').show();
    }

</script>

</html>
