# -*- coding:UTF-8 -*-
import os
import sys
import json
import logging
import logging.handlers
import time
import datetime

import recommend_model_pb2
import config_pb2
from utils import CommonUtils
import happybase

#####################################################################################
# global
logger = None

#####################################################################################
# logger
def init_logger(name):
    global logger
    logger = logging.getLogger(name)
    logger.setLevel(level=logging.INFO)
    handler = logging.handlers.RotatingFileHandler(CommonUtils.log_path + 'service.log', maxBytes=20000000, backupCount=5)
    #handler = logging.FileHandler(CommonUtils.log_path + "service.log")
    #handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)

    logger.addHandler(handler)

######################################################################################
def init_service_cfg(cfg_file):
    if cfg_file.strip() == "":
        logger.error("init_service_cfg the file name is null.")
        return None

    if not os.path.exists(cfg_file):
        logger.error("init_service_cfg the file is not exist.")
        return None

    m = config_pb2.ItemTagSync()
    try:
        from google.protobuf import text_format
        f = open(cfg_file, "r")
        text_format.Parse(f.read(), m)
    except text_format.ParseError as e:
        logger.error('init_service_cfg parse error.')

    f.close()
    return m

##################################################################################

def sync_build(info, pool_dict):
    #print 'sync_build'
    if not info:
        logger.error('in sync_build, info is none.')
        return
    if not pool_dict:
        logger.error('in sync_build, valid pool is null.')
        return

    model = recommend_model_pb2.ItemTagsModel()
    model.bussiness_id = info.bussiness_id

    pool_sum = 0
    pool_valid = 0
    tag_cnt = 0
    for k in pool_dict:
        pool_sum += 1
        pool_item = k.rsplit('_', 1)
        if len(pool_item) != 2:
            logger.warn('in sync_build pool_item:' + k + 'is not valid')
            continue

        pool_valid += 1
        tag_info = model.infos.add()
        tag_info.key.pool = pool_item[0]
        tag_info.key.id = pool_item[1]

        for id in pool_dict[k]:
            tag_cnt += 1
            vtag = tag_info.val.tags.add()
            vtag.tag_id = int(id)
            vtag.tag_val = pool_dict[k][id]

    #print(model)
    # sync
    res_file = CommonUtils.write_sync_pb(info.model_name, model.SerializeToString())
    logger.info("process get result pool:" + str(pool_sum) + " valid pool:" + str(pool_valid) + " tag:" + str(tag_cnt)
        + " model:" + str(res_file))

def get_item_tags(info):
    if not info:
        logger.error("in get_item_tags, config info is not valid.")
        return None

    column_list = []
    name_val_dict = {}
    for tag_info in info.tag_infos:
        if tag_info.name.strip() == "":
            logger.error("in get_item_tags, config tag name is not valid.")
            continue

        l = []
        l.append(tag_info.id)
        l.append(tag_info.expires)
        l.append(tag_info.val_type)
        name_val_dict["cf:"+tag_info.name] = l
        column_list.append("cf:"+tag_info.name)

    #make columns
    if not column_list:
        logger.error("in get_item_tags, the config tag infos null.")
        return None

    if info.table_name.strip() == "":
        logger.error("in ge_item_tags, the config table name is null.")
        return None

    logger.info('in get_item_tags, the config table:'+info.table_name)
    # get connection
    t = CommonUtils.get_hbase_table_obj(info.table_name)
    if not t:
        logger.error("in get_item_tags, get table obj failed.")
        return None

    d_pool = {}
    row_sum = 0
    row_valid = 0
    tag_sum = 0
    for k, v in t.scan(columns=column_list, include_timestamp=False):
        row_sum += 1
        logger.debug('get row: ' + str(k) + ' ' + str(v))

        pool_item = k.rsplit('_', 1)
        if len(pool_item) != 2:
            logger.warn('in get_item_tags row key:' + k + 'is not valid')
            continue

        row_valid += 1
        #pool_id, item_id = pool_item
        id_val_dict = {}
        for sk in v:
            sk_val = name_val_dict.get(sk)
            if sk_val is None:
                logger.warn('in row vals:' + str(v) + ' config tag not found skey: ' + str(sk))
                continue
            tag_id, expire, val_type = sk_val

            tag_time = v[sk]
            tag_val = tag_time
            logger.debug("get row sk:" + sk + " val:" + str(tag_time))

            result_tag_val = 0
            if val_type == 0:
                if not tag_val.isdigit():
                    logger.debug('in row key:' + str(k) + ' tag_val:' + tag_val + ' is not config type ')
                    continue
                result_tag_val = int(tag_val)
            elif val_type == 1:
                # if not tag_val.isalnum():
                if tag_val.strip() == "":
                    logger.warn('in row key:' + str(k) + ' tag_val:' + tag_val + ' is not config type ')
                    continue
                spu_id = CityHash64(tag_val)
                result_tag_val = (long(spu_id) / 2)
            else:
                continue

            if result_tag_val == 0:
                logger.warn('in row key:' + str(k) + ' tag:' + str(kv) + ' tag_val:' + tag_val + ' result 0')
                continue

            id_val_dict[tag_id] = result_tag_val
            tag_sum += 1

        if not id_val_dict:
            logger.warn('in get_item_tags, not valid tags.rowkey:'+k)
        else:
            d_pool[k] = id_val_dict

    logger.info('in get_item_tags row sum:' + str(row_sum) + ' row valid:' + str(row_valid) + ' tag sum:' + str(tag_sum))
    return d_pool

def service_main(infos):
    for info in infos:
        d_pool_tag = get_item_tags(info)
        if not d_pool_tag:
            continue
        sync_build(info, d_pool_tag)

if __name__ == '__main__':
    if len(sys.argv) < 3:
        print 'usage: service test/prod config'
        exit()

    init_logger(sys.argv[1])
    if not logger:
        exit()

    ser_cfg = init_service_cfg(sys.argv[2])
    if not ser_cfg:
        logger.error('init service config error.')
        exit()

    print str(ser_cfg)
    # work main loop
    while True:
        service_main(ser_cfg.item_infos)

        # 0,no need work loop
        if ser_cfg.check_timer == 0:
            break

        time.sleep(ser_cfg.check_timer)
        continue

    logger.info("work exit.")
    exit()