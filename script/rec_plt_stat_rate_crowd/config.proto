syntax = "proto3";

message SrcInfo { //input file列表
    string file = 1;
    repeated string stat_keys = 2;
    repeated string rate_vals = 3;
}

message StatRateInfo {
    int32 type = 1; //ctr=1, cvr=2
    string model_name = 2; //模型（文件）名
    repeated string src_files = 3;
    repeated string stat_keys = 4;
    repeated string rate_vals = 5;
}

message ServiceConfig {  //同步服务全局配置
    int32 check_timer = 1; //检查更新周期，单位秒(s)
    repeated StatRateInfo infos = 2;
}
