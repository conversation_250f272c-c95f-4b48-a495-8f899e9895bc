#!/usr/bin/env python
# _*_ coding:utf-8 _*_

#
# athor: bluse
# create : 2019.02.19
#
import os
import sys
import logging
import warnings

def create_path(path):
    if not os.path.isdir(path):
        os.mkdir(path)

def config_logger(path,section):
    if section == 'prod':
        logging.basicConfig(filename=path+'logger.log',
                        format   = '[%(asctime)s  %(filename)s %(lineno)d] %(message)s',
                        datefmt  = '%Y-%m-%d %A %H:%M:%S',
                        filemode = 'w',
                        level = logging.INFO) 
    else:
        logging.basicConfig(filename=path+'logger.log',
                format   = '[%(asctime)s  %(filename)s %(lineno)d] %(message)s',
                datefmt  = '%Y-%m-%d %A %H:%M:%S',
                filemode = 'w',
                level = logging.DEBUG)