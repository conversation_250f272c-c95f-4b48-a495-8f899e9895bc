#!/usr/bin/env python
# -*- coding:utf-8 -*-

#
# author: leeviv
# date: 2021.07.16
#
import sys
import os
import io
import datetime
import time
import warnings
import logging
import shutil
import schedule
import hashlib
import subprocess
import traceback
import json
import re
import _thread
import threading
import numpy as np
import codecs

save_num = 15

#获取所有的tag文件
def getTagFileList(del_path):  
    tag_file_list = []
    for fname in os.listdir(del_path):
        fname_path = os.path.join(del_path, fname)
        if os.path.isdir(fname_path):
            continue
        if fname.find('.tag') == -1:
            continue
        tag_file_list.append(fname)
    return tag_file_list


#根据tag前缀名获取数据文件名
def getDataFileListByTag(pre_name, fdir_path):
    data_file_list = []
    for fname in os.listdir(fdir_path):
        fname_path = os.path.join(fdir_path, fname)
        if os.path.isdir(fname_path):
            continue
        if fname.find('.tag') != -1:
            continue
        if fname.find(pre_name) != -1:
            data_file_list.append(fname)
    return data_file_list



#删除的多余的数据文件
def delExtraData(data_file_list, fdir_path):
    data_file_list.sort()
    cnt = len(data_file_list) - save_num
    for fname in data_file_list[0:cnt]:
        fname_path = os.path.join(fdir_path, fname)
        if os.path.exists(fname_path):
            logging.info("del file:{0}".format(fname_path))
            os.remove(fname_path) 


#根据tag文件，删除过期文件
def delExpireFile(fdir_path):
    tag_file_list = getTagFileList(fdir_path)
    for tag_file in tag_file_list:
        item = tag_file.split('.tag')
        if len(item) != 2:
            continue
        data_file_list = getDataFileListByTag(item[0], fdir_path)
        if len(data_file_list) <= save_num:
            continue
        logging.info("del path:{0}, by tag_file:{1}".format(fdir_path, tag_file))
        delExtraData(data_file_list, fdir_path)



def main(section, del_path):
    if not os.path.exists(del_path):
        return
    for fdir in os.listdir(del_path):
        fdir_path = os.path.join(del_path, fdir)
        if os.path.isdir(fdir_path):
            delExpireFile(fdir_path)


if __name__ == '__main__':
    if len(sys.argv) < 2:
        warnings.warn("please input local/dev/prod parameter")
        sys.exit()
    logging.basicConfig(format='%(asctime)s - %(pathname)s[line:%(lineno)d] - %(levelname)s: %(message)s', level=logging.DEBUG)
    section = sys.argv[1]
    work_path = sys.argv[2]
    main(section, work_path)
    schedule.every().day.at("17:00").do(main, section, work_path)
    while True:
        schedule.run_pending()
        time.sleep(1)

