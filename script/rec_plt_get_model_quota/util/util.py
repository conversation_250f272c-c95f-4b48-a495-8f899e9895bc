#!/usr/bin/evn python
# _*_ coding:utf-8 _*_

#
# author: bluse.lee
# date: 2019.01.15
#
import os
import sys
import logging
import time
from copy import deepcopy

#初始化
def init():
    global md5_dict
    md5_dict = {}

    md5_dict["exposure_conf"] = ""
    md5_dict["site_country"] = ""
    md5_dict["bvpm_conf"] = ""
    md5_dict["goods_bvpm"] = ""
    md5_dict["goods_profile"] = ""
    md5_dict["stat_exposure"] = ""
    
    global data_dict_flag
    data_dict_flag = {}
    data_dict_flag["exposure_conf"] = 1
    data_dict_flag["site_country"] = 1
    data_dict_flag["bvpm_conf"] = 1
    data_dict_flag["goods_bvpm"] = 1
    data_dict_flag["goods_profile"] = 1
    data_dict_flag["stat_exposure"] = 1

    global  path_conf_data
    path_conf_data = "/data/exposure/conf/"
    global  path_exposure_data
    path_exposure_data =  "/data/exposure/stat_result/"
    global  path_goods_data
    path_goods_data =  "/data/exposure/stat_exposure_today/"
    global  country_cate_min_exposure_dict1
    country_cate_min_exposure_dict1 = {}
    global  country_cate_min_exposure_dict2
    country_cate_min_exposure_dict2 = {}
    global  site_country_dict1
    site_country_dict1 = {}
    global  site_country_dict2
    site_country_dict2 = {}
    global  bvpm_conf_dict1
    bvpm_conf_dict1 = {}
    global  bvpm_conf_dict2
    bvpm_conf_dict2 = {}
    global  goods_bvpm_dict1
    goods_bvpm_dict1 = {}
    global  goods_bvpm_dict2
    goods_bvpm_dict2 = {}
    global  new_goods_dict1
    new_goods_dict1 = {}
    global  new_goods_dict2
    new_goods_dict2 = {}
    global  goods_exposure_dict1
    goods_exposure_dict1 = {}
    global  goods_exposure_dict2
    goods_exposure_dict2 = {}

    
def remove_file(filename):
    if os.path.exists(filename):
        os.remove(filename) 

def create_path(path):
    if not os.path.isdir(path):
        os.mkdir(path)
        
def config_logger(path, file_name, section):
    if section == 'prod':
        logging.basicConfig(filename=path+file_name,
                        format   = '[%(asctime)s  %(filename)s %(lineno)d] %(message)s',
                        datefmt  = '%Y-%m-%d %A %H:%M:%S',
                        filemode = 'w',
                        level=logging.INFO) 
    else:
        logging.basicConfig(filename = path+file_name,
                format   = '[%(asctime)s  %(filename)s %(lineno)d] %(message)s',
                datefmt  = '%Y-%m-%d %A %H:%M:%S',
                filemode = 'w',
                level=logging.DEBUG)
                
def check_file(md5_key, file_path, tag_file):
  global md5_dict
  old_md5 = md5_dict[md5_key]
  tag_path_file = file_path + tag_file
  with open(tag_path_file, 'r') as f:
    lines = f.readlines()
    first_line = lines[0].strip()
    md5,data_file = first_line.split("  ") [0:2]
    if old_md5 == md5:
      return ""
    else:
      md5 = md5.strip()
      md5_dict[md5_key] = md5
      data_file = data_file.strip()
      return data_file
  return ""

def update_exposure_conf(dict_key, path_file, file_name):
    data_dict = {}
    global data_dict_flag
    global country_cate_min_exposure_dict2
    global country_cate_min_exposure_dict1
    frd = open(path_file + file_name, "r")
    for row in frd.readlines():
      site_id,country,cate,min_exposure = row.split("\t")[0:4]
      country = country.strip()
      cate = cate.strip()
      site_id = site_id.upper()
      min_exposure = min_exposure.strip()
      if min_exposure != "NULL" and len(country) >= 1 and len(cate) >= 1 and len(min_exposure) >= 1:
        key = site_id + "," + country + "," + cate
        data_dict[key] = int(min_exposure)
    logging.info("ok,UpdateExposureConf:{0}".format(file_name))
    frd.close()
    if len(data_dict) > 0:
      if data_dict_flag[dict_key] == 1:
        country_cate_min_exposure_dict2.clear()
        country_cate_min_exposure_dict2 = deepcopy(data_dict)
        data_dict_flag[dict_key] = 2
      else:
        country_cate_min_exposure_dict1.clear()
        country_cate_min_exposure_dict1 = deepcopy(data_dict)
        data_dict_flag[dict_key] = 1
    return

def update_sitecountry_conf(dict_key, path_file, file_name):
    data_dict = {}
    global data_dict_flag
    global site_country_dict2
    global site_country_dict1
    data_path_file = path_file + file_name
    frd = open(data_path_file, "r")
    for row in frd.readlines():
      site_uid, country = row.split("\t")[0:2]
      country = country.strip()
      site_uid = site_uid.strip()
      if len(country) >= 1 and len(site_uid) >= 1:
        data_dict[site_uid] = country      
    logging.info("ok,update_sitecountry_conf:{0}".format(file_name))
    frd.close()
    if len(data_dict) > 0:
      if data_dict_flag[dict_key] == 1:
        site_country_dict2.clear()
        site_country_dict2 = deepcopy(data_dict)
        data_dict_flag[dict_key] = 2
      else:
        site_country_dict1.clear()
        site_country_dict1 = deepcopy(data_dict)
        data_dict_flag[dict_key] = 1
    return

def update_bvpm_conf(dict_key, path_file, file_name):
    data_dict = {}
    global data_dict_flag
    global bvpm_conf_dict2
    global bvpm_conf_dict1
    data_path_file = path_file + file_name
    frd = open(data_path_file, "r")
    for row in frd.readlines():
      site_id,country,cate,min_value = row.split("\t")[0:4]
      country = country.strip()
      cate = cate.strip()
      site_id = site_id.strip()
      min_value = min_value.strip()
      if len(country) >= 1 and len(cate) >= 1 and len(min_value) >= 1:
        key = site_id + "," + country + "," + cate
        data_dict[key] = min_value
    logging.info("ok,update_bvpm_conf:{0}".format(file_name))
    frd.close()
    if len(data_dict) > 0:
      if data_dict_flag[dict_key] == 1:
        bvpm_conf_dict2.clear()
        bvpm_conf_dict2 = deepcopy(data_dict)
        data_dict_flag[dict_key] = 2
      else:
        bvpm_conf_dict1.clear()
        bvpm_conf_dict1 = deepcopy(data_dict)
        data_dict_flag[dict_key] = 1
    return

def update_goods_bvpm(dict_key, path_file, file_name):
    #703827,HK,7,2005,952,35.9664
    data_dict = {}
    global data_dict_flag
    global goods_bvpm_dict2
    global goods_bvpm_dict1
    data_path_file = path_file + file_name
    frd = open(data_path_file, "r")
    for row in frd.readlines():
      goods_id, country, site_id, cate, exp_num, bvpm = row.split(",")[0:6]
      goods_id = goods_id.strip()
      country = country.strip()
      site_id = site_id.strip()
      cate = cate.strip()
      exp_num = exp_num.strip()
      bvpm = bvpm.strip()
      if len(goods_id) >= 1 and len(country) >= 1 and len(site_id) >= 1:
        key = goods_id + "," + country + "," + site_id
        data_dict[key] = float(bvpm)
    logging.info("ok,update_goods_bvpm:{0}".format(file_name))
    frd.close()
    if len(data_dict) > 0:
      if data_dict_flag[dict_key] == 1:
        goods_bvpm_dict2.clear()
        goods_bvpm_dict2 = deepcopy(data_dict)
        data_dict_flag[dict_key] = 2
      else:
        goods_bvpm_dict1.clear()
        goods_bvpm_dict1 = deepcopy(data_dict)
        data_dict_flag[dict_key] = 1
    return

def update_goods_profile(dict_key, path_file, file_name):
    #705563,AU,7,1757,0
    data_dict = {}
    global data_dict_flag
    global new_goods_dict2
    global new_goods_dict1
    data_path_file = path_file + file_name
    frd = open(data_path_file, "r")
    for row in frd.readlines():
      goods_id, country, site_id, cate, priority = row.split(",")[0:5]
      goods_id = goods_id.strip()
      country = country.strip()
      site_id = site_id.strip()
      cate = cate.strip()
      priority = priority.strip()
      if len(goods_id) >= 1 and len(country) >= 1 and len(site_id) >= 1 and len(priority) >= 1:
        key = goods_id + "," + country + "," + site_id
        data_dict[key] = cate + "," + priority
    logging.info("ok,update_goods_profile:{0}".format(file_name))
    frd.close()
    if len(data_dict) > 0:
      if data_dict_flag[dict_key] == 1:
        new_goods_dict2.clear()
        new_goods_dict2 = deepcopy(data_dict)
        data_dict_flag[dict_key] = 2
      else:
        new_goods_dict1.clear()
        new_goods_dict1 = deepcopy(data_dict)
        data_dict_flag[dict_key] = 1
    return


def update_goods_exposure(dict_key, path_file, file_name):
    data_dict = {}
    global data_dict_flag
    global goods_exposure_dict2
    global goods_exposure_dict1
    data_path_file = path_file + file_name
    frd = open(data_path_file, "r")
    for row in frd.readlines():
      country, cate, goods_id, site_id, exp_num = row.split(",")[0:5]
      goods_id = goods_id.strip()
      country = country.strip()
      site_id = site_id.strip()
      cate = cate.strip()
      exp_num = exp_num.strip()
      if len(goods_id) >= 1 and len(country) >= 1 and len(site_id) >= 1 and len(exp_num) >= 1:
        key = goods_id + "," + country + "," + site_id
        data_dict[key] = int(exp_num)
    logging.info("ok,update_goods_exposure:{0}".format(file_name))
    frd.close()
    if len(data_dict) > 0:
      if data_dict_flag[dict_key] == 1:
        goods_exposure_dict2.clear()
        goods_exposure_dict2 = deepcopy(data_dict)
        data_dict_flag[dict_key] = 2
      else:
        goods_exposure_dict1.clear()
        goods_exposure_dict1 = deepcopy(data_dict)
        data_dict_flag[dict_key] = 1
    return


def get_country(site_uid):
  country = ""
  if data_dict_flag["site_country"] == 1:
    country = site_country_dict1.get(site_uid, "")
  else:
    country = site_country_dict2.get(site_uid, "")
  #logging.info("get_country:{0}".format(country))
  return country

def get_goods_cate_priority(site_id, site_uid, goods_id):
  cate = ""
  priority = "0"
  country = get_country(site_uid)
  key = goods_id + "," + country + "," + site_id
  value = ""
  if data_dict_flag["goods_profile"] == 1:
    value = new_goods_dict1.get(key, "")
  else:
    value = new_goods_dict2.get(key, "")
  if len(value):
    cate, priority = value.split(",")[0:2]
    cate = cate.strip()
    priority = priority.strip()
  #logging.info("get_goods_cate_priority:{0},{1},{2}".format(goods_id,cate,priority))
  return cate, int(priority)

def get_exposure_conf(site_id, country, cate):
  key = site_id + "," + country + "," + cate
  if data_dict_flag["exposure_conf"] == 1:
    min_exposure = country_cate_min_exposure_dict1.get(key, 0)
  else:
    min_exposure = country_cate_min_exposure_dict2.get(key, 0)

  if min_exposure == 0:
    key = site_id + ",other," + cate
    if data_dict_flag["exposure_conf"] == 1:
      min_exposure = country_cate_min_exposure_dict1.get(key, 0)
    else:
      min_exposure = country_cate_min_exposure_dict2.get(key, 0)
  #logging.info("get_exposure_conf:{0},{1},{2}".format(country, cate, min_exposure))
  return min_exposure


def get_market(site_id, site_uid, goods_id):
    cate, priority = get_goods_cate_priority(site_id, site_uid, goods_id)
    return priority

def get_exposure_num(site_id, site_uid, goods_id):
  country = get_country(site_uid)
  key = goods_id + "," + country + "," + site_id
  exp_num = 0
  if data_dict_flag["stat_exposure"] == 1:
    exp_num = goods_exposure_dict1.get(key, 0)
  else:
    exp_num = goods_exposure_dict2.get(key, 0)
  #logging.info("get_exposure_num:{0},{1}".format(goods_id,exp_num))
  return exp_num

def get_bvpm(site_id, site_uid, goods_id):
  country = get_country(site_uid)
  key = goods_id + "," + country + "," + site_id
  bvpm = 0.0
  if data_dict_flag["goods_bvpm"] == 1:
    bvpm = goods_bvpm_dict1.get(key, 0)
  else:
    bvpm = goods_bvpm_dict2.get(key, 0)
  #logging.info("get_bvpm:{0},{1}".format(goods_id,bvpm))
  return bvpm

def get_bvpm_conf(site_id, country, cate):
  key = site_id + "," + country + "," + cate
  bvpm = 0.0
  if data_dict_flag["bvpm_conf"] == 1:
    bvpm = bvpm_conf_dict1.get(key, 0)
  else:
    bvpm = bvpm_conf_dict2.get(key, 0)
  #logging.info("get_bvpm_conf:{0},{1},{2}".format(country, cate, bvpm))
  return bvpm

def update_stat_data(name, delay):
   while 1:
      data_file = check_file("exposure_conf", path_conf_data , "new_goods_min_exposure.conf.tag")
      if len(data_file) > 0:
        update_exposure_conf("exposure_conf",path_conf_data, data_file)
      
      data_file = check_file("site_country", path_conf_data , "site_country.conf.tag")
      if len(data_file) > 0:
        update_sitecountry_conf("site_country", path_conf_data, data_file)
      
      data_file = check_file("bvpm_conf", path_conf_data , "site_country_cate_bvpm.conf.tag")
      if len(data_file) > 0:
        update_bvpm_conf("bvpm_conf", path_conf_data, data_file)

      #data_file = check_file("goods_bvpm", path_goods_data , "new_goods_28_bvpm.dat.tag")
      #if len(data_file) > 0:
      #  update_goods_bvpm("goods_bvpm", path_goods_data, data_file)

      #data_file = check_file("goods_profile", path_goods_data , "new_goods.dat.tag")
      #if len(data_file) > 0:
      #  update_goods_profile("goods_profile", path_goods_data, data_file)

      #data_file = check_file("stat_exposure", path_exposure_data , "stat_exposure_num.dat.tag")
      #if len(data_file) > 0:
      #  update_goods_exposure("stat_exposure", path_exposure_data, data_file)     

      time.sleep(delay)
