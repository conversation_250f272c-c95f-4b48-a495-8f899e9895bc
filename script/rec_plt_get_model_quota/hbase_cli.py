# -*- coding:UTF-8 -*-
import sys
import os
import json
import time,datetime
import happybase
import logging


class HbaseCli():
    def __init__(self, host):
        self.host = host
        try:
            self.pool  = happybase.ConnectionPool(size=8, host=self.host)
        except Exception as e:
            logging.info("ConnectionPool fail,host:{0}".format(host))

    def read_row(self, table, key, cf_name, flag_timestamp):
        if table == '':
            return None
        with self.pool.connection() as conn:
            if conn is None:
                logging.info("connection is None")
                return None
            t = conn.table(table)
            try:
              if flag_timestamp == True:
                value = t.row(key,include_timestamp=True)
              else:
                value = t.row(key)
            except Exception as e:
                conn.close()
                logging.info("get row fail,table:{0},key:{1}".format(table, key))
                return None
            if cf_name == "":
              conn.close()
              return value
            if len(value) == 0:
                logging.info("row is empty")
                conn.close()
                return None
            conn.close()
            return value["cf:" + cf_name]

    def read_rows(self, table, key_list, flag_timestamp):
        if table == '':
            return None
        with self.pool.connection() as conn:
            if conn is None:
                logging.info("connection is None")
                return None
            t = conn.table(table)
            try:
              if flag_timestamp == True:
                value_list = t.rows(key_list, include_timestamp=True)
              else:
                value_list = t.rows(key_list)
            except Exception as e:
                conn.close()
                logging.info("get rows fail,table:{0}".format(table))
                return None
            conn.close()
            return dict(value_list)

if __name__ == '__main__':
    if len(sys.argv) < 4:
      sys.exit()
    host = sys.argv[1]
    tabl = sys.argv[2]
    key = sys.argv[3]
    cli = HbaseCli(host)
    value = cli.read_row(tabl, key, "", True)
    print value


   
