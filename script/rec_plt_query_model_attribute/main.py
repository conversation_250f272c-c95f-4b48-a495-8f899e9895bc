#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import warnings
import logging
import traceback
from threading import Timer

import json
import tornado.web
import tornado.httpserver
import tornado.ioloop
from concurrent.futures import ThreadPoolExecutor
from tornado.ioloop import <PERSON><PERSON><PERSON>
from tornado.concurrent import run_on_executor

from src import util

from src import query_processor as query_process
from src import query_pools

env = None
query_pools_obj = None
task_num = None

path = sys.path[0]
log_path = path + "/log/"
util.create_path(log_path)
logger = util.config_logger(log_path + "recplt_query_model_attr.log", 100, 7) 

class ServerHandlerQueryModelAttr(tornado.web.RequestHandler):
    executor = ThreadPoolExecutor(task_num)
    query_process_obj = query_process.QueryProcessor(env, logger)
    @tornado.gen.coroutine
    def post(self):
        result_dict = dict()
        req_json = tornado.escape.json_decode(self.request.body)
        #logger.info("req_json:%s", req_json)
        rt, rsp_dic = self.query_process_obj.process_task(req_json, query_pools_obj)

        data= json.dumps(rsp_dic)

        self.write(data)
        self.finish()

def handle_task():
    #logger.info("timer start")
    query_pools_obj.update_data()
    Timer(10, handle_task, ()).start()

def init_timer():
    logger.info("init timer")
    Timer(10, handle_task, ()).start()
    

def start_tornodo_server():
    app = tornado.web.Application([(r'/recommend/query_model_attribute', ServerHandlerQueryModelAttr)])

    server = tornado.httpserver.HTTPServer(app)
    server.bind(8080)    
    server.start(1)
    tornado.ioloop.IOLoop.instance().start()

def main():
    
    global env
    env = sys.argv[1]
    task_num = int(sys.argv[2])
    if task_num < 1:
        task_num = 4

    logger.info("start tornodo server")
    global query_pools_obj
    query_pools_obj = query_pools.QueryPools()
    query_pools_obj.init(env, logger)
    #initial update timer
    init_timer()

    start_tornodo_server()

if __name__ == '__main__':
    if len(sys.argv) < 2:
        warnings.warn("please input [local/dev/prod 8]parameter")
        sys.exit()
    main()
