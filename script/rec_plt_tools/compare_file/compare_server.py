import json
import threading
import time

from bottle import run, hook, response, route, request
from datetime import datetime

from diff_file import compare
from sqlite_oper import SqliteOper
from utils import CommonUtils


@hook('after_request')
def enable_cors():
    response.headers['Access-Control-Allow-Origin'] = '*'


@route('/compare', method='POST')
def compare_file():
    if not request.json:
        CommonUtils.logger.error('compare_file, json is null.')
        return {"status": "1", "msg": "content is null."}

    info = {}
    try:
        if 's3_path1' not in request.json.keys():
            ex = Exception("file1 no null")
            raise ex

        if 's3_path2' not in request.json.keys():
            ex = Exception("file2 no null")
            raise ex

        if 'compare_type' not in request.json.keys():
            ex = Exception("file2 no null")
            raise ex

        uuid = datetime.now().strftime('%Y%m%d%H%M%S%f')
        SqliteOper().create_record(uuid, request.json['s3_path1'], request.json['s3_path2'], request.json['compare_type'])
        data = dict()
        data['uuid'] = uuid
        info['status'] = 0
        info['msg'] = 'success'
        info['data'] = data
    except Exception as e:
        msg = "Exception {}".format(e)
        info['status'] = -1
        info['msg'] = msg
        CommonUtils.logger.info(msg)

    return json.dumps(info)


@route('/result', method='POST')
def compare_result():
    if not request.json:
        CommonUtils.logger.error('compare_result, json is null.')
        return {"status": "1", "msg": "content is null."}

    info = {}
    try:
        if 'uuid' not in request.json.keys():
            ex = Exception("uuid no null")
            raise ex

        record = SqliteOper().get_record(request.json['uuid'])
        if record is None:
            ex = Exception("uuid no exists error")
            raise ex

        data = dict()
        data['result_path'] = record[0]
        data['status'] = record[1]
        data['msg'] = record[2]
        info['status'] = 0
        info['msg'] = 'success'
        info['data'] = data
    except Exception as e:
        msg = "Exception {}".format(e)
        info['status'] = -1
        info['msg'] = msg
        CommonUtils.logger.info(msg)

    return json.dumps(info)


def compare_worker():
    while True:
        info = SqliteOper().get_compare_record()
        if info is None:
            time.sleep(60)
            continue

        uuid = info[0]
        s3_path1 = info[1]
        s3_path2 = info[2]
        compare_type = info[3]
        compare(uuid, s3_path1, s3_path2, compare_type)


if __name__ == '__main__':
    CommonUtils.create_path(CommonUtils.log_path)
    CommonUtils.config_logger(CommonUtils.log_path, 'diff.log')

    server = threading.Thread(target=compare_worker)
    # # run_on(port_number) #Run in main thread
    # # server.daemon = True # Do not make us wait for you to exit
    server.start()

    run(host='0.0.0.0', port=8080, debug=False)


