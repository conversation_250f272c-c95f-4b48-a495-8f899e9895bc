import glob
import hashlib
import json
import logging
import os
from datetime import datetime
from utils import CommonUtils
import requests


def update_file(service_name, suffix=''):
    try:
        directory = '/data/done/'
        if not os.path.exists(directory):
            os.mkdirs(directory)

        os.chdir(directory)

        data = get_data(service_name, suffix)
        md5 = hashlib.md5(data.encode('utf8')).hexdigest()
        service_tag = '{}.pb.tag'.format(service_name)
        service_file = '{}.pb.{}'.format(service_name, datetime.now().strftime('%Y%m%d%H%M%S'))

        service_tag_md5 = ''
        if os.path.isfile(service_tag):
            with open(service_tag, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    first_line = lines[0]
                    service_tag_md5 = first_line.split(' ')[0].strip()
            if service_tag_md5 == md5:
                print('equal....')
                return

        with open(service_file, "w", encoding="utf-8") as f2:
            f2.write(data)

        cmd = 'md5sum {} > {}'.format(service_file, service_tag)
        ret = os.system(cmd)
        if ret == 0:
            print("{} finish".format(cmd))
        else:
            print("{} fail".format(cmd))
            message = "{} fail".format(cmd)
            CommonUtils.alarm_report(message, CommonUtils.ERROR_APOLLO)
            raise
    except Exception as e:
        print(e)
        logging.ERROR("apollo error occurred %s ", e)
        message = "apollo error occurred {} ".format(e)
        CommonUtils.alarm_report(message, CommonUtils.ERROR_APOLLO)



def get_data(service_name, suffix=''):
    # online check test dev
    url = 'http://metaserver.shein.com:8080/services/config'
    #url = 'http://metaserver.uat.shein.com:8080/services/config'
    #url = 'http://**********:18080/services/config'
    #url = 'http://10.53.1.37:8080/services/config'
    r = requests.get(url)
    if r.status_code != 200:
        print('{} get connect fail ...'.format(url))
        message = '{} get connect fail ...'.format(url)
        CommonUtils.alarm_report(message, CommonUtils.ERROR_APOLLO)
        raise

    homepageUrl = r.json()[0]['homepageUrl']
    url = '{}/configfiles/json/recplt/default/{}.txt'.format(homepageUrl, service_name)
    if 'dat' == suffix:
        url = '{}/configfiles/json/recplt/default/{}.dat.txt'.format(homepageUrl, service_name)
    r = requests.get(url)
    if r.status_code != 200:
        print('{} get connect fail ...'.format(url))
        message = '{} get connect fail ...'.format(url)
        CommonUtils.alarm_report(message, CommonUtils.ERROR_APOLLO)
        raise

    content = r.json()['content']
    return content


def tag_task():
    service_name = 'rerank_service_master'
    update_file(service_name, 'dat')
    service_name = 'rerank_service_gray'
    update_file(service_name, 'dat')
    service_name = 'rerank_service_debug'
    update_file(service_name, 'dat')
    service_name = 'match_service_master'
    update_file(service_name, 'dat')
    service_name = 'match_service_gray'
    update_file(service_name, 'dat')
    service_name = 'match_service_debug'
    update_file(service_name, 'dat')
    service_name = 'rank_service_master'
    update_file(service_name, 'dat')
    service_name = 'rank_service_gray'
    update_file(service_name, 'dat')
    service_name = 'rank_service_debug'
    update_file(service_name, 'dat')
    service_name = 'idmp2_param_set1_debug'
    update_file(service_name)
    service_name = 'idmp2_adapter_route_param'
    update_file(service_name)
    service_name = 'control_config_debug'
    update_file(service_name)
    service_name = 'control_config_gray'
    update_file(service_name)
    service_name = 'control_config_master'
    update_file(service_name)
    service_name = 'control_param_gray'
    update_file(service_name)
    service_name = 'control_param_master'
    update_file(service_name)


if __name__ == "__main__":
    tag_task()
