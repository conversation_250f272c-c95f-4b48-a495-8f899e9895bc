# _*_ coding:utf-8 _*_
import datetime
import json
import requests
import os
import sys
import logging
from logging.handlers import RotatingFileHandler
import time

from datetime import timedelta


class CommonUtils:
    path = sys.path[0]
    core_path = '/data/core/'
    log_path = '/data/log/rec-tools/'
    pvlog_path = '/data/pvlog/'
    record_path = '/data/sqlite/'
    section = 'dev'
    core_interval = 2   # 两分钟
    tag_interval = 1
    logger = logging.getLogger()

    ERROR_CORE = 2001000
    ERROR_COPY_RECOVER = 6001001
    ERROR_APOLLO = 6001002

    def __init__(self):
        pass


    @staticmethod
    def create_path(path):
        if not os.path.isdir(path):
            os.mkdir(path)

    @staticmethod
    def config_logger(path, log_name):
        CommonUtils.logger.setLevel(logging.WARNING)
        rotate_handler = RotatingFileHandler(filename=path + log_name,
                                          maxBytes=5 * 1024 * 1024,
                                          backupCount=5,
                                          encoding="utf-8")

        rotate_handler.setFormatter(logging.Formatter(
            fmt='[%(asctime)s  %(filename)s %(lineno)d] %(message)s',
            datefmt='%Y-%m-%d %A %H:%M:%S'
        ))

        CommonUtils.logger.addHandler(rotate_handler)

    @staticmethod
    def get_file_size(file_path):
        # file_path = unicode(file_path,'utf8')

        if not os.path.exists(file_path):
            return 0

        file_size = os.path.getsize(file_path)
        return file_size

    # 把时间戳转化为时间: 1479264792 to 2016-11-16 10:53:12
    @staticmethod
    def timestamp_to_time(timestamp):
        timeStruct = time.localtime(timestamp)
        return time.strftime('%Y-%m-%d %H:%M:%S',timeStruct)

    # 获取文件的访问时间
    @staticmethod
    def get_file_access_time(file_path):
        t = os.path.getatime(file_path)
        return CommonUtils.timestamp_to_time(t)


    # 获取文件的创建时间
    @staticmethod
    def get_file_create_time(file_path):
        t = os.path.getctime(file_path)
        return CommonUtils.timestamp_to_time(t)


    # 获取文件的修改时间
    @staticmethod
    def get_file_modify_time(file_path):
        t = os.path.getmtime(file_path)
        return CommonUtils.timestamp_to_time(t)


    @staticmethod
    def delete_file(file_name):
        # fileName = unicode(file_name, "utf8")
        ret = False
        if os.path.isfile(file_name):
            try:
                os.remove(file_name)
                ret = True
            except OSError as e:
                logging.error("Error: %s - %s. delete file %s error" % (e.filename, e.strerror, file_name))

            logging.info("delete file %s finish", file_name)

        return ret


    @staticmethod
    def create_file(file_name, content):
        try:
            data = open(file_name, 'w')  # 清空文件内容再写
            data.write(content)  # 只能写字符串
        except IOError as err:
            logging.error("creat file_name error : %s", str(err))
        finally:
            if 'data' in locals():
                data.close()

    @staticmethod
    def create_file(file_name, content):
        try:
            data = open(file_name, 'w')  # 清空文件内容再写
            data.write(content)  # 只能写字符串
        except IOError as err:
            logging.error("creat file_name error : %s", str(err))
        finally:
            if 'data' in locals():
                data.close()


    @staticmethod
    def read_small_file(file_name):
        try:
            with open(file_name, 'r') as f:
                file_content = f.read()
                # print "read file " + file_name
            if not file_content:
                print("no data in file " + file_name)
                logging.error("no data in file %s",file_name)
        except IOError as e:
            print("I/O error({0}): {1}".format(e.errno, e.strerror))
            logging.error("IOError in file %s, I/O error(%s): %s ", file_name, e.errno, e.strerror)
        except:
            print("Unexpected error:", sys.exc_info()[0])
            logging.error("Unexpected error: %s", sys.exc_info()[0])

        return file_content

    @staticmethod
    def alarm_report(message, err_code=6001001, metric_name='rec_plt_tools'):
        labels_data = dict()
        labels_data['err_code'] = err_code
        labels_data['err_msg'] = message

        headers = {"Content-Type": "application/json"}
        url = 'http://pm-laserver-master.prod.abc.sheincorp.cn/api/v1.0/kafka/monitor_metrics'
        t = time.time()

        alarm_data = dict()
        alarm_data['metric_name'] = metric_name
        alarm_data['labels'] = labels_data
        alarm_data['value'] = 1
        alarm_data['timestamp'] = (int(round(t * 1000)))
        try:
            requests.post(url, data=json.dumps(alarm_data), headers=headers)
        except requests.exceptions.Timeout:
            print('requests.exceptions.Timeout')
        except requests.exceptions.TooManyRedirects:
            print('requests.exceptions.TooManyRedirects')
        except requests.exceptions.RequestException as e:
            print(e)

    # 输入目录路径，输出最新文件完整路径
    @staticmethod
    def find_new_file(dir):
        file_lists = os.listdir(dir)
        if not file_lists:
            return None
        file_lists.sort(key=lambda fn: os.path.getmtime(os.path.join(dir, fn)) if not os.path.isdir(os.path.join(dir, fn)) else 0)
        print('new： ' + file_lists[-1])
        new_file = os.path.join(dir, file_lists[-1])
        print('new path：' + new_file)
        return new_file


    @staticmethod
    def is_not_blank(str):
        if str and str.strip():
            # str is not None AND str is not empty or blank
            return True
        # str is None OR str is empty or blank
        return False


    @staticmethod
    def get_date(days):
        return datetime.datetime.now() - timedelta(days=days)
