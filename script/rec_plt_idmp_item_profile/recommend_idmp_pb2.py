# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: recommend_idmp.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='recommend_idmp.proto',
  package='abc.recommend_plt.idmp',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x14recommend_idmp.proto\x12\x16\x61\x62\x63.recommend_plt.idmp\"#\n\x03Tag\x12\x0e\n\x06tag_id\x18\x01 \x01(\x05\x12\x0c\n\x04vals\x18\x02 \x03(\x03\"=\n\x07ItemKey\x12\x10\n\x08rec_type\x18\x01 \x01(\x05\x12\x0f\n\x07pool_id\x18\x02 \x01(\t\x12\x0f\n\x07item_id\x18\x03 \x01(\x03\"4\n\x07ItemTag\x12)\n\x04tags\x18\x02 \x03(\x0b\x32\x1b.abc.recommend_plt.idmp.Tag\"A\n\x0bItemProfile\x12\x32\n\titem_tags\x18\x01 \x03(\x0b\x32\x1f.abc.recommend_plt.idmp.ItemTag\"H\n\x12ItemProfileRequest\x12\x10\n\x08rec_type\x18\x01 \x01(\x05\x12\x0f\n\x07pool_id\x18\x02 \x01(\t\x12\x0f\n\x07item_id\x18\x03 \x03(\x03\"\xdb\x01\n\x13ItemProfileResponse\x12\x39\n\x0citem_profile\x18\x01 \x01(\x0b\x32#.abc.recommend_plt.idmp.ItemProfile\x12J\n\x06status\x18\x02 \x01(\x0e\x32:.abc.recommend_plt.idmp.ItemProfileResponse.ResponseStatus\"=\n\x0eResponseStatus\x12\x0b\n\x07SUCCESS\x10\x00\x12\x13\n\x0fPARAMETER_EMPTY\x10\x01\x12\t\n\x05\x45RROR\x10\x02\x32\x81\x01\n\x12ItemProfileService\x12k\n\x0eGetItemProfile\x12*.abc.recommend_plt.idmp.ItemProfileRequest\x1a+.abc.recommend_plt.idmp.ItemProfileResponse\"\x00\x62\x06proto3')
)



_ITEMPROFILERESPONSE_RESPONSESTATUS = _descriptor.EnumDescriptor(
  name='ResponseStatus',
  full_name='abc.recommend_plt.idmp.ItemProfileResponse.ResponseStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SUCCESS', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PARAMETER_EMPTY', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ERROR', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=502,
  serialized_end=563,
)
_sym_db.RegisterEnumDescriptor(_ITEMPROFILERESPONSE_RESPONSESTATUS)


_TAG = _descriptor.Descriptor(
  name='Tag',
  full_name='abc.recommend_plt.idmp.Tag',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_id', full_name='abc.recommend_plt.idmp.Tag.tag_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vals', full_name='abc.recommend_plt.idmp.Tag.vals', index=1,
      number=2, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=48,
  serialized_end=83,
)


_ITEMKEY = _descriptor.Descriptor(
  name='ItemKey',
  full_name='abc.recommend_plt.idmp.ItemKey',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rec_type', full_name='abc.recommend_plt.idmp.ItemKey.rec_type', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pool_id', full_name='abc.recommend_plt.idmp.ItemKey.pool_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_id', full_name='abc.recommend_plt.idmp.ItemKey.item_id', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=85,
  serialized_end=146,
)


_ITEMTAG = _descriptor.Descriptor(
  name='ItemTag',
  full_name='abc.recommend_plt.idmp.ItemTag',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tags', full_name='abc.recommend_plt.idmp.ItemTag.tags', index=0,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=148,
  serialized_end=200,
)


_ITEMPROFILE = _descriptor.Descriptor(
  name='ItemProfile',
  full_name='abc.recommend_plt.idmp.ItemProfile',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='item_tags', full_name='abc.recommend_plt.idmp.ItemProfile.item_tags', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=202,
  serialized_end=267,
)


_ITEMPROFILEREQUEST = _descriptor.Descriptor(
  name='ItemProfileRequest',
  full_name='abc.recommend_plt.idmp.ItemProfileRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rec_type', full_name='abc.recommend_plt.idmp.ItemProfileRequest.rec_type', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pool_id', full_name='abc.recommend_plt.idmp.ItemProfileRequest.pool_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_id', full_name='abc.recommend_plt.idmp.ItemProfileRequest.item_id', index=2,
      number=3, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=269,
  serialized_end=341,
)


_ITEMPROFILERESPONSE = _descriptor.Descriptor(
  name='ItemProfileResponse',
  full_name='abc.recommend_plt.idmp.ItemProfileResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='item_profile', full_name='abc.recommend_plt.idmp.ItemProfileResponse.item_profile', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='abc.recommend_plt.idmp.ItemProfileResponse.status', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _ITEMPROFILERESPONSE_RESPONSESTATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=344,
  serialized_end=563,
)

_ITEMTAG.fields_by_name['tags'].message_type = _TAG
_ITEMPROFILE.fields_by_name['item_tags'].message_type = _ITEMTAG
_ITEMPROFILERESPONSE.fields_by_name['item_profile'].message_type = _ITEMPROFILE
_ITEMPROFILERESPONSE.fields_by_name['status'].enum_type = _ITEMPROFILERESPONSE_RESPONSESTATUS
_ITEMPROFILERESPONSE_RESPONSESTATUS.containing_type = _ITEMPROFILERESPONSE
DESCRIPTOR.message_types_by_name['Tag'] = _TAG
DESCRIPTOR.message_types_by_name['ItemKey'] = _ITEMKEY
DESCRIPTOR.message_types_by_name['ItemTag'] = _ITEMTAG
DESCRIPTOR.message_types_by_name['ItemProfile'] = _ITEMPROFILE
DESCRIPTOR.message_types_by_name['ItemProfileRequest'] = _ITEMPROFILEREQUEST
DESCRIPTOR.message_types_by_name['ItemProfileResponse'] = _ITEMPROFILERESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Tag = _reflection.GeneratedProtocolMessageType('Tag', (_message.Message,), {
  'DESCRIPTOR' : _TAG,
  '__module__' : 'recommend_idmp_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.idmp.Tag)
  })
_sym_db.RegisterMessage(Tag)

ItemKey = _reflection.GeneratedProtocolMessageType('ItemKey', (_message.Message,), {
  'DESCRIPTOR' : _ITEMKEY,
  '__module__' : 'recommend_idmp_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.idmp.ItemKey)
  })
_sym_db.RegisterMessage(ItemKey)

ItemTag = _reflection.GeneratedProtocolMessageType('ItemTag', (_message.Message,), {
  'DESCRIPTOR' : _ITEMTAG,
  '__module__' : 'recommend_idmp_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.idmp.ItemTag)
  })
_sym_db.RegisterMessage(ItemTag)

ItemProfile = _reflection.GeneratedProtocolMessageType('ItemProfile', (_message.Message,), {
  'DESCRIPTOR' : _ITEMPROFILE,
  '__module__' : 'recommend_idmp_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.idmp.ItemProfile)
  })
_sym_db.RegisterMessage(ItemProfile)

ItemProfileRequest = _reflection.GeneratedProtocolMessageType('ItemProfileRequest', (_message.Message,), {
  'DESCRIPTOR' : _ITEMPROFILEREQUEST,
  '__module__' : 'recommend_idmp_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.idmp.ItemProfileRequest)
  })
_sym_db.RegisterMessage(ItemProfileRequest)

ItemProfileResponse = _reflection.GeneratedProtocolMessageType('ItemProfileResponse', (_message.Message,), {
  'DESCRIPTOR' : _ITEMPROFILERESPONSE,
  '__module__' : 'recommend_idmp_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.idmp.ItemProfileResponse)
  })
_sym_db.RegisterMessage(ItemProfileResponse)



_ITEMPROFILESERVICE = _descriptor.ServiceDescriptor(
  name='ItemProfileService',
  full_name='abc.recommend_plt.idmp.ItemProfileService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=566,
  serialized_end=695,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetItemProfile',
    full_name='abc.recommend_plt.idmp.ItemProfileService.GetItemProfile',
    index=0,
    containing_service=None,
    input_type=_ITEMPROFILEREQUEST,
    output_type=_ITEMPROFILERESPONSE,
    serialized_options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_ITEMPROFILESERVICE)

DESCRIPTOR.services_by_name['ItemProfileService'] = _ITEMPROFILESERVICE

# @@protoc_insertion_point(module_scope)
