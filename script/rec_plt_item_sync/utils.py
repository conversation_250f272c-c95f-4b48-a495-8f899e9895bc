# -*- coding:UTF-8 -*-
import os
import sys
import logging
import json
import time,datetime
import hashlib

class CommonUtils:
    path = sys.path[0]
    proto_path = path + '/proto/'
    script_path = path + '/script/'
    log_path = '/data/log/'
    base_path = '/data/base/'
    data_path = '/data/data0/'
    db_path = '/data/sqlite/'
    s3_data_dev = 's3://abc-rec/test/dev/'
    s3_data = 's3://abc-rec/prod/base/'
    zk_host_dev = '*************'
    zk_host = '************'
    namespace = ''
    namespacex = ''
    redis_nodes_dev = [{'host': 'recplt-redis-cluster-dev', 'port': '7000'},]
    #redis_nodes = [{'host': 'recplt-redis-cluster-prod-default', 'port': '7000'},]
    redis_nodes = [{'host': 'abc-sz-rec-model.vjdj1z.clustercfg.usw2.cache.amazonaws.com', 'port': '9736'},]

    def __init__(self):
        pass

    @staticmethod
    def getHbaseHost():
        if len(sys.argv) > 1 and sys.argv[1] == 'prod':
            return CommonUtils.zk_host
        else:
            return CommonUtils.zk_host_dev

    @staticmethod
    def get_redis_nodes():
        if len(sys.argv) > 1 and sys.argv[1] == 'prod':
            return CommonUtils.redis_nodes 
        else:
            return CommonUtils.redis_nodes_dev

    @staticmethod
    def create_path(path):
        if not os.path.isdir(path):
            os.mkdir(path)

    @staticmethod
    def get_datetime():
        now = int(time.time())
        timearr = time.localtime(now)
        datetime = time.strftime('%Y%m%d%H%M%S', timearr)
        return datetime

    @staticmethod
    def create_file(file_name, content, type):
        try:
            data = open(file_name, type)
            data.write(content)
        except IOError as err:
            logging.error("creat file_name error : %s", str(err))
        finally:
            if 'data' in locals():
                data.close()

    @staticmethod
    def delete_file(file_name):
        # fileName = unicode(file_name, "utf8")
        if os.path.isfile(file_name):
            try:
                os.remove(file_name)
            except OSError as e:
                logging.error("Error: %s - %s. delete file %s error" % (e.filename, e.strerror, file_name))

    @staticmethod
    def write_sync_pb(model_name, model_file, saveLocal):
        whole_model_file = CommonUtils.data_path + model_file
        tag_file = model_name + '.tag'
        whole_tag_file = CommonUtils.data_path + tag_file
        md5sum = CommonUtils.md5_calc(whole_model_file)
        tag_content = md5sum + ' ' + model_file
        CommonUtils.create_file(whole_tag_file, tag_content, 'w')

        if len(sys.argv) > 1 and sys.argv[1] == 'prod':
            s3_path = CommonUtils.s3_data
        else:
            s3_path = CommonUtils.s3_data_dev

        cmd_sync_model = 'aws s3 cp ' + whole_model_file + ' ' + s3_path
        os.system(cmd_sync_model)

        cmd_sync_tag = 'aws s3 cp ' + whole_tag_file + ' ' + s3_path
        os.system(cmd_sync_tag)

        if not saveLocal:
            CommonUtils.delete_file(whole_model_file)
            CommonUtils.delete_file(whole_tag_file)

        return model_file

    @staticmethod
    def sum_file(fobj):
        m = hashlib.md5()
        while True:
            data_flow = fobj.read(8096)
            if not data_flow:
                break
            m.update(data_flow)
        return m.hexdigest()

    @staticmethod
    def md5_calc(file_name):
        # Returns an md5 hash for file fname, or stdin if fname is "-"
        if file_name == '-':
            ret = CommonUtils.sum_file(sys.stdin)
        else:
            try:
                f = open(file_name, 'rb')
            except:
                return 'Failed to open file'
            ret = CommonUtils.sum_file(f)
            f.close()
        return ret

    @staticmethod
    def get_file_modify_time(file_path):
        if os.path.exists(file_path):
            #file_path = unicode(file_path, 'utf8')
            t = os.path.getmtime(file_path)
            return t
        return 0

    @staticmethod
    def get_tag_data_file(tag_path, tag_file):
        md5 = None
        data_file = None

        if not os.path.exists(tag_path+tag_file):
            return ''

        with open(tag_path+tag_file, 'r') as f:
            for l in f:
                tup = l.rstrip('\n').rstrip().split()
                md5 = tup[0]
                data_file = tup[1]

        if not md5:
            return ''

        if not data_file:
            return ''

        md5sum = CommonUtils.md5_calc(tag_path + data_file)

        if md5 != md5sum:
            return ''

        return data_file

    @staticmethod
    def get_tag_md5_file(tag_path, tag_file, old_md5):
        md5 = None
        data_file = None

        if not os.path.exists(tag_path+tag_file):
            return '', ''

        with open(tag_path+tag_file, 'r') as f:
            for l in f:
                tup = l.rstrip('\n').rstrip().split()
                md5 = tup[0]
                data_file = tup[1]

        if not md5:
            return '', ''

        if not data_file:
            return '', ''

        if md5 == old_md5:
            return '', ''

        md5sum = CommonUtils.md5_calc(tag_path + data_file)
        if md5 != md5sum:
            return '', ''

        return md5, data_file

    @staticmethod
    def get_tag_md5_file2(tag_path, tag_file, old_md5):
        md5 = None
        data_file = None

        #print 'get tag md5 file2',tag_path,tag_file
        if not os.path.exists(tag_path + tag_file):
            return '', ''

        with open(tag_path + tag_file, 'r') as f:
            for l in f:
                tup = l.rstrip('\n').rstrip().split()
                md5 = tup[0]
                data_file = tup[1]

        if not md5:
            return '', ''

        if not data_file:
            return '', ''

        if md5 == old_md5:
            return '', data_file

        md5sum = CommonUtils.md5_calc(tag_path + data_file)
        if md5 != md5sum:
            return '', ''

        return md5, data_file
