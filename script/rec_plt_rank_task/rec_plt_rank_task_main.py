#!/usr/bin/env python
# _*_ coding:utf-8 _*_

#
# athor: bluse
# create : 2019.01.18
#
import os
import sys
import logging
import warnings
import util
import goods_rank_task

def main(section):
    path = sys.path[0]
    log_path = path + '/log/'

    util.create_path(log_path)
    util.config_logger(log_path, section)
 
    task = goods_rank_task.PltItemDaily(path, section)
    ret = task.task_main()
    if not ret:
        logging.error("calculate b1_item_tag_site error");

if __name__ == '__main__':
    if len(sys.argv) < 2:
        warnings.warn("please input last days")
        sys.exit()
    section = sys.argv[1]
    main(section)
