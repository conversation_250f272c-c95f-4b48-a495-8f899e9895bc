#!/bin/sh

set -e
#set -x

check_partition() {
    table=$1
    dt=$2
    cnt=`hive -e "show partitions ${table};" |grep "dt=${dt}"|wc -l`
    echo $cnt
    if  [ $cnt -eq 0 ]
    then
        echo  "${table} ${dt} not ok"
        exit 1
    fi
}

cd /home/<USER>/recplt
dt=`date  +"%Y%m%d" -d  "-1 days"`

check_partition dw.dw_pub_site_gds_info_td_snap ${dt}
check_partition dm.dm_goods_basic_data_d ${dt}

hive -e "select site_id, site_l1_cd from dw.dw_pub_site_td"  > conf/site_country.conf.${dt}
cd conf
md5sum site_country.conf.${dt} > site_country.conf.tag
aws s3 cp site_country.conf.${dt} s3://dsdata01/rec_plt_svr/base/
aws s3 cp site_country.conf.tag s3://dsdata01/rec_plt_svr/base/
awk 'BEGIN{FS="\t";OFS=":"}{print $1,$2}'  site_country.conf.${dt} > site_country.conf
map_str=""
for site in `cat site_country.conf`
do
    map_str=${map_str}${site}","
done
echo ${map_str}

aws s3 cp s3://dsdata01/rec_plt_svr/base/site_warehouse_map.conf.${dt} site_warehouse_map.conf.${dt}
awk 'BEGIN{FS=",";OFS=":"}{print $1,$2}'  site_warehouse_map.conf.${dt} > site_warehouse_map.conf
map_str2=""
for site in `cat site_warehouse_map.conf`
do
    map_str2=${map_str2}${site}","
done
echo ${map_str2}


hive -e "
select supplier_id,
case
when cate_nm like '%生产%' then 1
when cate_nm like '%外协%' then 2
else 3
end
from dw.dw_pty_supplier_td;" > supplier_type.conf.${dt}
md5sum supplier_type.conf.${dt} > supplier_type.conf.tag
awk 'BEGIN{FS="\t";OFS=":"}{print $1,$2}'  supplier_type.conf.${dt} > supplier_type.conf
map_str3=""
for site in `cat supplier_type.conf`
do
    map_str3=${map_str3}${site}","
done
echo ${map_str3}
aws s3 cp supplier_type.conf.${dt}  s3://dsdata01/rec_plt_svr/base/
aws s3 cp supplier_type.conf.tag  s3://dsdata01/rec_plt_svr/base/


cd /home/<USER>/recplt
hive -e "
drop table recplt.recplt_site_country_goods;
create table recplt.recplt_site_country_goods as
select a.*,b.spu
from
(select * from (
select *,
ROW_NUMBER() OVER(PARTITION BY site_tp,site_country,goods_id ORDER BY frst_sale_time) AS rn
from
(select site_tp,str_to_map('${map_str}')[site_id] as site_country, str_to_map('${map_str2}')[site_id] as warehouse,
regexp_replace(goods_id,'rw_','') as goods_id, goods_sn,
regexp_replace(sku_cate_id,'rw_','') as sku_cate_id,
supplier_id, str_to_map('${map_str3}')[supplier_id] as supplier_type,
frst_sale_time, onsale_flag, sale_flag,is_virtual_stock
from dw.dw_pub_site_gds_info_td_snap
where dt='${dt}' and site_tp in ('shein', 'romwe')
)t1
)t2
where rn=1
) a
join (
select skc,spu from dw.dw_pub_skc_info_td group by skc,spu
)b
on a.goods_sn = b.skc
"

hive -e "
select goods_sn,goods_id,sku_cate_id,concat_ws('-', collect_set(site_country)),concat_ws('|', collect_set(site_id)), warehouse, if(site_tp='shein', 7,9)
from(
select site_tp,goods_sn,
regexp_replace(goods_id,'rw_','') as goods_id,
regexp_replace(sku_cate_id,'rw_','') as sku_cate_id,
str_to_map('${map_str}')[site_id] as site_country,site_id,str_to_map('${map_str2}')[site_id] as warehouse
from dw.dw_pub_site_gds_info_td_snap
where dt='${dt}' and site_tp in ('shein', 'romwe') and sale_flag=1 and is_virtual_stock=0
)t
group by site_tp,goods_sn,goods_id,sku_cate_id,warehouse
" > data/shein_goods_warehouse.dat.${dt}
cd data
md5sum shein_goods_warehouse.dat.${dt} > shein_goods_warehouse.dat.tag
aws s3 cp shein_goods_warehouse.dat.${dt}  s3://dsdata01/rec_plt_svr/base/
aws s3 cp shein_goods_warehouse.dat.tag  s3://dsdata01/rec_plt_svr/base/


cd /home/<USER>/recplt
hive -e "
drop table recplt.recplt_shein_goods_tag_site1;
create table recplt.recplt_shein_goods_tag_site1 as
select distinct t1.site_tp,site_country, goods_id,
concat_ws('|',new_cate_1_id, new_cate_2_id, new_cate_3_id, new_cate_4_id, t1.sku_cate_id, supplier_type, frst_sale_time, spu) as tags
from
(select site_tp, site_country, goods_id, sku_cate_id, supplier_type, frst_sale_time,spu
from recplt.recplt_site_country_goods
where sale_flag=1
)t1
join(
select site_tp,
regexp_replace(new_cate_1_id,'rw_','') as new_cate_1_id,
regexp_replace(new_cate_2_id,'rw_','') as new_cate_2_id,
regexp_replace(new_cate_3_id,'rw_','') as new_cate_3_id,
regexp_replace(new_cate_4_id,'rw_','') as new_cate_4_id,
regexp_replace(sku_cate_id,'rw_','') as sku_cate_id
from dw.dw_pub_sku_category_td
where site_tp in ('shein', 'romwe') 
)t2
on t1.site_tp = t2.site_tp and t1.sku_cate_id = t2.sku_cate_id"

hive -e "
drop table recplt.recplt_shein_goods_tag_site2;
create table recplt.recplt_shein_goods_tag_site2 as
select distinct attribute_value_name as site_country,goods_id, '1' as tags
from
(select distinct skc,attribute_value_name
From dw.dw_pub_gds_skc_attribute_pdc_td
where attribute_name_id=169 and attribute_value_name_id in (1605,1606,1607,1608) and onsale_flag=1
)t1
join (
select goods_id,goods_sn
from recplt.recplt_site_country_goods
)t2
on t1.skc = t2.goods_sn;"


sdt=`date  +"%Y%m%d" -d  "-7 days"`
edt=`date  +"%Y%m%d" -d  "-1 days"`


hive -e "
drop table recplt.recplt_shein_goods_tag_site3;
create table recplt.recplt_shein_goods_tag_site3 as
select site_tp,site_country,goods_id, concat_ws('|', string(sum(expose_pv)),string(sum(pay_amt)),string(sum(sale_cnt))) as tags
from(
select site_tp,str_to_map('${map_str}')[site_id] as site_country,goods_id,
if(sum(expose_pv) is null,0,sum(expose_pv)) as expose_pv,
if(sum(pay_amt) is null,0,sum(pay_amt)) as pay_amt,
if(sum(sale_cnt) is null,0,sum(sale_cnt)) as sale_cnt
from dm.dm_goods_basic_data_d
where dt>=${sdt} and dt<=${edt} and str_to_map('${map_str}')[site_id] is not null and site_tp in ('shein', 'romwe')
group by site_tp,str_to_map('${map_str}')[site_id],goods_id
)t
group by site_tp,site_country,goods_id
"

hive -e "
set tez.queue.name=label;
select t1.site_country, t1.goods_id,
concat_ws('|', t1.tags, if(t2.tags is NULL, '0', '1'), if(t3.tags is NULL, '0|0|0', t3.tags)) as tags,
if(t1.site_tp='shein',7,9)
from recplt.recplt_shein_goods_tag_site1 t1
left join recplt.recplt_shein_goods_tag_site2 t2
on t1.site_country = t2.site_country and t1.goods_id = t2.goods_id
left join recplt.recplt_shein_goods_tag_site3 t3
on t1.site_tp = t3.site_tp and t1.site_country = t3.site_country and t1.goods_id = t3.goods_id
" > data/shein_goods_tag_site.dat.${dt}

hive -e "
select distinct goods_id,tags, if(site_tp='shein',7,9)
from recplt.recplt_shein_goods_tag_site1
" > data/shein_goods_tag.dat.${dt}

cd data/

md5sum shein_goods_tag.dat.${dt} > shein_goods_tag.dat.tag
aws s3 cp shein_goods_tag.dat.${dt} s3://dsdata01/rec_plt_svr/base/
aws s3 cp shein_goods_tag.dat.tag s3://dsdata01/rec_plt_svr/base/

md5sum shein_goods_tag_site.dat.${dt} > shein_goods_tag_site.dat.tag
aws s3 cp shein_goods_tag_site.dat.${dt} s3://dsdata01/rec_plt_svr/base/
aws s3 cp shein_goods_tag_site.dat.tag s3://dsdata01/rec_plt_svr/base/
