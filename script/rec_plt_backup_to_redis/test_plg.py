#!/usr/bin/env python
# _*_ coding:utf-8 _*_

import sys
import os
import time
import requests
import redis
import logging
import json
import collections
import warnings
from rediscluster import StrictRedisCluster
from datetime import datetime
from datetime import timedelta

sys.path.append(r'./py_proto')
import recommend_model_pb2 as recommend_model_pb2
import hashlib
import subprocess

tagfile_md5 = {}

data_id = {'rec_list_b1_pinleiguan_v01':23}

section = 'dev'

def ReturnLogObject(date_time):
    logger = logging.getLogger(__name__)
    logger.setLevel(level=logging.INFO)
    handler = logging.FileHandler('/data/log/' + 'backup-redis.log')
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    return logger

def config_logger(path, file_name, section):
    if section == 'prod':
        logging.basicConfig(filename=path+file_name,
                format   = '[%(asctime)s  %(filename)s %(lineno)d] %(message)s',
                datefmt  = '%Y-%m-%d %A %H:%M:%S',
                filemode = 'w',
                level=logging.INFO) 
    else:
        logging.basicConfig(filename = path+file_name,
                format   = '[%(asctime)s  %(filename)s %(lineno)d] %(message)s',
                datefmt  = '%Y-%m-%d %A %H:%M:%S',
                filemode = 'w',
                level=logging.INFO)

def alarm_data_error(key):
    alarm_dict = { 
            1:{'err_code':10001,'err_msg':'Redis Connect fail.'},
            2:{'err_code':10002,'err_msg':'Splice Key fail.(bussiness_id == 0 or data_id == 0 or fea_dict == None)'},
            3:{'err_code':10003,'err_msg':'model tag file incomplete'},
	    4:{'err_code':10004,'err_msg':'redis.dat file is null.'},
            5:{'err_code':10005,'err_msg':'pinleiguan backup data is null'}
          }
    return alarm_dict[key]

#初始化Redis集群
def RedisCluster(env):
    redisconn = None
    if env == 'local':
	redis_nodes =  [
			    {'host': '***************','port':7001},
			    {'host': '***************','port':7002},
			    {'host': '***************','port':7003},
			    {'host': '***************','port':7004},
		            {'host': '***************','port':7005},
		            {'host': '***************','port':7006}
		       ]
    elif env == 'prod':
        redis_nodes = [
		        {'host':'************','port':9736}
		      ]
    elif env == 'test':
	redis_nodes = [ 
		       {'host':'abc-service-redis-master','port':6379}
                      ]
    elif env == 'stage':
	redis_nodes = [
                       {'host':'abc-sz-stage-pde.vjdj1z.0001.usw2.cache.amazonaws.com','port':9736}
                      ]
    try:
        redisconn = StrictRedisCluster(startup_nodes=redis_nodes,decode_responses=False,skip_full_coverage_check=True)
        return redisconn
    except Exception,e:
        print "Connect Error!"
        alarm_report(alarm_data_error(1),section)
        return redisconn
                       
#模型数据文件序列化
def CheckPbMsg(model_file):
    fr = open(model_file, "rb")
    item_tag_model =  recommend_model_pb2.RecListModel()
    item_tag_model.ParseFromString(fr.read())
    fr.close()
    return item_tag_model

#获取模型数据文件对象
def getModelObject(model_file):
    fr = open(model_file, "rb")
    item_tag_model =  recommend_model_pb2.RecListModel()
    item_tag_model.ParseFromString(fr.read())
    fr.close()
    return item_tag_model

#获取序列化后的模型文件数据，并存放在字典中
def getModelDataAndInsertRedis(pathFile, item_tag_model, data_id, redisconn, logger):
    print item_tag_model.bussiness_id
    print len(item_tag_model.rec_list_infos)
    redis_dict = {}
    cnt = 0
    for rec_info in item_tag_model.rec_list_infos:
        dim_dict = {}
        rec_key = ''
        for i in range(len(rec_info.key.dimensions)):
            dim_dict[rec_info.key.dimensions[i].fea] = rec_info.key.dimensions[i].val
        rec_key = RegModelRedisKey(item_tag_model.bussiness_id,data_id,dim_dict,rec_key)
        print '--->',rec_key
        if rec_key == '':
            continue
        rec_val = rec_info.val.SerializeToString()
        #redis_dict[rec_key] = rec_val
	redisconn.set(rec_key,rec_val,ex=7*24*3600)
	cnt += 1
    print '---> cnt = ', cnt
    logger.info(pathFile + ': Insert Redis ' + str(cnt) + ' data key-value')
    #return redis_dict
       
#组装key    
def RegModelRedisKey(bussiness_id,data_id,dim_dict,key):
    if bussiness_id == 0 or data_id == 0 or len(dim_dict) == 0:
        alarm_report(alarm_data_error(2))
        return ''
    key = 'rec_b' + str(bussiness_id) + '_d' + str(data_id)
    for it in sorted(dim_dict):
        key = key + '_' + it + '_' + dim_dict[it]
    return key

#初始化数据路径
def initOfPath(path):        
    path = path
    print path
    if not os.path.exists(path):
        os.makedirs(path)
    return path

#从/data/done/下数据文件的tag，获取MD5和数据文件名
def getDataFileMD5AndName(data_file_tag,path,logger):
    fr = open(path + data_file_tag,'r+')
    lines = fr.readline()
    fr.close()
    item = lines.strip().split('  ')
    if len(item) != 2:
        alarm_report(alarm_data_error(3),section)
	logger.error('not get datafile and md5 from /data/done/.tag.')
	return
    md5 = item[0]
    data_file_name = item[1]
    it = data_file_name.strip().split('.')
    data_file_name_pre = it[0] + '.' + it[1]
    return md5, data_file_name_pre,data_file_name

#数据插入RedisCluster
def DataInsertRedisCluster(pathFile, data_id, env, logger):
    redisconn = RedisCluster(env)
    if redisconn == None:
	logger.error('Redis connect failed.')
        alarm_report(alarm_data_error(1),section)
        return
    logger.info('Redis connect success.')
    item_tag_model = CheckPbMsg(pathFile)
    logger.info(pathFile + ':Begin Insert Redis.')
    getModelDataAndInsertRedis(pathFile, item_tag_model, data_id, redisconn, logger)
    logger.info(pathFile + ':Insert Redis Finished.')

#通过配置文件获取data_id 和 tag_file
def GetDataIDAndWatchTagFile(confpath,id_tagfile = {}):
    fr = open(confpath,'r+')
    dt = fr.read()
    redis_info = json.loads(dt)
    print type(redis_info)
    fr.close()
    id_tagfile = dict(redis_info)
    return id_tagfile

#load上一次各个数据模型文件的MD5
def loadModelFileMD5(md5ofconf):
    fr = open(md5ofconf,'r+')
    for line in fr.readlines():
        item = line.strip().split('\t')
        if len(item) != 2:
            continue
        tagfile_md5[item[0]] = item[1]
    fr.close()

#write更新后的各个数据模型文件的MD5到文件
def writeModelFileMD5(md5ofconf):
    fw = open(md5ofconf,'w+')
    for key,value in tagfile_md5.items():
        fw.write(key + '\t' + value + '\n')
    fw.close()

#从/data/done/中变化了的数据文件，并插入到RedisCluster中
def DownloadFromPathAndInsertRedis(data_id,data_file_tag,data_file,path,pathofmd5conf,env,logger):
    loadModelFileMD5(pathofmd5conf)
    md5, data_file_name_pre,data_file_name = getDataFileMD5AndName(data_file_tag,path,logger)
    if data_file_name_pre not in tagfile_md5:
        tagfile_md5[data_file_name_pre] = ''

    if tagfile_md5[data_file_name_pre] != md5:
	logger.info(data_file_name + ' md5 was changed ' + md5)
        tagfile_md5[data_file_name_pre] = md5
        DataInsertRedisCluster(path + data_file_name, data_id, env, logger)
    writeModelFileMD5(pathofmd5conf)

#通过key获取Redis中key对应的数据
def getDataValByKeyFromRedis(bussiness_id,data_id,fea_dict,key):
    key = RegModelRedisKey(bussiness_id,data_id,fea_dict,key)
    redisconn = RedisCluster()
    if redisconn == None:
        alarm_report(alarm_data_error(1),section)
        #sys.exit(1)
        return []
    val = redisconn.get(key)
    print val
    print type(val)
    dataVal = []
    item_model = recommend_model_pb2.RecListVal()
    item_model.ParseFromString(val)
    for item in item_model.items:
        dataVal.append(item)
        print item
        print item.id
        print item.score
        print item.cate
    return dataVal

#创建日志文件夹
def create_path(path):
    if not os.path.isdir(path):
        os.mkdir(path)


#告警上报
def alarm_report(labels_data,section):
    headers = {"Content-Type": "application/json"}
    url = 'http://pm-laserver-master.prod/api/v1.0/kafka/monitor_metrics'
    if (section == 'dev'):
        url = 'http://pm-laserver-master.prod.abc.sheincorp.cn/api/v1.0/kafka/monitor_metrics'
    t = time.time()
    alarm_data = dict()
    alarm_data['metric_name'] = 'recplt_modeldata_to_redis'
    alarm_data['labels'] = labels_data
    alarm_data['value'] = 1
    alarm_data['timestamp'] = (int(round(t * 1000)))
    print json.dumps(alarm_data)
    r = requests.post(url, data=json.dumps(alarm_data), headers=headers)
    print("alarm report " + r.text)


#返回xxxx.pb.tag文件中的MD5和文件名
def returnNewMD5AndFileName(filename):
    fr = open(filename,'r+')
    line = fr.readline()
    item = line.strip().split(' ')
    if len(item) != 2:
	logging.error("{0} file appear error".format(filename))
	return '',''
    md5 = item[0]
    filename = item[1]
    return md5 , filename

#组装品类馆redis key
def getInsertRedisKey(dim_dict):
    rec_key = 'rec_recovery|'
    flag_key = False
    if 'bussiness_id' not in dim_dict:
	logging.error("bussiness_id no exist")
        return flag_key,rec_key
    rec_key += ('b1='+ str(dim_dict['bussiness_id']) + '|')
    rec_key += ('scene_id=' + str(10) + '|')
    if 'site_id' not in dim_dict:
	logging.error("site_id no exist")
	return flag_key,rec_key
    rec_key += ('site_id=' + dim_dict['site_id'] + '|')
    if 'site_uid' not in dim_dict:
	logging.error("site_uid no exist")
        return flag_key,rec_key
    rec_key += ('site_uid=' + dim_dict['site_uid'] + '|')
    if 'language'not in dim_dict:
        logging.error("language no exist")
        return flag_key,rec_key
    rec_key += ('language=' + dim_dict['language'] + '|')
    if 'pool_id' not in dim_dict:
        logging.error("pool_id no exist")
        return flag_key,rec_key
    flag_key = True
    rec_key += ('pool_id=' + dim_dict['pool_id'])
    return flag_key,rec_key

#组装品类馆redis val
def getInsertRedisVal(dim_val):
    rec_val = ''
    flag_val = False
    count = 0
    for val in dim_val:
	rec_val += (str(val) + '|')
        count += 1
        flag_val = True
        if count == 300:
            break
    return flag_val,rec_val
    
#更新品类馆兜底数据到redis
def UpdatePLGbackupDataToRedis(pathfile, env):
    item_model = getModelObject(pathfile)
    count = 0
    redisconn = RedisCluster(env)
    if len(item_model.rec_list_infos) == 0:
	logging.error('pinleiguan backup data is null')
        alarm_report(alarm_data_error(5),env)
    for rec_info in item_model.rec_list_infos:
        dim_dict = {}
        dim_dict['bussiness_id'] = item_model.bussiness_id
        rec_key = ''
        for i in range(len(rec_info.key.dimensions)):
            dim_dict[rec_info.key.dimensions[i].fea] = rec_info.key.dimensions[i].val
        flag_key,rec_key = getInsertRedisKey(dim_dict)
        if not flag_key:
	    logging.error('rec_key is error:------->{0}'.format(rec_key))
            continue
	logging.info('rec_key:------->{0}'.format(rec_key))
        count += 1
        dim_val = {}
        if len(rec_info.val.items) == 0:
	    logging.error('rec_key corresponding to value is null:------->{0}'.format(rec_key))
            continue
        for i in range(len(rec_info.val.items)):
            dim_val[rec_info.val.items[i].id] = rec_info.val.items[i].score
	dim_val = sorted(dim_val)
        rec_val = ''
        flag_val,rec_val = getInsertRedisVal(dim_val)
        if not flag_val:
            logging.error('rec_val is error:------->{0}'.format(rec_val)) 	
        logging.info('rec_val:------->{0}'.format(rec_val))
        if count == 100:
            break
        redisconn.set(rec_key,rec_val,ex=7*24*3600)
    logging.info('Insert pinleiguan backup {0} key-val '.format(count))
     
         
#主函数
def main(env,common_path):
    old_plg_md5 = ''   #品类馆MD5
    #old_syd_shein_md5 = ''   #首页底shein ---MD5
    #old_syd_romwe_md5 = ''   #首页底rowme ---Md5
    #old_sxy_shein_md5 = ''   #商详页shein ---MD5
    #old_sxy_rowme_md5 = ''   #商详页rowme ---MD5
    #old_sxl_md5 = ''   #社区信息流MD5
    while True:
    	new_plg_md5, plg_name = returnNewMD5AndFileName(common_path + 'rec_list_b1_pinleiguan_v01.pb.tag')
	#new_syd_shein_md5, syd_shein_name = returnNewMD5AndFileName('/data/done/rec_list_b1_best_seller_v01.pb.tag')
        #new_syd_rowme_md5, syd_rowme_name = returnNewMD5AndFileName('/data/done/rec_list_b1_best_seller_romwe_v01.pb.tag')
        #new_sxy_shein_md5, sxy_shein_name = returnNewMD5AndFileName('/data/done/')
	if old_plg_md5 != new_plg_md5:
            logging.info("Begin update pinleiguan redis data old_md5:{0} changed new_md5:{1}".format(old_plg_md5,new_plg_md5))
	    UpdatePLGbackupDataToRedis(common_path + plg_name, env)
            old_plg_md5 = new_plg_md5
            logging.info("pinlieguan redis data update finsih")
	time.sleep(5)

if __name__ == '__main__':
    if len(sys.argv) < 3:
	warnings.warn("please input env ,bussiness_id, scene_id, site_id, site_uid, language, pool_id")
	sys.exit()

    log_path = './log/' 
    create_path(log_path)
    
    env = sys.argv[1]	
    fea_dict = dict([arg.split('=') for arg in sys.argv[2:]])
    flag_key,rec_key = getInsertRedisKey(fea_dict)
    if not flag_key:
    	print ("input key failed")
    redisconn = RedisCluster(env)
    print (redisconn.get(rec_key))
