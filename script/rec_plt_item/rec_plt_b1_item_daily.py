#!/usr/bin/env python
# _*_ coding:utf-8 _*_

import sys
import os
import time
from datetime import datetime
from datetime import timedelta

import py_proto.recommend_model_pb2 as recommend_model_pb2
import hashlib
import subprocess

class PltItemDaily(object):
    #指定tag 模型文件名称
    model_base_name = 'b1_item_daily.pb'
    plt_b1_item_daily = 'plt_b1_item_daily_all.txt'

    def __init__(self,path):        
        self.GetDate()
        self.path = path + '/' + 'tagdata/'
        print self.path
        if not os.path.exists(self.path):
            os.makedirs(self.path)

    def GetDate(self):
        #获取dt
        yesterday = datetime.today() + timedelta(-1)
        dt=yesterday.strftime("%Y%m%d")
        #dt = datetime.now().strftime("%Y%m%d")
        print("datetime:", dt)
        self.dt = dt

    def CheckPbMsg(self, model_file):
        #check protobuf file
        fr = open(model_file, "rb")
        item_profile_model1 =  recommend_model_pb2.ItemProfileModel()
        item_profile_model1.ParseFromString(fr.read())
        fr.close()
        print item_profile_model1

    #同步文件到s3
    def SyncTagFileToS3(self, pb_file, tag_file):
        cmd = 'aws s3 cp ' + self.path + pb_file + " " + "s3://dsdata01/rec_plt_svr/base/" + pb_file
        print cmd
        retcode = subprocess.call(cmd, shell=True)
        print retcode
        
        cmd_tag = 'aws s3 cp ' + self.path + tag_file + " " +" s3://dsdata01/rec_plt_svr/base/" + tag_file
        print cmd_tag
        ret = subprocess.call(cmd_tag, shell=True)
        print ret

    #保存ModelTagFile
    def SaveModelTagFile(self, item_profile_model):
        #write pb message to text file
        dtime = datetime.now().strftime("%Y%m%d%H%M%S")
        model_file_name = self.path + self.model_base_name + "." + dtime
        model_tag_file = self.model_base_name + "." + dtime
        print model_file_name

        serialize_data = item_profile_model.SerializeToString()
        with open(model_file_name, "wb")as f:
            f.write(serialize_data)

        #获取md5 value
        md5val = hashlib.md5(open(model_file_name,'rb').read()).hexdigest()
        print md5val

        model_tag_name = self.path + self.model_base_name + ".tag"
        model_check_file = self.model_base_name + ".tag"
        print model_tag_name
        tag_file = open(model_tag_name, 'wb')
        data = md5val + " " + model_tag_file
        tag_file.write(data)
        tag_file.close()

        #self.CheckPbMsg(model_file_name)
        #同步文件到s3
        self.SyncTagFileToS3(model_tag_file, model_check_file)

    def CalculateHiveData(self):
        item_profile_model =  recommend_model_pb2.ItemProfileModel()
        item_profile_model.bussiness_id = 1 ###recommend_common.proto BussinessId
        tag_attr = item_profile_model.tag_attrs.add()
        tag_attr.name = "oos_flag"
        tag_attr.val_type = recommend_model_pb2.ItemProfileModel.TAG_VAL_TYPE_UNIT

        tag_attr = item_profile_model.tag_attrs.add()
        tag_attr.name = "oos_size_num"
        tag_attr.val_type = recommend_model_pb2.ItemProfileModel.TAG_VAL_TYPE_UNIT

        tag_attr = item_profile_model.tag_attrs.add()
        tag_attr.name = "cate_id"
        tag_attr.val_type = recommend_model_pb2.ItemProfileModel.TAG_VAL_TYPE_UNIT
        
        plt_b1_item_file = self.path + self.plt_b1_item_daily
        frd = open(plt_b1_item_file, "rb")

        for row in frd.readlines():
            goods_id, cate_id,oos_flag,oos_size_num,site_uid = row.split()
            #print("{0},{1}".format(goods_id,cate_id))
            oos_flag_int = int(oos_flag)
            if oos_flag_int== 1 or oos_flag_int== 2:
                item_profile = item_profile_model.item_profiles.add()
                item_profile.id = goods_id
                item_profile.pool = site_uid
                item_tag = item_profile.tags.add()
                item_tag.name = "oos_flag"
                item_val = item_tag.vals.append(oos_flag_int)

                item_tag = item_profile.tags.add()
                item_tag.name = "oos_size_num"
                item_val = item_tag.vals.append(int(oos_size_num))

                item_tag = item_profile.tags.add()
                item_tag.name = "cate_id"
                item_val = item_tag.vals.append(int(cate_id))

        frd.close()

        #pb 写入到文件文件
        self.SaveModelTagFile(item_profile_model)

    def JoinDataFromHiveTable(self):
        hive_join_str = """
        hive -e "
        set  mapreduce.job.reduces=20;
        select distinct goods_id,sku_cate_id 
        from dw.dw_pub_site_gds_info_td_snap t1
        join
        (select goods_sn, status, ROW_NUMBER() OVER(PARTITION BY goods_sn,size ORDER BY last_update_time desc) as rank 
        from ods.ods_sc_oos_record_251 
        where dt={0})t2
        on t1.goods_sn = t2.goods_sn
        where t1.dt={1} and t2.status=0 and t1.site_tp='shein' and t2.rank=1" > {2}{3}
        """.format(self.dt, self.dt, self.path, self.plt_b1_item_daily)
        
        subprocess.call(hive_join_str, shell=True)
'''
def main():
    print "--start collect item data--"
    path = sys.path[0]
    plt_item_daily = PltItemDaily(path)
    #join data
    plt_item_daily.JoinDataFromHiveTable()

    #calculate data to s3
    plt_item_daily.CalculateHiveData()
    print "-- collect item data end---"

if __name__ == '__main__':
    main()
'''
