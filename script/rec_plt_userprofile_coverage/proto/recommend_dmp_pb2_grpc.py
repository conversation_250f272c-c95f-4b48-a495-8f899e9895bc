# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import recommend_dmp_pb2 as recommend__dmp__pb2


class UserProfileServiceStub(object):
  """rpc 服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetUserProfile = channel.unary_unary(
        '/abc.recommend_plt.dmp.UserProfileService/GetUserProfile',
        request_serializer=recommend__dmp__pb2.UserProfileRequest.SerializeToString,
        response_deserializer=recommend__dmp__pb2.UserProfileResponse.FromString,
        )


class UserProfileServiceServicer(object):
  """rpc 服务
  """

  def GetUserProfile(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_UserProfileServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetUserProfile': grpc.unary_unary_rpc_method_handler(
          servicer.GetUserProfile,
          request_deserializer=recommend__dmp__pb2.UserProfileRequest.FromString,
          response_serializer=recommend__dmp__pb2.UserProfileResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'abc.recommend_plt.dmp.UserProfileService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
