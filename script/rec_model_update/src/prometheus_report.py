#!/usr/bin/env python
# _*_ coding:utf-8 _*_

import os
import sys

import traceback
import logging
import time
import requests
import json
import hashlib

from . import global_config as global_config

PREFIX_CODE = 10000
HTTP_ERROR_CODE = 400
UNKOWN_EXCEPTION = 200
PARAMETER_ERROR = 300
MYSQL_EXCUTE_ERROR = 100
TASK_OVER_STOCK = 500
TASK_MODEL_DISPATCH_HEART_BEAT = 600
TASK_MODEL_UPDATE_HEART_BEAT = 600

gconfig = global_config.GlobalVariable()

def check_alarm(hash_str):
    m = hashlib.md5()
    m.update(hash_str.encode("utf8"))
    md5_hex = m.hexdigest()

    cur_timestamp = int(time.time())
    ret = gconfig.has_dict_key(md5_hex)
    if ret is True:    
        ret, pre_timestamp = gconfig.get_dict_val(md5_hex)
        if cur_timestamp - pre_timestamp > 3600:
            gconfig.set_dict_val(md5_hex, cur_timestamp)
            return True
        else:
            return False
    else: 
        gconfig.set_dict_val(md5_hex, cur_timestamp)

    return True


def prometheus_report(metric_name, labels_data):
    url = 'http://pm-laserver-master.prod.abc.sheincorp.cn/api/v1.0/kafka/monitor_metrics'
    t = time.time()
    alarm_data = dict()
    alarm_data['metric_name'] = "rec_plt_model_update_new"
    alarm_data['labels'] = labels_data
    alarm_data['value'] = 1
    alarm_data['timestamp'] = (int(round(t * 1000)))

    hash_str = str(labels_data['err_code']) + labels_data['err_msg']
    rt = check_alarm(hash_str)
    if rt is False:
        logging.info("current alarm information happen in the laster hour, err_code={0} err_msg={1}".format(labels_data['err_code'], labels_data['err_msg']))
        return 

    try:
        headers = {"Content-Type": "application/json"}
        requests.post(url, data=json.dumps(alarm_data), headers=headers)
    except requests.exceptions.Timeout:
        logging.error("url:{0} request timeout".format(url))
    except requests.exceptions.TooManyRedirects:
        #logging.error(traceback.format_exc())
        logging.error("url:{0} TooManyRedirects".format(url))
    except requests.exceptions.RequestException as e:
        # catastrophic error. bail.
        #logging.error(traceback.format_exc())
        logging.error("url:{0} exception".format(url))

    logging.info("prometheus alarm report success")