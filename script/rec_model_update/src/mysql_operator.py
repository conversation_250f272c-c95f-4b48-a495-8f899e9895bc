#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pymysql
from datetime import datetime
from datetime import timedelta
import traceback
from . import prometheus_report as prometheus

class MysqlOperator():

    def __init__(self, logger, db, host, port, user, password):
        self.logger = logger 
        self.mysql_conn = None
        self.mysql_conn_status = False
        self.db = db
        self.host = host
        self.port = port 
        self.user = user
        self.password = password

    def __del__(self):
        if self.mysql_conn is not None:
            self.mysql_conn.close()

    def close_connect(self):
        if self.mysql_conn is not None:
            self.logger.info("close db")
            self.mysql_conn.close()
            self.mysql_conn_status = False

    def connect_database(self):
        rt = True
        try:
            self.logger.info("ht=%s, user_name:%s password:%s, db:%s port:%s", self.host, self.user, self.password, self.db, self.port)
            if self.mysql_conn_status is False:
                self.mysql_conn = pymysql.connect(host=self.host, user=self.user, password=self.password, db=self.db, port=int(self.port))
                self.mysql_conn_status = True
                try:
                    cur = self.mysql_conn.cursor()
                except OperationalError:
                    self.logger.error("----{0}".format(traceback.format_exc()))
        except Exception as err:
            self.mysql_conn_status = False
            #prometheus.prometheus_report(prometheus.alarm_data_error(6))
            rt = False
            self.logger.error(traceback.format_exc())
        return rt

    def check_ping(self):
        rt = False
        try:
            #rt = self.mysql_conn.open
            #if rt is False:
            #    self.logger.info("db disconnect")
            #    self.connect_database()
            self.mysql_conn.ping(reconnect=True)
        except Exception as err:
            self.connect_database()
            self.logger.info("db connect missing, traceback:%s", traceback.format_exc())

    def excute_sql(self, sql):
        config_list = list()
        try:
            self.check_ping()

            cursor = self.mysql_conn.cursor()
            count = cursor.execute(sql)
            self.mysql_conn.commit()
            #self.logger.info("mysql:%s data total:%d", sql, count)

            for row in cursor.fetchall():
                #self.logger.info("row length:%d", len(row))
                config_list.append(row)

            cursor.close()
            if len(config_list) > 0:
                return True, count, config_list
        except Exception as err:
            self.logger.error(traceback.format_exc())
        
        return False, 0, config_list
        
    def insert_data(self, sql):
        rt = True
        try:
            self.check_ping()

            cursor = self.mysql_conn.cursor()
            count = cursor.execute(sql)
            self.mysql_conn.commit()
            cursor.close()
        except Exception as err:
            self.logger.error("excetion:%s sql:%s",traceback.format_exc(), sql)
            self.mysql_conn.rollback()
            rt = False
        
        return rt
    
    def update_data(self, sql):
        rt = True
        try:
            self.check_ping()
            
            cursor = self.mysql_conn.cursor()
            count = cursor.execute(sql)
            self.mysql_conn.commit()
            cursor.close()
        except Exception as err:
            self.logger.error("excetion:%s sql:%s",traceback.format_exc(), sql)
            self.mysql_conn.rollback()
            rt = False
        
        return rt

    
