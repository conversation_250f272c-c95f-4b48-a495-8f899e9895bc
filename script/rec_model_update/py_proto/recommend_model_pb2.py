# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto/recommend_model.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from py_proto import recommend_idmp_pb2 as proto_dot_recommend__idmp__pb2
from py_proto import recommend_common_pb2 as proto_dot_recommend__common__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='proto/recommend_model.proto',
  package='abc.recommend_plt.model',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1bproto/recommend_model.proto\x12\x17\x61\x62\x63.recommend_plt.model\x1a\x1aproto/recommend_idmp.proto\x1a\x1cproto/recommend_common.proto\"\'\n\x0bItemTagsKey\x12\x0c\n\x04pool\x18\x01 \x01(\t\x12\n\n\x02id\x18\x02 \x01(\t\"\x7f\n\x0bItemTagsVal\x12\x36\n\x04tags\x18\x01 \x03(\x0b\x32(.abc.recommend_plt.model.ItemTagsVal.Tag\x1a\x38\n\x03Tag\x12\x0e\n\x06tag_id\x18\x01 \x01(\x05\x12\x0f\n\x07tag_val\x18\x02 \x01(\x03\x12\x10\n\x08tag_vals\x18\x03 \x03(\x03\"t\n\x0cItemTagsInfo\x12\x31\n\x03key\x18\x01 \x01(\x0b\x32$.abc.recommend_plt.model.ItemTagsKey\x12\x31\n\x03val\x18\x02 \x01(\x0b\x32$.abc.recommend_plt.model.ItemTagsVal\"[\n\rItemTagsModel\x12\x14\n\x0c\x62ussiness_id\x18\x01 \x01(\x05\x12\x34\n\x05infos\x18\x02 \x03(\x0b\x32%.abc.recommend_plt.model.ItemTagsInfo\"m\n\x0fItemProfileInfo\x12,\n\x03key\x18\x01 \x01(\x0b\x32\x1f.abc.recommend_plt.idmp.ItemKey\x12,\n\x03val\x18\x02 \x01(\x0b\x32\x1f.abc.recommend_plt.idmp.ItemTag\"K\n\x10ItemProfileModel\x12\x37\n\x05infos\x18\x01 \x03(\x0b\x32(.abc.recommend_plt.model.ItemProfileInfo\"Y\n\x0fTransformFeaVal\x12\x0f\n\x07raw_fea\x18\x01 \x01(\t\x12\x0f\n\x07raw_val\x18\x02 \x01(\t\x12\x11\n\ttrans_fea\x18\x03 \x01(\t\x12\x11\n\ttrans_val\x18\x04 \x01(\t\"\xc9\x01\n\x07RecList\x12>\n\ndimensions\x18\x01 \x03(\x0b\x32*.abc.recommend_plt.model.RecList.Dimension\x12\x34\n\x05items\x18\x02 \x03(\x0b\x32%.abc.recommend_plt.model.RecList.Item\x1a%\n\tDimension\x12\x0b\n\x03\x66\x65\x61\x18\x01 \x01(\t\x12\x0b\n\x03val\x18\x02 \x01(\t\x1a!\n\x04Item\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05score\x18\x02 \x01(\x02\"v\n\nRecListKey\x12\x41\n\ndimensions\x18\x01 \x03(\x0b\x32-.abc.recommend_plt.model.RecListKey.Dimension\x1a%\n\tDimension\x12\x0b\n\x03\x66\x65\x61\x18\x01 \x01(\t\x12\x0b\n\x03val\x18\x02 \x01(\t\"\x89\x01\n\nRecListVal\x12\x37\n\x05items\x18\x02 \x03(\x0b\x32(.abc.recommend_plt.model.RecListVal.Item\x1a\x42\n\x04Item\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05score\x18\x02 \x01(\x02\x12\x0c\n\x04\x63\x61te\x18\x03 \x01(\x05\x12\x11\n\tparent_id\x18\x04 \x01(\x03\"q\n\x0bRecListInfo\x12\x30\n\x03key\x18\x01 \x01(\x0b\x32#.abc.recommend_plt.model.RecListKey\x12\x30\n\x03val\x18\x02 \x01(\x0b\x32#.abc.recommend_plt.model.RecListVal\"\xeb\x01\n\x0cRecListModel\x12\x14\n\x0c\x62ussiness_id\x18\x01 \x01(\r\x12\x10\n\x08model_id\x18\x02 \x01(\t\x12\x33\n\trec_lists\x18\x03 \x03(\x0b\x32 .abc.recommend_plt.model.RecList\x12@\n\x0etrans_fea_vals\x18\x04 \x03(\x0b\x32(.abc.recommend_plt.model.TransformFeaVal\x12<\n\x0erec_list_infos\x18\x05 \x03(\x0b\x32$.abc.recommend_plt.model.RecListInfo\"5\n\x11\x46\x65\x61tureMappingKey\x12\x0f\n\x07raw_fea\x18\x01 \x01(\t\x12\x0f\n\x07raw_val\x18\x02 \x01(\t\"5\n\x11\x46\x65\x61tureMappingVal\x12\x0f\n\x07map_fea\x18\x01 \x01(\t\x12\x0f\n\x07map_val\x18\x02 \x01(\t\"R\n\x16\x46\x65\x61tureMappingMultiVal\x12\x38\n\x04vals\x18\x01 \x03(\x0b\x32*.abc.recommend_plt.model.FeatureMappingVal\"\x86\x01\n\x12\x46\x65\x61tureMappingInfo\x12\x37\n\x03key\x18\x01 \x01(\x0b\x32*.abc.recommend_plt.model.FeatureMappingKey\x12\x37\n\x03val\x18\x02 \x01(\x0b\x32*.abc.recommend_plt.model.FeatureMappingVal\"g\n\x13\x46\x65\x61tureMappingModel\x12\x14\n\x0c\x62ussiness_id\x18\x01 \x01(\r\x12:\n\x05infos\x18\x02 \x03(\x0b\x32+.abc.recommend_plt.model.FeatureMappingInfo\"x\n\x0bStatRateKey\x12\x42\n\ndimensions\x18\x01 \x03(\x0b\x32..abc.recommend_plt.model.StatRateKey.Dimension\x1a%\n\tDimension\x12\x0b\n\x03\x66\x65\x61\x18\x01 \x01(\t\x12\x0b\n\x03val\x18\x02 \x01(\t\"p\n\x0bStatRateVal\x12<\n\x07metrics\x18\x01 \x03(\x0b\x32+.abc.recommend_plt.model.StatRateVal.Metric\x1a#\n\x06Metric\x12\x0c\n\x04type\x18\x01 \x01(\x05\x12\x0b\n\x03val\x18\x02 \x01(\x02\"N\n\x0cStatRateInfo\x12\x0b\n\x03key\x18\x01 \x01(\x04\x12\x31\n\x03val\x18\x02 \x01(\x0b\x32$.abc.recommend_plt.model.StatRateVal\"[\n\rStatRateModel\x12\x14\n\x0c\x62ussiness_id\x18\x01 \x01(\r\x12\x34\n\x05infos\x18\x02 \x03(\x0b\x32%.abc.recommend_plt.model.StatRateInfo\"8\n\x0f\x43\x61teRelationKey\x12\x10\n\x08site_uid\x18\x01 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x02 \x01(\t\"H\n\x0f\x43\x61teRelationVal\x12\x35\n\x05infos\x18\x01 \x03(\x0b\x32&.abc.recommend_plt.common.CateRelation\"#\n\x0fItemRelationKey\x12\x10\n\x08item_ids\x18\x01 \x01(\t\"1\n\x0fItemRelationVal\x12\x0f\n\x07item_id\x18\x01 \x01(\x03\x12\r\n\x05score\x18\x02 \x01(\x02\"N\n\x14ItemRelationMultiVal\x12\x36\n\x04vals\x18\x01 \x03(\x0b\x32(.abc.recommend_plt.model.ItemRelationVal\"\x85\x01\n\x10ItemRelationInfo\x12\x35\n\x03key\x18\x01 \x01(\x0b\x32(.abc.recommend_plt.model.ItemRelationKey\x12:\n\x03val\x18\x02 \x01(\x0b\x32-.abc.recommend_plt.model.ItemRelationMultiVal\"M\n\x11ItemRelationModel\x12\x38\n\x05infos\x18\x01 \x03(\x0b\x32).abc.recommend_plt.model.ItemRelationInfob\x06proto3')
  ,
  dependencies=[proto_dot_recommend__idmp__pb2.DESCRIPTOR,proto_dot_recommend__common__pb2.DESCRIPTOR,])




_ITEMTAGSKEY = _descriptor.Descriptor(
  name='ItemTagsKey',
  full_name='abc.recommend_plt.model.ItemTagsKey',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='pool', full_name='abc.recommend_plt.model.ItemTagsKey.pool', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='abc.recommend_plt.model.ItemTagsKey.id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=114,
  serialized_end=153,
)


_ITEMTAGSVAL_TAG = _descriptor.Descriptor(
  name='Tag',
  full_name='abc.recommend_plt.model.ItemTagsVal.Tag',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_id', full_name='abc.recommend_plt.model.ItemTagsVal.Tag.tag_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_val', full_name='abc.recommend_plt.model.ItemTagsVal.Tag.tag_val', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_vals', full_name='abc.recommend_plt.model.ItemTagsVal.Tag.tag_vals', index=2,
      number=3, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=226,
  serialized_end=282,
)

_ITEMTAGSVAL = _descriptor.Descriptor(
  name='ItemTagsVal',
  full_name='abc.recommend_plt.model.ItemTagsVal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tags', full_name='abc.recommend_plt.model.ItemTagsVal.tags', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_ITEMTAGSVAL_TAG, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=155,
  serialized_end=282,
)


_ITEMTAGSINFO = _descriptor.Descriptor(
  name='ItemTagsInfo',
  full_name='abc.recommend_plt.model.ItemTagsInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='abc.recommend_plt.model.ItemTagsInfo.key', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='abc.recommend_plt.model.ItemTagsInfo.val', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=284,
  serialized_end=400,
)


_ITEMTAGSMODEL = _descriptor.Descriptor(
  name='ItemTagsModel',
  full_name='abc.recommend_plt.model.ItemTagsModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bussiness_id', full_name='abc.recommend_plt.model.ItemTagsModel.bussiness_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='infos', full_name='abc.recommend_plt.model.ItemTagsModel.infos', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=402,
  serialized_end=493,
)


_ITEMPROFILEINFO = _descriptor.Descriptor(
  name='ItemProfileInfo',
  full_name='abc.recommend_plt.model.ItemProfileInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='abc.recommend_plt.model.ItemProfileInfo.key', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='abc.recommend_plt.model.ItemProfileInfo.val', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=495,
  serialized_end=604,
)


_ITEMPROFILEMODEL = _descriptor.Descriptor(
  name='ItemProfileModel',
  full_name='abc.recommend_plt.model.ItemProfileModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='infos', full_name='abc.recommend_plt.model.ItemProfileModel.infos', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=606,
  serialized_end=681,
)


_TRANSFORMFEAVAL = _descriptor.Descriptor(
  name='TransformFeaVal',
  full_name='abc.recommend_plt.model.TransformFeaVal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='raw_fea', full_name='abc.recommend_plt.model.TransformFeaVal.raw_fea', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='raw_val', full_name='abc.recommend_plt.model.TransformFeaVal.raw_val', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_fea', full_name='abc.recommend_plt.model.TransformFeaVal.trans_fea', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_val', full_name='abc.recommend_plt.model.TransformFeaVal.trans_val', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=683,
  serialized_end=772,
)


_RECLIST_DIMENSION = _descriptor.Descriptor(
  name='Dimension',
  full_name='abc.recommend_plt.model.RecList.Dimension',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fea', full_name='abc.recommend_plt.model.RecList.Dimension.fea', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='abc.recommend_plt.model.RecList.Dimension.val', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=904,
  serialized_end=941,
)

_RECLIST_ITEM = _descriptor.Descriptor(
  name='Item',
  full_name='abc.recommend_plt.model.RecList.Item',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='abc.recommend_plt.model.RecList.Item.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='score', full_name='abc.recommend_plt.model.RecList.Item.score', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=943,
  serialized_end=976,
)

_RECLIST = _descriptor.Descriptor(
  name='RecList',
  full_name='abc.recommend_plt.model.RecList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='dimensions', full_name='abc.recommend_plt.model.RecList.dimensions', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='items', full_name='abc.recommend_plt.model.RecList.items', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_RECLIST_DIMENSION, _RECLIST_ITEM, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=775,
  serialized_end=976,
)


_RECLISTKEY_DIMENSION = _descriptor.Descriptor(
  name='Dimension',
  full_name='abc.recommend_plt.model.RecListKey.Dimension',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fea', full_name='abc.recommend_plt.model.RecListKey.Dimension.fea', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='abc.recommend_plt.model.RecListKey.Dimension.val', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=904,
  serialized_end=941,
)

_RECLISTKEY = _descriptor.Descriptor(
  name='RecListKey',
  full_name='abc.recommend_plt.model.RecListKey',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='dimensions', full_name='abc.recommend_plt.model.RecListKey.dimensions', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_RECLISTKEY_DIMENSION, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=978,
  serialized_end=1096,
)


_RECLISTVAL_ITEM = _descriptor.Descriptor(
  name='Item',
  full_name='abc.recommend_plt.model.RecListVal.Item',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='abc.recommend_plt.model.RecListVal.Item.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='score', full_name='abc.recommend_plt.model.RecListVal.Item.score', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cate', full_name='abc.recommend_plt.model.RecListVal.Item.cate', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='parent_id', full_name='abc.recommend_plt.model.RecListVal.Item.parent_id', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1170,
  serialized_end=1236,
)

_RECLISTVAL = _descriptor.Descriptor(
  name='RecListVal',
  full_name='abc.recommend_plt.model.RecListVal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='items', full_name='abc.recommend_plt.model.RecListVal.items', index=0,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_RECLISTVAL_ITEM, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1099,
  serialized_end=1236,
)


_RECLISTINFO = _descriptor.Descriptor(
  name='RecListInfo',
  full_name='abc.recommend_plt.model.RecListInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='abc.recommend_plt.model.RecListInfo.key', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='abc.recommend_plt.model.RecListInfo.val', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1238,
  serialized_end=1351,
)


_RECLISTMODEL = _descriptor.Descriptor(
  name='RecListModel',
  full_name='abc.recommend_plt.model.RecListModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bussiness_id', full_name='abc.recommend_plt.model.RecListModel.bussiness_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_id', full_name='abc.recommend_plt.model.RecListModel.model_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_lists', full_name='abc.recommend_plt.model.RecListModel.rec_lists', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_fea_vals', full_name='abc.recommend_plt.model.RecListModel.trans_fea_vals', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_list_infos', full_name='abc.recommend_plt.model.RecListModel.rec_list_infos', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1354,
  serialized_end=1589,
)


_FEATUREMAPPINGKEY = _descriptor.Descriptor(
  name='FeatureMappingKey',
  full_name='abc.recommend_plt.model.FeatureMappingKey',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='raw_fea', full_name='abc.recommend_plt.model.FeatureMappingKey.raw_fea', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='raw_val', full_name='abc.recommend_plt.model.FeatureMappingKey.raw_val', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1591,
  serialized_end=1644,
)


_FEATUREMAPPINGVAL = _descriptor.Descriptor(
  name='FeatureMappingVal',
  full_name='abc.recommend_plt.model.FeatureMappingVal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='map_fea', full_name='abc.recommend_plt.model.FeatureMappingVal.map_fea', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='map_val', full_name='abc.recommend_plt.model.FeatureMappingVal.map_val', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1646,
  serialized_end=1699,
)


_FEATUREMAPPINGMULTIVAL = _descriptor.Descriptor(
  name='FeatureMappingMultiVal',
  full_name='abc.recommend_plt.model.FeatureMappingMultiVal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='vals', full_name='abc.recommend_plt.model.FeatureMappingMultiVal.vals', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1701,
  serialized_end=1783,
)


_FEATUREMAPPINGINFO = _descriptor.Descriptor(
  name='FeatureMappingInfo',
  full_name='abc.recommend_plt.model.FeatureMappingInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='abc.recommend_plt.model.FeatureMappingInfo.key', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='abc.recommend_plt.model.FeatureMappingInfo.val', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1786,
  serialized_end=1920,
)


_FEATUREMAPPINGMODEL = _descriptor.Descriptor(
  name='FeatureMappingModel',
  full_name='abc.recommend_plt.model.FeatureMappingModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bussiness_id', full_name='abc.recommend_plt.model.FeatureMappingModel.bussiness_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='infos', full_name='abc.recommend_plt.model.FeatureMappingModel.infos', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1922,
  serialized_end=2025,
)


_STATRATEKEY_DIMENSION = _descriptor.Descriptor(
  name='Dimension',
  full_name='abc.recommend_plt.model.StatRateKey.Dimension',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fea', full_name='abc.recommend_plt.model.StatRateKey.Dimension.fea', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='abc.recommend_plt.model.StatRateKey.Dimension.val', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=904,
  serialized_end=941,
)

_STATRATEKEY = _descriptor.Descriptor(
  name='StatRateKey',
  full_name='abc.recommend_plt.model.StatRateKey',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='dimensions', full_name='abc.recommend_plt.model.StatRateKey.dimensions', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_STATRATEKEY_DIMENSION, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2027,
  serialized_end=2147,
)


_STATRATEVAL_METRIC = _descriptor.Descriptor(
  name='Metric',
  full_name='abc.recommend_plt.model.StatRateVal.Metric',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='abc.recommend_plt.model.StatRateVal.Metric.type', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='abc.recommend_plt.model.StatRateVal.Metric.val', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2226,
  serialized_end=2261,
)

_STATRATEVAL = _descriptor.Descriptor(
  name='StatRateVal',
  full_name='abc.recommend_plt.model.StatRateVal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='metrics', full_name='abc.recommend_plt.model.StatRateVal.metrics', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_STATRATEVAL_METRIC, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2149,
  serialized_end=2261,
)


_STATRATEINFO = _descriptor.Descriptor(
  name='StatRateInfo',
  full_name='abc.recommend_plt.model.StatRateInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='abc.recommend_plt.model.StatRateInfo.key', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='abc.recommend_plt.model.StatRateInfo.val', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2263,
  serialized_end=2341,
)


_STATRATEMODEL = _descriptor.Descriptor(
  name='StatRateModel',
  full_name='abc.recommend_plt.model.StatRateModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bussiness_id', full_name='abc.recommend_plt.model.StatRateModel.bussiness_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='infos', full_name='abc.recommend_plt.model.StatRateModel.infos', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2343,
  serialized_end=2434,
)


_CATERELATIONKEY = _descriptor.Descriptor(
  name='CateRelationKey',
  full_name='abc.recommend_plt.model.CateRelationKey',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='site_uid', full_name='abc.recommend_plt.model.CateRelationKey.site_uid', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='abc.recommend_plt.model.CateRelationKey.category_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2436,
  serialized_end=2492,
)


_CATERELATIONVAL = _descriptor.Descriptor(
  name='CateRelationVal',
  full_name='abc.recommend_plt.model.CateRelationVal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='infos', full_name='abc.recommend_plt.model.CateRelationVal.infos', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2494,
  serialized_end=2566,
)


_ITEMRELATIONKEY = _descriptor.Descriptor(
  name='ItemRelationKey',
  full_name='abc.recommend_plt.model.ItemRelationKey',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='item_ids', full_name='abc.recommend_plt.model.ItemRelationKey.item_ids', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2568,
  serialized_end=2603,
)


_ITEMRELATIONVAL = _descriptor.Descriptor(
  name='ItemRelationVal',
  full_name='abc.recommend_plt.model.ItemRelationVal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='item_id', full_name='abc.recommend_plt.model.ItemRelationVal.item_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='score', full_name='abc.recommend_plt.model.ItemRelationVal.score', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2605,
  serialized_end=2654,
)


_ITEMRELATIONMULTIVAL = _descriptor.Descriptor(
  name='ItemRelationMultiVal',
  full_name='abc.recommend_plt.model.ItemRelationMultiVal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='vals', full_name='abc.recommend_plt.model.ItemRelationMultiVal.vals', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2656,
  serialized_end=2734,
)


_ITEMRELATIONINFO = _descriptor.Descriptor(
  name='ItemRelationInfo',
  full_name='abc.recommend_plt.model.ItemRelationInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='abc.recommend_plt.model.ItemRelationInfo.key', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='abc.recommend_plt.model.ItemRelationInfo.val', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2737,
  serialized_end=2870,
)


_ITEMRELATIONMODEL = _descriptor.Descriptor(
  name='ItemRelationModel',
  full_name='abc.recommend_plt.model.ItemRelationModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='infos', full_name='abc.recommend_plt.model.ItemRelationModel.infos', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2872,
  serialized_end=2949,
)

_ITEMTAGSVAL_TAG.containing_type = _ITEMTAGSVAL
_ITEMTAGSVAL.fields_by_name['tags'].message_type = _ITEMTAGSVAL_TAG
_ITEMTAGSINFO.fields_by_name['key'].message_type = _ITEMTAGSKEY
_ITEMTAGSINFO.fields_by_name['val'].message_type = _ITEMTAGSVAL
_ITEMTAGSMODEL.fields_by_name['infos'].message_type = _ITEMTAGSINFO
_ITEMPROFILEINFO.fields_by_name['key'].message_type = proto_dot_recommend__idmp__pb2._ITEMKEY
_ITEMPROFILEINFO.fields_by_name['val'].message_type = proto_dot_recommend__idmp__pb2._ITEMTAG
_ITEMPROFILEMODEL.fields_by_name['infos'].message_type = _ITEMPROFILEINFO
_RECLIST_DIMENSION.containing_type = _RECLIST
_RECLIST_ITEM.containing_type = _RECLIST
_RECLIST.fields_by_name['dimensions'].message_type = _RECLIST_DIMENSION
_RECLIST.fields_by_name['items'].message_type = _RECLIST_ITEM
_RECLISTKEY_DIMENSION.containing_type = _RECLISTKEY
_RECLISTKEY.fields_by_name['dimensions'].message_type = _RECLISTKEY_DIMENSION
_RECLISTVAL_ITEM.containing_type = _RECLISTVAL
_RECLISTVAL.fields_by_name['items'].message_type = _RECLISTVAL_ITEM
_RECLISTINFO.fields_by_name['key'].message_type = _RECLISTKEY
_RECLISTINFO.fields_by_name['val'].message_type = _RECLISTVAL
_RECLISTMODEL.fields_by_name['rec_lists'].message_type = _RECLIST
_RECLISTMODEL.fields_by_name['trans_fea_vals'].message_type = _TRANSFORMFEAVAL
_RECLISTMODEL.fields_by_name['rec_list_infos'].message_type = _RECLISTINFO
_FEATUREMAPPINGMULTIVAL.fields_by_name['vals'].message_type = _FEATUREMAPPINGVAL
_FEATUREMAPPINGINFO.fields_by_name['key'].message_type = _FEATUREMAPPINGKEY
_FEATUREMAPPINGINFO.fields_by_name['val'].message_type = _FEATUREMAPPINGVAL
_FEATUREMAPPINGMODEL.fields_by_name['infos'].message_type = _FEATUREMAPPINGINFO
_STATRATEKEY_DIMENSION.containing_type = _STATRATEKEY
_STATRATEKEY.fields_by_name['dimensions'].message_type = _STATRATEKEY_DIMENSION
_STATRATEVAL_METRIC.containing_type = _STATRATEVAL
_STATRATEVAL.fields_by_name['metrics'].message_type = _STATRATEVAL_METRIC
_STATRATEINFO.fields_by_name['val'].message_type = _STATRATEVAL
_STATRATEMODEL.fields_by_name['infos'].message_type = _STATRATEINFO
_CATERELATIONVAL.fields_by_name['infos'].message_type = proto_dot_recommend__common__pb2._CATERELATION
_ITEMRELATIONMULTIVAL.fields_by_name['vals'].message_type = _ITEMRELATIONVAL
_ITEMRELATIONINFO.fields_by_name['key'].message_type = _ITEMRELATIONKEY
_ITEMRELATIONINFO.fields_by_name['val'].message_type = _ITEMRELATIONMULTIVAL
_ITEMRELATIONMODEL.fields_by_name['infos'].message_type = _ITEMRELATIONINFO
DESCRIPTOR.message_types_by_name['ItemTagsKey'] = _ITEMTAGSKEY
DESCRIPTOR.message_types_by_name['ItemTagsVal'] = _ITEMTAGSVAL
DESCRIPTOR.message_types_by_name['ItemTagsInfo'] = _ITEMTAGSINFO
DESCRIPTOR.message_types_by_name['ItemTagsModel'] = _ITEMTAGSMODEL
DESCRIPTOR.message_types_by_name['ItemProfileInfo'] = _ITEMPROFILEINFO
DESCRIPTOR.message_types_by_name['ItemProfileModel'] = _ITEMPROFILEMODEL
DESCRIPTOR.message_types_by_name['TransformFeaVal'] = _TRANSFORMFEAVAL
DESCRIPTOR.message_types_by_name['RecList'] = _RECLIST
DESCRIPTOR.message_types_by_name['RecListKey'] = _RECLISTKEY
DESCRIPTOR.message_types_by_name['RecListVal'] = _RECLISTVAL
DESCRIPTOR.message_types_by_name['RecListInfo'] = _RECLISTINFO
DESCRIPTOR.message_types_by_name['RecListModel'] = _RECLISTMODEL
DESCRIPTOR.message_types_by_name['FeatureMappingKey'] = _FEATUREMAPPINGKEY
DESCRIPTOR.message_types_by_name['FeatureMappingVal'] = _FEATUREMAPPINGVAL
DESCRIPTOR.message_types_by_name['FeatureMappingMultiVal'] = _FEATUREMAPPINGMULTIVAL
DESCRIPTOR.message_types_by_name['FeatureMappingInfo'] = _FEATUREMAPPINGINFO
DESCRIPTOR.message_types_by_name['FeatureMappingModel'] = _FEATUREMAPPINGMODEL
DESCRIPTOR.message_types_by_name['StatRateKey'] = _STATRATEKEY
DESCRIPTOR.message_types_by_name['StatRateVal'] = _STATRATEVAL
DESCRIPTOR.message_types_by_name['StatRateInfo'] = _STATRATEINFO
DESCRIPTOR.message_types_by_name['StatRateModel'] = _STATRATEMODEL
DESCRIPTOR.message_types_by_name['CateRelationKey'] = _CATERELATIONKEY
DESCRIPTOR.message_types_by_name['CateRelationVal'] = _CATERELATIONVAL
DESCRIPTOR.message_types_by_name['ItemRelationKey'] = _ITEMRELATIONKEY
DESCRIPTOR.message_types_by_name['ItemRelationVal'] = _ITEMRELATIONVAL
DESCRIPTOR.message_types_by_name['ItemRelationMultiVal'] = _ITEMRELATIONMULTIVAL
DESCRIPTOR.message_types_by_name['ItemRelationInfo'] = _ITEMRELATIONINFO
DESCRIPTOR.message_types_by_name['ItemRelationModel'] = _ITEMRELATIONMODEL
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ItemTagsKey = _reflection.GeneratedProtocolMessageType('ItemTagsKey', (_message.Message,), dict(
  DESCRIPTOR = _ITEMTAGSKEY,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemTagsKey)
  ))
_sym_db.RegisterMessage(ItemTagsKey)

ItemTagsVal = _reflection.GeneratedProtocolMessageType('ItemTagsVal', (_message.Message,), dict(

  Tag = _reflection.GeneratedProtocolMessageType('Tag', (_message.Message,), dict(
    DESCRIPTOR = _ITEMTAGSVAL_TAG,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemTagsVal.Tag)
    ))
  ,
  DESCRIPTOR = _ITEMTAGSVAL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemTagsVal)
  ))
_sym_db.RegisterMessage(ItemTagsVal)
_sym_db.RegisterMessage(ItemTagsVal.Tag)

ItemTagsInfo = _reflection.GeneratedProtocolMessageType('ItemTagsInfo', (_message.Message,), dict(
  DESCRIPTOR = _ITEMTAGSINFO,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemTagsInfo)
  ))
_sym_db.RegisterMessage(ItemTagsInfo)

ItemTagsModel = _reflection.GeneratedProtocolMessageType('ItemTagsModel', (_message.Message,), dict(
  DESCRIPTOR = _ITEMTAGSMODEL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemTagsModel)
  ))
_sym_db.RegisterMessage(ItemTagsModel)

ItemProfileInfo = _reflection.GeneratedProtocolMessageType('ItemProfileInfo', (_message.Message,), dict(
  DESCRIPTOR = _ITEMPROFILEINFO,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemProfileInfo)
  ))
_sym_db.RegisterMessage(ItemProfileInfo)

ItemProfileModel = _reflection.GeneratedProtocolMessageType('ItemProfileModel', (_message.Message,), dict(
  DESCRIPTOR = _ITEMPROFILEMODEL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemProfileModel)
  ))
_sym_db.RegisterMessage(ItemProfileModel)

TransformFeaVal = _reflection.GeneratedProtocolMessageType('TransformFeaVal', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFORMFEAVAL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.TransformFeaVal)
  ))
_sym_db.RegisterMessage(TransformFeaVal)

RecList = _reflection.GeneratedProtocolMessageType('RecList', (_message.Message,), dict(

  Dimension = _reflection.GeneratedProtocolMessageType('Dimension', (_message.Message,), dict(
    DESCRIPTOR = _RECLIST_DIMENSION,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecList.Dimension)
    ))
  ,

  Item = _reflection.GeneratedProtocolMessageType('Item', (_message.Message,), dict(
    DESCRIPTOR = _RECLIST_ITEM,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecList.Item)
    ))
  ,
  DESCRIPTOR = _RECLIST,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecList)
  ))
_sym_db.RegisterMessage(RecList)
_sym_db.RegisterMessage(RecList.Dimension)
_sym_db.RegisterMessage(RecList.Item)

RecListKey = _reflection.GeneratedProtocolMessageType('RecListKey', (_message.Message,), dict(

  Dimension = _reflection.GeneratedProtocolMessageType('Dimension', (_message.Message,), dict(
    DESCRIPTOR = _RECLISTKEY_DIMENSION,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecListKey.Dimension)
    ))
  ,
  DESCRIPTOR = _RECLISTKEY,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecListKey)
  ))
_sym_db.RegisterMessage(RecListKey)
_sym_db.RegisterMessage(RecListKey.Dimension)

RecListVal = _reflection.GeneratedProtocolMessageType('RecListVal', (_message.Message,), dict(

  Item = _reflection.GeneratedProtocolMessageType('Item', (_message.Message,), dict(
    DESCRIPTOR = _RECLISTVAL_ITEM,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecListVal.Item)
    ))
  ,
  DESCRIPTOR = _RECLISTVAL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecListVal)
  ))
_sym_db.RegisterMessage(RecListVal)
_sym_db.RegisterMessage(RecListVal.Item)

RecListInfo = _reflection.GeneratedProtocolMessageType('RecListInfo', (_message.Message,), dict(
  DESCRIPTOR = _RECLISTINFO,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecListInfo)
  ))
_sym_db.RegisterMessage(RecListInfo)

RecListModel = _reflection.GeneratedProtocolMessageType('RecListModel', (_message.Message,), dict(
  DESCRIPTOR = _RECLISTMODEL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecListModel)
  ))
_sym_db.RegisterMessage(RecListModel)

FeatureMappingKey = _reflection.GeneratedProtocolMessageType('FeatureMappingKey', (_message.Message,), dict(
  DESCRIPTOR = _FEATUREMAPPINGKEY,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.FeatureMappingKey)
  ))
_sym_db.RegisterMessage(FeatureMappingKey)

FeatureMappingVal = _reflection.GeneratedProtocolMessageType('FeatureMappingVal', (_message.Message,), dict(
  DESCRIPTOR = _FEATUREMAPPINGVAL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.FeatureMappingVal)
  ))
_sym_db.RegisterMessage(FeatureMappingVal)

FeatureMappingMultiVal = _reflection.GeneratedProtocolMessageType('FeatureMappingMultiVal', (_message.Message,), dict(
  DESCRIPTOR = _FEATUREMAPPINGMULTIVAL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.FeatureMappingMultiVal)
  ))
_sym_db.RegisterMessage(FeatureMappingMultiVal)

FeatureMappingInfo = _reflection.GeneratedProtocolMessageType('FeatureMappingInfo', (_message.Message,), dict(
  DESCRIPTOR = _FEATUREMAPPINGINFO,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.FeatureMappingInfo)
  ))
_sym_db.RegisterMessage(FeatureMappingInfo)

FeatureMappingModel = _reflection.GeneratedProtocolMessageType('FeatureMappingModel', (_message.Message,), dict(
  DESCRIPTOR = _FEATUREMAPPINGMODEL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.FeatureMappingModel)
  ))
_sym_db.RegisterMessage(FeatureMappingModel)

StatRateKey = _reflection.GeneratedProtocolMessageType('StatRateKey', (_message.Message,), dict(

  Dimension = _reflection.GeneratedProtocolMessageType('Dimension', (_message.Message,), dict(
    DESCRIPTOR = _STATRATEKEY_DIMENSION,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.StatRateKey.Dimension)
    ))
  ,
  DESCRIPTOR = _STATRATEKEY,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.StatRateKey)
  ))
_sym_db.RegisterMessage(StatRateKey)
_sym_db.RegisterMessage(StatRateKey.Dimension)

StatRateVal = _reflection.GeneratedProtocolMessageType('StatRateVal', (_message.Message,), dict(

  Metric = _reflection.GeneratedProtocolMessageType('Metric', (_message.Message,), dict(
    DESCRIPTOR = _STATRATEVAL_METRIC,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.StatRateVal.Metric)
    ))
  ,
  DESCRIPTOR = _STATRATEVAL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.StatRateVal)
  ))
_sym_db.RegisterMessage(StatRateVal)
_sym_db.RegisterMessage(StatRateVal.Metric)

StatRateInfo = _reflection.GeneratedProtocolMessageType('StatRateInfo', (_message.Message,), dict(
  DESCRIPTOR = _STATRATEINFO,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.StatRateInfo)
  ))
_sym_db.RegisterMessage(StatRateInfo)

StatRateModel = _reflection.GeneratedProtocolMessageType('StatRateModel', (_message.Message,), dict(
  DESCRIPTOR = _STATRATEMODEL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.StatRateModel)
  ))
_sym_db.RegisterMessage(StatRateModel)

CateRelationKey = _reflection.GeneratedProtocolMessageType('CateRelationKey', (_message.Message,), dict(
  DESCRIPTOR = _CATERELATIONKEY,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.CateRelationKey)
  ))
_sym_db.RegisterMessage(CateRelationKey)

CateRelationVal = _reflection.GeneratedProtocolMessageType('CateRelationVal', (_message.Message,), dict(
  DESCRIPTOR = _CATERELATIONVAL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.CateRelationVal)
  ))
_sym_db.RegisterMessage(CateRelationVal)

ItemRelationKey = _reflection.GeneratedProtocolMessageType('ItemRelationKey', (_message.Message,), dict(
  DESCRIPTOR = _ITEMRELATIONKEY,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemRelationKey)
  ))
_sym_db.RegisterMessage(ItemRelationKey)

ItemRelationVal = _reflection.GeneratedProtocolMessageType('ItemRelationVal', (_message.Message,), dict(
  DESCRIPTOR = _ITEMRELATIONVAL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemRelationVal)
  ))
_sym_db.RegisterMessage(ItemRelationVal)

ItemRelationMultiVal = _reflection.GeneratedProtocolMessageType('ItemRelationMultiVal', (_message.Message,), dict(
  DESCRIPTOR = _ITEMRELATIONMULTIVAL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemRelationMultiVal)
  ))
_sym_db.RegisterMessage(ItemRelationMultiVal)

ItemRelationInfo = _reflection.GeneratedProtocolMessageType('ItemRelationInfo', (_message.Message,), dict(
  DESCRIPTOR = _ITEMRELATIONINFO,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemRelationInfo)
  ))
_sym_db.RegisterMessage(ItemRelationInfo)

ItemRelationModel = _reflection.GeneratedProtocolMessageType('ItemRelationModel', (_message.Message,), dict(
  DESCRIPTOR = _ITEMRELATIONMODEL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemRelationModel)
  ))
_sym_db.RegisterMessage(ItemRelationModel)


# @@protoc_insertion_point(module_scope)
