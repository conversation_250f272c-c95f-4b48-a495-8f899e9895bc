# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto/recommend_model.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='proto/recommend_model.proto',
  package='abc.recommend_plt.model',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1bproto/recommend_model.proto\x12\x17\x61\x62\x63.recommend_plt.model\"\x83\x02\n\x10SiteRecListModel\x12L\n\rsite_rec_list\x18\x01 \x03(\x0b\x32\x35.abc.recommend_plt.model.SiteRecListModel.SiteRecList\x1a\xa0\x01\n\x0bSiteRecList\x12\x10\n\x08site_uid\x18\x01 \x01(\t\x12I\n\x05items\x18\x02 \x03(\x0b\x32:.abc.recommend_plt.model.SiteRecListModel.SiteRecList.Item\x1a\x34\n\x04Item\x12\x0f\n\x07item_id\x18\x01 \x01(\x05\x12\r\n\x05score\x18\x02 \x01(\x02\x12\x0c\n\x04\x63\x61te\x18\x03 \x01(\x05\"\xeb\x05\n\x17MatcherParamByBussiness\x12\x14\n\x0c\x62ussiness_id\x18\x01 \x01(\r\x12`\n\x14rec_list_model_param\x18\x02 \x01(\x0b\x32\x42.abc.recommend_plt.model.MatcherParamByBussiness.RecListModelParam\x12T\n\rcombine_param\x18\x03 \x01(\x0b\x32=.abc.recommend_plt.model.MatcherParamByBussiness.CombineParam\x1a\xbf\x01\n\x11RecListModelParam\x12\x62\n\x05\x61ttrs\x18\x01 \x03(\x0b\x32S.abc.recommend_plt.model.MatcherParamByBussiness.RecListModelParam.RecListModelAttr\x1a\x46\n\x10RecListModelAttr\x12\x0f\n\x07\x64\x61ta_id\x18\x01 \x01(\r\x12\r\n\x05model\x18\x02 \x01(\t\x12\x12\n\ndimensions\x18\x03 \x03(\t\x1a\xbf\x02\n\x0c\x43ombineParam\x12\\\n\x05plans\x18\x01 \x03(\x0b\x32M.abc.recommend_plt.model.MatcherParamByBussiness.CombineParam.CombinationPlan\x1aG\n\x16PercentileDistribution\x12\x0f\n\x07\x64\x61ta_id\x18\x01 \x01(\r\x12\r\n\x05model\x18\x02 \x01(\t\x12\r\n\x05ratio\x18\x03 \x01(\x02\x1a\x87\x01\n\x0f\x43ombinationPlan\x12\x0f\n\x07plan_id\x18\x01 \x01(\r\x12\x63\n\x05\x64ists\x18\x02 \x03(\x0b\x32T.abc.recommend_plt.model.MatcherParamByBussiness.CombineParam.PercentileDistribution\"\xcc\x05\n\x18MatcherParamByBussiness2\x12\x14\n\x0c\x62ussiness_id\x18\x01 \x01(\r\x12W\n\x0estrategy_param\x18\x02 \x03(\x0b\x32?.abc.recommend_plt.model.MatcherParamByBussiness2.StrategyParam\x12V\n\x0escene_plan_cfg\x18\x03 \x03(\x0b\x32>.abc.recommend_plt.model.MatcherParamByBussiness2.ScenePlanCfg\x12_\n\x13\x65xp_params_plan_cfg\x18\x04 \x03(\x0b\x32\x42.abc.recommend_plt.model.MatcherParamByBussiness2.ExpParamsPlanCfg\x1a\xed\x01\n\rStrategyParam\x12\x0f\n\x07plan_id\x18\x01 \x01(\r\x12\x14\n\x0c\x64\x65\x66\x61ult_flag\x18\x02 \x01(\r\x12[\n\tdata_list\x18\x03 \x03(\x0b\x32H.abc.recommend_plt.model.MatcherParamByBussiness2.StrategyParam.DataList\x1aX\n\x08\x44\x61taList\x12\x0f\n\x07\x64\x61ta_id\x18\x01 \x01(\r\x12\r\n\x05model\x18\x02 \x01(\t\x12\r\n\x05ratio\x18\x03 \x01(\x02\x12\x0b\n\x03\x66\x65\x61\x18\x04 \x03(\t\x12\x10\n\x08priority\x18\x05 \x01(\r\x1a\x46\n\x0cScenePlanCfg\x12\x10\n\x08scene_id\x18\x01 \x01(\r\x12\x0f\n\x07plan_id\x18\x02 \x01(\r\x12\x13\n\x0brec_max_num\x18\x03 \x01(\r\x1aP\n\x10\x45xpParamsPlanCfg\x12\x14\n\x0c\x65xp_param_id\x18\x01 \x01(\t\x12\x15\n\rexp_param_val\x18\x02 \x01(\t\x12\x0f\n\x07plan_id\x18\x03 \x01(\r\"e\n\x0cMatcherParam\x12U\n\x1amatcher_param_by_bussiness\x18\x01 \x03(\x0b\x32\x31.abc.recommend_plt.model.MatcherParamByBussiness2\"\xb4\x16\n\x16RankerParamByBussiness\x12\x14\n\x0c\x62ussiness_id\x18\x01 \x01(\r\x12_\n\x14plus_size_cate_param\x18\x02 \x01(\x0b\x32\x41.abc.recommend_plt.model.RankerParamByBussiness.PlusSizeCateParam\x12X\n\x10\x62ucket_gen_param\x18\x03 \x01(\x0b\x32>.abc.recommend_plt.model.RankerParamByBussiness.BucketGenParam\x12\\\n\x12\x62ucket_quato_param\x18\x04 \x01(\x0b\x32@.abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam\x12\x63\n\x16user_tag_promote_param\x18\x05 \x01(\x0b\x32\x43.abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam\x12o\n\x1citem_inventory_promote_param\x18\x06 \x01(\x0b\x32I.abc.recommend_plt.model.RankerParamByBussiness.ItemInventoryPromoteParam\x12j\n\x1aitem_profile_data_id_param\x18\x07 \x01(\x0b\x32\x46.abc.recommend_plt.model.RankerParamByBussiness.ItemProfileDataIdParam\x1a!\n\x11PlusSizeCateParam\x12\x0c\n\x04\x63\x61te\x18\x01 \x03(\r\x1a\xb5\x02\n\x0e\x42ucketGenParam\x12m\n\x14item_tag_gen_buckets\x18\x01 \x03(\x0b\x32O.abc.recommend_plt.model.RankerParamByBussiness.BucketGenParam.ItemTagGenBucket\x1a\xb3\x01\n\x10ItemTagGenBucket\x12\x10\n\x08tag_name\x18\x01 \x01(\t\x12\x0f\n\x07tag_val\x18\x02 \x01(\x04\x12\x11\n\tbucket_id\x18\x03 \x01(\r\x12\x13\n\x0b\x62ucket_name\x18\x04 \x01(\t\x12\x16\n\x0e\x65lse_bucket_id\x18\x05 \x01(\r\x12\x18\n\x10\x65lse_bucket_name\x18\x06 \x01(\t\x12\x10\n\x08tag_sval\x18\x07 \x01(\x04\x12\x10\n\x08tag_eval\x18\x08 \x01(\x04\x1a\xc2\x04\n\x10\x42ucketQuatoParam\x12_\n\x0bquato_plans\x18\x01 \x03(\x0b\x32J.abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam.QuatoPlan\x1aJ\n\x0ePreferedBucket\x12\x11\n\tbucket_id\x18\x01 \x01(\r\x12\x13\n\x0b\x62ucket_name\x18\x02 \x01(\t\x12\x10\n\x08quantity\x18\x03 \x01(\r\x1aI\n\x10PercentileBucket\x12\x11\n\tbucket_id\x18\x01 \x01(\r\x12\x13\n\x0b\x62ucket_name\x18\x02 \x01(\t\x12\r\n\x05ratio\x18\x03 \x01(\x02\x1a\xb5\x02\n\tQuatoPlan\x12\x0f\n\x07plan_id\x18\x01 \x01(\t\x12i\n\x10prefered_buckets\x18\x02 \x03(\x0b\x32O.abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam.PreferedBucket\x12m\n\x12percnetile_buckets\x18\x03 \x03(\x0b\x32Q.abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam.PercentileBucket\x12\x1c\n\x14percnetile_group_num\x18\x04 \x01(\r\x12\x1f\n\x17percnetile_group_resort\x18\x05 \x01(\x08\x1a\xc5\x04\n\x13UserTagPromoteParam\x12\x66\n\rpromote_plans\x18\x01 \x03(\x0b\x32O.abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.PromotePlan\x1a:\n\nRankWeight\x12\r\n\x05srank\x18\x01 \x01(\r\x12\r\n\x05\x65rank\x18\x02 \x01(\r\x12\x0e\n\x06weight\x18\x03 \x01(\x02\x1a\x80\x02\n\x0bPromoteInfo\x12\x14\n\x0cpromote_name\x18\x01 \x01(\t\x12\x12\n\ngroup_name\x18\x02 \x01(\t\x12\x15\n\ritem_tag_name\x18\x03 \x01(\t\x12\x15\n\ruser_tag_hkey\x18\x04 \x01(\r\x12\x17\n\x0fuser_tag_format\x18\x05 \x01(\r\x12\x1a\n\x12user_tag_rank_from\x18\x06 \x01(\r\x12\x64\n\x0crank_weights\x18\x07 \x03(\x0b\x32N.abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.RankWeight\x1a\x86\x01\n\x0bPromotePlan\x12\x0f\n\x07plan_id\x18\x01 \x01(\t\x12\x66\n\rpromote_infos\x18\x02 \x03(\x0b\x32O.abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.PromoteInfo\x1a\x84\x04\n\x19ItemInventoryPromoteParam\x12l\n\rpromote_plans\x18\x01 \x03(\x0b\x32U.abc.recommend_plt.model.RankerParamByBussiness.ItemInventoryPromoteParam.PromotePlan\x1a.\n\x0b\x46omulaParam\x12\t\n\x01\x61\x18\x01 \x01(\x02\x12\t\n\x01\x62\x18\x02 \x01(\x02\x12\t\n\x01\x63\x18\x03 \x01(\x02\x1a\xba\x01\n\x0bPromoteInfo\x12\x1e\n\x16item_inventory_data_id\x18\x01 \x01(\r\x12\x1e\n\x16size_sale_stat_data_id\x18\x02 \x01(\r\x12k\n\x0c\x66omula_param\x18\x03 \x01(\x0b\x32U.abc.recommend_plt.model.RankerParamByBussiness.ItemInventoryPromoteParam.FomulaParam\x1a\x8b\x01\n\x0bPromotePlan\x12\x0f\n\x07plan_id\x18\x01 \x01(\t\x12k\n\x0cpromote_info\x18\x02 \x01(\x0b\x32U.abc.recommend_plt.model.RankerParamByBussiness.ItemInventoryPromoteParam.PromoteInfo\x1a\xb9\x01\n\x16ItemProfileDataIdParam\x12g\n\x05pairs\x18\x01 \x03(\x0b\x32X.abc.recommend_plt.model.RankerParamByBussiness.ItemProfileDataIdParam.TagnameDataIdPair\x1a\x36\n\x11TagnameDataIdPair\x12\x10\n\x08tag_name\x18\x01 \x01(\t\x12\x0f\n\x07\x64\x61ta_id\x18\x02 \x01(\r\"p\n\x12RankerParamByScene\x12\x10\n\x08scene_id\x18\x01 \x01(\r\x12 \n\x18user_tag_promote_plan_id\x18\x02 \x01(\t\x12&\n\x1eitem_inventory_promote_plan_id\x18\x03 \x01(\t\"\xad\x01\n\x0bRankerParam\x12R\n\x19ranker_param_by_bussiness\x18\x01 \x03(\x0b\x32/.abc.recommend_plt.model.RankerParamByBussiness\x12J\n\x15ranker_param_by_scene\x18\x02 \x03(\x0b\x32+.abc.recommend_plt.model.RankerParamByScene\"\x85\x04\n\x10ItemProfileModel\x12\x14\n\x0c\x62ussiness_id\x18\x01 \x01(\r\x12\x44\n\ttag_attrs\x18\x02 \x03(\x0b\x32\x31.abc.recommend_plt.model.ItemProfileModel.TagAttr\x12L\n\ritem_profiles\x18\x03 \x03(\x0b\x32\x35.abc.recommend_plt.model.ItemProfileModel.ItemProfile\x1a_\n\x07TagAttr\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x46\n\x08val_type\x18\x02 \x01(\x0e\x32\x34.abc.recommend_plt.model.ItemProfileModel.TagValType\x1a%\n\x07ItemTag\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0c\n\x04vals\x18\x02 \x03(\x04\x1ah\n\x0bItemProfile\x12\n\n\x02id\x18\x01 \x01(\t\x12?\n\x04tags\x18\x02 \x03(\x0b\x32\x31.abc.recommend_plt.model.ItemProfileModel.ItemTag\x12\x0c\n\x04pool\x18\x03 \x01(\t\"U\n\nTagValType\x12\x18\n\x14TAG_VAL_TYPE_UNKNOWN\x10\x00\x12\x15\n\x11TAG_VAL_TYPE_UNIT\x10\x01\x12\x16\n\x12TAG_VAL_TYPE_MULTI\x10\x02\"<\n\x0eItemProfileKey\x12\x0c\n\x04pool\x18\x01 \x01(\t\x12\n\n\x02id\x18\x02 \x01(\t\x12\x10\n\x08tag_name\x18\x03 \x01(\t\"X\n\x0eItemProfileVal\x12\x10\n\x08int_vals\x18\x01 \x03(\x04\x12\x10\n\x08str_vals\x18\x02 \x03(\t\x12\x10\n\x08val_type\x18\x03 \x01(\t\x12\x10\n\x08is_multi\x18\x04 \x01(\x08\"}\n\x0fItemProfileInfo\x12\x34\n\x03key\x18\x01 \x01(\x0b\x32\'.abc.recommend_plt.model.ItemProfileKey\x12\x34\n\x03val\x18\x02 \x01(\x0b\x32\'.abc.recommend_plt.model.ItemProfileVal\"c\n\x12ItemProfileModelV2\x12\x14\n\x0c\x62ussiness_id\x18\x01 \x01(\r\x12\x37\n\x05infos\x18\x02 \x03(\x0b\x32(.abc.recommend_plt.model.ItemProfileInfo\"\'\n\x0bItemTagsKey\x12\x0c\n\x04pool\x18\x01 \x01(\t\x12\n\n\x02id\x18\x02 \x01(\t\"m\n\x0bItemTagsVal\x12\x36\n\x04tags\x18\x01 \x03(\x0b\x32(.abc.recommend_plt.model.ItemTagsVal.Tag\x1a&\n\x03Tag\x12\x0e\n\x06tag_id\x18\x01 \x01(\x05\x12\x0f\n\x07tag_val\x18\x02 \x01(\x03\"t\n\x0cItemTagsInfo\x12\x31\n\x03key\x18\x01 \x01(\x0b\x32$.abc.recommend_plt.model.ItemTagsKey\x12\x31\n\x03val\x18\x02 \x01(\x0b\x32$.abc.recommend_plt.model.ItemTagsVal\"[\n\rItemTagsModel\x12\x14\n\x0c\x62ussiness_id\x18\x01 \x01(\x05\x12\x34\n\x05infos\x18\x02 \x03(\x0b\x32%.abc.recommend_plt.model.ItemTagsInfo\"Y\n\x0fTransformFeaVal\x12\x0f\n\x07raw_fea\x18\x01 \x01(\t\x12\x0f\n\x07raw_val\x18\x02 \x01(\t\x12\x11\n\ttrans_fea\x18\x03 \x01(\t\x12\x11\n\ttrans_val\x18\x04 \x01(\t\"\xc9\x01\n\x07RecList\x12>\n\ndimensions\x18\x01 \x03(\x0b\x32*.abc.recommend_plt.model.RecList.Dimension\x12\x34\n\x05items\x18\x02 \x03(\x0b\x32%.abc.recommend_plt.model.RecList.Item\x1a%\n\tDimension\x12\x0b\n\x03\x66\x65\x61\x18\x01 \x01(\t\x12\x0b\n\x03val\x18\x02 \x01(\t\x1a!\n\x04Item\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05score\x18\x02 \x01(\x02\"v\n\nRecListKey\x12\x41\n\ndimensions\x18\x01 \x03(\x0b\x32-.abc.recommend_plt.model.RecListKey.Dimension\x1a%\n\tDimension\x12\x0b\n\x03\x66\x65\x61\x18\x01 \x01(\t\x12\x0b\n\x03val\x18\x02 \x01(\t\"v\n\nRecListVal\x12\x37\n\x05items\x18\x02 \x03(\x0b\x32(.abc.recommend_plt.model.RecListVal.Item\x1a/\n\x04Item\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05score\x18\x02 \x01(\x02\x12\x0c\n\x04\x63\x61te\x18\x03 \x01(\x05\"q\n\x0bRecListInfo\x12\x30\n\x03key\x18\x01 \x01(\x0b\x32#.abc.recommend_plt.model.RecListKey\x12\x30\n\x03val\x18\x02 \x01(\x0b\x32#.abc.recommend_plt.model.RecListVal\"\xeb\x01\n\x0cRecListModel\x12\x14\n\x0c\x62ussiness_id\x18\x01 \x01(\r\x12\x10\n\x08model_id\x18\x02 \x01(\t\x12\x33\n\trec_lists\x18\x03 \x03(\x0b\x32 .abc.recommend_plt.model.RecList\x12@\n\x0etrans_fea_vals\x18\x04 \x03(\x0b\x32(.abc.recommend_plt.model.TransformFeaVal\x12<\n\x0erec_list_infos\x18\x05 \x03(\x0b\x32$.abc.recommend_plt.model.RecListInfo\"C\n\x0fSizeSaleStatKey\x12\x14\n\x0csite_country\x18\x01 \x01(\t\x12\x0c\n\x04\x63\x61te\x18\x02 \x01(\t\x12\x0c\n\x04size\x18\x03 \x01(\t\"\"\n\x0fSizeSaleStatVal\x12\x0f\n\x07percent\x18\x01 \x01(\x02\"\x80\x01\n\x10SizeSaleStatInfo\x12\x35\n\x03key\x18\x01 \x01(\x0b\x32(.abc.recommend_plt.model.SizeSaleStatKey\x12\x35\n\x03val\x18\x02 \x01(\x0b\x32(.abc.recommend_plt.model.SizeSaleStatVal\"c\n\x11SizeSaleStatModel\x12\x14\n\x0c\x62ussiness_id\x18\x01 \x01(\r\x12\x38\n\x05infos\x18\x02 \x03(\x0b\x32).abc.recommend_plt.model.SizeSaleStatInfo\"5\n\x11\x46\x65\x61tureMappingKey\x12\x0f\n\x07raw_fea\x18\x01 \x01(\t\x12\x0f\n\x07raw_val\x18\x02 \x01(\t\"5\n\x11\x46\x65\x61tureMappingVal\x12\x0f\n\x07map_fea\x18\x01 \x01(\t\x12\x0f\n\x07map_val\x18\x02 \x01(\t\"\x86\x01\n\x12\x46\x65\x61tureMappingInfo\x12\x37\n\x03key\x18\x01 \x01(\x0b\x32*.abc.recommend_plt.model.FeatureMappingKey\x12\x37\n\x03val\x18\x02 \x01(\x0b\x32*.abc.recommend_plt.model.FeatureMappingVal\"g\n\x13\x46\x65\x61tureMappingModel\x12\x14\n\x0c\x62ussiness_id\x18\x01 \x01(\r\x12:\n\x05infos\x18\x02 \x03(\x0b\x32+.abc.recommend_plt.model.FeatureMappingInfo\"x\n\x0bStatRateKey\x12\x42\n\ndimensions\x18\x01 \x03(\x0b\x32..abc.recommend_plt.model.StatRateKey.Dimension\x1a%\n\tDimension\x12\x0b\n\x03\x66\x65\x61\x18\x01 \x01(\t\x12\x0b\n\x03val\x18\x02 \x01(\t\"p\n\x0bStatRateVal\x12<\n\x07metrics\x18\x01 \x03(\x0b\x32+.abc.recommend_plt.model.StatRateVal.Metric\x1a#\n\x06Metric\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0b\n\x03val\x18\x02 \x01(\x02\"t\n\x0cStatRateInfo\x12\x31\n\x03key\x18\x01 \x01(\x0b\x32$.abc.recommend_plt.model.StatRateKey\x12\x31\n\x03val\x18\x02 \x01(\x0b\x32$.abc.recommend_plt.model.StatRateVal\"[\n\rStatRateModel\x12\x14\n\x0c\x62ussiness_id\x18\x01 \x01(\r\x12\x34\n\x05infos\x18\x02 \x03(\x0b\x32%.abc.recommend_plt.model.StatRateInfob\x06proto3')
)



_ITEMPROFILEMODEL_TAGVALTYPE = _descriptor.EnumDescriptor(
  name='TagValType',
  full_name='abc.recommend_plt.model.ItemProfileModel.TagValType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TAG_VAL_TYPE_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TAG_VAL_TYPE_UNIT', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TAG_VAL_TYPE_MULTI', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=5484,
  serialized_end=5569,
)
_sym_db.RegisterEnumDescriptor(_ITEMPROFILEMODEL_TAGVALTYPE)


_SITERECLISTMODEL_SITERECLIST_ITEM = _descriptor.Descriptor(
  name='Item',
  full_name='abc.recommend_plt.model.SiteRecListModel.SiteRecList.Item',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='item_id', full_name='abc.recommend_plt.model.SiteRecListModel.SiteRecList.Item.item_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='score', full_name='abc.recommend_plt.model.SiteRecListModel.SiteRecList.Item.score', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cate', full_name='abc.recommend_plt.model.SiteRecListModel.SiteRecList.Item.cate', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=264,
  serialized_end=316,
)

_SITERECLISTMODEL_SITERECLIST = _descriptor.Descriptor(
  name='SiteRecList',
  full_name='abc.recommend_plt.model.SiteRecListModel.SiteRecList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='site_uid', full_name='abc.recommend_plt.model.SiteRecListModel.SiteRecList.site_uid', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='items', full_name='abc.recommend_plt.model.SiteRecListModel.SiteRecList.items', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_SITERECLISTMODEL_SITERECLIST_ITEM, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=156,
  serialized_end=316,
)

_SITERECLISTMODEL = _descriptor.Descriptor(
  name='SiteRecListModel',
  full_name='abc.recommend_plt.model.SiteRecListModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='site_rec_list', full_name='abc.recommend_plt.model.SiteRecListModel.site_rec_list', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_SITERECLISTMODEL_SITERECLIST, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=57,
  serialized_end=316,
)


_MATCHERPARAMBYBUSSINESS_RECLISTMODELPARAM_RECLISTMODELATTR = _descriptor.Descriptor(
  name='RecListModelAttr',
  full_name='abc.recommend_plt.model.MatcherParamByBussiness.RecListModelParam.RecListModelAttr',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='data_id', full_name='abc.recommend_plt.model.MatcherParamByBussiness.RecListModelParam.RecListModelAttr.data_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model', full_name='abc.recommend_plt.model.MatcherParamByBussiness.RecListModelParam.RecListModelAttr.model', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dimensions', full_name='abc.recommend_plt.model.MatcherParamByBussiness.RecListModelParam.RecListModelAttr.dimensions', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=674,
  serialized_end=744,
)

_MATCHERPARAMBYBUSSINESS_RECLISTMODELPARAM = _descriptor.Descriptor(
  name='RecListModelParam',
  full_name='abc.recommend_plt.model.MatcherParamByBussiness.RecListModelParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='attrs', full_name='abc.recommend_plt.model.MatcherParamByBussiness.RecListModelParam.attrs', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_MATCHERPARAMBYBUSSINESS_RECLISTMODELPARAM_RECLISTMODELATTR, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=553,
  serialized_end=744,
)

_MATCHERPARAMBYBUSSINESS_COMBINEPARAM_PERCENTILEDISTRIBUTION = _descriptor.Descriptor(
  name='PercentileDistribution',
  full_name='abc.recommend_plt.model.MatcherParamByBussiness.CombineParam.PercentileDistribution',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='data_id', full_name='abc.recommend_plt.model.MatcherParamByBussiness.CombineParam.PercentileDistribution.data_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model', full_name='abc.recommend_plt.model.MatcherParamByBussiness.CombineParam.PercentileDistribution.model', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ratio', full_name='abc.recommend_plt.model.MatcherParamByBussiness.CombineParam.PercentileDistribution.ratio', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=857,
  serialized_end=928,
)

_MATCHERPARAMBYBUSSINESS_COMBINEPARAM_COMBINATIONPLAN = _descriptor.Descriptor(
  name='CombinationPlan',
  full_name='abc.recommend_plt.model.MatcherParamByBussiness.CombineParam.CombinationPlan',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='abc.recommend_plt.model.MatcherParamByBussiness.CombineParam.CombinationPlan.plan_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dists', full_name='abc.recommend_plt.model.MatcherParamByBussiness.CombineParam.CombinationPlan.dists', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=931,
  serialized_end=1066,
)

_MATCHERPARAMBYBUSSINESS_COMBINEPARAM = _descriptor.Descriptor(
  name='CombineParam',
  full_name='abc.recommend_plt.model.MatcherParamByBussiness.CombineParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plans', full_name='abc.recommend_plt.model.MatcherParamByBussiness.CombineParam.plans', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_MATCHERPARAMBYBUSSINESS_COMBINEPARAM_PERCENTILEDISTRIBUTION, _MATCHERPARAMBYBUSSINESS_COMBINEPARAM_COMBINATIONPLAN, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=747,
  serialized_end=1066,
)

_MATCHERPARAMBYBUSSINESS = _descriptor.Descriptor(
  name='MatcherParamByBussiness',
  full_name='abc.recommend_plt.model.MatcherParamByBussiness',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bussiness_id', full_name='abc.recommend_plt.model.MatcherParamByBussiness.bussiness_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_list_model_param', full_name='abc.recommend_plt.model.MatcherParamByBussiness.rec_list_model_param', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='combine_param', full_name='abc.recommend_plt.model.MatcherParamByBussiness.combine_param', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_MATCHERPARAMBYBUSSINESS_RECLISTMODELPARAM, _MATCHERPARAMBYBUSSINESS_COMBINEPARAM, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=319,
  serialized_end=1066,
)


_MATCHERPARAMBYBUSSINESS2_STRATEGYPARAM_DATALIST = _descriptor.Descriptor(
  name='DataList',
  full_name='abc.recommend_plt.model.MatcherParamByBussiness2.StrategyParam.DataList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='data_id', full_name='abc.recommend_plt.model.MatcherParamByBussiness2.StrategyParam.DataList.data_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model', full_name='abc.recommend_plt.model.MatcherParamByBussiness2.StrategyParam.DataList.model', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ratio', full_name='abc.recommend_plt.model.MatcherParamByBussiness2.StrategyParam.DataList.ratio', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fea', full_name='abc.recommend_plt.model.MatcherParamByBussiness2.StrategyParam.DataList.fea', index=3,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='priority', full_name='abc.recommend_plt.model.MatcherParamByBussiness2.StrategyParam.DataList.priority', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1543,
  serialized_end=1631,
)

_MATCHERPARAMBYBUSSINESS2_STRATEGYPARAM = _descriptor.Descriptor(
  name='StrategyParam',
  full_name='abc.recommend_plt.model.MatcherParamByBussiness2.StrategyParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='abc.recommend_plt.model.MatcherParamByBussiness2.StrategyParam.plan_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default_flag', full_name='abc.recommend_plt.model.MatcherParamByBussiness2.StrategyParam.default_flag', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data_list', full_name='abc.recommend_plt.model.MatcherParamByBussiness2.StrategyParam.data_list', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_MATCHERPARAMBYBUSSINESS2_STRATEGYPARAM_DATALIST, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1394,
  serialized_end=1631,
)

_MATCHERPARAMBYBUSSINESS2_SCENEPLANCFG = _descriptor.Descriptor(
  name='ScenePlanCfg',
  full_name='abc.recommend_plt.model.MatcherParamByBussiness2.ScenePlanCfg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='scene_id', full_name='abc.recommend_plt.model.MatcherParamByBussiness2.ScenePlanCfg.scene_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='abc.recommend_plt.model.MatcherParamByBussiness2.ScenePlanCfg.plan_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_max_num', full_name='abc.recommend_plt.model.MatcherParamByBussiness2.ScenePlanCfg.rec_max_num', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1633,
  serialized_end=1703,
)

_MATCHERPARAMBYBUSSINESS2_EXPPARAMSPLANCFG = _descriptor.Descriptor(
  name='ExpParamsPlanCfg',
  full_name='abc.recommend_plt.model.MatcherParamByBussiness2.ExpParamsPlanCfg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='exp_param_id', full_name='abc.recommend_plt.model.MatcherParamByBussiness2.ExpParamsPlanCfg.exp_param_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exp_param_val', full_name='abc.recommend_plt.model.MatcherParamByBussiness2.ExpParamsPlanCfg.exp_param_val', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='abc.recommend_plt.model.MatcherParamByBussiness2.ExpParamsPlanCfg.plan_id', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1705,
  serialized_end=1785,
)

_MATCHERPARAMBYBUSSINESS2 = _descriptor.Descriptor(
  name='MatcherParamByBussiness2',
  full_name='abc.recommend_plt.model.MatcherParamByBussiness2',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bussiness_id', full_name='abc.recommend_plt.model.MatcherParamByBussiness2.bussiness_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='strategy_param', full_name='abc.recommend_plt.model.MatcherParamByBussiness2.strategy_param', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scene_plan_cfg', full_name='abc.recommend_plt.model.MatcherParamByBussiness2.scene_plan_cfg', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exp_params_plan_cfg', full_name='abc.recommend_plt.model.MatcherParamByBussiness2.exp_params_plan_cfg', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_MATCHERPARAMBYBUSSINESS2_STRATEGYPARAM, _MATCHERPARAMBYBUSSINESS2_SCENEPLANCFG, _MATCHERPARAMBYBUSSINESS2_EXPPARAMSPLANCFG, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1069,
  serialized_end=1785,
)


_MATCHERPARAM = _descriptor.Descriptor(
  name='MatcherParam',
  full_name='abc.recommend_plt.model.MatcherParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='matcher_param_by_bussiness', full_name='abc.recommend_plt.model.MatcherParam.matcher_param_by_bussiness', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1787,
  serialized_end=1888,
)


_RANKERPARAMBYBUSSINESS_PLUSSIZECATEPARAM = _descriptor.Descriptor(
  name='PlusSizeCateParam',
  full_name='abc.recommend_plt.model.RankerParamByBussiness.PlusSizeCateParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='cate', full_name='abc.recommend_plt.model.RankerParamByBussiness.PlusSizeCateParam.cate', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2542,
  serialized_end=2575,
)

_RANKERPARAMBYBUSSINESS_BUCKETGENPARAM_ITEMTAGGENBUCKET = _descriptor.Descriptor(
  name='ItemTagGenBucket',
  full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketGenParam.ItemTagGenBucket',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketGenParam.ItemTagGenBucket.tag_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_val', full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketGenParam.ItemTagGenBucket.tag_val', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bucket_id', full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketGenParam.ItemTagGenBucket.bucket_id', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bucket_name', full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketGenParam.ItemTagGenBucket.bucket_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='else_bucket_id', full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketGenParam.ItemTagGenBucket.else_bucket_id', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='else_bucket_name', full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketGenParam.ItemTagGenBucket.else_bucket_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_sval', full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketGenParam.ItemTagGenBucket.tag_sval', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_eval', full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketGenParam.ItemTagGenBucket.tag_eval', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2708,
  serialized_end=2887,
)

_RANKERPARAMBYBUSSINESS_BUCKETGENPARAM = _descriptor.Descriptor(
  name='BucketGenParam',
  full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketGenParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='item_tag_gen_buckets', full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketGenParam.item_tag_gen_buckets', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_RANKERPARAMBYBUSSINESS_BUCKETGENPARAM_ITEMTAGGENBUCKET, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2578,
  serialized_end=2887,
)

_RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM_PREFEREDBUCKET = _descriptor.Descriptor(
  name='PreferedBucket',
  full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam.PreferedBucket',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bucket_id', full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam.PreferedBucket.bucket_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bucket_name', full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam.PreferedBucket.bucket_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam.PreferedBucket.quantity', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3007,
  serialized_end=3081,
)

_RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM_PERCENTILEBUCKET = _descriptor.Descriptor(
  name='PercentileBucket',
  full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam.PercentileBucket',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bucket_id', full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam.PercentileBucket.bucket_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bucket_name', full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam.PercentileBucket.bucket_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ratio', full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam.PercentileBucket.ratio', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3083,
  serialized_end=3156,
)

_RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM_QUATOPLAN = _descriptor.Descriptor(
  name='QuatoPlan',
  full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam.QuatoPlan',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam.QuatoPlan.plan_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='prefered_buckets', full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam.QuatoPlan.prefered_buckets', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='percnetile_buckets', full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam.QuatoPlan.percnetile_buckets', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='percnetile_group_num', full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam.QuatoPlan.percnetile_group_num', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='percnetile_group_resort', full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam.QuatoPlan.percnetile_group_resort', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3159,
  serialized_end=3468,
)

_RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM = _descriptor.Descriptor(
  name='BucketQuatoParam',
  full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='quato_plans', full_name='abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam.quato_plans', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM_PREFEREDBUCKET, _RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM_PERCENTILEBUCKET, _RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM_QUATOPLAN, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2890,
  serialized_end=3468,
)

_RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM_RANKWEIGHT = _descriptor.Descriptor(
  name='RankWeight',
  full_name='abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.RankWeight',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='srank', full_name='abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.RankWeight.srank', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='erank', full_name='abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.RankWeight.erank', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='weight', full_name='abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.RankWeight.weight', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3598,
  serialized_end=3656,
)

_RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM_PROMOTEINFO = _descriptor.Descriptor(
  name='PromoteInfo',
  full_name='abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.PromoteInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='promote_name', full_name='abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.PromoteInfo.promote_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='group_name', full_name='abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.PromoteInfo.group_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_tag_name', full_name='abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.PromoteInfo.item_tag_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_tag_hkey', full_name='abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.PromoteInfo.user_tag_hkey', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_tag_format', full_name='abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.PromoteInfo.user_tag_format', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_tag_rank_from', full_name='abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.PromoteInfo.user_tag_rank_from', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rank_weights', full_name='abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.PromoteInfo.rank_weights', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3659,
  serialized_end=3915,
)

_RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM_PROMOTEPLAN = _descriptor.Descriptor(
  name='PromotePlan',
  full_name='abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.PromotePlan',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.PromotePlan.plan_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='promote_infos', full_name='abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.PromotePlan.promote_infos', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3918,
  serialized_end=4052,
)

_RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM = _descriptor.Descriptor(
  name='UserTagPromoteParam',
  full_name='abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='promote_plans', full_name='abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.promote_plans', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM_RANKWEIGHT, _RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM_PROMOTEINFO, _RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM_PROMOTEPLAN, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3471,
  serialized_end=4052,
)

_RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM_FOMULAPARAM = _descriptor.Descriptor(
  name='FomulaParam',
  full_name='abc.recommend_plt.model.RankerParamByBussiness.ItemInventoryPromoteParam.FomulaParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='a', full_name='abc.recommend_plt.model.RankerParamByBussiness.ItemInventoryPromoteParam.FomulaParam.a', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='b', full_name='abc.recommend_plt.model.RankerParamByBussiness.ItemInventoryPromoteParam.FomulaParam.b', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='c', full_name='abc.recommend_plt.model.RankerParamByBussiness.ItemInventoryPromoteParam.FomulaParam.c', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4194,
  serialized_end=4240,
)

_RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM_PROMOTEINFO = _descriptor.Descriptor(
  name='PromoteInfo',
  full_name='abc.recommend_plt.model.RankerParamByBussiness.ItemInventoryPromoteParam.PromoteInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='item_inventory_data_id', full_name='abc.recommend_plt.model.RankerParamByBussiness.ItemInventoryPromoteParam.PromoteInfo.item_inventory_data_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='size_sale_stat_data_id', full_name='abc.recommend_plt.model.RankerParamByBussiness.ItemInventoryPromoteParam.PromoteInfo.size_sale_stat_data_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fomula_param', full_name='abc.recommend_plt.model.RankerParamByBussiness.ItemInventoryPromoteParam.PromoteInfo.fomula_param', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4243,
  serialized_end=4429,
)

_RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM_PROMOTEPLAN = _descriptor.Descriptor(
  name='PromotePlan',
  full_name='abc.recommend_plt.model.RankerParamByBussiness.ItemInventoryPromoteParam.PromotePlan',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='abc.recommend_plt.model.RankerParamByBussiness.ItemInventoryPromoteParam.PromotePlan.plan_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='promote_info', full_name='abc.recommend_plt.model.RankerParamByBussiness.ItemInventoryPromoteParam.PromotePlan.promote_info', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4432,
  serialized_end=4571,
)

_RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM = _descriptor.Descriptor(
  name='ItemInventoryPromoteParam',
  full_name='abc.recommend_plt.model.RankerParamByBussiness.ItemInventoryPromoteParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='promote_plans', full_name='abc.recommend_plt.model.RankerParamByBussiness.ItemInventoryPromoteParam.promote_plans', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM_FOMULAPARAM, _RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM_PROMOTEINFO, _RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM_PROMOTEPLAN, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4055,
  serialized_end=4571,
)

_RANKERPARAMBYBUSSINESS_ITEMPROFILEDATAIDPARAM_TAGNAMEDATAIDPAIR = _descriptor.Descriptor(
  name='TagnameDataIdPair',
  full_name='abc.recommend_plt.model.RankerParamByBussiness.ItemProfileDataIdParam.TagnameDataIdPair',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='abc.recommend_plt.model.RankerParamByBussiness.ItemProfileDataIdParam.TagnameDataIdPair.tag_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data_id', full_name='abc.recommend_plt.model.RankerParamByBussiness.ItemProfileDataIdParam.TagnameDataIdPair.data_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4705,
  serialized_end=4759,
)

_RANKERPARAMBYBUSSINESS_ITEMPROFILEDATAIDPARAM = _descriptor.Descriptor(
  name='ItemProfileDataIdParam',
  full_name='abc.recommend_plt.model.RankerParamByBussiness.ItemProfileDataIdParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='pairs', full_name='abc.recommend_plt.model.RankerParamByBussiness.ItemProfileDataIdParam.pairs', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_RANKERPARAMBYBUSSINESS_ITEMPROFILEDATAIDPARAM_TAGNAMEDATAIDPAIR, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4574,
  serialized_end=4759,
)

_RANKERPARAMBYBUSSINESS = _descriptor.Descriptor(
  name='RankerParamByBussiness',
  full_name='abc.recommend_plt.model.RankerParamByBussiness',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bussiness_id', full_name='abc.recommend_plt.model.RankerParamByBussiness.bussiness_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plus_size_cate_param', full_name='abc.recommend_plt.model.RankerParamByBussiness.plus_size_cate_param', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bucket_gen_param', full_name='abc.recommend_plt.model.RankerParamByBussiness.bucket_gen_param', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bucket_quato_param', full_name='abc.recommend_plt.model.RankerParamByBussiness.bucket_quato_param', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_tag_promote_param', full_name='abc.recommend_plt.model.RankerParamByBussiness.user_tag_promote_param', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_inventory_promote_param', full_name='abc.recommend_plt.model.RankerParamByBussiness.item_inventory_promote_param', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_profile_data_id_param', full_name='abc.recommend_plt.model.RankerParamByBussiness.item_profile_data_id_param', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_RANKERPARAMBYBUSSINESS_PLUSSIZECATEPARAM, _RANKERPARAMBYBUSSINESS_BUCKETGENPARAM, _RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM, _RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM, _RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM, _RANKERPARAMBYBUSSINESS_ITEMPROFILEDATAIDPARAM, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1891,
  serialized_end=4759,
)


_RANKERPARAMBYSCENE = _descriptor.Descriptor(
  name='RankerParamByScene',
  full_name='abc.recommend_plt.model.RankerParamByScene',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='scene_id', full_name='abc.recommend_plt.model.RankerParamByScene.scene_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_tag_promote_plan_id', full_name='abc.recommend_plt.model.RankerParamByScene.user_tag_promote_plan_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_inventory_promote_plan_id', full_name='abc.recommend_plt.model.RankerParamByScene.item_inventory_promote_plan_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4761,
  serialized_end=4873,
)


_RANKERPARAM = _descriptor.Descriptor(
  name='RankerParam',
  full_name='abc.recommend_plt.model.RankerParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ranker_param_by_bussiness', full_name='abc.recommend_plt.model.RankerParam.ranker_param_by_bussiness', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ranker_param_by_scene', full_name='abc.recommend_plt.model.RankerParam.ranker_param_by_scene', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4876,
  serialized_end=5049,
)


_ITEMPROFILEMODEL_TAGATTR = _descriptor.Descriptor(
  name='TagAttr',
  full_name='abc.recommend_plt.model.ItemProfileModel.TagAttr',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='abc.recommend_plt.model.ItemProfileModel.TagAttr.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val_type', full_name='abc.recommend_plt.model.ItemProfileModel.TagAttr.val_type', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5242,
  serialized_end=5337,
)

_ITEMPROFILEMODEL_ITEMTAG = _descriptor.Descriptor(
  name='ItemTag',
  full_name='abc.recommend_plt.model.ItemProfileModel.ItemTag',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='abc.recommend_plt.model.ItemProfileModel.ItemTag.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vals', full_name='abc.recommend_plt.model.ItemProfileModel.ItemTag.vals', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5339,
  serialized_end=5376,
)

_ITEMPROFILEMODEL_ITEMPROFILE = _descriptor.Descriptor(
  name='ItemProfile',
  full_name='abc.recommend_plt.model.ItemProfileModel.ItemProfile',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='abc.recommend_plt.model.ItemProfileModel.ItemProfile.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tags', full_name='abc.recommend_plt.model.ItemProfileModel.ItemProfile.tags', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pool', full_name='abc.recommend_plt.model.ItemProfileModel.ItemProfile.pool', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5378,
  serialized_end=5482,
)

_ITEMPROFILEMODEL = _descriptor.Descriptor(
  name='ItemProfileModel',
  full_name='abc.recommend_plt.model.ItemProfileModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bussiness_id', full_name='abc.recommend_plt.model.ItemProfileModel.bussiness_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_attrs', full_name='abc.recommend_plt.model.ItemProfileModel.tag_attrs', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_profiles', full_name='abc.recommend_plt.model.ItemProfileModel.item_profiles', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_ITEMPROFILEMODEL_TAGATTR, _ITEMPROFILEMODEL_ITEMTAG, _ITEMPROFILEMODEL_ITEMPROFILE, ],
  enum_types=[
    _ITEMPROFILEMODEL_TAGVALTYPE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5052,
  serialized_end=5569,
)


_ITEMPROFILEKEY = _descriptor.Descriptor(
  name='ItemProfileKey',
  full_name='abc.recommend_plt.model.ItemProfileKey',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='pool', full_name='abc.recommend_plt.model.ItemProfileKey.pool', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='abc.recommend_plt.model.ItemProfileKey.id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='abc.recommend_plt.model.ItemProfileKey.tag_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5571,
  serialized_end=5631,
)


_ITEMPROFILEVAL = _descriptor.Descriptor(
  name='ItemProfileVal',
  full_name='abc.recommend_plt.model.ItemProfileVal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='int_vals', full_name='abc.recommend_plt.model.ItemProfileVal.int_vals', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='str_vals', full_name='abc.recommend_plt.model.ItemProfileVal.str_vals', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val_type', full_name='abc.recommend_plt.model.ItemProfileVal.val_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_multi', full_name='abc.recommend_plt.model.ItemProfileVal.is_multi', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5633,
  serialized_end=5721,
)


_ITEMPROFILEINFO = _descriptor.Descriptor(
  name='ItemProfileInfo',
  full_name='abc.recommend_plt.model.ItemProfileInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='abc.recommend_plt.model.ItemProfileInfo.key', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='abc.recommend_plt.model.ItemProfileInfo.val', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5723,
  serialized_end=5848,
)


_ITEMPROFILEMODELV2 = _descriptor.Descriptor(
  name='ItemProfileModelV2',
  full_name='abc.recommend_plt.model.ItemProfileModelV2',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bussiness_id', full_name='abc.recommend_plt.model.ItemProfileModelV2.bussiness_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='infos', full_name='abc.recommend_plt.model.ItemProfileModelV2.infos', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5850,
  serialized_end=5949,
)


_ITEMTAGSKEY = _descriptor.Descriptor(
  name='ItemTagsKey',
  full_name='abc.recommend_plt.model.ItemTagsKey',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='pool', full_name='abc.recommend_plt.model.ItemTagsKey.pool', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='abc.recommend_plt.model.ItemTagsKey.id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5951,
  serialized_end=5990,
)


_ITEMTAGSVAL_TAG = _descriptor.Descriptor(
  name='Tag',
  full_name='abc.recommend_plt.model.ItemTagsVal.Tag',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_id', full_name='abc.recommend_plt.model.ItemTagsVal.Tag.tag_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_val', full_name='abc.recommend_plt.model.ItemTagsVal.Tag.tag_val', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6063,
  serialized_end=6101,
)

_ITEMTAGSVAL = _descriptor.Descriptor(
  name='ItemTagsVal',
  full_name='abc.recommend_plt.model.ItemTagsVal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tags', full_name='abc.recommend_plt.model.ItemTagsVal.tags', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_ITEMTAGSVAL_TAG, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5992,
  serialized_end=6101,
)


_ITEMTAGSINFO = _descriptor.Descriptor(
  name='ItemTagsInfo',
  full_name='abc.recommend_plt.model.ItemTagsInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='abc.recommend_plt.model.ItemTagsInfo.key', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='abc.recommend_plt.model.ItemTagsInfo.val', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6103,
  serialized_end=6219,
)


_ITEMTAGSMODEL = _descriptor.Descriptor(
  name='ItemTagsModel',
  full_name='abc.recommend_plt.model.ItemTagsModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bussiness_id', full_name='abc.recommend_plt.model.ItemTagsModel.bussiness_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='infos', full_name='abc.recommend_plt.model.ItemTagsModel.infos', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6221,
  serialized_end=6312,
)


_TRANSFORMFEAVAL = _descriptor.Descriptor(
  name='TransformFeaVal',
  full_name='abc.recommend_plt.model.TransformFeaVal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='raw_fea', full_name='abc.recommend_plt.model.TransformFeaVal.raw_fea', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='raw_val', full_name='abc.recommend_plt.model.TransformFeaVal.raw_val', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_fea', full_name='abc.recommend_plt.model.TransformFeaVal.trans_fea', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_val', full_name='abc.recommend_plt.model.TransformFeaVal.trans_val', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6314,
  serialized_end=6403,
)


_RECLIST_DIMENSION = _descriptor.Descriptor(
  name='Dimension',
  full_name='abc.recommend_plt.model.RecList.Dimension',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fea', full_name='abc.recommend_plt.model.RecList.Dimension.fea', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='abc.recommend_plt.model.RecList.Dimension.val', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6535,
  serialized_end=6572,
)

_RECLIST_ITEM = _descriptor.Descriptor(
  name='Item',
  full_name='abc.recommend_plt.model.RecList.Item',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='abc.recommend_plt.model.RecList.Item.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='score', full_name='abc.recommend_plt.model.RecList.Item.score', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6574,
  serialized_end=6607,
)

_RECLIST = _descriptor.Descriptor(
  name='RecList',
  full_name='abc.recommend_plt.model.RecList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='dimensions', full_name='abc.recommend_plt.model.RecList.dimensions', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='items', full_name='abc.recommend_plt.model.RecList.items', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_RECLIST_DIMENSION, _RECLIST_ITEM, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6406,
  serialized_end=6607,
)


_RECLISTKEY_DIMENSION = _descriptor.Descriptor(
  name='Dimension',
  full_name='abc.recommend_plt.model.RecListKey.Dimension',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fea', full_name='abc.recommend_plt.model.RecListKey.Dimension.fea', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='abc.recommend_plt.model.RecListKey.Dimension.val', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6535,
  serialized_end=6572,
)

_RECLISTKEY = _descriptor.Descriptor(
  name='RecListKey',
  full_name='abc.recommend_plt.model.RecListKey',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='dimensions', full_name='abc.recommend_plt.model.RecListKey.dimensions', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_RECLISTKEY_DIMENSION, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6609,
  serialized_end=6727,
)


_RECLISTVAL_ITEM = _descriptor.Descriptor(
  name='Item',
  full_name='abc.recommend_plt.model.RecListVal.Item',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='abc.recommend_plt.model.RecListVal.Item.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='score', full_name='abc.recommend_plt.model.RecListVal.Item.score', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cate', full_name='abc.recommend_plt.model.RecListVal.Item.cate', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6800,
  serialized_end=6847,
)

_RECLISTVAL = _descriptor.Descriptor(
  name='RecListVal',
  full_name='abc.recommend_plt.model.RecListVal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='items', full_name='abc.recommend_plt.model.RecListVal.items', index=0,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_RECLISTVAL_ITEM, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6729,
  serialized_end=6847,
)


_RECLISTINFO = _descriptor.Descriptor(
  name='RecListInfo',
  full_name='abc.recommend_plt.model.RecListInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='abc.recommend_plt.model.RecListInfo.key', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='abc.recommend_plt.model.RecListInfo.val', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6849,
  serialized_end=6962,
)


_RECLISTMODEL = _descriptor.Descriptor(
  name='RecListModel',
  full_name='abc.recommend_plt.model.RecListModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bussiness_id', full_name='abc.recommend_plt.model.RecListModel.bussiness_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_id', full_name='abc.recommend_plt.model.RecListModel.model_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_lists', full_name='abc.recommend_plt.model.RecListModel.rec_lists', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_fea_vals', full_name='abc.recommend_plt.model.RecListModel.trans_fea_vals', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_list_infos', full_name='abc.recommend_plt.model.RecListModel.rec_list_infos', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6965,
  serialized_end=7200,
)


_SIZESALESTATKEY = _descriptor.Descriptor(
  name='SizeSaleStatKey',
  full_name='abc.recommend_plt.model.SizeSaleStatKey',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='site_country', full_name='abc.recommend_plt.model.SizeSaleStatKey.site_country', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cate', full_name='abc.recommend_plt.model.SizeSaleStatKey.cate', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='size', full_name='abc.recommend_plt.model.SizeSaleStatKey.size', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7202,
  serialized_end=7269,
)


_SIZESALESTATVAL = _descriptor.Descriptor(
  name='SizeSaleStatVal',
  full_name='abc.recommend_plt.model.SizeSaleStatVal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='percent', full_name='abc.recommend_plt.model.SizeSaleStatVal.percent', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7271,
  serialized_end=7305,
)


_SIZESALESTATINFO = _descriptor.Descriptor(
  name='SizeSaleStatInfo',
  full_name='abc.recommend_plt.model.SizeSaleStatInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='abc.recommend_plt.model.SizeSaleStatInfo.key', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='abc.recommend_plt.model.SizeSaleStatInfo.val', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7308,
  serialized_end=7436,
)


_SIZESALESTATMODEL = _descriptor.Descriptor(
  name='SizeSaleStatModel',
  full_name='abc.recommend_plt.model.SizeSaleStatModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bussiness_id', full_name='abc.recommend_plt.model.SizeSaleStatModel.bussiness_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='infos', full_name='abc.recommend_plt.model.SizeSaleStatModel.infos', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7438,
  serialized_end=7537,
)


_FEATUREMAPPINGKEY = _descriptor.Descriptor(
  name='FeatureMappingKey',
  full_name='abc.recommend_plt.model.FeatureMappingKey',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='raw_fea', full_name='abc.recommend_plt.model.FeatureMappingKey.raw_fea', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='raw_val', full_name='abc.recommend_plt.model.FeatureMappingKey.raw_val', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7539,
  serialized_end=7592,
)


_FEATUREMAPPINGVAL = _descriptor.Descriptor(
  name='FeatureMappingVal',
  full_name='abc.recommend_plt.model.FeatureMappingVal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='map_fea', full_name='abc.recommend_plt.model.FeatureMappingVal.map_fea', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='map_val', full_name='abc.recommend_plt.model.FeatureMappingVal.map_val', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7594,
  serialized_end=7647,
)


_FEATUREMAPPINGINFO = _descriptor.Descriptor(
  name='FeatureMappingInfo',
  full_name='abc.recommend_plt.model.FeatureMappingInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='abc.recommend_plt.model.FeatureMappingInfo.key', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='abc.recommend_plt.model.FeatureMappingInfo.val', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7650,
  serialized_end=7784,
)


_FEATUREMAPPINGMODEL = _descriptor.Descriptor(
  name='FeatureMappingModel',
  full_name='abc.recommend_plt.model.FeatureMappingModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bussiness_id', full_name='abc.recommend_plt.model.FeatureMappingModel.bussiness_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='infos', full_name='abc.recommend_plt.model.FeatureMappingModel.infos', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7786,
  serialized_end=7889,
)


_STATRATEKEY_DIMENSION = _descriptor.Descriptor(
  name='Dimension',
  full_name='abc.recommend_plt.model.StatRateKey.Dimension',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fea', full_name='abc.recommend_plt.model.StatRateKey.Dimension.fea', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='abc.recommend_plt.model.StatRateKey.Dimension.val', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6535,
  serialized_end=6572,
)

_STATRATEKEY = _descriptor.Descriptor(
  name='StatRateKey',
  full_name='abc.recommend_plt.model.StatRateKey',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='dimensions', full_name='abc.recommend_plt.model.StatRateKey.dimensions', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_STATRATEKEY_DIMENSION, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7891,
  serialized_end=8011,
)


_STATRATEVAL_METRIC = _descriptor.Descriptor(
  name='Metric',
  full_name='abc.recommend_plt.model.StatRateVal.Metric',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='abc.recommend_plt.model.StatRateVal.Metric.type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='abc.recommend_plt.model.StatRateVal.Metric.val', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8090,
  serialized_end=8125,
)

_STATRATEVAL = _descriptor.Descriptor(
  name='StatRateVal',
  full_name='abc.recommend_plt.model.StatRateVal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='metrics', full_name='abc.recommend_plt.model.StatRateVal.metrics', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_STATRATEVAL_METRIC, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8013,
  serialized_end=8125,
)


_STATRATEINFO = _descriptor.Descriptor(
  name='StatRateInfo',
  full_name='abc.recommend_plt.model.StatRateInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='abc.recommend_plt.model.StatRateInfo.key', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='abc.recommend_plt.model.StatRateInfo.val', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8127,
  serialized_end=8243,
)


_STATRATEMODEL = _descriptor.Descriptor(
  name='StatRateModel',
  full_name='abc.recommend_plt.model.StatRateModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bussiness_id', full_name='abc.recommend_plt.model.StatRateModel.bussiness_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='infos', full_name='abc.recommend_plt.model.StatRateModel.infos', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8245,
  serialized_end=8336,
)

_SITERECLISTMODEL_SITERECLIST_ITEM.containing_type = _SITERECLISTMODEL_SITERECLIST
_SITERECLISTMODEL_SITERECLIST.fields_by_name['items'].message_type = _SITERECLISTMODEL_SITERECLIST_ITEM
_SITERECLISTMODEL_SITERECLIST.containing_type = _SITERECLISTMODEL
_SITERECLISTMODEL.fields_by_name['site_rec_list'].message_type = _SITERECLISTMODEL_SITERECLIST
_MATCHERPARAMBYBUSSINESS_RECLISTMODELPARAM_RECLISTMODELATTR.containing_type = _MATCHERPARAMBYBUSSINESS_RECLISTMODELPARAM
_MATCHERPARAMBYBUSSINESS_RECLISTMODELPARAM.fields_by_name['attrs'].message_type = _MATCHERPARAMBYBUSSINESS_RECLISTMODELPARAM_RECLISTMODELATTR
_MATCHERPARAMBYBUSSINESS_RECLISTMODELPARAM.containing_type = _MATCHERPARAMBYBUSSINESS
_MATCHERPARAMBYBUSSINESS_COMBINEPARAM_PERCENTILEDISTRIBUTION.containing_type = _MATCHERPARAMBYBUSSINESS_COMBINEPARAM
_MATCHERPARAMBYBUSSINESS_COMBINEPARAM_COMBINATIONPLAN.fields_by_name['dists'].message_type = _MATCHERPARAMBYBUSSINESS_COMBINEPARAM_PERCENTILEDISTRIBUTION
_MATCHERPARAMBYBUSSINESS_COMBINEPARAM_COMBINATIONPLAN.containing_type = _MATCHERPARAMBYBUSSINESS_COMBINEPARAM
_MATCHERPARAMBYBUSSINESS_COMBINEPARAM.fields_by_name['plans'].message_type = _MATCHERPARAMBYBUSSINESS_COMBINEPARAM_COMBINATIONPLAN
_MATCHERPARAMBYBUSSINESS_COMBINEPARAM.containing_type = _MATCHERPARAMBYBUSSINESS
_MATCHERPARAMBYBUSSINESS.fields_by_name['rec_list_model_param'].message_type = _MATCHERPARAMBYBUSSINESS_RECLISTMODELPARAM
_MATCHERPARAMBYBUSSINESS.fields_by_name['combine_param'].message_type = _MATCHERPARAMBYBUSSINESS_COMBINEPARAM
_MATCHERPARAMBYBUSSINESS2_STRATEGYPARAM_DATALIST.containing_type = _MATCHERPARAMBYBUSSINESS2_STRATEGYPARAM
_MATCHERPARAMBYBUSSINESS2_STRATEGYPARAM.fields_by_name['data_list'].message_type = _MATCHERPARAMBYBUSSINESS2_STRATEGYPARAM_DATALIST
_MATCHERPARAMBYBUSSINESS2_STRATEGYPARAM.containing_type = _MATCHERPARAMBYBUSSINESS2
_MATCHERPARAMBYBUSSINESS2_SCENEPLANCFG.containing_type = _MATCHERPARAMBYBUSSINESS2
_MATCHERPARAMBYBUSSINESS2_EXPPARAMSPLANCFG.containing_type = _MATCHERPARAMBYBUSSINESS2
_MATCHERPARAMBYBUSSINESS2.fields_by_name['strategy_param'].message_type = _MATCHERPARAMBYBUSSINESS2_STRATEGYPARAM
_MATCHERPARAMBYBUSSINESS2.fields_by_name['scene_plan_cfg'].message_type = _MATCHERPARAMBYBUSSINESS2_SCENEPLANCFG
_MATCHERPARAMBYBUSSINESS2.fields_by_name['exp_params_plan_cfg'].message_type = _MATCHERPARAMBYBUSSINESS2_EXPPARAMSPLANCFG
_MATCHERPARAM.fields_by_name['matcher_param_by_bussiness'].message_type = _MATCHERPARAMBYBUSSINESS2
_RANKERPARAMBYBUSSINESS_PLUSSIZECATEPARAM.containing_type = _RANKERPARAMBYBUSSINESS
_RANKERPARAMBYBUSSINESS_BUCKETGENPARAM_ITEMTAGGENBUCKET.containing_type = _RANKERPARAMBYBUSSINESS_BUCKETGENPARAM
_RANKERPARAMBYBUSSINESS_BUCKETGENPARAM.fields_by_name['item_tag_gen_buckets'].message_type = _RANKERPARAMBYBUSSINESS_BUCKETGENPARAM_ITEMTAGGENBUCKET
_RANKERPARAMBYBUSSINESS_BUCKETGENPARAM.containing_type = _RANKERPARAMBYBUSSINESS
_RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM_PREFEREDBUCKET.containing_type = _RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM
_RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM_PERCENTILEBUCKET.containing_type = _RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM
_RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM_QUATOPLAN.fields_by_name['prefered_buckets'].message_type = _RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM_PREFEREDBUCKET
_RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM_QUATOPLAN.fields_by_name['percnetile_buckets'].message_type = _RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM_PERCENTILEBUCKET
_RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM_QUATOPLAN.containing_type = _RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM
_RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM.fields_by_name['quato_plans'].message_type = _RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM_QUATOPLAN
_RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM.containing_type = _RANKERPARAMBYBUSSINESS
_RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM_RANKWEIGHT.containing_type = _RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM
_RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM_PROMOTEINFO.fields_by_name['rank_weights'].message_type = _RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM_RANKWEIGHT
_RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM_PROMOTEINFO.containing_type = _RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM
_RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM_PROMOTEPLAN.fields_by_name['promote_infos'].message_type = _RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM_PROMOTEINFO
_RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM_PROMOTEPLAN.containing_type = _RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM
_RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM.fields_by_name['promote_plans'].message_type = _RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM_PROMOTEPLAN
_RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM.containing_type = _RANKERPARAMBYBUSSINESS
_RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM_FOMULAPARAM.containing_type = _RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM
_RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM_PROMOTEINFO.fields_by_name['fomula_param'].message_type = _RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM_FOMULAPARAM
_RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM_PROMOTEINFO.containing_type = _RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM
_RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM_PROMOTEPLAN.fields_by_name['promote_info'].message_type = _RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM_PROMOTEINFO
_RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM_PROMOTEPLAN.containing_type = _RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM
_RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM.fields_by_name['promote_plans'].message_type = _RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM_PROMOTEPLAN
_RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM.containing_type = _RANKERPARAMBYBUSSINESS
_RANKERPARAMBYBUSSINESS_ITEMPROFILEDATAIDPARAM_TAGNAMEDATAIDPAIR.containing_type = _RANKERPARAMBYBUSSINESS_ITEMPROFILEDATAIDPARAM
_RANKERPARAMBYBUSSINESS_ITEMPROFILEDATAIDPARAM.fields_by_name['pairs'].message_type = _RANKERPARAMBYBUSSINESS_ITEMPROFILEDATAIDPARAM_TAGNAMEDATAIDPAIR
_RANKERPARAMBYBUSSINESS_ITEMPROFILEDATAIDPARAM.containing_type = _RANKERPARAMBYBUSSINESS
_RANKERPARAMBYBUSSINESS.fields_by_name['plus_size_cate_param'].message_type = _RANKERPARAMBYBUSSINESS_PLUSSIZECATEPARAM
_RANKERPARAMBYBUSSINESS.fields_by_name['bucket_gen_param'].message_type = _RANKERPARAMBYBUSSINESS_BUCKETGENPARAM
_RANKERPARAMBYBUSSINESS.fields_by_name['bucket_quato_param'].message_type = _RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM
_RANKERPARAMBYBUSSINESS.fields_by_name['user_tag_promote_param'].message_type = _RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM
_RANKERPARAMBYBUSSINESS.fields_by_name['item_inventory_promote_param'].message_type = _RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM
_RANKERPARAMBYBUSSINESS.fields_by_name['item_profile_data_id_param'].message_type = _RANKERPARAMBYBUSSINESS_ITEMPROFILEDATAIDPARAM
_RANKERPARAM.fields_by_name['ranker_param_by_bussiness'].message_type = _RANKERPARAMBYBUSSINESS
_RANKERPARAM.fields_by_name['ranker_param_by_scene'].message_type = _RANKERPARAMBYSCENE
_ITEMPROFILEMODEL_TAGATTR.fields_by_name['val_type'].enum_type = _ITEMPROFILEMODEL_TAGVALTYPE
_ITEMPROFILEMODEL_TAGATTR.containing_type = _ITEMPROFILEMODEL
_ITEMPROFILEMODEL_ITEMTAG.containing_type = _ITEMPROFILEMODEL
_ITEMPROFILEMODEL_ITEMPROFILE.fields_by_name['tags'].message_type = _ITEMPROFILEMODEL_ITEMTAG
_ITEMPROFILEMODEL_ITEMPROFILE.containing_type = _ITEMPROFILEMODEL
_ITEMPROFILEMODEL.fields_by_name['tag_attrs'].message_type = _ITEMPROFILEMODEL_TAGATTR
_ITEMPROFILEMODEL.fields_by_name['item_profiles'].message_type = _ITEMPROFILEMODEL_ITEMPROFILE
_ITEMPROFILEMODEL_TAGVALTYPE.containing_type = _ITEMPROFILEMODEL
_ITEMPROFILEINFO.fields_by_name['key'].message_type = _ITEMPROFILEKEY
_ITEMPROFILEINFO.fields_by_name['val'].message_type = _ITEMPROFILEVAL
_ITEMPROFILEMODELV2.fields_by_name['infos'].message_type = _ITEMPROFILEINFO
_ITEMTAGSVAL_TAG.containing_type = _ITEMTAGSVAL
_ITEMTAGSVAL.fields_by_name['tags'].message_type = _ITEMTAGSVAL_TAG
_ITEMTAGSINFO.fields_by_name['key'].message_type = _ITEMTAGSKEY
_ITEMTAGSINFO.fields_by_name['val'].message_type = _ITEMTAGSVAL
_ITEMTAGSMODEL.fields_by_name['infos'].message_type = _ITEMTAGSINFO
_RECLIST_DIMENSION.containing_type = _RECLIST
_RECLIST_ITEM.containing_type = _RECLIST
_RECLIST.fields_by_name['dimensions'].message_type = _RECLIST_DIMENSION
_RECLIST.fields_by_name['items'].message_type = _RECLIST_ITEM
_RECLISTKEY_DIMENSION.containing_type = _RECLISTKEY
_RECLISTKEY.fields_by_name['dimensions'].message_type = _RECLISTKEY_DIMENSION
_RECLISTVAL_ITEM.containing_type = _RECLISTVAL
_RECLISTVAL.fields_by_name['items'].message_type = _RECLISTVAL_ITEM
_RECLISTINFO.fields_by_name['key'].message_type = _RECLISTKEY
_RECLISTINFO.fields_by_name['val'].message_type = _RECLISTVAL
_RECLISTMODEL.fields_by_name['rec_lists'].message_type = _RECLIST
_RECLISTMODEL.fields_by_name['trans_fea_vals'].message_type = _TRANSFORMFEAVAL
_RECLISTMODEL.fields_by_name['rec_list_infos'].message_type = _RECLISTINFO
_SIZESALESTATINFO.fields_by_name['key'].message_type = _SIZESALESTATKEY
_SIZESALESTATINFO.fields_by_name['val'].message_type = _SIZESALESTATVAL
_SIZESALESTATMODEL.fields_by_name['infos'].message_type = _SIZESALESTATINFO
_FEATUREMAPPINGINFO.fields_by_name['key'].message_type = _FEATUREMAPPINGKEY
_FEATUREMAPPINGINFO.fields_by_name['val'].message_type = _FEATUREMAPPINGVAL
_FEATUREMAPPINGMODEL.fields_by_name['infos'].message_type = _FEATUREMAPPINGINFO
_STATRATEKEY_DIMENSION.containing_type = _STATRATEKEY
_STATRATEKEY.fields_by_name['dimensions'].message_type = _STATRATEKEY_DIMENSION
_STATRATEVAL_METRIC.containing_type = _STATRATEVAL
_STATRATEVAL.fields_by_name['metrics'].message_type = _STATRATEVAL_METRIC
_STATRATEINFO.fields_by_name['key'].message_type = _STATRATEKEY
_STATRATEINFO.fields_by_name['val'].message_type = _STATRATEVAL
_STATRATEMODEL.fields_by_name['infos'].message_type = _STATRATEINFO
DESCRIPTOR.message_types_by_name['SiteRecListModel'] = _SITERECLISTMODEL
DESCRIPTOR.message_types_by_name['MatcherParamByBussiness'] = _MATCHERPARAMBYBUSSINESS
DESCRIPTOR.message_types_by_name['MatcherParamByBussiness2'] = _MATCHERPARAMBYBUSSINESS2
DESCRIPTOR.message_types_by_name['MatcherParam'] = _MATCHERPARAM
DESCRIPTOR.message_types_by_name['RankerParamByBussiness'] = _RANKERPARAMBYBUSSINESS
DESCRIPTOR.message_types_by_name['RankerParamByScene'] = _RANKERPARAMBYSCENE
DESCRIPTOR.message_types_by_name['RankerParam'] = _RANKERPARAM
DESCRIPTOR.message_types_by_name['ItemProfileModel'] = _ITEMPROFILEMODEL
DESCRIPTOR.message_types_by_name['ItemProfileKey'] = _ITEMPROFILEKEY
DESCRIPTOR.message_types_by_name['ItemProfileVal'] = _ITEMPROFILEVAL
DESCRIPTOR.message_types_by_name['ItemProfileInfo'] = _ITEMPROFILEINFO
DESCRIPTOR.message_types_by_name['ItemProfileModelV2'] = _ITEMPROFILEMODELV2
DESCRIPTOR.message_types_by_name['ItemTagsKey'] = _ITEMTAGSKEY
DESCRIPTOR.message_types_by_name['ItemTagsVal'] = _ITEMTAGSVAL
DESCRIPTOR.message_types_by_name['ItemTagsInfo'] = _ITEMTAGSINFO
DESCRIPTOR.message_types_by_name['ItemTagsModel'] = _ITEMTAGSMODEL
DESCRIPTOR.message_types_by_name['TransformFeaVal'] = _TRANSFORMFEAVAL
DESCRIPTOR.message_types_by_name['RecList'] = _RECLIST
DESCRIPTOR.message_types_by_name['RecListKey'] = _RECLISTKEY
DESCRIPTOR.message_types_by_name['RecListVal'] = _RECLISTVAL
DESCRIPTOR.message_types_by_name['RecListInfo'] = _RECLISTINFO
DESCRIPTOR.message_types_by_name['RecListModel'] = _RECLISTMODEL
DESCRIPTOR.message_types_by_name['SizeSaleStatKey'] = _SIZESALESTATKEY
DESCRIPTOR.message_types_by_name['SizeSaleStatVal'] = _SIZESALESTATVAL
DESCRIPTOR.message_types_by_name['SizeSaleStatInfo'] = _SIZESALESTATINFO
DESCRIPTOR.message_types_by_name['SizeSaleStatModel'] = _SIZESALESTATMODEL
DESCRIPTOR.message_types_by_name['FeatureMappingKey'] = _FEATUREMAPPINGKEY
DESCRIPTOR.message_types_by_name['FeatureMappingVal'] = _FEATUREMAPPINGVAL
DESCRIPTOR.message_types_by_name['FeatureMappingInfo'] = _FEATUREMAPPINGINFO
DESCRIPTOR.message_types_by_name['FeatureMappingModel'] = _FEATUREMAPPINGMODEL
DESCRIPTOR.message_types_by_name['StatRateKey'] = _STATRATEKEY
DESCRIPTOR.message_types_by_name['StatRateVal'] = _STATRATEVAL
DESCRIPTOR.message_types_by_name['StatRateInfo'] = _STATRATEINFO
DESCRIPTOR.message_types_by_name['StatRateModel'] = _STATRATEMODEL
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

SiteRecListModel = _reflection.GeneratedProtocolMessageType('SiteRecListModel', (_message.Message,), dict(

  SiteRecList = _reflection.GeneratedProtocolMessageType('SiteRecList', (_message.Message,), dict(

    Item = _reflection.GeneratedProtocolMessageType('Item', (_message.Message,), dict(
      DESCRIPTOR = _SITERECLISTMODEL_SITERECLIST_ITEM,
      __module__ = 'proto.recommend_model_pb2'
      # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.SiteRecListModel.SiteRecList.Item)
      ))
    ,
    DESCRIPTOR = _SITERECLISTMODEL_SITERECLIST,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.SiteRecListModel.SiteRecList)
    ))
  ,
  DESCRIPTOR = _SITERECLISTMODEL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.SiteRecListModel)
  ))
_sym_db.RegisterMessage(SiteRecListModel)
_sym_db.RegisterMessage(SiteRecListModel.SiteRecList)
_sym_db.RegisterMessage(SiteRecListModel.SiteRecList.Item)

MatcherParamByBussiness = _reflection.GeneratedProtocolMessageType('MatcherParamByBussiness', (_message.Message,), dict(

  RecListModelParam = _reflection.GeneratedProtocolMessageType('RecListModelParam', (_message.Message,), dict(

    RecListModelAttr = _reflection.GeneratedProtocolMessageType('RecListModelAttr', (_message.Message,), dict(
      DESCRIPTOR = _MATCHERPARAMBYBUSSINESS_RECLISTMODELPARAM_RECLISTMODELATTR,
      __module__ = 'proto.recommend_model_pb2'
      # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.MatcherParamByBussiness.RecListModelParam.RecListModelAttr)
      ))
    ,
    DESCRIPTOR = _MATCHERPARAMBYBUSSINESS_RECLISTMODELPARAM,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.MatcherParamByBussiness.RecListModelParam)
    ))
  ,

  CombineParam = _reflection.GeneratedProtocolMessageType('CombineParam', (_message.Message,), dict(

    PercentileDistribution = _reflection.GeneratedProtocolMessageType('PercentileDistribution', (_message.Message,), dict(
      DESCRIPTOR = _MATCHERPARAMBYBUSSINESS_COMBINEPARAM_PERCENTILEDISTRIBUTION,
      __module__ = 'proto.recommend_model_pb2'
      # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.MatcherParamByBussiness.CombineParam.PercentileDistribution)
      ))
    ,

    CombinationPlan = _reflection.GeneratedProtocolMessageType('CombinationPlan', (_message.Message,), dict(
      DESCRIPTOR = _MATCHERPARAMBYBUSSINESS_COMBINEPARAM_COMBINATIONPLAN,
      __module__ = 'proto.recommend_model_pb2'
      # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.MatcherParamByBussiness.CombineParam.CombinationPlan)
      ))
    ,
    DESCRIPTOR = _MATCHERPARAMBYBUSSINESS_COMBINEPARAM,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.MatcherParamByBussiness.CombineParam)
    ))
  ,
  DESCRIPTOR = _MATCHERPARAMBYBUSSINESS,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.MatcherParamByBussiness)
  ))
_sym_db.RegisterMessage(MatcherParamByBussiness)
_sym_db.RegisterMessage(MatcherParamByBussiness.RecListModelParam)
_sym_db.RegisterMessage(MatcherParamByBussiness.RecListModelParam.RecListModelAttr)
_sym_db.RegisterMessage(MatcherParamByBussiness.CombineParam)
_sym_db.RegisterMessage(MatcherParamByBussiness.CombineParam.PercentileDistribution)
_sym_db.RegisterMessage(MatcherParamByBussiness.CombineParam.CombinationPlan)

MatcherParamByBussiness2 = _reflection.GeneratedProtocolMessageType('MatcherParamByBussiness2', (_message.Message,), dict(

  StrategyParam = _reflection.GeneratedProtocolMessageType('StrategyParam', (_message.Message,), dict(

    DataList = _reflection.GeneratedProtocolMessageType('DataList', (_message.Message,), dict(
      DESCRIPTOR = _MATCHERPARAMBYBUSSINESS2_STRATEGYPARAM_DATALIST,
      __module__ = 'proto.recommend_model_pb2'
      # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.MatcherParamByBussiness2.StrategyParam.DataList)
      ))
    ,
    DESCRIPTOR = _MATCHERPARAMBYBUSSINESS2_STRATEGYPARAM,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.MatcherParamByBussiness2.StrategyParam)
    ))
  ,

  ScenePlanCfg = _reflection.GeneratedProtocolMessageType('ScenePlanCfg', (_message.Message,), dict(
    DESCRIPTOR = _MATCHERPARAMBYBUSSINESS2_SCENEPLANCFG,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.MatcherParamByBussiness2.ScenePlanCfg)
    ))
  ,

  ExpParamsPlanCfg = _reflection.GeneratedProtocolMessageType('ExpParamsPlanCfg', (_message.Message,), dict(
    DESCRIPTOR = _MATCHERPARAMBYBUSSINESS2_EXPPARAMSPLANCFG,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.MatcherParamByBussiness2.ExpParamsPlanCfg)
    ))
  ,
  DESCRIPTOR = _MATCHERPARAMBYBUSSINESS2,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.MatcherParamByBussiness2)
  ))
_sym_db.RegisterMessage(MatcherParamByBussiness2)
_sym_db.RegisterMessage(MatcherParamByBussiness2.StrategyParam)
_sym_db.RegisterMessage(MatcherParamByBussiness2.StrategyParam.DataList)
_sym_db.RegisterMessage(MatcherParamByBussiness2.ScenePlanCfg)
_sym_db.RegisterMessage(MatcherParamByBussiness2.ExpParamsPlanCfg)

MatcherParam = _reflection.GeneratedProtocolMessageType('MatcherParam', (_message.Message,), dict(
  DESCRIPTOR = _MATCHERPARAM,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.MatcherParam)
  ))
_sym_db.RegisterMessage(MatcherParam)

RankerParamByBussiness = _reflection.GeneratedProtocolMessageType('RankerParamByBussiness', (_message.Message,), dict(

  PlusSizeCateParam = _reflection.GeneratedProtocolMessageType('PlusSizeCateParam', (_message.Message,), dict(
    DESCRIPTOR = _RANKERPARAMBYBUSSINESS_PLUSSIZECATEPARAM,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RankerParamByBussiness.PlusSizeCateParam)
    ))
  ,

  BucketGenParam = _reflection.GeneratedProtocolMessageType('BucketGenParam', (_message.Message,), dict(

    ItemTagGenBucket = _reflection.GeneratedProtocolMessageType('ItemTagGenBucket', (_message.Message,), dict(
      DESCRIPTOR = _RANKERPARAMBYBUSSINESS_BUCKETGENPARAM_ITEMTAGGENBUCKET,
      __module__ = 'proto.recommend_model_pb2'
      # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RankerParamByBussiness.BucketGenParam.ItemTagGenBucket)
      ))
    ,
    DESCRIPTOR = _RANKERPARAMBYBUSSINESS_BUCKETGENPARAM,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RankerParamByBussiness.BucketGenParam)
    ))
  ,

  BucketQuatoParam = _reflection.GeneratedProtocolMessageType('BucketQuatoParam', (_message.Message,), dict(

    PreferedBucket = _reflection.GeneratedProtocolMessageType('PreferedBucket', (_message.Message,), dict(
      DESCRIPTOR = _RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM_PREFEREDBUCKET,
      __module__ = 'proto.recommend_model_pb2'
      # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam.PreferedBucket)
      ))
    ,

    PercentileBucket = _reflection.GeneratedProtocolMessageType('PercentileBucket', (_message.Message,), dict(
      DESCRIPTOR = _RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM_PERCENTILEBUCKET,
      __module__ = 'proto.recommend_model_pb2'
      # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam.PercentileBucket)
      ))
    ,

    QuatoPlan = _reflection.GeneratedProtocolMessageType('QuatoPlan', (_message.Message,), dict(
      DESCRIPTOR = _RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM_QUATOPLAN,
      __module__ = 'proto.recommend_model_pb2'
      # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam.QuatoPlan)
      ))
    ,
    DESCRIPTOR = _RANKERPARAMBYBUSSINESS_BUCKETQUATOPARAM,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RankerParamByBussiness.BucketQuatoParam)
    ))
  ,

  UserTagPromoteParam = _reflection.GeneratedProtocolMessageType('UserTagPromoteParam', (_message.Message,), dict(

    RankWeight = _reflection.GeneratedProtocolMessageType('RankWeight', (_message.Message,), dict(
      DESCRIPTOR = _RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM_RANKWEIGHT,
      __module__ = 'proto.recommend_model_pb2'
      # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.RankWeight)
      ))
    ,

    PromoteInfo = _reflection.GeneratedProtocolMessageType('PromoteInfo', (_message.Message,), dict(
      DESCRIPTOR = _RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM_PROMOTEINFO,
      __module__ = 'proto.recommend_model_pb2'
      # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.PromoteInfo)
      ))
    ,

    PromotePlan = _reflection.GeneratedProtocolMessageType('PromotePlan', (_message.Message,), dict(
      DESCRIPTOR = _RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM_PROMOTEPLAN,
      __module__ = 'proto.recommend_model_pb2'
      # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam.PromotePlan)
      ))
    ,
    DESCRIPTOR = _RANKERPARAMBYBUSSINESS_USERTAGPROMOTEPARAM,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RankerParamByBussiness.UserTagPromoteParam)
    ))
  ,

  ItemInventoryPromoteParam = _reflection.GeneratedProtocolMessageType('ItemInventoryPromoteParam', (_message.Message,), dict(

    FomulaParam = _reflection.GeneratedProtocolMessageType('FomulaParam', (_message.Message,), dict(
      DESCRIPTOR = _RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM_FOMULAPARAM,
      __module__ = 'proto.recommend_model_pb2'
      # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RankerParamByBussiness.ItemInventoryPromoteParam.FomulaParam)
      ))
    ,

    PromoteInfo = _reflection.GeneratedProtocolMessageType('PromoteInfo', (_message.Message,), dict(
      DESCRIPTOR = _RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM_PROMOTEINFO,
      __module__ = 'proto.recommend_model_pb2'
      # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RankerParamByBussiness.ItemInventoryPromoteParam.PromoteInfo)
      ))
    ,

    PromotePlan = _reflection.GeneratedProtocolMessageType('PromotePlan', (_message.Message,), dict(
      DESCRIPTOR = _RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM_PROMOTEPLAN,
      __module__ = 'proto.recommend_model_pb2'
      # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RankerParamByBussiness.ItemInventoryPromoteParam.PromotePlan)
      ))
    ,
    DESCRIPTOR = _RANKERPARAMBYBUSSINESS_ITEMINVENTORYPROMOTEPARAM,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RankerParamByBussiness.ItemInventoryPromoteParam)
    ))
  ,

  ItemProfileDataIdParam = _reflection.GeneratedProtocolMessageType('ItemProfileDataIdParam', (_message.Message,), dict(

    TagnameDataIdPair = _reflection.GeneratedProtocolMessageType('TagnameDataIdPair', (_message.Message,), dict(
      DESCRIPTOR = _RANKERPARAMBYBUSSINESS_ITEMPROFILEDATAIDPARAM_TAGNAMEDATAIDPAIR,
      __module__ = 'proto.recommend_model_pb2'
      # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RankerParamByBussiness.ItemProfileDataIdParam.TagnameDataIdPair)
      ))
    ,
    DESCRIPTOR = _RANKERPARAMBYBUSSINESS_ITEMPROFILEDATAIDPARAM,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RankerParamByBussiness.ItemProfileDataIdParam)
    ))
  ,
  DESCRIPTOR = _RANKERPARAMBYBUSSINESS,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RankerParamByBussiness)
  ))
_sym_db.RegisterMessage(RankerParamByBussiness)
_sym_db.RegisterMessage(RankerParamByBussiness.PlusSizeCateParam)
_sym_db.RegisterMessage(RankerParamByBussiness.BucketGenParam)
_sym_db.RegisterMessage(RankerParamByBussiness.BucketGenParam.ItemTagGenBucket)
_sym_db.RegisterMessage(RankerParamByBussiness.BucketQuatoParam)
_sym_db.RegisterMessage(RankerParamByBussiness.BucketQuatoParam.PreferedBucket)
_sym_db.RegisterMessage(RankerParamByBussiness.BucketQuatoParam.PercentileBucket)
_sym_db.RegisterMessage(RankerParamByBussiness.BucketQuatoParam.QuatoPlan)
_sym_db.RegisterMessage(RankerParamByBussiness.UserTagPromoteParam)
_sym_db.RegisterMessage(RankerParamByBussiness.UserTagPromoteParam.RankWeight)
_sym_db.RegisterMessage(RankerParamByBussiness.UserTagPromoteParam.PromoteInfo)
_sym_db.RegisterMessage(RankerParamByBussiness.UserTagPromoteParam.PromotePlan)
_sym_db.RegisterMessage(RankerParamByBussiness.ItemInventoryPromoteParam)
_sym_db.RegisterMessage(RankerParamByBussiness.ItemInventoryPromoteParam.FomulaParam)
_sym_db.RegisterMessage(RankerParamByBussiness.ItemInventoryPromoteParam.PromoteInfo)
_sym_db.RegisterMessage(RankerParamByBussiness.ItemInventoryPromoteParam.PromotePlan)
_sym_db.RegisterMessage(RankerParamByBussiness.ItemProfileDataIdParam)
_sym_db.RegisterMessage(RankerParamByBussiness.ItemProfileDataIdParam.TagnameDataIdPair)

RankerParamByScene = _reflection.GeneratedProtocolMessageType('RankerParamByScene', (_message.Message,), dict(
  DESCRIPTOR = _RANKERPARAMBYSCENE,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RankerParamByScene)
  ))
_sym_db.RegisterMessage(RankerParamByScene)

RankerParam = _reflection.GeneratedProtocolMessageType('RankerParam', (_message.Message,), dict(
  DESCRIPTOR = _RANKERPARAM,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RankerParam)
  ))
_sym_db.RegisterMessage(RankerParam)

ItemProfileModel = _reflection.GeneratedProtocolMessageType('ItemProfileModel', (_message.Message,), dict(

  TagAttr = _reflection.GeneratedProtocolMessageType('TagAttr', (_message.Message,), dict(
    DESCRIPTOR = _ITEMPROFILEMODEL_TAGATTR,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemProfileModel.TagAttr)
    ))
  ,

  ItemTag = _reflection.GeneratedProtocolMessageType('ItemTag', (_message.Message,), dict(
    DESCRIPTOR = _ITEMPROFILEMODEL_ITEMTAG,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemProfileModel.ItemTag)
    ))
  ,

  ItemProfile = _reflection.GeneratedProtocolMessageType('ItemProfile', (_message.Message,), dict(
    DESCRIPTOR = _ITEMPROFILEMODEL_ITEMPROFILE,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemProfileModel.ItemProfile)
    ))
  ,
  DESCRIPTOR = _ITEMPROFILEMODEL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemProfileModel)
  ))
_sym_db.RegisterMessage(ItemProfileModel)
_sym_db.RegisterMessage(ItemProfileModel.TagAttr)
_sym_db.RegisterMessage(ItemProfileModel.ItemTag)
_sym_db.RegisterMessage(ItemProfileModel.ItemProfile)

ItemProfileKey = _reflection.GeneratedProtocolMessageType('ItemProfileKey', (_message.Message,), dict(
  DESCRIPTOR = _ITEMPROFILEKEY,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemProfileKey)
  ))
_sym_db.RegisterMessage(ItemProfileKey)

ItemProfileVal = _reflection.GeneratedProtocolMessageType('ItemProfileVal', (_message.Message,), dict(
  DESCRIPTOR = _ITEMPROFILEVAL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemProfileVal)
  ))
_sym_db.RegisterMessage(ItemProfileVal)

ItemProfileInfo = _reflection.GeneratedProtocolMessageType('ItemProfileInfo', (_message.Message,), dict(
  DESCRIPTOR = _ITEMPROFILEINFO,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemProfileInfo)
  ))
_sym_db.RegisterMessage(ItemProfileInfo)

ItemProfileModelV2 = _reflection.GeneratedProtocolMessageType('ItemProfileModelV2', (_message.Message,), dict(
  DESCRIPTOR = _ITEMPROFILEMODELV2,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemProfileModelV2)
  ))
_sym_db.RegisterMessage(ItemProfileModelV2)

ItemTagsKey = _reflection.GeneratedProtocolMessageType('ItemTagsKey', (_message.Message,), dict(
  DESCRIPTOR = _ITEMTAGSKEY,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemTagsKey)
  ))
_sym_db.RegisterMessage(ItemTagsKey)

ItemTagsVal = _reflection.GeneratedProtocolMessageType('ItemTagsVal', (_message.Message,), dict(

  Tag = _reflection.GeneratedProtocolMessageType('Tag', (_message.Message,), dict(
    DESCRIPTOR = _ITEMTAGSVAL_TAG,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemTagsVal.Tag)
    ))
  ,
  DESCRIPTOR = _ITEMTAGSVAL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemTagsVal)
  ))
_sym_db.RegisterMessage(ItemTagsVal)
_sym_db.RegisterMessage(ItemTagsVal.Tag)

ItemTagsInfo = _reflection.GeneratedProtocolMessageType('ItemTagsInfo', (_message.Message,), dict(
  DESCRIPTOR = _ITEMTAGSINFO,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemTagsInfo)
  ))
_sym_db.RegisterMessage(ItemTagsInfo)

ItemTagsModel = _reflection.GeneratedProtocolMessageType('ItemTagsModel', (_message.Message,), dict(
  DESCRIPTOR = _ITEMTAGSMODEL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemTagsModel)
  ))
_sym_db.RegisterMessage(ItemTagsModel)

TransformFeaVal = _reflection.GeneratedProtocolMessageType('TransformFeaVal', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFORMFEAVAL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.TransformFeaVal)
  ))
_sym_db.RegisterMessage(TransformFeaVal)

RecList = _reflection.GeneratedProtocolMessageType('RecList', (_message.Message,), dict(

  Dimension = _reflection.GeneratedProtocolMessageType('Dimension', (_message.Message,), dict(
    DESCRIPTOR = _RECLIST_DIMENSION,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecList.Dimension)
    ))
  ,

  Item = _reflection.GeneratedProtocolMessageType('Item', (_message.Message,), dict(
    DESCRIPTOR = _RECLIST_ITEM,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecList.Item)
    ))
  ,
  DESCRIPTOR = _RECLIST,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecList)
  ))
_sym_db.RegisterMessage(RecList)
_sym_db.RegisterMessage(RecList.Dimension)
_sym_db.RegisterMessage(RecList.Item)

RecListKey = _reflection.GeneratedProtocolMessageType('RecListKey', (_message.Message,), dict(

  Dimension = _reflection.GeneratedProtocolMessageType('Dimension', (_message.Message,), dict(
    DESCRIPTOR = _RECLISTKEY_DIMENSION,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecListKey.Dimension)
    ))
  ,
  DESCRIPTOR = _RECLISTKEY,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecListKey)
  ))
_sym_db.RegisterMessage(RecListKey)
_sym_db.RegisterMessage(RecListKey.Dimension)

RecListVal = _reflection.GeneratedProtocolMessageType('RecListVal', (_message.Message,), dict(

  Item = _reflection.GeneratedProtocolMessageType('Item', (_message.Message,), dict(
    DESCRIPTOR = _RECLISTVAL_ITEM,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecListVal.Item)
    ))
  ,
  DESCRIPTOR = _RECLISTVAL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecListVal)
  ))
_sym_db.RegisterMessage(RecListVal)
_sym_db.RegisterMessage(RecListVal.Item)

RecListInfo = _reflection.GeneratedProtocolMessageType('RecListInfo', (_message.Message,), dict(
  DESCRIPTOR = _RECLISTINFO,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecListInfo)
  ))
_sym_db.RegisterMessage(RecListInfo)

RecListModel = _reflection.GeneratedProtocolMessageType('RecListModel', (_message.Message,), dict(
  DESCRIPTOR = _RECLISTMODEL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecListModel)
  ))
_sym_db.RegisterMessage(RecListModel)

SizeSaleStatKey = _reflection.GeneratedProtocolMessageType('SizeSaleStatKey', (_message.Message,), dict(
  DESCRIPTOR = _SIZESALESTATKEY,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.SizeSaleStatKey)
  ))
_sym_db.RegisterMessage(SizeSaleStatKey)

SizeSaleStatVal = _reflection.GeneratedProtocolMessageType('SizeSaleStatVal', (_message.Message,), dict(
  DESCRIPTOR = _SIZESALESTATVAL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.SizeSaleStatVal)
  ))
_sym_db.RegisterMessage(SizeSaleStatVal)

SizeSaleStatInfo = _reflection.GeneratedProtocolMessageType('SizeSaleStatInfo', (_message.Message,), dict(
  DESCRIPTOR = _SIZESALESTATINFO,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.SizeSaleStatInfo)
  ))
_sym_db.RegisterMessage(SizeSaleStatInfo)

SizeSaleStatModel = _reflection.GeneratedProtocolMessageType('SizeSaleStatModel', (_message.Message,), dict(
  DESCRIPTOR = _SIZESALESTATMODEL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.SizeSaleStatModel)
  ))
_sym_db.RegisterMessage(SizeSaleStatModel)

FeatureMappingKey = _reflection.GeneratedProtocolMessageType('FeatureMappingKey', (_message.Message,), dict(
  DESCRIPTOR = _FEATUREMAPPINGKEY,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.FeatureMappingKey)
  ))
_sym_db.RegisterMessage(FeatureMappingKey)

FeatureMappingVal = _reflection.GeneratedProtocolMessageType('FeatureMappingVal', (_message.Message,), dict(
  DESCRIPTOR = _FEATUREMAPPINGVAL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.FeatureMappingVal)
  ))
_sym_db.RegisterMessage(FeatureMappingVal)

FeatureMappingInfo = _reflection.GeneratedProtocolMessageType('FeatureMappingInfo', (_message.Message,), dict(
  DESCRIPTOR = _FEATUREMAPPINGINFO,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.FeatureMappingInfo)
  ))
_sym_db.RegisterMessage(FeatureMappingInfo)

FeatureMappingModel = _reflection.GeneratedProtocolMessageType('FeatureMappingModel', (_message.Message,), dict(
  DESCRIPTOR = _FEATUREMAPPINGMODEL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.FeatureMappingModel)
  ))
_sym_db.RegisterMessage(FeatureMappingModel)

StatRateKey = _reflection.GeneratedProtocolMessageType('StatRateKey', (_message.Message,), dict(

  Dimension = _reflection.GeneratedProtocolMessageType('Dimension', (_message.Message,), dict(
    DESCRIPTOR = _STATRATEKEY_DIMENSION,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.StatRateKey.Dimension)
    ))
  ,
  DESCRIPTOR = _STATRATEKEY,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.StatRateKey)
  ))
_sym_db.RegisterMessage(StatRateKey)
_sym_db.RegisterMessage(StatRateKey.Dimension)

StatRateVal = _reflection.GeneratedProtocolMessageType('StatRateVal', (_message.Message,), dict(

  Metric = _reflection.GeneratedProtocolMessageType('Metric', (_message.Message,), dict(
    DESCRIPTOR = _STATRATEVAL_METRIC,
    __module__ = 'proto.recommend_model_pb2'
    # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.StatRateVal.Metric)
    ))
  ,
  DESCRIPTOR = _STATRATEVAL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.StatRateVal)
  ))
_sym_db.RegisterMessage(StatRateVal)
_sym_db.RegisterMessage(StatRateVal.Metric)

StatRateInfo = _reflection.GeneratedProtocolMessageType('StatRateInfo', (_message.Message,), dict(
  DESCRIPTOR = _STATRATEINFO,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.StatRateInfo)
  ))
_sym_db.RegisterMessage(StatRateInfo)

StatRateModel = _reflection.GeneratedProtocolMessageType('StatRateModel', (_message.Message,), dict(
  DESCRIPTOR = _STATRATEMODEL,
  __module__ = 'proto.recommend_model_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.model.StatRateModel)
  ))
_sym_db.RegisterMessage(StatRateModel)


# @@protoc_insertion_point(module_scope)
