import os,sys
import requests
import time
import codecs
import json
import logging
import warnings
from time import ctime,sleep

if __name__=='__main__':
    path = sys.path[0]
    log_path = path + '/log/'
    if not os.path.isdir(log_path):
        os.mkdir(log_path)
    logging.basicConfig(filename=log_path+'nav_logger_sc.log',
                        format   = '[%(asctime)s  %(filename)s %(lineno)d] %(message)s',
                        datefmt  = '%Y-%m-%d %A %H:%M:%S',
                        filemode = 'w',
                        level = logging.INFO)
 
    headers_dict = {'Content-Type':'application/json; charset=UTF-8'}
    api = 'http://recommend-materiel-es.abc.shein.com:9400/_xpack/sql/'
    api2 = 'http://recommend-svr-srch-recall-adapter-prod-default-s1.prod.abc.sheincorp.cn/recommend/rec_srch_recall'
    while(1):
        param_dict = {}
        sc_dict = {}
        param_dict["query"] = "select site_uid from rec_item_info_access_5_5007 where sc_url_id is not null group by site_uid"
        param_dict["fetch_size"] = 10000
        data_json = json.dumps(param_dict)
        r=requests.post(api, data=data_json, headers=headers_dict)
        dict_json = json.loads(r.text)
        list_data = dict_json.get('rows')
        for list_sub in list_data:
            url = 'http://recommend-materiel-es.abc.shein.com:9400/rec_item_info_access_5_5007/_settings/'
            param = {"max_result_window":"8000000"}
            r=requests.put(url, json=param)

            logging.info("start init, site_uid:{0}".format(str(list_sub[0])))
            param_dict["fetch_size"] = 100000
            param_dict["query"] = "select site_id,site_uid,language_flag,sc_url_id,tagId from rec_item_info_access_5_5007 where site_uid = '" + str(list_sub[0]) + "' and sc_url_id is not null and tagId is not null"
            data_json2 = json.dumps(param_dict)
            r=requests.post(api, data=data_json2, headers=headers_dict)
            dict_json2 = json.loads(r.text)
            list_data2 = dict_json2.get('rows')
            sc_dict.clear()
            for list_sub2 in list_data2:
                tmp_dict = {}
                tmp_dict["site_id"] = list_sub2[0]
                tmp_dict["site_uid"] = list_sub2[1]
                tmp_dict["language_flag"] = list_sub2[2]
                tmp_dict["req_num"] = 1000
                tmp_dict["rec_type"] = 5
                sc_url_id = str(list_sub2[3])
                if sc_url_id == "None":
                    continue
                list_sc = sc_url_id.split(",")
                for sc in list_sc:
                    key = str(list_sub2[0]) + "," + str(list_sub2[1]) + "," + str(list_sub2[2]) + "," + sc + "," + str(list_sub2[4])
                    if  key in sc_dict:
                        continue
                    tmp_dict["rule_id"] = "recplt_pool:5007|recplt_srch:9|sc_url_id:" + sc + "|tagId:" + str(list_sub2[4])
                    data_json3 = json.dumps(tmp_dict)
                    r=requests.post(api2, data=data_json3, headers=headers_dict)
                    logging.info("json:{0},{1}".format(data_json3, r.status_code))
                    sc_dict[key] = 1
            list_data2.clear()
        list_data.clear()
        logging.info("end init")
        time.sleep(120)