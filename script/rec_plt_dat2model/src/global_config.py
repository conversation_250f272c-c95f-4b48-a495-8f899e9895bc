#!/usr/bin/evn python
# _*_ coding:utf-8 _*_

#
# author: bluse.lee
# date: 2019.07.15
#
import os
import sys
import logging


#python 单例类
class Singleton(object):
    def __new__(cls, *args, **kw):
        if not hasattr(cls, '_instance'):
            orig = super(Singleton, cls)
            cls._instance = orig.__new__(cls, *args, **kw)
        return cls._instance

class GlobalConfig(Singleton):
    env = 'dev'       #默认为prod
    def __init__(self):
        pass
    
    def __del__(self):
        pass

    def set_env(self, env):
        self.env = env

    def get_env(self):
        return self.env