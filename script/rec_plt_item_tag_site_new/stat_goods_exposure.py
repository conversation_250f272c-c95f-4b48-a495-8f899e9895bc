#!/usr/bin/env python
# _*_ coding:utf-8 _*_

import sys
import os
import time
import math
import logging
import json
import requests
from datetime import datetime, date, timedelta
from copy import deepcopy
import hashlib
import subprocess
import download
import util
import upload
import stat_new_goods
import codecs
from math import ceil

class PltItemDaily(object):
    #默认曝光最小量
    default_min_exposure_num = 1000

    site_country_dict = {}
    #country,cate:min_exposure
    country_cate_min_exposure_dict = {}

    country_cate_add_exposure_dict = {}

    #country,last_cate,goods_id:exposure
    new_goods_exposure_dict = {}
    and_pv_rate_dict = {}
    ios_pv_rate_dict = {}
    pwa_pv_rate_dict = {}
    pc_pv_rate_dict = {}

    def __init__(self,path,section): 
        self.section = section 
        self.conf_path = path + '/' + 'conf/'
        self.exposure_dat_day7_path = path + '/' + 'exposure_dat_day7/'
        self.exposure_dat_today_path = path + '/' + 'exposure_dat_today/'
        self.tmp_dat_path = path + '/' + 'tmp_dat/'
        self.stat_exposure_result_path = "/data/exposure/new_stat_result/"

        if not os.path.exists(self.conf_path):
            os.makedirs(self.conf_path)
        if not os.path.exists(self.exposure_dat_day7_path):
            os.makedirs(self.exposure_dat_day7_path)
        if not os.path.exists(self.exposure_dat_today_path):
            os.makedirs(self.exposure_dat_today_path)
        if not os.path.exists(self.tmp_dat_path):
            os.makedirs(self.tmp_dat_path)
        if not os.path.exists(self.stat_exposure_result_path):
            os.makedirs(self.stat_exposure_result_path)
 
    #初始化站点和国家映射关系
    def init_site_country_dict(self):
      file_name = download.get_site_country_conf_filename(self.section)
      if len(file_name) == 0:
        logging.info("get_site_country_conf_filename fail")
        return False
      data_path_file = self.tmp_dat_path + file_name
      if os.path.exists(data_path_file) == False:
        logging.info("file not exists:{0}".format(data_path_file))
        return False
      logging.info("site_country_conf_filename:{0}".format(file_name))
      #self.site_country_dict.clear()
      with open(data_path_file) as f:
        for row in f:
          site_uid, country = row.split("\t")[0:2]
          site_uid = site_uid.strip()
          country = country.strip()
          if len(country) >= 1 and len(site_uid) >= 1:
            self.site_country_dict[site_uid] = country.upper()      
      logging.info("init_site_country_dict ok")
      return True
    
    #获取配置文件中国家+类目的 最小曝光量
    def init_country_cate_min_exposure_dict(self):
      file_name = download.get_site_country_cate_exposure_conf_filename(self.section)
      if len(file_name) == 0:
        logging.info("new_goods_min_exposure_conf fail")
        return False
      path_file = self.tmp_dat_path + file_name
      if os.path.exists(path_file) == False:
        logging.info("file not exists:{0}".format(path_file))
        return False
      #self.country_cate_min_exposure_dict.clear()  
      with open(path_file) as f:
        for row in f:
          row = row.strip()
          site_id, country,cate,min_exposure, add_pv, and_rate, ios_rate, pwa_rate, pc_rate = row.split("\t")[0:9]
          key = site_id + "," + country + "," + cate
          if len(country) >= 1 and len(cate) >= 1 and len(min_exposure) >= 1:
            self.country_cate_min_exposure_dict[key] = min_exposure
          if len(country) >= 1 and len(cate) >= 1 and add_pv != "0" and  len(add_pv) >= 1:
            self.country_cate_add_exposure_dict[key] = add_pv
          if len(country) >= 1 and len(cate) >= 1 and and_rate != "NULL" and len(and_rate) >= 1:
            self.and_pv_rate_dict[key] = and_rate
          if len(country) >= 1 and len(cate) >= 1 and ios_rate != "NULL" and len(ios_rate) >= 1:
            self.ios_pv_rate_dict[key] = ios_rate
          if len(country) >= 1 and len(cate) >= 1 and pwa_rate != "NULL" and len(pwa_rate) >= 1:
            self.pwa_pv_rate_dict[key] = pwa_rate
          if len(country) >= 1 and len(cate) >= 1 and pc_rate != "NULL" and len(pc_rate) >= 1:

            self.pc_pv_rate_dict[key] = pc_rate

      logging.info("init_country_cate_min_exposure_dict ok")
      return True

    #获取最小曝光量（国家+类目维度）
    def get_min_exposure_num(self, site_id, country, last_cate):
      key = site_id + "," + country + "," + last_cate
      min_exposure = self.country_cate_min_exposure_dict.get(key, "")
      if len(min_exposure) == 0 or min_exposure == "NULL":
        key = site_id + ",other," + last_cate
        min_exposure = self.country_cate_min_exposure_dict.get(key, "")
        if len(min_exposure) == 0 or min_exposure == "NULL":
          return self.default_min_exposure_num, False
      return int(min_exposure), True

    #获取流量换算比例
    def get_pv_rate(self, site_id, country, last_cate, site_uid):
      key = site_id + "," + country + "," + last_cate
      rate = "1.0"
      if site_uid.find("and") == 0:
        rate = self.and_pv_rate_dict.get(key, "1.0")
      elif site_uid.find("ios") == 0:
        rate = self.ios_pv_rate_dict.get(key, "1.0")
      elif site_uid.find("pw") == 0:
        rate = self.pwa_pv_rate_dict.get(key, "1.0")
      else:
        rate = self.pc_pv_rate_dict.get(key, "1.0")
      return float(rate)

    #统计6天的曝光
    def stat_7day_exposure(self):
      path_file = self.exposure_dat_day7_path
      e8_time = 8*3600
      for i in range(2, 8):
        date = time.strftime("%Y%m%d", time.localtime(time.time() + e8_time - 3600*24*i))
        download.download_day_exposure_file(self.section, date, path_file)
      file_list = []
      download.get_exposure_file(self.section, path_file, file_list)
      for file_name in file_list:
        logging.info("stat_7day_exposure.files:{0}".format(file_name))
        with open(path_file + file_name) as f:
          for row in f:
            #AR      0       1727    114355  2       0       0       0.0     1       0       0
            #site_id, site_id, cate,goods_id,expose_pv,click_pv,pay_cnt,pay_amt,expose_uv,click_uv,pay_uv
            try:
              country, site_id, site_uid, last_cate, goods_id, exposure_num, click_pv, pay_cnt, pay_amt, expose_uv, click_uv, pay_uv  = row.split("\t")[0:12]
            except Exception as e:
              logging.info("stat_7day_exposure, bad_data.files:{0},{1}".format(file_name, row))
              continue
            site_id = site_id.strip()
            site_uid = site_uid.strip()
            last_cate = last_cate.strip()  
            goods_id = goods_id.strip()  
            exposure_num = exposure_num.strip()  
            click_pv = click_pv.strip()  
            pay_cnt = pay_cnt.strip()
            pay_amt = pay_amt.strip()
            expose_uv = expose_uv.strip()
            click_uv = click_uv.strip()
            pay_uv = pay_uv.strip()
            #判断是否为新品
            key = goods_id + "," + country + "," + site_id
            if stat_new_goods.is_new_goods(site_id, country, goods_id, last_cate) == False:
              continue
            key = country + "," + last_cate + "," + goods_id + "," + site_id
            tmp_exposure_num = self.new_goods_exposure_dict.get(key, 0)
            rate = self.get_pv_rate(site_id, country, last_cate, site_uid)
            self.new_goods_exposure_dict[key] = tmp_exposure_num + int(ceil(rate * int(exposure_num)))
      logging.info("stat_7day_exposure finish,new_goods_exposure_dict.size:{0}".format(len(self.new_goods_exposure_dict)))

    #统计当前+昨天的曝光
    def stat_rt_exposure(self):
      path_file = self.exposure_dat_today_path
      e8_time = 8*3600
      yesterday = time.strftime("%Y%m%d", time.localtime(time.time() + e8_time - 3600*24))
      today = time.strftime("%Y%m%d", time.localtime(time.time() + e8_time))
      download.download_rt_exposure_file(self.section, yesterday, path_file)
      download.download_rt_exposure_file(self.section, today, path_file)
      file_list = []
      download.get_exposure_file(self.section, path_file, file_list)
      for file_name in file_list:
        logging.info("rt_exposure.files:{0}".format(file_name))
        with codecs.open(path_file + file_name, 'r', 'utf-8') as f:
          for row in f:
            row = row.strip()
            # goods_id, site_uid, last_cate, supplier_id, exposure_num, 点击数, 下单数, 下单总金额
            try:
              goods_id, site_uid, last_cate, supplier_id, exposure_num, tmp1, tmp2, tmp3 = row.split(",")[0:8]
            except Exception as e:
              logging.info("bad_data.files:{0},{1}".format(file_name, row))
              continue
            # 过滤掉rw站 site_uid
            site_id = "7"
            if site_uid.find("rw") >= 0:
              site_id = "9"
            country = self.site_country_dict.get(site_uid, "")
            if len(country) <= 0:
              continue
            #判断是否为新品
            key = goods_id + "," + country + "," + site_id
            if stat_new_goods.is_new_goods(site_id, country, goods_id, last_cate) == False:
              continue
            key = country + "," + last_cate  + "," + goods_id + "," + site_id
            tmp_exposure_num = self.new_goods_exposure_dict.get(key, 0)
            rate = self.get_pv_rate(site_id, country, last_cate, site_uid)
            self.new_goods_exposure_dict[key] = tmp_exposure_num + int(ceil(rate * int(exposure_num)))
      logging.info("stat_rt_exposure finish,new_goods_exposure_dict.size:{0}".format(len(self.new_goods_exposure_dict)))

    def del_tmp_dat(self):
      if self.section != "local":
        cmd = "rm " + self.exposure_dat_day7_path + "* -f"
        subprocess.call(cmd, shell=True)
        cmd = "rm " + self.exposure_dat_today_path + "* -f"
        subprocess.call(cmd, shell=True)
        util.del_history_file(self.tmp_dat_path)
        util.del_history_file(self.stat_exposure_result_path)

    #保存曝光数据 7天+当天
    def save_exposure_data(self):
      if len(self.new_goods_exposure_dict) == 0:
        return
      dtime = datetime.now().strftime("%Y%m%d%H%M")
      write_data_file = self.stat_exposure_result_path + "stat_exposure_num.dat" + "." + dtime
      data_file = open(write_data_file, 'wb')
      for key, values in self.new_goods_exposure_dict.items():
        data = key + "," + str(values) + "\n"
        data_file.write(data.encode())
      data_file.close()

      md5val = hashlib.md5(open(write_data_file,'rb').read()).hexdigest()
      tag_file_name = self.stat_exposure_result_path + "stat_exposure_num.dat.tag"
      tag_file = open(tag_file_name, 'wb')
      data = md5val + "  " + "stat_exposure_num.dat" + "." + dtime
      tag_file.write(data.encode())
      tag_file.close()
        
    def get_new_item_top_exposure(self):
      tmp_dict = {}
      tmp_list = []
      pool_total_dict = {}
      pool_count_dict = {}
      new_item_top_exposure_dict = {}
      for key, values in  self.new_goods_exposure_dict.items():
        country, last_cate, goods_id, site_id = key.split(",")[0:4]
        exposure_num = values  
        pool_id = site_id + "_" + country + "_" + last_cate
        min_exposure, defaut_exposure_flag = self.get_min_exposure_num(site_id, country, last_cate)
        if exposure_num < min_exposure and stat_new_goods.is_new_arrival_3(goods_id, country, site_id, last_cate) > 0:
          key = pool_id + "," + goods_id
          tmp_dict[key] = exposure_num
          # 获取国家+末级类目维度的未达标的总数
          count = pool_total_dict.get(pool_id, 0)
          pool_total_dict[pool_id] = count + 1

      # 按曝光数逆序排列
      tmp_list = sorted(tmp_dict.items(), key=lambda d:d[1], reverse=True)

      # 取TOP前20%
      data_list = []
      for tup in tmp_list:
        key = tup[0]
        exposure_num = tup[1]
        pool_id, goods_id = key.split(",")[0:2]
        total = pool_total_dict.get(pool_id, 0)
        max_num =  int(ceil(0.2 * total))
        count = pool_count_dict.get(pool_id, 0)
        if count < max_num:
          pool_count_dict[pool_id] = count + 1
          new_item_top_exposure_dict[key] = exposure_num
          data_list.append(key + "," + str(exposure_num))
      util.save_data_and_tag_file(self.section, self.stat_exposure_result_path, "new_item_top_exposure.dat", data_list, False)
      logging.info("new_item_top_exposure_dict.size:{0}".format(len(new_item_top_exposure_dict)))
      return new_item_top_exposure_dict
        

    #归并曝光量,生产pb文件
    def merge_exposure_data(self):
      today = datetime.today().strftime("%Y-%m-%d")
      data_list = []
      standard_goods_exposure = []
      new_item_flag_dict = {}
      tmp_new_goods_dict = deepcopy(stat_new_goods.get_new_goods())
      new_item_flag_dict = deepcopy(stat_new_goods.get_new_item_flag_dict())
      logging.info("new_goods_dict.size:{0}".format(len(tmp_new_goods_dict)))
      logging.info("new_goods_exposure_dict.size:{0}".format(len(self.new_goods_exposure_dict)))
      logging.info("new_item_flag_dict.size:{0}".format(len(new_item_flag_dict)))
      new_item_top_exposure_dict = self.get_new_item_top_exposure()
      for key, values in  self.new_goods_exposure_dict.items():
        country, last_cate, goods_id, site_id = key.split(",")[0:4]
        exposure_num = values
  
        pool_id = site_id + "_" + country
        item_id = goods_id

        key = goods_id + "," + country + "," + site_id + "," + last_cate
        tmp_new_goods_dict.pop(key, "")
        score = 0
        #曝光是否达标
        min_exposure, defaut_exposure_flag = self.get_min_exposure_num(site_id, country, last_cate)
        if exposure_num >= min_exposure:
            standard_goods_exposure.append(key)
            key2 = site_id + "," + country + "," + last_cate
            add_exposure = self.country_cate_add_exposure_dict.get(key2, "")
            values2 = new_item_flag_dict.get(key, "")
            if values2 == "":
                continue
            market_country, new_item_hq, new_item_flag = values2.split(",")[0:3]
            new_item_flag_dict[key] = market_country + "," + new_item_hq +  "," + "0"
            if country == "AR" and len(add_exposure) > 0 and len(values2):
              if country in market_country and exposure_num < int(add_exposure):
                diff = int(add_exposure) - exposure_num
                # 对商品进行流量加权
                data_list.append(pool_id + "," + item_id + ",0," + str(exposure_num) + "," + str(diff) + ",0")
              else:
                data_list.append(pool_id + "," + item_id + "," + "0," + str(exposure_num)+ ",0,0")
            else:
              data_list.append(pool_id + "," + item_id + "," + "0," + str(exposure_num) + ",0,0")
        else:
            score = min_exposure - exposure_num
            key3 = site_id + "_" + country + "_" + last_cate + "," + goods_id
            if (key3 in new_item_top_exposure_dict):
              data_list.append(pool_id + "," + item_id + "," + str(score) + "," + str(exposure_num) + ",0," + str(exposure_num))
            else:
              data_list.append(pool_id + "," + item_id + "," + str(score) + "," + str(exposure_num) + ",0,0")

      for key, values in tmp_new_goods_dict.items():
        goods_id, country, site_id, cate =  key.split(",")[0:4]
        pool_id = site_id + "_" + country
        item_id = goods_id
        min_exposure, defaut_exposure_flag = self.get_min_exposure_num(site_id, country, cate)
        data_list.append(pool_id + "," + item_id + "," + str(min_exposure) + ",0,0,0")
      
      new_item_flag_list = []
      for key, values in new_item_flag_dict.items():
        goods_id, country, site_id, cate =  key.split(",")[0:4]
        market_country, new_item_hq, new_item_flag =  values.split(",")[0:3]
        pool_id = site_id + "_" + country
        new_item_flag_list.append(pool_id + "," + goods_id + "," + new_item_hq + "," + new_item_flag)

      logging.info("after merge, tmp_new_goods_dict.size:{0}".format(len(tmp_new_goods_dict)))
      logging.info("after merge, new_goods_dict.size:{0}".format(stat_new_goods.count_new_goods()))

      if len(data_list) > 0:
        data_list.insert(0,"pool_id,item_id,new_item_score,rt_exposure_pv7,support_new_item_score,new_item_top_exposure")
        util.save_data_and_tag_file(self.section, self.stat_exposure_result_path, "item_tag_b1_goods_site_country_p2.dat", data_list, True)

      if len(new_item_flag_list) > 0:
        new_item_flag_list.insert(0,"pool_id,item_id,new_item_hq,new_item_flag")
        util.save_data_and_tag_file(self.section, self.stat_exposure_result_path, "item_tag_b1_goods_site_country_p3.dat", new_item_flag_list, True)

      util.save_data_and_tag_file(self.section, self.stat_exposure_result_path, "standard_exposure.dat", standard_goods_exposure, False)
      logging.info("merge_exposure_data, data_list.size:{0}".format(len(data_list)))

    def cal_exposure_data(self):
      while (1):     
        if stat_new_goods.count_new_goods() == 0:
          logging.info("new_goods_dict.size is 0")
          time.sleep(10)
          continue

        logging.info("start cal_exposure_data") 
        rt = self.init_site_country_dict()
        if rt == False:
          time.sleep(5)
          continue

        rt = self.init_country_cate_min_exposure_dict()
        if rt == False:
          time.sleep(5)
          continue

        self.new_goods_exposure_dict.clear()

        self.stat_7day_exposure()
        self.stat_rt_exposure()
        self.save_exposure_data()
        self.merge_exposure_data()
        #清理历史文件
        self.del_tmp_dat()
        logging.info("end cal_exposure_data")
        time.sleep(1800)
