#!/usr/bin/env python
# _*_ coding:utf-8 _*_

#
# athor: bluse
# create : 2019.01.18
#
import os
import sys
import logging
import warnings
import threading
#import Queue
import queue
#from queue import Queue
import time
import traceback
import json
import random
import socket
from datetime import datetime
from datetime import timedelta

import src.rec_plt_http as rec_plt_http
import src.rec_plt_b1_item_inventory as rec_plt_b1_item_inventory
import src.rec_plt_item_inventory as rec_plt_item_inventory
import src.recplt_site_warehouse_map as recplt_site_warehouse
import src.util as util
import src.prometheus_report as report
import src.global_variable as global_variable
import src.redis_operator as redis_op
import src.query_stock_mode_http as query_stock_mode_http
import src.prometheus_client as prometheus_client
from src.util import SkuLoad

data_queue = queue.Queue(maxsize=5000)

query_stock_queue = queue.Queue(maxsize=5000)

loop_flag = True
plt_file_name_prefix = 'plt_b1_item_inventory_all.txt'

def dispatch_task(data_path, query_stock_queue, env):
    recplt_item_inventory_obj = rec_plt_item_inventory.RecpltItemInventory(data_path, env)
    rt = recplt_item_inventory_obj.parse_goods_id(query_stock_queue)

def calculate_inventory_task(data_path, data_queue, plt_file_name, env):
    recplt_item_inventory_obj = rec_plt_item_inventory.RecpltItemInventory(data_path, env)
    global loop_flag
    cnt = 0
    try:
        logging.info(data_path+ plt_file_name)
        fwr = open(data_path+ plt_file_name, "w", encoding="utf-8")
        queue_empty_cnt = 0

        while loop_flag:
            try:
                data_str = data_queue.get(timeout = 5)
                data = json.loads(data_str)
             #   logging.info("data:{0}".format(data))
                virtual_type = data['type']
                if virtual_type == 'stop':
                    loop_flag = False
                    logging.info("finish calculate inventory data")
                elif virtual_type == '1':
                    #处理虚拟库存
                    req_param = data['req_body']
                    cnt+=1
                    #logging.info("req_param:%s", req_param)
                    rt, rsp_list_dict = recplt_item_inventory_obj.prepare_virtual_item_request(req_param)
                    if not rt:
                        logging.error("prepare request error")
                    else:
                        rt, new_list_dict = recplt_item_inventory_obj.join_virtual_item_data(req_param, rsp_list_dict)
                        if rt is True:
                            recplt_item_inventory_obj.write_data_2_plt_tag(fwr, new_list_dict)
                elif virtual_type == '0':
                    #处理真实库存
                    req_param = data['req_body']
                    sale_size_body = data['sale_size_body']
                    cnt += len(req_param)
                    rt, rsp_list_dict = recplt_item_inventory_obj.prepare_real_item_request(req_param, sale_size_body)
                    if not rt:
                        logging.error("prepare request error")
                    else:
                        new_list_dict = recplt_item_inventory_obj.join_data(req_param, rsp_list_dict)
                        recplt_item_inventory_obj.write_data_2_plt_tag(fwr, new_list_dict)

                data_queue.task_done()
            except queue.Empty:
                logging.info("Queue is Empty")
                queue_empty_cnt += 1
                if queue_empty_cnt > 4:
                    loop_flag = False
            
    except Exception as e:
        logging.error("exception {0}".format(traceback.format_exc()))
        report_dic = dict()
        report_dic['err_code'] = report.PREFIX_CODE
        report_dic['err_msg'] = "happen exception err_msg=" + traceback.format_exc()
        report.prometheus_report(report_dic)
    
    logging.info("[queue calculate] goods_sn total=%d", cnt)

def query_stock_mode_task(data_path, data_queue, query_stock_queue, env):
    query_stock_mode_obj = query_stock_mode_http.QueryStockModeHttp()
    queue_empty_cnt = 0
    while True:
        try:
            data_str = query_stock_queue.get(timeout = 5)
            data = json.loads(data_str) 
            query_stock_mode_obj.prepare(data, data_queue)

            virtual_type = data['type']
            if virtual_type == 'stop':
                logging.info("query_stock_mode_task thread exit")
                break
        except queue.Empty:
            logging.info("Queue is Empty")
            queue_empty_cnt += 1
            if queue_empty_cnt > 4:
                break
        except Exception as e:
            logging.error("exception {0}".format(traceback.format_exc()))
            report_dic = dict()
            report_dic['err_code'] = report.PREFIX_CODE
            report_dic['err_msg'] = "happen exception err_msg=" + traceback.format_exc()
            report.prometheus_report(report_dic)

    logging.info("mismatching stock total:%d", query_stock_mode_obj.GetVirtaulTotal())


def backup_inventory_calculate_data(data_path, oos_calculate_list):
    dtime = datetime.now().strftime("%Y%m%d%H%M%S")
    for plt_file_name in oos_calculate_list:
        src_file = data_path + plt_file_name
        dst_file = data_path + plt_file_name + '.' + dtime
        util.CopyFile(src_file, dst_file)

def remove_inventory_calculate_data(plt_b1_item_inventory_obj, oos_file_list):
    for list_file in os.listdir(plt_b1_item_inventory_obj.data_path):
        tag_file = plt_b1_item_inventory_obj.model_base_name + '.tag'
        dat_file = plt_b1_item_inventory_obj.dat_base_name + '.tag'
        
        filter_file = [tag_file, dat_file]
        for file_val in oos_file_list:
            filter_file.append(file_val)

        if list_file not in filter_file:
            prefix = os.path.splitext(list_file)

            try:  
                dt = prefix[1].strip('.') #获取文件日期后缀
                dtime = datetime.now().strftime("%Y%m%d%H%M%S")
                diff = (int)(dtime) - int(dt)
                if diff > 100000: #保留前三个小时的日志
                    os.remove(plt_b1_item_inventory_obj.data_path + list_file) #定时删除多余文件
            except Exception as e:
                pass 

def main(section,task_num):
    global loop_flag
    path = sys.path[0]
    log_path = path + '/log/inventory/'
    data_path = path + '/tagdata/'
    global plt_file_name_prefix
    util.create_path(log_path)
    util.create_path(data_path)
    host_name = socket.gethostname()
    log_file_prefix = "inventory_log_" + host_name
    util.config_logger(log_path, section, log_file_prefix)
    if section == 'gray':
        plt_file_name_prefix = 'gray_' + plt_file_name_prefix
    #regist prometheus metric
    prometheus_cli = prometheus_client.PrometheusClient()
    incr_metric_list = list()
    incr_metric_list.append("oos_p_total")
    incr_metric_list.append("inventory_qps_real_total")
    incr_metric_list.append("inventory_qps_virtual_total")
    incr_metric_list.append("inventory_http_error_total")
    incr_metric_list.append("stock_http_error_total")
    incr_metric_list.append("stock_http_total")
    incr_metric_list.append("stock_model_mismatch")
    prometheus_cli.register_incr(incr_metric_list)

    incr_oos_metric_list = list()
    incr_oos_metric_list.append('oos_p_country')
    prometheus_cli.register_oos_labels(incr_oos_metric_list)
    #同步site_id to warehouse 映射关系， 一天只同步一份文件
    recplt_size_ware = recplt_site_warehouse.SiteWare(data_path) 
    recplt_size_ware.parse_site_warehouse()
    logging.info("start calculate b1 item inventory")
    util.SkuLoad(data_path).load()

    threads = []
    oos_file_list = list()
    #initial inventory task 
    for index in range(task_num):
        oosp_file_name = plt_file_name_prefix + '.' + str(index)
        oos_file_list.append(oosp_file_name)
        t = threading.Thread(target=calculate_inventory_task, args=(data_path,data_queue,oosp_file_name,section,))
        t.start()
        threads.append(t)

    for index in range(task_num):
        t = threading.Thread(target=query_stock_mode_task, args=(data_path,data_queue, query_stock_queue, section,))
        t.start()
        threads.append(t)
    #initial dispatch task
    t = threading.Thread(target=dispatch_task, args=(data_path,query_stock_queue,section,))
    t.start()
    threads.append(t)

    #等待所有线程执行完任务
    for t in threads:
        t.join(timeout=2400)
    
    #聚合oos_p 数据
    logging.info("start calculate oos_p data")
    plt_b1_item_inventory_obj = rec_plt_b1_item_inventory.PltItemDaily(path, section)
    ret = plt_b1_item_inventory_obj.ProcessItemInventory(oos_file_list)

    g_variable = global_variable.GlobalVariable()
    rt, ware_house = g_variable.get_dict_val('rec_ware_house')
    rt, size_sale = g_variable.get_dict_val('rec_size_sale')

    redis_op_obj = redis_op.RedisOperator(section)
    redis_op_obj.connect()
    key = 'rec_plt_oos_p'
    logging.info("redis_hask_key:%s ware_house:%s size_sale:%s", key, ware_house, size_sale)
    dict_val =dict()
    dict_val['rec_ware_house'] = ware_house
    dict_val['rec_size_sale'] = size_sale
    redis_op_obj.hmset(key, dict_val)
    
    backup_inventory_calculate_data(data_path, oos_file_list)

    plt_b1_item_inventory_obj = rec_plt_b1_item_inventory.PltItemDaily(path, section)
    remove_inventory_calculate_data(plt_b1_item_inventory_obj, oos_file_list)

    util.remove_dir_log_file(log_path, 'logger.log')
    logging.info("---end calculate oos_p task---")

if __name__ == '__main__':
    if len(sys.argv) < 2:
        warnings.warn("please input local/dev/prod parameter")
        sys.exit()
    section = sys.argv[1]
    task_num = int(sys.argv[2])
    if task_num <= 0:
        task_num = 4

    main(section, task_num)
