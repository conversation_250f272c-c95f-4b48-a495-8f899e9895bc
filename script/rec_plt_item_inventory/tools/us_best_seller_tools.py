#!/usr/bin/env python
# _*_ coding:utf-8 _*_

# 系统库
import sys
import os
from datetime import datetime, date, timedelta

# 自有库
import recommend_model_pb2


def today():
    return date.today().strftime('%Y%m%d')


def yesterday():
    return (date.today() - timedelta(days=1)).strftime('%Y%m%d')


class USBestSellerTools(object):

    site_uid = 'ioshub'
    local_path = './'
    rec_file_name = 'us_best_seller'
       
    def __init__(self):        
        print "init sucess"
               
    def get_data(self):
        rec_list = recommend_model_pb2.SiteRecListModel()
        l1 = rec_list.site_rec_list.add()
        l1.site_uid = self.site_uid

        idx = 0
        while idx < 1001:
            goods_id = 10000 + idx
            sku_cate_id = 1000 + idx
            g = l1.items.add()
            g.item_id = goods_id
            g.score = 0.125609
            g.cate = sku_cate_id
            idx += 1
            
        return rec_list
    
    def show_data(self,data):
        print(data.site_rec_list[0].site_uid)
        for i in range(20):
            print(
                data.site_rec_list[0].items[i].item_id,
                data.site_rec_list[0].items[i].score,
                data.site_rec_list[0].items[i].cate
            )

    def save_data(self, data):
        serialize_data = data.SerializeToString()

        fn = '{0}.pb.{1}'.format(
            self.rec_file_name,
            datetime.utcnow().strftime('%Y%m%d%H%M%S')
        )
        fn2 = '{0}.pb.tag'.format(self.rec_file_name)

        file_path = os.path.join(self.local_path,fn)

        with open(file_path, 'wb') as f:
            f.write(serialize_data)
        print('create result file {0}'.format(file_path))

        cmd1 = 'md5sum {0} > {1}'.format(fn,fn2)

        print(cmd1)

        os.system(cmd1)

        return 0
    
    def save_2_s3(self):
        data = self.get_data()
        self.save_data(data)   

        
def main():
    ubs = USBestSellerTools()
    ubs.save_2_s3()

    
if __name__ == '__main__':
    main()