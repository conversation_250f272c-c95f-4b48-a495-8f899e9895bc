#!/usr/bin/evn python
# _*_ coding:utf-8 _*_

import os
import sys
import time
import warnings
import logging
import traceback
import threading
import tornado.web
import tornado.httpserver
import tornado.ioloop
from concurrent.futures import ThreadPoolExecutor
from tornado.ioloop import <PERSON><PERSON><PERSON>
from tornado.concurrent import run_on_executor

from src import tag_attribute_update as tag_attr_upd
from src import update_config as upd_conf
from src import util as util
from proto import rec_tag_attribute_pb2 as pb 
import google.protobuf.text_format as text_formats

class HttpServerHandler(tornado.web.RequestHandler):
    executor = ThreadPoolExecutor(1)

    @run_on_executor
    def precess_post_request(self):
        rsp_data = dict()
        try:
            req_json = tornado.escape.json_decode(self.request.body)
            logging.info("req_body:%s", req_json)

            update_config_obj = upd_conf.UpdateConfig()
            rt = update_config_obj.update_tag_attribute_conf(req_json)

            if rt is True:
                rsp_data['code'] = 0
                rsp_data['msg'] = "Successful"
            else:
                rsp_data['code'] = 10401
                rsp_data['msg'] = "Failure"

        except Exception as e:
            rsp_data['code'] = 10400
            rsp_data['msg'] = "parameter invalid"

            logging.info(traceback.format_exc())

        return json.dumps(rsp_data)

    #@tornado.web.asynchronous 
    @tornado.gen.coroutine
    def post(self):
        data = yield self.precess_post_request()
        self.write(data)
        self.finish()

def update_task():
    update_config_obj = upd_conf.UpdateConfig()

    host = util.get_config_param('prod', 'mysql_host')
    user = util.get_config_param('prod', 'mysql_user')
    db = util.get_config_param('prod', 'mysql_database')
    port = util.get_config_param('prod', 'mysql_port')
    password = util.get_config_param('prod', 'mysql_password')
    rec_tag_table_name = util.get_config_param('prod', 'rec_tag')
    #连接db
    tag_attribute_upd_obj = tag_attr_upd.TagAttributeUpdate(db, host, port, user, password, rec_tag_table_name)
    tag_attribute_upd_obj.connect_database()

    while True:
        config_file = update_config_obj.get_config_file()
        rt, config_data_file, md5val = update_config_obj.get_file_info(config_file)
        if rt is True:
            x_tag_attribute =  pb.XTagAttribute()
            with open("/data/done/" + config_data_file, "r") as fr:
                #x_tag_attribute.ParseFromString(fr.read())
                data = fr.read()
                text_formats.Parse(data, x_tag_attribute)

            for tag_attribute in x_tag_attribute.tag_attr_info:
                item_type = tag_attribute.item_type
                tag_file_info = tag_attribute.tag_file_info
                tag_header = tag_attribute.tag_header
                logging.info("item_type:%d tag_file_info:%s tag_header:%s", item_type, tag_file_info, tag_header)
                tag_attribute_upd_obj.parse(tag_file_info, item_type, tag_header)
    
        time.sleep(600)


def main():
    section = 'prod'
    path = sys.path[0]
    log_path = path + '/log/'

    util.create_path(log_path)
    util.config_logger(log_path, section)

    app = tornado.web.Application([(r'/recplt/tag_register', HttpServerHandler)])

    t = threading.Thread(target=update_task, args=())
    t.deamon = True
    t.start()

    server = tornado.httpserver.HTTPServer(app)
    logging.info("start web server")
    server.bind(8080)    
    server.start(1)
    tornado.ioloop.IOLoop.instance().start()

if __name__ == '__main__':
    main()