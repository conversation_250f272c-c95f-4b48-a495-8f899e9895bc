# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto/rec_tag_attribute.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='proto/rec_tag_attribute.proto',
  package='abc.recommend_plt.tag_attribute',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1dproto/rec_tag_attribute.proto\x12\x1f\x61\x62\x63.recommend_plt.tag_attribute\"P\n\x10TagAttributeInfo\x12\x11\n\titem_type\x18\x01 \x01(\x05\x12\x15\n\rtag_file_info\x18\x02 \x01(\t\x12\x12\n\ntag_header\x18\x03 \x01(\t\"Y\n\rXTagAttribute\x12H\n\rtag_attr_info\x18\x01 \x03(\x0b\x32\x31.abc.recommend_plt.tag_attribute.TagAttributeInfob\x06proto3')
)




_TAGATTRIBUTEINFO = _descriptor.Descriptor(
  name='TagAttributeInfo',
  full_name='abc.recommend_plt.tag_attribute.TagAttributeInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='item_type', full_name='abc.recommend_plt.tag_attribute.TagAttributeInfo.item_type', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_file_info', full_name='abc.recommend_plt.tag_attribute.TagAttributeInfo.tag_file_info', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_header', full_name='abc.recommend_plt.tag_attribute.TagAttributeInfo.tag_header', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=66,
  serialized_end=146,
)


_XTAGATTRIBUTE = _descriptor.Descriptor(
  name='XTagAttribute',
  full_name='abc.recommend_plt.tag_attribute.XTagAttribute',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_attr_info', full_name='abc.recommend_plt.tag_attribute.XTagAttribute.tag_attr_info', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=148,
  serialized_end=237,
)

_XTAGATTRIBUTE.fields_by_name['tag_attr_info'].message_type = _TAGATTRIBUTEINFO
DESCRIPTOR.message_types_by_name['TagAttributeInfo'] = _TAGATTRIBUTEINFO
DESCRIPTOR.message_types_by_name['XTagAttribute'] = _XTAGATTRIBUTE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

TagAttributeInfo = _reflection.GeneratedProtocolMessageType('TagAttributeInfo', (_message.Message,), dict(
  DESCRIPTOR = _TAGATTRIBUTEINFO,
  __module__ = 'proto.rec_tag_attribute_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.tag_attribute.TagAttributeInfo)
  ))
_sym_db.RegisterMessage(TagAttributeInfo)

XTagAttribute = _reflection.GeneratedProtocolMessageType('XTagAttribute', (_message.Message,), dict(
  DESCRIPTOR = _XTAGATTRIBUTE,
  __module__ = 'proto.rec_tag_attribute_pb2'
  # @@protoc_insertion_point(class_scope:abc.recommend_plt.tag_attribute.XTagAttribute)
  ))
_sym_db.RegisterMessage(XTagAttribute)


# @@protoc_insertion_point(module_scope)
