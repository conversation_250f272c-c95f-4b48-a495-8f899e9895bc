# _*_ coding:utf-8 _*_
#!/usr/bin/env python

#
# athor: steven
# create : 2019.05.29
#
import logging
import os
import sys
import time
import warnings

from utils import CommonUtils

def main():

    CommonUtils.create_path(CommonUtils.log_path)
    CommonUtils.config_logger(CommonUtils.log_path, CommonUtils.section, 'filebeat.log')

    logging.info("start filebeat tools ....")
    print("start filebeat tools ....")

    if CommonUtils.section == 'dev':
        cmd = 'nohup /usr/local/filebeat/filebeat -e -c /usr/local/filebeat/filebeat-test-info.yml >/dev/null 2>&1  &'
        os.system(cmd)
    else:
        cmd = 'nohup /usr/local/filebeat/filebeat -e -c /usr/local/filebeat/filebeat-master-info.yml >/dev/null 2>&1  &'
        os.system(cmd)

    logging.info("end filebeat tools ....")
    print("end filebeat tools ....")

    while True:
        time.sleep(3600)


if __name__ == '__main__':
    if len(sys.argv) < 2:
        warnings.warn("please input path and dev or prod parameter")
        sys.exit()

    CommonUtils.section = sys.argv[1]

    main()

