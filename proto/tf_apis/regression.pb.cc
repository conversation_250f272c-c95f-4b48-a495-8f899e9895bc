// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/tf_apis/regression.proto

#include "proto/tf_apis/regression.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
extern PROTOBUF_INTERNAL_EXPORT_proto_2ftf_5fapis_2finput_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_Input_proto_2ftf_5fapis_2finput_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2ftf_5fapis_2fmodel_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_ModelSpec_proto_2ftf_5fapis_2fmodel_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2ftf_5fapis_2fregression_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_Regression_proto_2ftf_5fapis_2fregression_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2ftf_5fapis_2fregression_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_RegressionResult_proto_2ftf_5fapis_2fregression_2eproto;
namespace tensorflow {
namespace serving {
class RegressionDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<Regression> _instance;
} _Regression_default_instance_;
class RegressionResultDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RegressionResult> _instance;
} _RegressionResult_default_instance_;
class RegressionRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RegressionRequest> _instance;
} _RegressionRequest_default_instance_;
class RegressionResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RegressionResponse> _instance;
} _RegressionResponse_default_instance_;
}  // namespace serving
}  // namespace tensorflow
static void InitDefaultsscc_info_Regression_proto_2ftf_5fapis_2fregression_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::serving::_Regression_default_instance_;
    new (ptr) ::tensorflow::serving::Regression();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::serving::Regression::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_Regression_proto_2ftf_5fapis_2fregression_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsscc_info_Regression_proto_2ftf_5fapis_2fregression_2eproto}, {}};

static void InitDefaultsscc_info_RegressionRequest_proto_2ftf_5fapis_2fregression_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::serving::_RegressionRequest_default_instance_;
    new (ptr) ::tensorflow::serving::RegressionRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::serving::RegressionRequest::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_RegressionRequest_proto_2ftf_5fapis_2fregression_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsscc_info_RegressionRequest_proto_2ftf_5fapis_2fregression_2eproto}, {
      &scc_info_ModelSpec_proto_2ftf_5fapis_2fmodel_2eproto.base,
      &scc_info_Input_proto_2ftf_5fapis_2finput_2eproto.base,}};

static void InitDefaultsscc_info_RegressionResponse_proto_2ftf_5fapis_2fregression_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::serving::_RegressionResponse_default_instance_;
    new (ptr) ::tensorflow::serving::RegressionResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::serving::RegressionResponse::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_RegressionResponse_proto_2ftf_5fapis_2fregression_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsscc_info_RegressionResponse_proto_2ftf_5fapis_2fregression_2eproto}, {
      &scc_info_ModelSpec_proto_2ftf_5fapis_2fmodel_2eproto.base,
      &scc_info_RegressionResult_proto_2ftf_5fapis_2fregression_2eproto.base,}};

static void InitDefaultsscc_info_RegressionResult_proto_2ftf_5fapis_2fregression_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::serving::_RegressionResult_default_instance_;
    new (ptr) ::tensorflow::serving::RegressionResult();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::serving::RegressionResult::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_RegressionResult_proto_2ftf_5fapis_2fregression_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsscc_info_RegressionResult_proto_2ftf_5fapis_2fregression_2eproto}, {
      &scc_info_Regression_proto_2ftf_5fapis_2fregression_2eproto.base,}};

static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_proto_2ftf_5fapis_2fregression_2eproto[4];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_proto_2ftf_5fapis_2fregression_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_proto_2ftf_5fapis_2fregression_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_proto_2ftf_5fapis_2fregression_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::tensorflow::serving::Regression, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::tensorflow::serving::Regression, value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::tensorflow::serving::RegressionResult, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::tensorflow::serving::RegressionResult, regressions_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::tensorflow::serving::RegressionRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::tensorflow::serving::RegressionRequest, model_spec_),
  PROTOBUF_FIELD_OFFSET(::tensorflow::serving::RegressionRequest, input_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::tensorflow::serving::RegressionResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::tensorflow::serving::RegressionResponse, model_spec_),
  PROTOBUF_FIELD_OFFSET(::tensorflow::serving::RegressionResponse, result_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::serving::Regression)},
  { 6, -1, sizeof(::tensorflow::serving::RegressionResult)},
  { 12, -1, sizeof(::tensorflow::serving::RegressionRequest)},
  { 19, -1, sizeof(::tensorflow::serving::RegressionResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::tensorflow::serving::_Regression_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::tensorflow::serving::_RegressionResult_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::tensorflow::serving::_RegressionRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::tensorflow::serving::_RegressionResponse_default_instance_),
};

const char descriptor_table_protodef_proto_2ftf_5fapis_2fregression_2eproto[] =
  "\n\036proto/tf_apis/regression.proto\022\022tensor"
  "flow.serving\032\031proto/tf_apis/input.proto\032"
  "\031proto/tf_apis/model.proto\"\033\n\nRegression"
  "\022\r\n\005value\030\001 \001(\002\"G\n\020RegressionResult\0223\n\013r"
  "egressions\030\001 \003(\0132\036.tensorflow.serving.Re"
  "gression\"p\n\021RegressionRequest\0221\n\nmodel_s"
  "pec\030\001 \001(\0132\035.tensorflow.serving.ModelSpec"
  "\022(\n\005input\030\002 \001(\0132\031.tensorflow.serving.Inp"
  "ut\"}\n\022RegressionResponse\0221\n\nmodel_spec\030\002"
  " \001(\0132\035.tensorflow.serving.ModelSpec\0224\n\006r"
  "esult\030\001 \001(\0132$.tensorflow.serving.Regress"
  "ionResultB\003\370\001\001b\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_proto_2ftf_5fapis_2fregression_2eproto_deps[2] = {
  &::descriptor_table_proto_2ftf_5fapis_2finput_2eproto,
  &::descriptor_table_proto_2ftf_5fapis_2fmodel_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_proto_2ftf_5fapis_2fregression_2eproto_sccs[4] = {
  &scc_info_Regression_proto_2ftf_5fapis_2fregression_2eproto.base,
  &scc_info_RegressionRequest_proto_2ftf_5fapis_2fregression_2eproto.base,
  &scc_info_RegressionResponse_proto_2ftf_5fapis_2fregression_2eproto.base,
  &scc_info_RegressionResult_proto_2ftf_5fapis_2fregression_2eproto.base,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_proto_2ftf_5fapis_2fregression_2eproto_once;
static bool descriptor_table_proto_2ftf_5fapis_2fregression_2eproto_initialized = false;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2ftf_5fapis_2fregression_2eproto = {
  &descriptor_table_proto_2ftf_5fapis_2fregression_2eproto_initialized, descriptor_table_protodef_proto_2ftf_5fapis_2fregression_2eproto, "proto/tf_apis/regression.proto", 462,
  &descriptor_table_proto_2ftf_5fapis_2fregression_2eproto_once, descriptor_table_proto_2ftf_5fapis_2fregression_2eproto_sccs, descriptor_table_proto_2ftf_5fapis_2fregression_2eproto_deps, 4, 2,
  schemas, file_default_instances, TableStruct_proto_2ftf_5fapis_2fregression_2eproto::offsets,
  file_level_metadata_proto_2ftf_5fapis_2fregression_2eproto, 4, file_level_enum_descriptors_proto_2ftf_5fapis_2fregression_2eproto, file_level_service_descriptors_proto_2ftf_5fapis_2fregression_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_proto_2ftf_5fapis_2fregression_2eproto = (  ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_proto_2ftf_5fapis_2fregression_2eproto), true);
namespace tensorflow {
namespace serving {

// ===================================================================

void Regression::InitAsDefaultInstance() {
}
class Regression::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Regression::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Regression::Regression()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.serving.Regression)
}
Regression::Regression(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
  _internal_metadata_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.serving.Regression)
}
Regression::Regression(const Regression& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  value_ = from.value_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.serving.Regression)
}

void Regression::SharedCtor() {
  value_ = 0;
}

Regression::~Regression() {
  // @@protoc_insertion_point(destructor:tensorflow.serving.Regression)
  SharedDtor();
}

void Regression::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == nullptr);
}

void Regression::ArenaDtor(void* object) {
  Regression* _this = reinterpret_cast< Regression* >(object);
  (void)_this;
}
void Regression::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Regression::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const Regression& Regression::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_Regression_proto_2ftf_5fapis_2fregression_2eproto.base);
  return *internal_default_instance();
}


void Regression::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.serving.Regression)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  value_ = 0;
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* Regression::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArenaNoVirtual(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // float value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          value_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool Regression::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.serving.Regression)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // float value = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (13 & 0xFF)) {

          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   float, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &value_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.serving.Regression)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.serving.Regression)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void Regression::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.serving.Regression)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float value = 1;
  if (!(this->value() <= 0 && this->value() >= 0)) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloat(1, this->value(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.serving.Regression)
}

::PROTOBUF_NAMESPACE_ID::uint8* Regression::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.serving.Regression)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float value = 1;
  if (!(this->value() <= 0 && this->value() >= 0)) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->value(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.serving.Regression)
  return target;
}

size_t Regression::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.serving.Regression)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float value = 1;
  if (!(this->value() <= 0 && this->value() >= 0)) {
    total_size += 1 + 4;
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Regression::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.serving.Regression)
  GOOGLE_DCHECK_NE(&from, this);
  const Regression* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<Regression>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.serving.Regression)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.serving.Regression)
    MergeFrom(*source);
  }
}

void Regression::MergeFrom(const Regression& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.serving.Regression)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from.value() <= 0 && from.value() >= 0)) {
    set_value(from.value());
  }
}

void Regression::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.serving.Regression)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Regression::CopyFrom(const Regression& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.serving.Regression)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Regression::IsInitialized() const {
  return true;
}

void Regression::Swap(Regression* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    Regression* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == nullptr) {
      delete temp;
    }
  }
}
void Regression::UnsafeArenaSwap(Regression* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void Regression::InternalSwap(Regression* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(value_, other->value_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Regression::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void RegressionResult::InitAsDefaultInstance() {
}
class RegressionResult::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RegressionResult::kRegressionsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RegressionResult::RegressionResult()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.serving.RegressionResult)
}
RegressionResult::RegressionResult(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
  _internal_metadata_(arena),
  regressions_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.serving.RegressionResult)
}
RegressionResult::RegressionResult(const RegressionResult& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      regressions_(from.regressions_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.serving.RegressionResult)
}

void RegressionResult::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RegressionResult_proto_2ftf_5fapis_2fregression_2eproto.base);
}

RegressionResult::~RegressionResult() {
  // @@protoc_insertion_point(destructor:tensorflow.serving.RegressionResult)
  SharedDtor();
}

void RegressionResult::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == nullptr);
}

void RegressionResult::ArenaDtor(void* object) {
  RegressionResult* _this = reinterpret_cast< RegressionResult* >(object);
  (void)_this;
}
void RegressionResult::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RegressionResult::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RegressionResult& RegressionResult::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RegressionResult_proto_2ftf_5fapis_2fregression_2eproto.base);
  return *internal_default_instance();
}


void RegressionResult::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.serving.RegressionResult)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  regressions_.Clear();
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* RegressionResult::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArenaNoVirtual(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .tensorflow.serving.Regression regressions = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(add_regressions(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 10);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool RegressionResult::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.serving.RegressionResult)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.serving.Regression regressions = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
                input, add_regressions()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.serving.RegressionResult)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.serving.RegressionResult)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void RegressionResult::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.serving.RegressionResult)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.serving.Regression regressions = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->regressions_size()); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->regressions(static_cast<int>(i)),
      output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.serving.RegressionResult)
}

::PROTOBUF_NAMESPACE_ID::uint8* RegressionResult::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.serving.RegressionResult)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.serving.Regression regressions = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->regressions_size()); i < n; i++) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->regressions(static_cast<int>(i)), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.serving.RegressionResult)
  return target;
}

size_t RegressionResult::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.serving.RegressionResult)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .tensorflow.serving.Regression regressions = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->regressions_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          this->regressions(static_cast<int>(i)));
    }
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RegressionResult::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.serving.RegressionResult)
  GOOGLE_DCHECK_NE(&from, this);
  const RegressionResult* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RegressionResult>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.serving.RegressionResult)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.serving.RegressionResult)
    MergeFrom(*source);
  }
}

void RegressionResult::MergeFrom(const RegressionResult& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.serving.RegressionResult)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  regressions_.MergeFrom(from.regressions_);
}

void RegressionResult::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.serving.RegressionResult)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RegressionResult::CopyFrom(const RegressionResult& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.serving.RegressionResult)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RegressionResult::IsInitialized() const {
  return true;
}

void RegressionResult::Swap(RegressionResult* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    RegressionResult* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == nullptr) {
      delete temp;
    }
  }
}
void RegressionResult::UnsafeArenaSwap(RegressionResult* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void RegressionResult::InternalSwap(RegressionResult* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  CastToBase(&regressions_)->InternalSwap(CastToBase(&other->regressions_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RegressionResult::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void RegressionRequest::InitAsDefaultInstance() {
  ::tensorflow::serving::_RegressionRequest_default_instance_._instance.get_mutable()->model_spec_ = const_cast< ::tensorflow::serving::ModelSpec*>(
      ::tensorflow::serving::ModelSpec::internal_default_instance());
  ::tensorflow::serving::_RegressionRequest_default_instance_._instance.get_mutable()->input_ = const_cast< ::tensorflow::serving::Input*>(
      ::tensorflow::serving::Input::internal_default_instance());
}
class RegressionRequest::HasBitSetters {
 public:
  static const ::tensorflow::serving::ModelSpec& model_spec(const RegressionRequest* msg);
  static const ::tensorflow::serving::Input& input(const RegressionRequest* msg);
};

const ::tensorflow::serving::ModelSpec&
RegressionRequest::HasBitSetters::model_spec(const RegressionRequest* msg) {
  return *msg->model_spec_;
}
const ::tensorflow::serving::Input&
RegressionRequest::HasBitSetters::input(const RegressionRequest* msg) {
  return *msg->input_;
}
void RegressionRequest::unsafe_arena_set_allocated_model_spec(
    ::tensorflow::serving::ModelSpec* model_spec) {
  if (GetArenaNoVirtual() == nullptr) {
    delete model_spec_;
  }
  model_spec_ = model_spec;
  if (model_spec) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.serving.RegressionRequest.model_spec)
}
void RegressionRequest::clear_model_spec() {
  if (GetArenaNoVirtual() == nullptr && model_spec_ != nullptr) {
    delete model_spec_;
  }
  model_spec_ = nullptr;
}
void RegressionRequest::unsafe_arena_set_allocated_input(
    ::tensorflow::serving::Input* input) {
  if (GetArenaNoVirtual() == nullptr) {
    delete input_;
  }
  input_ = input;
  if (input) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.serving.RegressionRequest.input)
}
void RegressionRequest::clear_input() {
  if (GetArenaNoVirtual() == nullptr && input_ != nullptr) {
    delete input_;
  }
  input_ = nullptr;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RegressionRequest::kModelSpecFieldNumber;
const int RegressionRequest::kInputFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RegressionRequest::RegressionRequest()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.serving.RegressionRequest)
}
RegressionRequest::RegressionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
  _internal_metadata_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.serving.RegressionRequest)
}
RegressionRequest::RegressionRequest(const RegressionRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_model_spec()) {
    model_spec_ = new ::tensorflow::serving::ModelSpec(*from.model_spec_);
  } else {
    model_spec_ = nullptr;
  }
  if (from.has_input()) {
    input_ = new ::tensorflow::serving::Input(*from.input_);
  } else {
    input_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.serving.RegressionRequest)
}

void RegressionRequest::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RegressionRequest_proto_2ftf_5fapis_2fregression_2eproto.base);
  ::memset(&model_spec_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&input_) -
      reinterpret_cast<char*>(&model_spec_)) + sizeof(input_));
}

RegressionRequest::~RegressionRequest() {
  // @@protoc_insertion_point(destructor:tensorflow.serving.RegressionRequest)
  SharedDtor();
}

void RegressionRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == nullptr);
  if (this != internal_default_instance()) delete model_spec_;
  if (this != internal_default_instance()) delete input_;
}

void RegressionRequest::ArenaDtor(void* object) {
  RegressionRequest* _this = reinterpret_cast< RegressionRequest* >(object);
  (void)_this;
}
void RegressionRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RegressionRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RegressionRequest& RegressionRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RegressionRequest_proto_2ftf_5fapis_2fregression_2eproto.base);
  return *internal_default_instance();
}


void RegressionRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.serving.RegressionRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == nullptr && model_spec_ != nullptr) {
    delete model_spec_;
  }
  model_spec_ = nullptr;
  if (GetArenaNoVirtual() == nullptr && input_ != nullptr) {
    delete input_;
  }
  input_ = nullptr;
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* RegressionRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArenaNoVirtual(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .tensorflow.serving.ModelSpec model_spec = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(mutable_model_spec(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .tensorflow.serving.Input input = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(mutable_input(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool RegressionRequest::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.serving.RegressionRequest)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.serving.ModelSpec model_spec = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_model_spec()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.serving.Input input = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_input()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.serving.RegressionRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.serving.RegressionRequest)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void RegressionRequest::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.serving.RegressionRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.serving.ModelSpec model_spec = 1;
  if (this->has_model_spec()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, HasBitSetters::model_spec(this), output);
  }

  // .tensorflow.serving.Input input = 2;
  if (this->has_input()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, HasBitSetters::input(this), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.serving.RegressionRequest)
}

::PROTOBUF_NAMESPACE_ID::uint8* RegressionRequest::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.serving.RegressionRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.serving.ModelSpec model_spec = 1;
  if (this->has_model_spec()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, HasBitSetters::model_spec(this), target);
  }

  // .tensorflow.serving.Input input = 2;
  if (this->has_input()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, HasBitSetters::input(this), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.serving.RegressionRequest)
  return target;
}

size_t RegressionRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.serving.RegressionRequest)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .tensorflow.serving.ModelSpec model_spec = 1;
  if (this->has_model_spec()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *model_spec_);
  }

  // .tensorflow.serving.Input input = 2;
  if (this->has_input()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *input_);
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RegressionRequest::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.serving.RegressionRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const RegressionRequest* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RegressionRequest>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.serving.RegressionRequest)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.serving.RegressionRequest)
    MergeFrom(*source);
  }
}

void RegressionRequest::MergeFrom(const RegressionRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.serving.RegressionRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_model_spec()) {
    mutable_model_spec()->::tensorflow::serving::ModelSpec::MergeFrom(from.model_spec());
  }
  if (from.has_input()) {
    mutable_input()->::tensorflow::serving::Input::MergeFrom(from.input());
  }
}

void RegressionRequest::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.serving.RegressionRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RegressionRequest::CopyFrom(const RegressionRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.serving.RegressionRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RegressionRequest::IsInitialized() const {
  return true;
}

void RegressionRequest::Swap(RegressionRequest* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    RegressionRequest* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == nullptr) {
      delete temp;
    }
  }
}
void RegressionRequest::UnsafeArenaSwap(RegressionRequest* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void RegressionRequest::InternalSwap(RegressionRequest* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(model_spec_, other->model_spec_);
  swap(input_, other->input_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RegressionRequest::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void RegressionResponse::InitAsDefaultInstance() {
  ::tensorflow::serving::_RegressionResponse_default_instance_._instance.get_mutable()->model_spec_ = const_cast< ::tensorflow::serving::ModelSpec*>(
      ::tensorflow::serving::ModelSpec::internal_default_instance());
  ::tensorflow::serving::_RegressionResponse_default_instance_._instance.get_mutable()->result_ = const_cast< ::tensorflow::serving::RegressionResult*>(
      ::tensorflow::serving::RegressionResult::internal_default_instance());
}
class RegressionResponse::HasBitSetters {
 public:
  static const ::tensorflow::serving::ModelSpec& model_spec(const RegressionResponse* msg);
  static const ::tensorflow::serving::RegressionResult& result(const RegressionResponse* msg);
};

const ::tensorflow::serving::ModelSpec&
RegressionResponse::HasBitSetters::model_spec(const RegressionResponse* msg) {
  return *msg->model_spec_;
}
const ::tensorflow::serving::RegressionResult&
RegressionResponse::HasBitSetters::result(const RegressionResponse* msg) {
  return *msg->result_;
}
void RegressionResponse::unsafe_arena_set_allocated_model_spec(
    ::tensorflow::serving::ModelSpec* model_spec) {
  if (GetArenaNoVirtual() == nullptr) {
    delete model_spec_;
  }
  model_spec_ = model_spec;
  if (model_spec) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.serving.RegressionResponse.model_spec)
}
void RegressionResponse::clear_model_spec() {
  if (GetArenaNoVirtual() == nullptr && model_spec_ != nullptr) {
    delete model_spec_;
  }
  model_spec_ = nullptr;
}
void RegressionResponse::unsafe_arena_set_allocated_result(
    ::tensorflow::serving::RegressionResult* result) {
  if (GetArenaNoVirtual() == nullptr) {
    delete result_;
  }
  result_ = result;
  if (result) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.serving.RegressionResponse.result)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RegressionResponse::kModelSpecFieldNumber;
const int RegressionResponse::kResultFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RegressionResponse::RegressionResponse()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.serving.RegressionResponse)
}
RegressionResponse::RegressionResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
  _internal_metadata_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.serving.RegressionResponse)
}
RegressionResponse::RegressionResponse(const RegressionResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_result()) {
    result_ = new ::tensorflow::serving::RegressionResult(*from.result_);
  } else {
    result_ = nullptr;
  }
  if (from.has_model_spec()) {
    model_spec_ = new ::tensorflow::serving::ModelSpec(*from.model_spec_);
  } else {
    model_spec_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.serving.RegressionResponse)
}

void RegressionResponse::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RegressionResponse_proto_2ftf_5fapis_2fregression_2eproto.base);
  ::memset(&result_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&model_spec_) -
      reinterpret_cast<char*>(&result_)) + sizeof(model_spec_));
}

RegressionResponse::~RegressionResponse() {
  // @@protoc_insertion_point(destructor:tensorflow.serving.RegressionResponse)
  SharedDtor();
}

void RegressionResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == nullptr);
  if (this != internal_default_instance()) delete result_;
  if (this != internal_default_instance()) delete model_spec_;
}

void RegressionResponse::ArenaDtor(void* object) {
  RegressionResponse* _this = reinterpret_cast< RegressionResponse* >(object);
  (void)_this;
}
void RegressionResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RegressionResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RegressionResponse& RegressionResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RegressionResponse_proto_2ftf_5fapis_2fregression_2eproto.base);
  return *internal_default_instance();
}


void RegressionResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.serving.RegressionResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == nullptr && result_ != nullptr) {
    delete result_;
  }
  result_ = nullptr;
  if (GetArenaNoVirtual() == nullptr && model_spec_ != nullptr) {
    delete model_spec_;
  }
  model_spec_ = nullptr;
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* RegressionResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArenaNoVirtual(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .tensorflow.serving.RegressionResult result = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(mutable_result(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .tensorflow.serving.ModelSpec model_spec = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(mutable_model_spec(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool RegressionResponse::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.serving.RegressionResponse)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.serving.RegressionResult result = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_result()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.serving.ModelSpec model_spec = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_model_spec()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.serving.RegressionResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.serving.RegressionResponse)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void RegressionResponse::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.serving.RegressionResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.serving.RegressionResult result = 1;
  if (this->has_result()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, HasBitSetters::result(this), output);
  }

  // .tensorflow.serving.ModelSpec model_spec = 2;
  if (this->has_model_spec()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, HasBitSetters::model_spec(this), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.serving.RegressionResponse)
}

::PROTOBUF_NAMESPACE_ID::uint8* RegressionResponse::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.serving.RegressionResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.serving.RegressionResult result = 1;
  if (this->has_result()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, HasBitSetters::result(this), target);
  }

  // .tensorflow.serving.ModelSpec model_spec = 2;
  if (this->has_model_spec()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, HasBitSetters::model_spec(this), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.serving.RegressionResponse)
  return target;
}

size_t RegressionResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.serving.RegressionResponse)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .tensorflow.serving.RegressionResult result = 1;
  if (this->has_result()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *result_);
  }

  // .tensorflow.serving.ModelSpec model_spec = 2;
  if (this->has_model_spec()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *model_spec_);
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RegressionResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.serving.RegressionResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const RegressionResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RegressionResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.serving.RegressionResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.serving.RegressionResponse)
    MergeFrom(*source);
  }
}

void RegressionResponse::MergeFrom(const RegressionResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.serving.RegressionResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_result()) {
    mutable_result()->::tensorflow::serving::RegressionResult::MergeFrom(from.result());
  }
  if (from.has_model_spec()) {
    mutable_model_spec()->::tensorflow::serving::ModelSpec::MergeFrom(from.model_spec());
  }
}

void RegressionResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.serving.RegressionResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RegressionResponse::CopyFrom(const RegressionResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.serving.RegressionResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RegressionResponse::IsInitialized() const {
  return true;
}

void RegressionResponse::Swap(RegressionResponse* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    RegressionResponse* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == nullptr) {
      delete temp;
    }
  }
}
void RegressionResponse::UnsafeArenaSwap(RegressionResponse* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void RegressionResponse::InternalSwap(RegressionResponse* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(result_, other->result_);
  swap(model_spec_, other->model_spec_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RegressionResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace serving
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::tensorflow::serving::Regression* Arena::CreateMaybeMessage< ::tensorflow::serving::Regression >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::serving::Regression >(arena);
}
template<> PROTOBUF_NOINLINE ::tensorflow::serving::RegressionResult* Arena::CreateMaybeMessage< ::tensorflow::serving::RegressionResult >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::serving::RegressionResult >(arena);
}
template<> PROTOBUF_NOINLINE ::tensorflow::serving::RegressionRequest* Arena::CreateMaybeMessage< ::tensorflow::serving::RegressionRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::serving::RegressionRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::tensorflow::serving::RegressionResponse* Arena::CreateMaybeMessage< ::tensorflow::serving::RegressionResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::serving::RegressionResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
