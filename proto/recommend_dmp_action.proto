syntax = "proto3";
package abc.dmp.service.filter;
option java_package = "com.shein.abc.dmp.service";
option cc_enable_arenas = true;
import "proto/recommend_feature.proto";

message UserKey {
    string member_id = 1;
    string device_id = 2;
    string cookie_id = 3;
    string ugid = 4;
}

message ActionFilter {
    repeated int32 goods_ids = 1; //需要保留的商品list（粗排结果）
    map<string, recommend_plt.fmp.Feature> context_feature= 2; //需要保留的上下文末级类目或前台类目
    repeated int32 hkeys = 3; //预处理特征 hkey 10000-20000
}

message UserProfileRequest {
    int32 site_id = 1; //品牌站点 7-shein 9-romwe
    UserKey user_key = 2; //用户信息
    ActionFilter action_filter = 3; //过滤配置
    string source = 4; //请求来源
}

message ActionResponse {
    map<string, recommend_plt.fmp.Feature> result = 1;
    enum ResponseStatus {
        SUCCESS = 0;//成功
        PARAMETER_EMPTY = 1;//参数为空
        ERROR = 2;//内部错误
        NOT_FOUND = 3;  // 找不到用户信息
        REDIS_TIMEOUT =4; //redis 超时
    }
    ResponseStatus status = 2;
}


// rpc 服务
service UserProfileFilterService {
    rpc GetUserActionList(UserProfileRequest) returns(ActionResponse) {}
}
