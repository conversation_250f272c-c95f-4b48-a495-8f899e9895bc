// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: proto/recommend_tdp.proto

#include "proto/recommend_tdp.pb.h"
#include "proto/recommend_tdp.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace abc {
namespace recommend_plt {
namespace tdp {

static const char* RecommendService_method_names[] = {
  "/abc.recommend_plt.tdp.RecommendService/doRecommend",
};

std::unique_ptr< RecommendService::Stub> RecommendService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< RecommendService::Stub> stub(new RecommendService::Stub(channel));
  return stub;
}

RecommendService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel)
  : channel_(channel), rpcmethod_doRecommend_(RecommendService_method_names[0], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status RecommendService::Stub::doRecommend(::grpc::ClientContext* context, const ::abc::recommend_plt::tdp::RecommendRequest& request, ::abc::recommend_plt::tdp::RecommendResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_doRecommend_, context, request, response);
}

void RecommendService::Stub::experimental_async::doRecommend(::grpc::ClientContext* context, const ::abc::recommend_plt::tdp::RecommendRequest* request, ::abc::recommend_plt::tdp::RecommendResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_doRecommend_, context, request, response, std::move(f));
}

void RecommendService::Stub::experimental_async::doRecommend(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::abc::recommend_plt::tdp::RecommendResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_doRecommend_, context, request, response, std::move(f));
}

void RecommendService::Stub::experimental_async::doRecommend(::grpc::ClientContext* context, const ::abc::recommend_plt::tdp::RecommendRequest* request, ::abc::recommend_plt::tdp::RecommendResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_doRecommend_, context, request, response, reactor);
}

void RecommendService::Stub::experimental_async::doRecommend(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::abc::recommend_plt::tdp::RecommendResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_doRecommend_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::abc::recommend_plt::tdp::RecommendResponse>* RecommendService::Stub::AsyncdoRecommendRaw(::grpc::ClientContext* context, const ::abc::recommend_plt::tdp::RecommendRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::abc::recommend_plt::tdp::RecommendResponse>::Create(channel_.get(), cq, rpcmethod_doRecommend_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::abc::recommend_plt::tdp::RecommendResponse>* RecommendService::Stub::PrepareAsyncdoRecommendRaw(::grpc::ClientContext* context, const ::abc::recommend_plt::tdp::RecommendRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::abc::recommend_plt::tdp::RecommendResponse>::Create(channel_.get(), cq, rpcmethod_doRecommend_, context, request, false);
}

RecommendService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RecommendService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< RecommendService::Service, ::abc::recommend_plt::tdp::RecommendRequest, ::abc::recommend_plt::tdp::RecommendResponse>(
          [](RecommendService::Service* service,
             ::grpc_impl::ServerContext* ctx,
             const ::abc::recommend_plt::tdp::RecommendRequest* req,
             ::abc::recommend_plt::tdp::RecommendResponse* resp) {
               return service->doRecommend(ctx, req, resp);
             }, this)));
}

RecommendService::Service::~Service() {
}

::grpc::Status RecommendService::Service::doRecommend(::grpc::ServerContext* context, const ::abc::recommend_plt::tdp::RecommendRequest* request, ::abc::recommend_plt::tdp::RecommendResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace abc
}  // namespace recommend_plt
}  // namespace tdp

