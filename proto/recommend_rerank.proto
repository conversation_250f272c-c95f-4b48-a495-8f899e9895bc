syntax = "proto3";
package abc.recommend_plt.rerank;

import "proto/recommend_api.proto";
import "proto/recommend_rank.proto";
import "proto/recommend_match.proto";

option cc_enable_arenas = true;

message ReRankResponse {
    abc.recommend_plt.api.RecommendResponse recommend_response = 1;  // 返回推荐请求
    abc.recommend_plt.rank.RankRequest rank_request = 2; //推荐多链路
}

// rpc 重排服务
service ReRanker {
    rpc DoReRank(abc.recommend_plt.rank.RankRequest) returns(ReRankResponse) {}
}
