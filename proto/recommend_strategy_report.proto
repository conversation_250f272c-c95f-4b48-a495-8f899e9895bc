syntax = "proto3";
package abc.recommend_plt.strategy;

message RecStrategyReport {
    message RecStrategyFea {
        int32 count = 1;
        string scene_id = 2;
        string item_source = 3;
        string step_id = 4;
        string strategy_id = 5;
        string item_fea = 6;
        int32 user_fea = 7;
    }
    repeated RecStrategyFea rec_strategy_fea = 1;

    // 策略维度指标上报
    message RecStrategyMetric {
        // 固定的上报维度
        string scene_id = 1;
        string item_source = 2;
        string step_id = 3;
        string metric_name = 4; // 指标维度 如P99,覆盖率
        string label = 5; // 可自定义的统计维度,比如策略ID
        int64 count = 6; // 计数
        int64 sum = 7; // 加和
        string service_name = 8;
        int64 ts = 9; // 推送到kafka的时间戳, 毫秒
        string poskey = 10;
   }
   repeated RecStrategyMetric rec_strategy_metric = 2;

   // 变更上报
   message RecServiceChange {
      string change_time = 1; // 变更的时间 2024-03-26 19:15:20
      string service = 2; // 上报变更的服务
      int32 change_type = 3; // 变更类型 服务版本更新:1, 策略配置更新:2,
      string before_change = 4; // 变更前关键信息
      string after_change = 5; //  变更后关键信息
      string change_detail = 6; // 其他变更详情
   }
}
