syntax = "proto3";
package abc.recommend_plt.process;

message KafkaInfo {
    string broker = 1;
    string topic = 2;
    string group_id = 3;
    bool is_sasl = 4;
    string kafka_auth_addr = 5;
    string username = 6;
    string token = 7;
    string kafka_cluster_name = 8;
}

message ProcessAttributeInfo {
    string service_name = 1;
    KafkaInfo kafka_info = 2;
    string fields = 3;    //fields format: fields1,fields2
    string partition_fields = 4; //partition_field format: partition_field1,partition_field2
    string multi_val_fields = 5; //record multiple-value field  format: field1,field2
}

message RecProcessConfig {
    repeated ProcessAttributeInfo process_attr_info = 1;
}