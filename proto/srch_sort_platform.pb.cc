// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/srch_sort_platform.proto

#include "proto/srch_sort_platform.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace srchRoute {
namespace goods_sort {
constexpr PbSortResponse::PbSortResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : err_msg_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , result_info_(nullptr)
  , code_(0){}
struct PbSortResponseDefaultTypeInternal {
  constexpr PbSortResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PbSortResponseDefaultTypeInternal() {}
  union {
    PbSortResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PbSortResponseDefaultTypeInternal _PbSortResponse_default_instance_;
constexpr PbSortRequest_UserInfoEntry_DoNotUse::PbSortRequest_UserInfoEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct PbSortRequest_UserInfoEntry_DoNotUseDefaultTypeInternal {
  constexpr PbSortRequest_UserInfoEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PbSortRequest_UserInfoEntry_DoNotUseDefaultTypeInternal() {}
  union {
    PbSortRequest_UserInfoEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PbSortRequest_UserInfoEntry_DoNotUseDefaultTypeInternal _PbSortRequest_UserInfoEntry_DoNotUse_default_instance_;
constexpr PbSortRequest_ContextEntry_DoNotUse::PbSortRequest_ContextEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct PbSortRequest_ContextEntry_DoNotUseDefaultTypeInternal {
  constexpr PbSortRequest_ContextEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PbSortRequest_ContextEntry_DoNotUseDefaultTypeInternal() {}
  union {
    PbSortRequest_ContextEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PbSortRequest_ContextEntry_DoNotUseDefaultTypeInternal _PbSortRequest_ContextEntry_DoNotUse_default_instance_;
constexpr PbSortRequest_RecallChannelsMetasEntry_DoNotUse::PbSortRequest_RecallChannelsMetasEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct PbSortRequest_RecallChannelsMetasEntry_DoNotUseDefaultTypeInternal {
  constexpr PbSortRequest_RecallChannelsMetasEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PbSortRequest_RecallChannelsMetasEntry_DoNotUseDefaultTypeInternal() {}
  union {
    PbSortRequest_RecallChannelsMetasEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PbSortRequest_RecallChannelsMetasEntry_DoNotUseDefaultTypeInternal _PbSortRequest_RecallChannelsMetasEntry_DoNotUse_default_instance_;
constexpr PbSortRequest_JsonRuleIdEntry_DoNotUse::PbSortRequest_JsonRuleIdEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct PbSortRequest_JsonRuleIdEntry_DoNotUseDefaultTypeInternal {
  constexpr PbSortRequest_JsonRuleIdEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PbSortRequest_JsonRuleIdEntry_DoNotUseDefaultTypeInternal() {}
  union {
    PbSortRequest_JsonRuleIdEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PbSortRequest_JsonRuleIdEntry_DoNotUseDefaultTypeInternal _PbSortRequest_JsonRuleIdEntry_DoNotUse_default_instance_;
constexpr PbSortRequest::PbSortRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : user_info_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , context_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , adp_goods_()
  , recall_goods_()
  , recall_channel_goods_tag_()
  , recall_channels_metas_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , json_rule_id_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , limit_(0){}
struct PbSortRequestDefaultTypeInternal {
  constexpr PbSortRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PbSortRequestDefaultTypeInternal() {}
  union {
    PbSortRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PbSortRequestDefaultTypeInternal _PbSortRequest_default_instance_;
constexpr ChannelMetas_ChannelMetasEntry_DoNotUse::ChannelMetas_ChannelMetasEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct ChannelMetas_ChannelMetasEntry_DoNotUseDefaultTypeInternal {
  constexpr ChannelMetas_ChannelMetasEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ChannelMetas_ChannelMetasEntry_DoNotUseDefaultTypeInternal() {}
  union {
    ChannelMetas_ChannelMetasEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ChannelMetas_ChannelMetasEntry_DoNotUseDefaultTypeInternal _ChannelMetas_ChannelMetasEntry_DoNotUse_default_instance_;
constexpr ChannelMetas::ChannelMetas(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : channel_metas_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}){}
struct ChannelMetasDefaultTypeInternal {
  constexpr ChannelMetasDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ChannelMetasDefaultTypeInternal() {}
  union {
    ChannelMetas _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ChannelMetasDefaultTypeInternal _ChannelMetas_default_instance_;
constexpr ReqGoods_ExtEntry_DoNotUse::ReqGoods_ExtEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct ReqGoods_ExtEntry_DoNotUseDefaultTypeInternal {
  constexpr ReqGoods_ExtEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ReqGoods_ExtEntry_DoNotUseDefaultTypeInternal() {}
  union {
    ReqGoods_ExtEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ReqGoods_ExtEntry_DoNotUseDefaultTypeInternal _ReqGoods_ExtEntry_DoNotUse_default_instance_;
constexpr ReqGoods::ReqGoods(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ext_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , tags_()
  , recall_channels_()
  , goods_tag_value_()
  , spu_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , goods_id_(uint64_t{0u})
  , score_(0)
  , priority_(0){}
struct ReqGoodsDefaultTypeInternal {
  constexpr ReqGoodsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ReqGoodsDefaultTypeInternal() {}
  union {
    ReqGoods _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ReqGoodsDefaultTypeInternal _ReqGoods_default_instance_;
constexpr GoodsTag::GoodsTag(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : tag_value_()
  , _tag_value_cached_byte_size_(0){}
struct GoodsTagDefaultTypeInternal {
  constexpr GoodsTagDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GoodsTagDefaultTypeInternal() {}
  union {
    GoodsTag _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GoodsTagDefaultTypeInternal _GoodsTag_default_instance_;
constexpr ResultInfo_MarksEntry_DoNotUse::ResultInfo_MarksEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct ResultInfo_MarksEntry_DoNotUseDefaultTypeInternal {
  constexpr ResultInfo_MarksEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ResultInfo_MarksEntry_DoNotUseDefaultTypeInternal() {}
  union {
    ResultInfo_MarksEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ResultInfo_MarksEntry_DoNotUseDefaultTypeInternal _ResultInfo_MarksEntry_DoNotUse_default_instance_;
constexpr ResultInfo_RequestExtEntry_DoNotUse::ResultInfo_RequestExtEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct ResultInfo_RequestExtEntry_DoNotUseDefaultTypeInternal {
  constexpr ResultInfo_RequestExtEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ResultInfo_RequestExtEntry_DoNotUseDefaultTypeInternal() {}
  union {
    ResultInfo_RequestExtEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ResultInfo_RequestExtEntry_DoNotUseDefaultTypeInternal _ResultInfo_RequestExtEntry_DoNotUse_default_instance_;
constexpr ResultInfo_RequestBizExtEntry_DoNotUse::ResultInfo_RequestBizExtEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct ResultInfo_RequestBizExtEntry_DoNotUseDefaultTypeInternal {
  constexpr ResultInfo_RequestBizExtEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ResultInfo_RequestBizExtEntry_DoNotUseDefaultTypeInternal() {}
  union {
    ResultInfo_RequestBizExtEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ResultInfo_RequestBizExtEntry_DoNotUseDefaultTypeInternal _ResultInfo_RequestBizExtEntry_DoNotUse_default_instance_;
constexpr ResultInfo::ResultInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : result_()
  , marks_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , request_ext_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , request_biz_ext_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , sort_status_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , fallback_reason_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , all_layer_success_(false)
  , fallback_(false){}
struct ResultInfoDefaultTypeInternal {
  constexpr ResultInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ResultInfoDefaultTypeInternal() {}
  union {
    ResultInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ResultInfoDefaultTypeInternal _ResultInfo_default_instance_;
constexpr ResGoods_ExtEntry_DoNotUse::ResGoods_ExtEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct ResGoods_ExtEntry_DoNotUseDefaultTypeInternal {
  constexpr ResGoods_ExtEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ResGoods_ExtEntry_DoNotUseDefaultTypeInternal() {}
  union {
    ResGoods_ExtEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ResGoods_ExtEntry_DoNotUseDefaultTypeInternal _ResGoods_ExtEntry_DoNotUse_default_instance_;
constexpr ResGoods_BizExtEntry_DoNotUse::ResGoods_BizExtEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct ResGoods_BizExtEntry_DoNotUseDefaultTypeInternal {
  constexpr ResGoods_BizExtEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ResGoods_BizExtEntry_DoNotUseDefaultTypeInternal() {}
  union {
    ResGoods_BizExtEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ResGoods_BizExtEntry_DoNotUseDefaultTypeInternal _ResGoods_BizExtEntry_DoNotUse_default_instance_;
constexpr ResGoods::ResGoods(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ext_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , biz_ext_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , goods_id_(uint64_t{0u})
  , score_(0)
  , priority_(0){}
struct ResGoodsDefaultTypeInternal {
  constexpr ResGoodsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ResGoodsDefaultTypeInternal() {}
  union {
    ResGoods _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ResGoodsDefaultTypeInternal _ResGoods_default_instance_;
}  // namespace goods_sort
}  // namespace srchRoute
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_proto_2fsrch_5fsort_5fplatform_2eproto[18];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_proto_2fsrch_5fsort_5fplatform_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_proto_2fsrch_5fsort_5fplatform_2eproto = nullptr;

const uint32_t TableStruct_proto_2fsrch_5fsort_5fplatform_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortResponse, code_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortResponse, err_msg_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortResponse, result_info_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest_UserInfoEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest_UserInfoEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest_UserInfoEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest_UserInfoEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest_ContextEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest_ContextEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest_ContextEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest_ContextEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest_RecallChannelsMetasEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest_RecallChannelsMetasEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest_RecallChannelsMetasEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest_RecallChannelsMetasEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest_JsonRuleIdEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest_JsonRuleIdEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest_JsonRuleIdEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest_JsonRuleIdEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest, limit_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest, user_info_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest, context_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest, adp_goods_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest, recall_goods_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest, recall_channel_goods_tag_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest, recall_channels_metas_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::PbSortRequest, json_rule_id_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ChannelMetas_ChannelMetasEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ChannelMetas_ChannelMetasEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ChannelMetas_ChannelMetasEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ChannelMetas_ChannelMetasEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ChannelMetas, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ChannelMetas, channel_metas_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ReqGoods_ExtEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ReqGoods_ExtEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ReqGoods_ExtEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ReqGoods_ExtEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ReqGoods, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ReqGoods, goods_id_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ReqGoods, score_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ReqGoods, spu_id_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ReqGoods, priority_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ReqGoods, ext_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ReqGoods, tags_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ReqGoods, recall_channels_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ReqGoods, goods_tag_value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::GoodsTag, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::GoodsTag, tag_value_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResultInfo_MarksEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResultInfo_MarksEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResultInfo_MarksEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResultInfo_MarksEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResultInfo_RequestExtEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResultInfo_RequestExtEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResultInfo_RequestExtEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResultInfo_RequestExtEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResultInfo_RequestBizExtEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResultInfo_RequestBizExtEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResultInfo_RequestBizExtEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResultInfo_RequestBizExtEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResultInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResultInfo, sort_status_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResultInfo, all_layer_success_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResultInfo, fallback_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResultInfo, fallback_reason_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResultInfo, result_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResultInfo, marks_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResultInfo, request_ext_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResultInfo, request_biz_ext_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResGoods_ExtEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResGoods_ExtEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResGoods_ExtEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResGoods_ExtEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResGoods_BizExtEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResGoods_BizExtEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResGoods_BizExtEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResGoods_BizExtEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResGoods, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResGoods, goods_id_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResGoods, score_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResGoods, priority_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResGoods, ext_),
  PROTOBUF_FIELD_OFFSET(::srchRoute::goods_sort::ResGoods, biz_ext_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::srchRoute::goods_sort::PbSortResponse)},
  { 9, 17, -1, sizeof(::srchRoute::goods_sort::PbSortRequest_UserInfoEntry_DoNotUse)},
  { 19, 27, -1, sizeof(::srchRoute::goods_sort::PbSortRequest_ContextEntry_DoNotUse)},
  { 29, 37, -1, sizeof(::srchRoute::goods_sort::PbSortRequest_RecallChannelsMetasEntry_DoNotUse)},
  { 39, 47, -1, sizeof(::srchRoute::goods_sort::PbSortRequest_JsonRuleIdEntry_DoNotUse)},
  { 49, -1, -1, sizeof(::srchRoute::goods_sort::PbSortRequest)},
  { 63, 71, -1, sizeof(::srchRoute::goods_sort::ChannelMetas_ChannelMetasEntry_DoNotUse)},
  { 73, -1, -1, sizeof(::srchRoute::goods_sort::ChannelMetas)},
  { 80, 88, -1, sizeof(::srchRoute::goods_sort::ReqGoods_ExtEntry_DoNotUse)},
  { 90, -1, -1, sizeof(::srchRoute::goods_sort::ReqGoods)},
  { 104, -1, -1, sizeof(::srchRoute::goods_sort::GoodsTag)},
  { 111, 119, -1, sizeof(::srchRoute::goods_sort::ResultInfo_MarksEntry_DoNotUse)},
  { 121, 129, -1, sizeof(::srchRoute::goods_sort::ResultInfo_RequestExtEntry_DoNotUse)},
  { 131, 139, -1, sizeof(::srchRoute::goods_sort::ResultInfo_RequestBizExtEntry_DoNotUse)},
  { 141, -1, -1, sizeof(::srchRoute::goods_sort::ResultInfo)},
  { 155, 163, -1, sizeof(::srchRoute::goods_sort::ResGoods_ExtEntry_DoNotUse)},
  { 165, 173, -1, sizeof(::srchRoute::goods_sort::ResGoods_BizExtEntry_DoNotUse)},
  { 175, -1, -1, sizeof(::srchRoute::goods_sort::ResGoods)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::srchRoute::goods_sort::_PbSortResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::srchRoute::goods_sort::_PbSortRequest_UserInfoEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::srchRoute::goods_sort::_PbSortRequest_ContextEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::srchRoute::goods_sort::_PbSortRequest_RecallChannelsMetasEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::srchRoute::goods_sort::_PbSortRequest_JsonRuleIdEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::srchRoute::goods_sort::_PbSortRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::srchRoute::goods_sort::_ChannelMetas_ChannelMetasEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::srchRoute::goods_sort::_ChannelMetas_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::srchRoute::goods_sort::_ReqGoods_ExtEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::srchRoute::goods_sort::_ReqGoods_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::srchRoute::goods_sort::_GoodsTag_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::srchRoute::goods_sort::_ResultInfo_MarksEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::srchRoute::goods_sort::_ResultInfo_RequestExtEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::srchRoute::goods_sort::_ResultInfo_RequestBizExtEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::srchRoute::goods_sort::_ResultInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::srchRoute::goods_sort::_ResGoods_ExtEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::srchRoute::goods_sort::_ResGoods_BizExtEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::srchRoute::goods_sort::_ResGoods_default_instance_),
};

const char descriptor_table_protodef_proto_2fsrch_5fsort_5fplatform_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\036proto/srch_sort_platform.proto\022\024srchRo"
  "ute.goods_sort\"f\n\016PbSortResponse\022\014\n\004code"
  "\030\001 \001(\005\022\017\n\007err_msg\030\002 \001(\t\0225\n\013result_info\030\003"
  " \001(\0132 .srchRoute.goods_sort.ResultInfo\"\316"
  "\005\n\rPbSortRequest\022\r\n\005limit\030\001 \001(\005\022D\n\tuser_"
  "info\030\002 \003(\01321.srchRoute.goods_sort.PbSort"
  "Request.UserInfoEntry\022A\n\007context\030\003 \003(\01320"
  ".srchRoute.goods_sort.PbSortRequest.Cont"
  "extEntry\0221\n\tadp_goods\030\004 \003(\0132\036.srchRoute."
  "goods_sort.ReqGoods\0224\n\014recall_goods\030\005 \003("
  "\0132\036.srchRoute.goods_sort.ReqGoods\022 \n\030rec"
  "all_channel_goods_tag\030\006 \003(\t\022[\n\025recall_ch"
  "annels_metas\030\007 \003(\0132<.srchRoute.goods_sor"
  "t.PbSortRequest.RecallChannelsMetasEntry"
  "\022I\n\014json_rule_id\030\010 \003(\01323.srchRoute.goods"
  "_sort.PbSortRequest.JsonRuleIdEntry\032/\n\rU"
  "serInfoEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t"
  ":\0028\001\032.\n\014ContextEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005val"
  "ue\030\002 \001(\t:\0028\001\032^\n\030RecallChannelsMetasEntry"
  "\022\013\n\003key\030\001 \001(\t\0221\n\005value\030\002 \001(\0132\".srchRoute"
  ".goods_sort.ChannelMetas:\0028\001\0321\n\017JsonRule"
  "IdEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\""
  "\220\001\n\014ChannelMetas\022K\n\rchannel_metas\030\001 \003(\0132"
  "4.srchRoute.goods_sort.ChannelMetas.Chan"
  "nelMetasEntry\0323\n\021ChannelMetasEntry\022\013\n\003ke"
  "y\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\"\217\002\n\010ReqGoods\022"
  "\020\n\010goods_id\030\001 \001(\004\022\r\n\005score\030\002 \001(\001\022\016\n\006spu_"
  "id\030\003 \001(\t\022\020\n\010priority\030\004 \001(\005\0224\n\003ext\030\005 \003(\0132"
  "\'.srchRoute.goods_sort.ReqGoods.ExtEntry"
  "\022\014\n\004tags\030\006 \003(\t\022\027\n\017recall_channels\030\007 \003(\t\022"
  "7\n\017goods_tag_value\030\010 \003(\0132\036.srchRoute.goo"
  "ds_sort.GoodsTag\032*\n\010ExtEntry\022\013\n\003key\030\001 \001("
  "\t\022\r\n\005value\030\002 \001(\t:\0028\001\"\035\n\010GoodsTag\022\021\n\ttag_"
  "value\030\001 \003(\005\"\377\003\n\nResultInfo\022\023\n\013sort_statu"
  "s\030\002 \001(\t\022\031\n\021all_layer_success\030\003 \001(\010\022\020\n\010fa"
  "llback\030\004 \001(\010\022\027\n\017fallback_reason\030\005 \001(\t\022.\n"
  "\006result\030\006 \003(\0132\036.srchRoute.goods_sort.Res"
  "Goods\022:\n\005marks\030\007 \003(\0132+.srchRoute.goods_s"
  "ort.ResultInfo.MarksEntry\022E\n\013request_ext"
  "\030\010 \003(\01320.srchRoute.goods_sort.ResultInfo"
  ".RequestExtEntry\022L\n\017request_biz_ext\030\t \003("
  "\01323.srchRoute.goods_sort.ResultInfo.Requ"
  "estBizExtEntry\032,\n\nMarksEntry\022\013\n\003key\030\001 \001("
  "\t\022\r\n\005value\030\002 \001(\t:\0028\001\0321\n\017RequestExtEntry\022"
  "\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\0324\n\022Reque"
  "stBizExtEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001("
  "\t:\0028\001\"\213\002\n\010ResGoods\022\020\n\010goods_id\030\001 \001(\004\022\r\n\005"
  "score\030\002 \001(\001\022\020\n\010priority\030\003 \001(\005\0224\n\003ext\030\004 \003"
  "(\0132\'.srchRoute.goods_sort.ResGoods.ExtEn"
  "try\022;\n\007biz_ext\030\005 \003(\0132*.srchRoute.goods_s"
  "ort.ResGoods.BizExtEntry\032*\n\010ExtEntry\022\013\n\003"
  "key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\032-\n\013BizExtEn"
  "try\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\0012\262\002\n\022"
  "PbMultiSortService\022X\n\tMultiSort\022#.srchRo"
  "ute.goods_sort.PbSortRequest\032$.srchRoute"
  ".goods_sort.PbSortResponse\"\000\022^\n\017SearchRo"
  "ughSort\022#.srchRoute.goods_sort.PbSortReq"
  "uest\032$.srchRoute.goods_sort.PbSortRespon"
  "se\"\000\022b\n\023SearchRelevanceSort\022#.srchRoute."
  "goods_sort.PbSortRequest\032$.srchRoute.goo"
  "ds_sort.PbSortResponse\"\000B4\n\036com.shein.ab"
  "c.route.goods_sortB\020SrchSortPlatformP\001b\006"
  "proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto = {
  false, false, 2486, descriptor_table_protodef_proto_2fsrch_5fsort_5fplatform_2eproto, "proto/srch_sort_platform.proto", 
  &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_once, nullptr, 0, 18,
  schemas, file_default_instances, TableStruct_proto_2fsrch_5fsort_5fplatform_2eproto::offsets,
  file_level_metadata_proto_2fsrch_5fsort_5fplatform_2eproto, file_level_enum_descriptors_proto_2fsrch_5fsort_5fplatform_2eproto, file_level_service_descriptors_proto_2fsrch_5fsort_5fplatform_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_getter() {
  return &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_proto_2fsrch_5fsort_5fplatform_2eproto(&descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto);
namespace srchRoute {
namespace goods_sort {

// ===================================================================

class PbSortResponse::_Internal {
 public:
  static const ::srchRoute::goods_sort::ResultInfo& result_info(const PbSortResponse* msg);
};

const ::srchRoute::goods_sort::ResultInfo&
PbSortResponse::_Internal::result_info(const PbSortResponse* msg) {
  return *msg->result_info_;
}
PbSortResponse::PbSortResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:srchRoute.goods_sort.PbSortResponse)
}
PbSortResponse::PbSortResponse(const PbSortResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  err_msg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    err_msg_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_err_msg().empty()) {
    err_msg_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_err_msg(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_result_info()) {
    result_info_ = new ::srchRoute::goods_sort::ResultInfo(*from.result_info_);
  } else {
    result_info_ = nullptr;
  }
  code_ = from.code_;
  // @@protoc_insertion_point(copy_constructor:srchRoute.goods_sort.PbSortResponse)
}

inline void PbSortResponse::SharedCtor() {
err_msg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  err_msg_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&result_info_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&code_) -
    reinterpret_cast<char*>(&result_info_)) + sizeof(code_));
}

PbSortResponse::~PbSortResponse() {
  // @@protoc_insertion_point(destructor:srchRoute.goods_sort.PbSortResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PbSortResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  err_msg_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete result_info_;
}

void PbSortResponse::ArenaDtor(void* object) {
  PbSortResponse* _this = reinterpret_cast< PbSortResponse* >(object);
  (void)_this;
}
void PbSortResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PbSortResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PbSortResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:srchRoute.goods_sort.PbSortResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  err_msg_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && result_info_ != nullptr) {
    delete result_info_;
  }
  result_info_ = nullptr;
  code_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PbSortResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 code = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          code_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string err_msg = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_err_msg();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "srchRoute.goods_sort.PbSortResponse.err_msg"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .srchRoute.goods_sort.ResultInfo result_info = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_result_info(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PbSortResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:srchRoute.goods_sort.PbSortResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 code = 1;
  if (this->_internal_code() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_code(), target);
  }

  // string err_msg = 2;
  if (!this->_internal_err_msg().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_err_msg().data(), static_cast<int>(this->_internal_err_msg().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "srchRoute.goods_sort.PbSortResponse.err_msg");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_err_msg(), target);
  }

  // .srchRoute.goods_sort.ResultInfo result_info = 3;
  if (this->_internal_has_result_info()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::result_info(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:srchRoute.goods_sort.PbSortResponse)
  return target;
}

size_t PbSortResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:srchRoute.goods_sort.PbSortResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string err_msg = 2;
  if (!this->_internal_err_msg().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_err_msg());
  }

  // .srchRoute.goods_sort.ResultInfo result_info = 3;
  if (this->_internal_has_result_info()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *result_info_);
  }

  // int32 code = 1;
  if (this->_internal_code() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_code());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PbSortResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PbSortResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PbSortResponse::GetClassData() const { return &_class_data_; }

void PbSortResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PbSortResponse *>(to)->MergeFrom(
      static_cast<const PbSortResponse &>(from));
}


void PbSortResponse::MergeFrom(const PbSortResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:srchRoute.goods_sort.PbSortResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_err_msg().empty()) {
    _internal_set_err_msg(from._internal_err_msg());
  }
  if (from._internal_has_result_info()) {
    _internal_mutable_result_info()->::srchRoute::goods_sort::ResultInfo::MergeFrom(from._internal_result_info());
  }
  if (from._internal_code() != 0) {
    _internal_set_code(from._internal_code());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PbSortResponse::CopyFrom(const PbSortResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:srchRoute.goods_sort.PbSortResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PbSortResponse::IsInitialized() const {
  return true;
}

void PbSortResponse::InternalSwap(PbSortResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &err_msg_, lhs_arena,
      &other->err_msg_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PbSortResponse, code_)
      + sizeof(PbSortResponse::code_)
      - PROTOBUF_FIELD_OFFSET(PbSortResponse, result_info_)>(
          reinterpret_cast<char*>(&result_info_),
          reinterpret_cast<char*>(&other->result_info_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PbSortResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_getter, &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_once,
      file_level_metadata_proto_2fsrch_5fsort_5fplatform_2eproto[0]);
}

// ===================================================================

PbSortRequest_UserInfoEntry_DoNotUse::PbSortRequest_UserInfoEntry_DoNotUse() {}
PbSortRequest_UserInfoEntry_DoNotUse::PbSortRequest_UserInfoEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void PbSortRequest_UserInfoEntry_DoNotUse::MergeFrom(const PbSortRequest_UserInfoEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata PbSortRequest_UserInfoEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_getter, &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_once,
      file_level_metadata_proto_2fsrch_5fsort_5fplatform_2eproto[1]);
}

// ===================================================================

PbSortRequest_ContextEntry_DoNotUse::PbSortRequest_ContextEntry_DoNotUse() {}
PbSortRequest_ContextEntry_DoNotUse::PbSortRequest_ContextEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void PbSortRequest_ContextEntry_DoNotUse::MergeFrom(const PbSortRequest_ContextEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata PbSortRequest_ContextEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_getter, &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_once,
      file_level_metadata_proto_2fsrch_5fsort_5fplatform_2eproto[2]);
}

// ===================================================================

PbSortRequest_RecallChannelsMetasEntry_DoNotUse::PbSortRequest_RecallChannelsMetasEntry_DoNotUse() {}
PbSortRequest_RecallChannelsMetasEntry_DoNotUse::PbSortRequest_RecallChannelsMetasEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void PbSortRequest_RecallChannelsMetasEntry_DoNotUse::MergeFrom(const PbSortRequest_RecallChannelsMetasEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata PbSortRequest_RecallChannelsMetasEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_getter, &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_once,
      file_level_metadata_proto_2fsrch_5fsort_5fplatform_2eproto[3]);
}

// ===================================================================

PbSortRequest_JsonRuleIdEntry_DoNotUse::PbSortRequest_JsonRuleIdEntry_DoNotUse() {}
PbSortRequest_JsonRuleIdEntry_DoNotUse::PbSortRequest_JsonRuleIdEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void PbSortRequest_JsonRuleIdEntry_DoNotUse::MergeFrom(const PbSortRequest_JsonRuleIdEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata PbSortRequest_JsonRuleIdEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_getter, &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_once,
      file_level_metadata_proto_2fsrch_5fsort_5fplatform_2eproto[4]);
}

// ===================================================================

class PbSortRequest::_Internal {
 public:
};

PbSortRequest::PbSortRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  user_info_(arena),
  context_(arena),
  adp_goods_(arena),
  recall_goods_(arena),
  recall_channel_goods_tag_(arena),
  recall_channels_metas_(arena),
  json_rule_id_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:srchRoute.goods_sort.PbSortRequest)
}
PbSortRequest::PbSortRequest(const PbSortRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      adp_goods_(from.adp_goods_),
      recall_goods_(from.recall_goods_),
      recall_channel_goods_tag_(from.recall_channel_goods_tag_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  user_info_.MergeFrom(from.user_info_);
  context_.MergeFrom(from.context_);
  recall_channels_metas_.MergeFrom(from.recall_channels_metas_);
  json_rule_id_.MergeFrom(from.json_rule_id_);
  limit_ = from.limit_;
  // @@protoc_insertion_point(copy_constructor:srchRoute.goods_sort.PbSortRequest)
}

inline void PbSortRequest::SharedCtor() {
limit_ = 0;
}

PbSortRequest::~PbSortRequest() {
  // @@protoc_insertion_point(destructor:srchRoute.goods_sort.PbSortRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PbSortRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PbSortRequest::ArenaDtor(void* object) {
  PbSortRequest* _this = reinterpret_cast< PbSortRequest* >(object);
  (void)_this;
  _this->user_info_. ~MapField();
  _this->context_. ~MapField();
  _this->recall_channels_metas_. ~MapField();
  _this->json_rule_id_. ~MapField();
}
inline void PbSortRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &PbSortRequest::ArenaDtor);
  }
}
void PbSortRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PbSortRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:srchRoute.goods_sort.PbSortRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  user_info_.Clear();
  context_.Clear();
  adp_goods_.Clear();
  recall_goods_.Clear();
  recall_channel_goods_tag_.Clear();
  recall_channels_metas_.Clear();
  json_rule_id_.Clear();
  limit_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PbSortRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 limit = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          limit_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // map<string, string> user_info = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&user_info_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<string, string> context = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&context_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .srchRoute.goods_sort.ReqGoods adp_goods = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_adp_goods(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .srchRoute.goods_sort.ReqGoods recall_goods = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_recall_goods(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated string recall_channel_goods_tag = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_recall_channel_goods_tag();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "srchRoute.goods_sort.PbSortRequest.recall_channel_goods_tag"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<50>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<string, .srchRoute.goods_sort.ChannelMetas> recall_channels_metas = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&recall_channels_metas_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<string, string> json_rule_id = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&json_rule_id_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<66>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PbSortRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:srchRoute.goods_sort.PbSortRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 limit = 1;
  if (this->_internal_limit() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_limit(), target);
  }

  // map<string, string> user_info = 2;
  if (!this->_internal_user_info().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "srchRoute.goods_sort.PbSortRequest.UserInfoEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "srchRoute.goods_sort.PbSortRequest.UserInfoEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_user_info().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_user_info().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_user_info().begin();
          it != this->_internal_user_info().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = PbSortRequest_UserInfoEntry_DoNotUse::Funcs::InternalSerialize(2, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_user_info().begin();
          it != this->_internal_user_info().end(); ++it) {
        target = PbSortRequest_UserInfoEntry_DoNotUse::Funcs::InternalSerialize(2, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // map<string, string> context = 3;
  if (!this->_internal_context().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "srchRoute.goods_sort.PbSortRequest.ContextEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "srchRoute.goods_sort.PbSortRequest.ContextEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_context().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_context().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_context().begin();
          it != this->_internal_context().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = PbSortRequest_ContextEntry_DoNotUse::Funcs::InternalSerialize(3, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_context().begin();
          it != this->_internal_context().end(); ++it) {
        target = PbSortRequest_ContextEntry_DoNotUse::Funcs::InternalSerialize(3, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // repeated .srchRoute.goods_sort.ReqGoods adp_goods = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_adp_goods_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, this->_internal_adp_goods(i), target, stream);
  }

  // repeated .srchRoute.goods_sort.ReqGoods recall_goods = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_recall_goods_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, this->_internal_recall_goods(i), target, stream);
  }

  // repeated string recall_channel_goods_tag = 6;
  for (int i = 0, n = this->_internal_recall_channel_goods_tag_size(); i < n; i++) {
    const auto& s = this->_internal_recall_channel_goods_tag(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "srchRoute.goods_sort.PbSortRequest.recall_channel_goods_tag");
    target = stream->WriteString(6, s, target);
  }

  // map<string, .srchRoute.goods_sort.ChannelMetas> recall_channels_metas = 7;
  if (!this->_internal_recall_channels_metas().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::srchRoute::goods_sort::ChannelMetas >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "srchRoute.goods_sort.PbSortRequest.RecallChannelsMetasEntry.key");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_recall_channels_metas().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_recall_channels_metas().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::srchRoute::goods_sort::ChannelMetas >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::srchRoute::goods_sort::ChannelMetas >::const_iterator
          it = this->_internal_recall_channels_metas().begin();
          it != this->_internal_recall_channels_metas().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = PbSortRequest_RecallChannelsMetasEntry_DoNotUse::Funcs::InternalSerialize(7, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::srchRoute::goods_sort::ChannelMetas >::const_iterator
          it = this->_internal_recall_channels_metas().begin();
          it != this->_internal_recall_channels_metas().end(); ++it) {
        target = PbSortRequest_RecallChannelsMetasEntry_DoNotUse::Funcs::InternalSerialize(7, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // map<string, string> json_rule_id = 8;
  if (!this->_internal_json_rule_id().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "srchRoute.goods_sort.PbSortRequest.JsonRuleIdEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "srchRoute.goods_sort.PbSortRequest.JsonRuleIdEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_json_rule_id().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_json_rule_id().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_json_rule_id().begin();
          it != this->_internal_json_rule_id().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = PbSortRequest_JsonRuleIdEntry_DoNotUse::Funcs::InternalSerialize(8, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_json_rule_id().begin();
          it != this->_internal_json_rule_id().end(); ++it) {
        target = PbSortRequest_JsonRuleIdEntry_DoNotUse::Funcs::InternalSerialize(8, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:srchRoute.goods_sort.PbSortRequest)
  return target;
}

size_t PbSortRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:srchRoute.goods_sort.PbSortRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, string> user_info = 2;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_user_info_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_user_info().begin();
      it != this->_internal_user_info().end(); ++it) {
    total_size += PbSortRequest_UserInfoEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // map<string, string> context = 3;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_context_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_context().begin();
      it != this->_internal_context().end(); ++it) {
    total_size += PbSortRequest_ContextEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // repeated .srchRoute.goods_sort.ReqGoods adp_goods = 4;
  total_size += 1UL * this->_internal_adp_goods_size();
  for (const auto& msg : this->adp_goods_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .srchRoute.goods_sort.ReqGoods recall_goods = 5;
  total_size += 1UL * this->_internal_recall_goods_size();
  for (const auto& msg : this->recall_goods_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated string recall_channel_goods_tag = 6;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(recall_channel_goods_tag_.size());
  for (int i = 0, n = recall_channel_goods_tag_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      recall_channel_goods_tag_.Get(i));
  }

  // map<string, .srchRoute.goods_sort.ChannelMetas> recall_channels_metas = 7;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_recall_channels_metas_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::srchRoute::goods_sort::ChannelMetas >::const_iterator
      it = this->_internal_recall_channels_metas().begin();
      it != this->_internal_recall_channels_metas().end(); ++it) {
    total_size += PbSortRequest_RecallChannelsMetasEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // map<string, string> json_rule_id = 8;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_json_rule_id_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_json_rule_id().begin();
      it != this->_internal_json_rule_id().end(); ++it) {
    total_size += PbSortRequest_JsonRuleIdEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // int32 limit = 1;
  if (this->_internal_limit() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_limit());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PbSortRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PbSortRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PbSortRequest::GetClassData() const { return &_class_data_; }

void PbSortRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PbSortRequest *>(to)->MergeFrom(
      static_cast<const PbSortRequest &>(from));
}


void PbSortRequest::MergeFrom(const PbSortRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:srchRoute.goods_sort.PbSortRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  user_info_.MergeFrom(from.user_info_);
  context_.MergeFrom(from.context_);
  adp_goods_.MergeFrom(from.adp_goods_);
  recall_goods_.MergeFrom(from.recall_goods_);
  recall_channel_goods_tag_.MergeFrom(from.recall_channel_goods_tag_);
  recall_channels_metas_.MergeFrom(from.recall_channels_metas_);
  json_rule_id_.MergeFrom(from.json_rule_id_);
  if (from._internal_limit() != 0) {
    _internal_set_limit(from._internal_limit());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PbSortRequest::CopyFrom(const PbSortRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:srchRoute.goods_sort.PbSortRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PbSortRequest::IsInitialized() const {
  return true;
}

void PbSortRequest::InternalSwap(PbSortRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  user_info_.InternalSwap(&other->user_info_);
  context_.InternalSwap(&other->context_);
  adp_goods_.InternalSwap(&other->adp_goods_);
  recall_goods_.InternalSwap(&other->recall_goods_);
  recall_channel_goods_tag_.InternalSwap(&other->recall_channel_goods_tag_);
  recall_channels_metas_.InternalSwap(&other->recall_channels_metas_);
  json_rule_id_.InternalSwap(&other->json_rule_id_);
  swap(limit_, other->limit_);
}

::PROTOBUF_NAMESPACE_ID::Metadata PbSortRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_getter, &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_once,
      file_level_metadata_proto_2fsrch_5fsort_5fplatform_2eproto[5]);
}

// ===================================================================

ChannelMetas_ChannelMetasEntry_DoNotUse::ChannelMetas_ChannelMetasEntry_DoNotUse() {}
ChannelMetas_ChannelMetasEntry_DoNotUse::ChannelMetas_ChannelMetasEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void ChannelMetas_ChannelMetasEntry_DoNotUse::MergeFrom(const ChannelMetas_ChannelMetasEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata ChannelMetas_ChannelMetasEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_getter, &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_once,
      file_level_metadata_proto_2fsrch_5fsort_5fplatform_2eproto[6]);
}

// ===================================================================

class ChannelMetas::_Internal {
 public:
};

ChannelMetas::ChannelMetas(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  channel_metas_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:srchRoute.goods_sort.ChannelMetas)
}
ChannelMetas::ChannelMetas(const ChannelMetas& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  channel_metas_.MergeFrom(from.channel_metas_);
  // @@protoc_insertion_point(copy_constructor:srchRoute.goods_sort.ChannelMetas)
}

inline void ChannelMetas::SharedCtor() {
}

ChannelMetas::~ChannelMetas() {
  // @@protoc_insertion_point(destructor:srchRoute.goods_sort.ChannelMetas)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ChannelMetas::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void ChannelMetas::ArenaDtor(void* object) {
  ChannelMetas* _this = reinterpret_cast< ChannelMetas* >(object);
  (void)_this;
  _this->channel_metas_. ~MapField();
}
inline void ChannelMetas::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &ChannelMetas::ArenaDtor);
  }
}
void ChannelMetas::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ChannelMetas::Clear() {
// @@protoc_insertion_point(message_clear_start:srchRoute.goods_sort.ChannelMetas)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  channel_metas_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ChannelMetas::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // map<string, string> channel_metas = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&channel_metas_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ChannelMetas::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:srchRoute.goods_sort.ChannelMetas)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, string> channel_metas = 1;
  if (!this->_internal_channel_metas().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "srchRoute.goods_sort.ChannelMetas.ChannelMetasEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "srchRoute.goods_sort.ChannelMetas.ChannelMetasEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_channel_metas().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_channel_metas().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_channel_metas().begin();
          it != this->_internal_channel_metas().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = ChannelMetas_ChannelMetasEntry_DoNotUse::Funcs::InternalSerialize(1, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_channel_metas().begin();
          it != this->_internal_channel_metas().end(); ++it) {
        target = ChannelMetas_ChannelMetasEntry_DoNotUse::Funcs::InternalSerialize(1, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:srchRoute.goods_sort.ChannelMetas)
  return target;
}

size_t ChannelMetas::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:srchRoute.goods_sort.ChannelMetas)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, string> channel_metas = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_channel_metas_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_channel_metas().begin();
      it != this->_internal_channel_metas().end(); ++it) {
    total_size += ChannelMetas_ChannelMetasEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ChannelMetas::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ChannelMetas::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ChannelMetas::GetClassData() const { return &_class_data_; }

void ChannelMetas::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ChannelMetas *>(to)->MergeFrom(
      static_cast<const ChannelMetas &>(from));
}


void ChannelMetas::MergeFrom(const ChannelMetas& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:srchRoute.goods_sort.ChannelMetas)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  channel_metas_.MergeFrom(from.channel_metas_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ChannelMetas::CopyFrom(const ChannelMetas& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:srchRoute.goods_sort.ChannelMetas)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ChannelMetas::IsInitialized() const {
  return true;
}

void ChannelMetas::InternalSwap(ChannelMetas* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  channel_metas_.InternalSwap(&other->channel_metas_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ChannelMetas::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_getter, &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_once,
      file_level_metadata_proto_2fsrch_5fsort_5fplatform_2eproto[7]);
}

// ===================================================================

ReqGoods_ExtEntry_DoNotUse::ReqGoods_ExtEntry_DoNotUse() {}
ReqGoods_ExtEntry_DoNotUse::ReqGoods_ExtEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void ReqGoods_ExtEntry_DoNotUse::MergeFrom(const ReqGoods_ExtEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata ReqGoods_ExtEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_getter, &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_once,
      file_level_metadata_proto_2fsrch_5fsort_5fplatform_2eproto[8]);
}

// ===================================================================

class ReqGoods::_Internal {
 public:
};

ReqGoods::ReqGoods(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  ext_(arena),
  tags_(arena),
  recall_channels_(arena),
  goods_tag_value_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:srchRoute.goods_sort.ReqGoods)
}
ReqGoods::ReqGoods(const ReqGoods& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      tags_(from.tags_),
      recall_channels_(from.recall_channels_),
      goods_tag_value_(from.goods_tag_value_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ext_.MergeFrom(from.ext_);
  spu_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    spu_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_spu_id().empty()) {
    spu_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_spu_id(), 
      GetArenaForAllocation());
  }
  ::memcpy(&goods_id_, &from.goods_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&priority_) -
    reinterpret_cast<char*>(&goods_id_)) + sizeof(priority_));
  // @@protoc_insertion_point(copy_constructor:srchRoute.goods_sort.ReqGoods)
}

inline void ReqGoods::SharedCtor() {
spu_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  spu_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&goods_id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&priority_) -
    reinterpret_cast<char*>(&goods_id_)) + sizeof(priority_));
}

ReqGoods::~ReqGoods() {
  // @@protoc_insertion_point(destructor:srchRoute.goods_sort.ReqGoods)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ReqGoods::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  spu_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ReqGoods::ArenaDtor(void* object) {
  ReqGoods* _this = reinterpret_cast< ReqGoods* >(object);
  (void)_this;
  _this->ext_. ~MapField();
}
inline void ReqGoods::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &ReqGoods::ArenaDtor);
  }
}
void ReqGoods::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ReqGoods::Clear() {
// @@protoc_insertion_point(message_clear_start:srchRoute.goods_sort.ReqGoods)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ext_.Clear();
  tags_.Clear();
  recall_channels_.Clear();
  goods_tag_value_.Clear();
  spu_id_.ClearToEmpty();
  ::memset(&goods_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&priority_) -
      reinterpret_cast<char*>(&goods_id_)) + sizeof(priority_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ReqGoods::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 goods_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          goods_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double score = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          score_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // string spu_id = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_spu_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "srchRoute.goods_sort.ReqGoods.spu_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 priority = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          priority_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // map<string, string> ext = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&ext_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated string tags = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_tags();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "srchRoute.goods_sort.ReqGoods.tags"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<50>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated string recall_channels = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_recall_channels();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "srchRoute.goods_sort.ReqGoods.recall_channels"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .srchRoute.goods_sort.GoodsTag goods_tag_value = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_goods_tag_value(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<66>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ReqGoods::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:srchRoute.goods_sort.ReqGoods)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 goods_id = 1;
  if (this->_internal_goods_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_goods_id(), target);
  }

  // double score = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_score = this->_internal_score();
  uint64_t raw_score;
  memcpy(&raw_score, &tmp_score, sizeof(tmp_score));
  if (raw_score != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_score(), target);
  }

  // string spu_id = 3;
  if (!this->_internal_spu_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_spu_id().data(), static_cast<int>(this->_internal_spu_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "srchRoute.goods_sort.ReqGoods.spu_id");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_spu_id(), target);
  }

  // int32 priority = 4;
  if (this->_internal_priority() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_priority(), target);
  }

  // map<string, string> ext = 5;
  if (!this->_internal_ext().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "srchRoute.goods_sort.ReqGoods.ExtEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "srchRoute.goods_sort.ReqGoods.ExtEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_ext().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_ext().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_ext().begin();
          it != this->_internal_ext().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = ReqGoods_ExtEntry_DoNotUse::Funcs::InternalSerialize(5, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_ext().begin();
          it != this->_internal_ext().end(); ++it) {
        target = ReqGoods_ExtEntry_DoNotUse::Funcs::InternalSerialize(5, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // repeated string tags = 6;
  for (int i = 0, n = this->_internal_tags_size(); i < n; i++) {
    const auto& s = this->_internal_tags(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "srchRoute.goods_sort.ReqGoods.tags");
    target = stream->WriteString(6, s, target);
  }

  // repeated string recall_channels = 7;
  for (int i = 0, n = this->_internal_recall_channels_size(); i < n; i++) {
    const auto& s = this->_internal_recall_channels(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "srchRoute.goods_sort.ReqGoods.recall_channels");
    target = stream->WriteString(7, s, target);
  }

  // repeated .srchRoute.goods_sort.GoodsTag goods_tag_value = 8;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_goods_tag_value_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(8, this->_internal_goods_tag_value(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:srchRoute.goods_sort.ReqGoods)
  return target;
}

size_t ReqGoods::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:srchRoute.goods_sort.ReqGoods)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, string> ext = 5;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_ext_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_ext().begin();
      it != this->_internal_ext().end(); ++it) {
    total_size += ReqGoods_ExtEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // repeated string tags = 6;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(tags_.size());
  for (int i = 0, n = tags_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      tags_.Get(i));
  }

  // repeated string recall_channels = 7;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(recall_channels_.size());
  for (int i = 0, n = recall_channels_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      recall_channels_.Get(i));
  }

  // repeated .srchRoute.goods_sort.GoodsTag goods_tag_value = 8;
  total_size += 1UL * this->_internal_goods_tag_value_size();
  for (const auto& msg : this->goods_tag_value_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string spu_id = 3;
  if (!this->_internal_spu_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_spu_id());
  }

  // uint64 goods_id = 1;
  if (this->_internal_goods_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_goods_id());
  }

  // double score = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_score = this->_internal_score();
  uint64_t raw_score;
  memcpy(&raw_score, &tmp_score, sizeof(tmp_score));
  if (raw_score != 0) {
    total_size += 1 + 8;
  }

  // int32 priority = 4;
  if (this->_internal_priority() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_priority());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ReqGoods::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ReqGoods::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ReqGoods::GetClassData() const { return &_class_data_; }

void ReqGoods::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ReqGoods *>(to)->MergeFrom(
      static_cast<const ReqGoods &>(from));
}


void ReqGoods::MergeFrom(const ReqGoods& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:srchRoute.goods_sort.ReqGoods)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  ext_.MergeFrom(from.ext_);
  tags_.MergeFrom(from.tags_);
  recall_channels_.MergeFrom(from.recall_channels_);
  goods_tag_value_.MergeFrom(from.goods_tag_value_);
  if (!from._internal_spu_id().empty()) {
    _internal_set_spu_id(from._internal_spu_id());
  }
  if (from._internal_goods_id() != 0) {
    _internal_set_goods_id(from._internal_goods_id());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_score = from._internal_score();
  uint64_t raw_score;
  memcpy(&raw_score, &tmp_score, sizeof(tmp_score));
  if (raw_score != 0) {
    _internal_set_score(from._internal_score());
  }
  if (from._internal_priority() != 0) {
    _internal_set_priority(from._internal_priority());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ReqGoods::CopyFrom(const ReqGoods& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:srchRoute.goods_sort.ReqGoods)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ReqGoods::IsInitialized() const {
  return true;
}

void ReqGoods::InternalSwap(ReqGoods* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ext_.InternalSwap(&other->ext_);
  tags_.InternalSwap(&other->tags_);
  recall_channels_.InternalSwap(&other->recall_channels_);
  goods_tag_value_.InternalSwap(&other->goods_tag_value_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &spu_id_, lhs_arena,
      &other->spu_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ReqGoods, priority_)
      + sizeof(ReqGoods::priority_)
      - PROTOBUF_FIELD_OFFSET(ReqGoods, goods_id_)>(
          reinterpret_cast<char*>(&goods_id_),
          reinterpret_cast<char*>(&other->goods_id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ReqGoods::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_getter, &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_once,
      file_level_metadata_proto_2fsrch_5fsort_5fplatform_2eproto[9]);
}

// ===================================================================

class GoodsTag::_Internal {
 public:
};

GoodsTag::GoodsTag(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  tag_value_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:srchRoute.goods_sort.GoodsTag)
}
GoodsTag::GoodsTag(const GoodsTag& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      tag_value_(from.tag_value_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:srchRoute.goods_sort.GoodsTag)
}

inline void GoodsTag::SharedCtor() {
}

GoodsTag::~GoodsTag() {
  // @@protoc_insertion_point(destructor:srchRoute.goods_sort.GoodsTag)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GoodsTag::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GoodsTag::ArenaDtor(void* object) {
  GoodsTag* _this = reinterpret_cast< GoodsTag* >(object);
  (void)_this;
}
void GoodsTag::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GoodsTag::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GoodsTag::Clear() {
// @@protoc_insertion_point(message_clear_start:srchRoute.goods_sort.GoodsTag)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  tag_value_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GoodsTag::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated int32 tag_value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_tag_value(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 8) {
          _internal_add_tag_value(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GoodsTag::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:srchRoute.goods_sort.GoodsTag)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int32 tag_value = 1;
  {
    int byte_size = _tag_value_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          1, _internal_tag_value(), byte_size, target);
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:srchRoute.goods_sort.GoodsTag)
  return target;
}

size_t GoodsTag::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:srchRoute.goods_sort.GoodsTag)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated int32 tag_value = 1;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->tag_value_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _tag_value_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GoodsTag::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GoodsTag::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GoodsTag::GetClassData() const { return &_class_data_; }

void GoodsTag::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GoodsTag *>(to)->MergeFrom(
      static_cast<const GoodsTag &>(from));
}


void GoodsTag::MergeFrom(const GoodsTag& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:srchRoute.goods_sort.GoodsTag)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  tag_value_.MergeFrom(from.tag_value_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GoodsTag::CopyFrom(const GoodsTag& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:srchRoute.goods_sort.GoodsTag)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GoodsTag::IsInitialized() const {
  return true;
}

void GoodsTag::InternalSwap(GoodsTag* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  tag_value_.InternalSwap(&other->tag_value_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GoodsTag::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_getter, &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_once,
      file_level_metadata_proto_2fsrch_5fsort_5fplatform_2eproto[10]);
}

// ===================================================================

ResultInfo_MarksEntry_DoNotUse::ResultInfo_MarksEntry_DoNotUse() {}
ResultInfo_MarksEntry_DoNotUse::ResultInfo_MarksEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void ResultInfo_MarksEntry_DoNotUse::MergeFrom(const ResultInfo_MarksEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata ResultInfo_MarksEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_getter, &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_once,
      file_level_metadata_proto_2fsrch_5fsort_5fplatform_2eproto[11]);
}

// ===================================================================

ResultInfo_RequestExtEntry_DoNotUse::ResultInfo_RequestExtEntry_DoNotUse() {}
ResultInfo_RequestExtEntry_DoNotUse::ResultInfo_RequestExtEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void ResultInfo_RequestExtEntry_DoNotUse::MergeFrom(const ResultInfo_RequestExtEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata ResultInfo_RequestExtEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_getter, &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_once,
      file_level_metadata_proto_2fsrch_5fsort_5fplatform_2eproto[12]);
}

// ===================================================================

ResultInfo_RequestBizExtEntry_DoNotUse::ResultInfo_RequestBizExtEntry_DoNotUse() {}
ResultInfo_RequestBizExtEntry_DoNotUse::ResultInfo_RequestBizExtEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void ResultInfo_RequestBizExtEntry_DoNotUse::MergeFrom(const ResultInfo_RequestBizExtEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata ResultInfo_RequestBizExtEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_getter, &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_once,
      file_level_metadata_proto_2fsrch_5fsort_5fplatform_2eproto[13]);
}

// ===================================================================

class ResultInfo::_Internal {
 public:
};

ResultInfo::ResultInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  result_(arena),
  marks_(arena),
  request_ext_(arena),
  request_biz_ext_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:srchRoute.goods_sort.ResultInfo)
}
ResultInfo::ResultInfo(const ResultInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      result_(from.result_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  marks_.MergeFrom(from.marks_);
  request_ext_.MergeFrom(from.request_ext_);
  request_biz_ext_.MergeFrom(from.request_biz_ext_);
  sort_status_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    sort_status_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_sort_status().empty()) {
    sort_status_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_sort_status(), 
      GetArenaForAllocation());
  }
  fallback_reason_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    fallback_reason_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_fallback_reason().empty()) {
    fallback_reason_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_fallback_reason(), 
      GetArenaForAllocation());
  }
  ::memcpy(&all_layer_success_, &from.all_layer_success_,
    static_cast<size_t>(reinterpret_cast<char*>(&fallback_) -
    reinterpret_cast<char*>(&all_layer_success_)) + sizeof(fallback_));
  // @@protoc_insertion_point(copy_constructor:srchRoute.goods_sort.ResultInfo)
}

inline void ResultInfo::SharedCtor() {
sort_status_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  sort_status_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
fallback_reason_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  fallback_reason_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&all_layer_success_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&fallback_) -
    reinterpret_cast<char*>(&all_layer_success_)) + sizeof(fallback_));
}

ResultInfo::~ResultInfo() {
  // @@protoc_insertion_point(destructor:srchRoute.goods_sort.ResultInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ResultInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  sort_status_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  fallback_reason_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ResultInfo::ArenaDtor(void* object) {
  ResultInfo* _this = reinterpret_cast< ResultInfo* >(object);
  (void)_this;
  _this->marks_. ~MapField();
  _this->request_ext_. ~MapField();
  _this->request_biz_ext_. ~MapField();
}
inline void ResultInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &ResultInfo::ArenaDtor);
  }
}
void ResultInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ResultInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:srchRoute.goods_sort.ResultInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  result_.Clear();
  marks_.Clear();
  request_ext_.Clear();
  request_biz_ext_.Clear();
  sort_status_.ClearToEmpty();
  fallback_reason_.ClearToEmpty();
  ::memset(&all_layer_success_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&fallback_) -
      reinterpret_cast<char*>(&all_layer_success_)) + sizeof(fallback_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ResultInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string sort_status = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_sort_status();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "srchRoute.goods_sort.ResultInfo.sort_status"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool all_layer_success = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          all_layer_success_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool fallback = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          fallback_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string fallback_reason = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_fallback_reason();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "srchRoute.goods_sort.ResultInfo.fallback_reason"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .srchRoute.goods_sort.ResGoods result = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_result(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<50>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<string, string> marks = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&marks_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<string, string> request_ext = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&request_ext_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<66>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<string, string> request_biz_ext = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&request_biz_ext_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<74>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ResultInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:srchRoute.goods_sort.ResultInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string sort_status = 2;
  if (!this->_internal_sort_status().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_sort_status().data(), static_cast<int>(this->_internal_sort_status().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "srchRoute.goods_sort.ResultInfo.sort_status");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_sort_status(), target);
  }

  // bool all_layer_success = 3;
  if (this->_internal_all_layer_success() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_all_layer_success(), target);
  }

  // bool fallback = 4;
  if (this->_internal_fallback() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_fallback(), target);
  }

  // string fallback_reason = 5;
  if (!this->_internal_fallback_reason().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_fallback_reason().data(), static_cast<int>(this->_internal_fallback_reason().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "srchRoute.goods_sort.ResultInfo.fallback_reason");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_fallback_reason(), target);
  }

  // repeated .srchRoute.goods_sort.ResGoods result = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_result_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(6, this->_internal_result(i), target, stream);
  }

  // map<string, string> marks = 7;
  if (!this->_internal_marks().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "srchRoute.goods_sort.ResultInfo.MarksEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "srchRoute.goods_sort.ResultInfo.MarksEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_marks().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_marks().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_marks().begin();
          it != this->_internal_marks().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = ResultInfo_MarksEntry_DoNotUse::Funcs::InternalSerialize(7, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_marks().begin();
          it != this->_internal_marks().end(); ++it) {
        target = ResultInfo_MarksEntry_DoNotUse::Funcs::InternalSerialize(7, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // map<string, string> request_ext = 8;
  if (!this->_internal_request_ext().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "srchRoute.goods_sort.ResultInfo.RequestExtEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "srchRoute.goods_sort.ResultInfo.RequestExtEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_request_ext().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_request_ext().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_request_ext().begin();
          it != this->_internal_request_ext().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = ResultInfo_RequestExtEntry_DoNotUse::Funcs::InternalSerialize(8, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_request_ext().begin();
          it != this->_internal_request_ext().end(); ++it) {
        target = ResultInfo_RequestExtEntry_DoNotUse::Funcs::InternalSerialize(8, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // map<string, string> request_biz_ext = 9;
  if (!this->_internal_request_biz_ext().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "srchRoute.goods_sort.ResultInfo.RequestBizExtEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "srchRoute.goods_sort.ResultInfo.RequestBizExtEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_request_biz_ext().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_request_biz_ext().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_request_biz_ext().begin();
          it != this->_internal_request_biz_ext().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = ResultInfo_RequestBizExtEntry_DoNotUse::Funcs::InternalSerialize(9, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_request_biz_ext().begin();
          it != this->_internal_request_biz_ext().end(); ++it) {
        target = ResultInfo_RequestBizExtEntry_DoNotUse::Funcs::InternalSerialize(9, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:srchRoute.goods_sort.ResultInfo)
  return target;
}

size_t ResultInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:srchRoute.goods_sort.ResultInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .srchRoute.goods_sort.ResGoods result = 6;
  total_size += 1UL * this->_internal_result_size();
  for (const auto& msg : this->result_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // map<string, string> marks = 7;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_marks_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_marks().begin();
      it != this->_internal_marks().end(); ++it) {
    total_size += ResultInfo_MarksEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // map<string, string> request_ext = 8;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_request_ext_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_request_ext().begin();
      it != this->_internal_request_ext().end(); ++it) {
    total_size += ResultInfo_RequestExtEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // map<string, string> request_biz_ext = 9;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_request_biz_ext_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_request_biz_ext().begin();
      it != this->_internal_request_biz_ext().end(); ++it) {
    total_size += ResultInfo_RequestBizExtEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // string sort_status = 2;
  if (!this->_internal_sort_status().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_sort_status());
  }

  // string fallback_reason = 5;
  if (!this->_internal_fallback_reason().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_fallback_reason());
  }

  // bool all_layer_success = 3;
  if (this->_internal_all_layer_success() != 0) {
    total_size += 1 + 1;
  }

  // bool fallback = 4;
  if (this->_internal_fallback() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ResultInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ResultInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ResultInfo::GetClassData() const { return &_class_data_; }

void ResultInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ResultInfo *>(to)->MergeFrom(
      static_cast<const ResultInfo &>(from));
}


void ResultInfo::MergeFrom(const ResultInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:srchRoute.goods_sort.ResultInfo)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  result_.MergeFrom(from.result_);
  marks_.MergeFrom(from.marks_);
  request_ext_.MergeFrom(from.request_ext_);
  request_biz_ext_.MergeFrom(from.request_biz_ext_);
  if (!from._internal_sort_status().empty()) {
    _internal_set_sort_status(from._internal_sort_status());
  }
  if (!from._internal_fallback_reason().empty()) {
    _internal_set_fallback_reason(from._internal_fallback_reason());
  }
  if (from._internal_all_layer_success() != 0) {
    _internal_set_all_layer_success(from._internal_all_layer_success());
  }
  if (from._internal_fallback() != 0) {
    _internal_set_fallback(from._internal_fallback());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ResultInfo::CopyFrom(const ResultInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:srchRoute.goods_sort.ResultInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ResultInfo::IsInitialized() const {
  return true;
}

void ResultInfo::InternalSwap(ResultInfo* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  result_.InternalSwap(&other->result_);
  marks_.InternalSwap(&other->marks_);
  request_ext_.InternalSwap(&other->request_ext_);
  request_biz_ext_.InternalSwap(&other->request_biz_ext_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &sort_status_, lhs_arena,
      &other->sort_status_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &fallback_reason_, lhs_arena,
      &other->fallback_reason_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ResultInfo, fallback_)
      + sizeof(ResultInfo::fallback_)
      - PROTOBUF_FIELD_OFFSET(ResultInfo, all_layer_success_)>(
          reinterpret_cast<char*>(&all_layer_success_),
          reinterpret_cast<char*>(&other->all_layer_success_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ResultInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_getter, &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_once,
      file_level_metadata_proto_2fsrch_5fsort_5fplatform_2eproto[14]);
}

// ===================================================================

ResGoods_ExtEntry_DoNotUse::ResGoods_ExtEntry_DoNotUse() {}
ResGoods_ExtEntry_DoNotUse::ResGoods_ExtEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void ResGoods_ExtEntry_DoNotUse::MergeFrom(const ResGoods_ExtEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata ResGoods_ExtEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_getter, &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_once,
      file_level_metadata_proto_2fsrch_5fsort_5fplatform_2eproto[15]);
}

// ===================================================================

ResGoods_BizExtEntry_DoNotUse::ResGoods_BizExtEntry_DoNotUse() {}
ResGoods_BizExtEntry_DoNotUse::ResGoods_BizExtEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void ResGoods_BizExtEntry_DoNotUse::MergeFrom(const ResGoods_BizExtEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata ResGoods_BizExtEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_getter, &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_once,
      file_level_metadata_proto_2fsrch_5fsort_5fplatform_2eproto[16]);
}

// ===================================================================

class ResGoods::_Internal {
 public:
};

ResGoods::ResGoods(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  ext_(arena),
  biz_ext_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:srchRoute.goods_sort.ResGoods)
}
ResGoods::ResGoods(const ResGoods& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ext_.MergeFrom(from.ext_);
  biz_ext_.MergeFrom(from.biz_ext_);
  ::memcpy(&goods_id_, &from.goods_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&priority_) -
    reinterpret_cast<char*>(&goods_id_)) + sizeof(priority_));
  // @@protoc_insertion_point(copy_constructor:srchRoute.goods_sort.ResGoods)
}

inline void ResGoods::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&goods_id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&priority_) -
    reinterpret_cast<char*>(&goods_id_)) + sizeof(priority_));
}

ResGoods::~ResGoods() {
  // @@protoc_insertion_point(destructor:srchRoute.goods_sort.ResGoods)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ResGoods::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void ResGoods::ArenaDtor(void* object) {
  ResGoods* _this = reinterpret_cast< ResGoods* >(object);
  (void)_this;
  _this->ext_. ~MapField();
  _this->biz_ext_. ~MapField();
}
inline void ResGoods::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &ResGoods::ArenaDtor);
  }
}
void ResGoods::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ResGoods::Clear() {
// @@protoc_insertion_point(message_clear_start:srchRoute.goods_sort.ResGoods)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ext_.Clear();
  biz_ext_.Clear();
  ::memset(&goods_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&priority_) -
      reinterpret_cast<char*>(&goods_id_)) + sizeof(priority_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ResGoods::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 goods_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          goods_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double score = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          score_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // int32 priority = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          priority_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // map<string, string> ext = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&ext_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<string, string> biz_ext = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&biz_ext_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ResGoods::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:srchRoute.goods_sort.ResGoods)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 goods_id = 1;
  if (this->_internal_goods_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_goods_id(), target);
  }

  // double score = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_score = this->_internal_score();
  uint64_t raw_score;
  memcpy(&raw_score, &tmp_score, sizeof(tmp_score));
  if (raw_score != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_score(), target);
  }

  // int32 priority = 3;
  if (this->_internal_priority() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_priority(), target);
  }

  // map<string, string> ext = 4;
  if (!this->_internal_ext().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "srchRoute.goods_sort.ResGoods.ExtEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "srchRoute.goods_sort.ResGoods.ExtEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_ext().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_ext().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_ext().begin();
          it != this->_internal_ext().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = ResGoods_ExtEntry_DoNotUse::Funcs::InternalSerialize(4, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_ext().begin();
          it != this->_internal_ext().end(); ++it) {
        target = ResGoods_ExtEntry_DoNotUse::Funcs::InternalSerialize(4, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // map<string, string> biz_ext = 5;
  if (!this->_internal_biz_ext().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "srchRoute.goods_sort.ResGoods.BizExtEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "srchRoute.goods_sort.ResGoods.BizExtEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_biz_ext().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_biz_ext().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_biz_ext().begin();
          it != this->_internal_biz_ext().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = ResGoods_BizExtEntry_DoNotUse::Funcs::InternalSerialize(5, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_biz_ext().begin();
          it != this->_internal_biz_ext().end(); ++it) {
        target = ResGoods_BizExtEntry_DoNotUse::Funcs::InternalSerialize(5, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:srchRoute.goods_sort.ResGoods)
  return target;
}

size_t ResGoods::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:srchRoute.goods_sort.ResGoods)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, string> ext = 4;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_ext_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_ext().begin();
      it != this->_internal_ext().end(); ++it) {
    total_size += ResGoods_ExtEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // map<string, string> biz_ext = 5;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_biz_ext_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_biz_ext().begin();
      it != this->_internal_biz_ext().end(); ++it) {
    total_size += ResGoods_BizExtEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // uint64 goods_id = 1;
  if (this->_internal_goods_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_goods_id());
  }

  // double score = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_score = this->_internal_score();
  uint64_t raw_score;
  memcpy(&raw_score, &tmp_score, sizeof(tmp_score));
  if (raw_score != 0) {
    total_size += 1 + 8;
  }

  // int32 priority = 3;
  if (this->_internal_priority() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_priority());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ResGoods::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ResGoods::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ResGoods::GetClassData() const { return &_class_data_; }

void ResGoods::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ResGoods *>(to)->MergeFrom(
      static_cast<const ResGoods &>(from));
}


void ResGoods::MergeFrom(const ResGoods& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:srchRoute.goods_sort.ResGoods)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  ext_.MergeFrom(from.ext_);
  biz_ext_.MergeFrom(from.biz_ext_);
  if (from._internal_goods_id() != 0) {
    _internal_set_goods_id(from._internal_goods_id());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_score = from._internal_score();
  uint64_t raw_score;
  memcpy(&raw_score, &tmp_score, sizeof(tmp_score));
  if (raw_score != 0) {
    _internal_set_score(from._internal_score());
  }
  if (from._internal_priority() != 0) {
    _internal_set_priority(from._internal_priority());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ResGoods::CopyFrom(const ResGoods& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:srchRoute.goods_sort.ResGoods)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ResGoods::IsInitialized() const {
  return true;
}

void ResGoods::InternalSwap(ResGoods* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ext_.InternalSwap(&other->ext_);
  biz_ext_.InternalSwap(&other->biz_ext_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ResGoods, priority_)
      + sizeof(ResGoods::priority_)
      - PROTOBUF_FIELD_OFFSET(ResGoods, goods_id_)>(
          reinterpret_cast<char*>(&goods_id_),
          reinterpret_cast<char*>(&other->goods_id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ResGoods::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_getter, &descriptor_table_proto_2fsrch_5fsort_5fplatform_2eproto_once,
      file_level_metadata_proto_2fsrch_5fsort_5fplatform_2eproto[17]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace goods_sort
}  // namespace srchRoute
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::srchRoute::goods_sort::PbSortResponse* Arena::CreateMaybeMessage< ::srchRoute::goods_sort::PbSortResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::srchRoute::goods_sort::PbSortResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::srchRoute::goods_sort::PbSortRequest_UserInfoEntry_DoNotUse* Arena::CreateMaybeMessage< ::srchRoute::goods_sort::PbSortRequest_UserInfoEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::srchRoute::goods_sort::PbSortRequest_UserInfoEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::srchRoute::goods_sort::PbSortRequest_ContextEntry_DoNotUse* Arena::CreateMaybeMessage< ::srchRoute::goods_sort::PbSortRequest_ContextEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::srchRoute::goods_sort::PbSortRequest_ContextEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::srchRoute::goods_sort::PbSortRequest_RecallChannelsMetasEntry_DoNotUse* Arena::CreateMaybeMessage< ::srchRoute::goods_sort::PbSortRequest_RecallChannelsMetasEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::srchRoute::goods_sort::PbSortRequest_RecallChannelsMetasEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::srchRoute::goods_sort::PbSortRequest_JsonRuleIdEntry_DoNotUse* Arena::CreateMaybeMessage< ::srchRoute::goods_sort::PbSortRequest_JsonRuleIdEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::srchRoute::goods_sort::PbSortRequest_JsonRuleIdEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::srchRoute::goods_sort::PbSortRequest* Arena::CreateMaybeMessage< ::srchRoute::goods_sort::PbSortRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::srchRoute::goods_sort::PbSortRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::srchRoute::goods_sort::ChannelMetas_ChannelMetasEntry_DoNotUse* Arena::CreateMaybeMessage< ::srchRoute::goods_sort::ChannelMetas_ChannelMetasEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::srchRoute::goods_sort::ChannelMetas_ChannelMetasEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::srchRoute::goods_sort::ChannelMetas* Arena::CreateMaybeMessage< ::srchRoute::goods_sort::ChannelMetas >(Arena* arena) {
  return Arena::CreateMessageInternal< ::srchRoute::goods_sort::ChannelMetas >(arena);
}
template<> PROTOBUF_NOINLINE ::srchRoute::goods_sort::ReqGoods_ExtEntry_DoNotUse* Arena::CreateMaybeMessage< ::srchRoute::goods_sort::ReqGoods_ExtEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::srchRoute::goods_sort::ReqGoods_ExtEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::srchRoute::goods_sort::ReqGoods* Arena::CreateMaybeMessage< ::srchRoute::goods_sort::ReqGoods >(Arena* arena) {
  return Arena::CreateMessageInternal< ::srchRoute::goods_sort::ReqGoods >(arena);
}
template<> PROTOBUF_NOINLINE ::srchRoute::goods_sort::GoodsTag* Arena::CreateMaybeMessage< ::srchRoute::goods_sort::GoodsTag >(Arena* arena) {
  return Arena::CreateMessageInternal< ::srchRoute::goods_sort::GoodsTag >(arena);
}
template<> PROTOBUF_NOINLINE ::srchRoute::goods_sort::ResultInfo_MarksEntry_DoNotUse* Arena::CreateMaybeMessage< ::srchRoute::goods_sort::ResultInfo_MarksEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::srchRoute::goods_sort::ResultInfo_MarksEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::srchRoute::goods_sort::ResultInfo_RequestExtEntry_DoNotUse* Arena::CreateMaybeMessage< ::srchRoute::goods_sort::ResultInfo_RequestExtEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::srchRoute::goods_sort::ResultInfo_RequestExtEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::srchRoute::goods_sort::ResultInfo_RequestBizExtEntry_DoNotUse* Arena::CreateMaybeMessage< ::srchRoute::goods_sort::ResultInfo_RequestBizExtEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::srchRoute::goods_sort::ResultInfo_RequestBizExtEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::srchRoute::goods_sort::ResultInfo* Arena::CreateMaybeMessage< ::srchRoute::goods_sort::ResultInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::srchRoute::goods_sort::ResultInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::srchRoute::goods_sort::ResGoods_ExtEntry_DoNotUse* Arena::CreateMaybeMessage< ::srchRoute::goods_sort::ResGoods_ExtEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::srchRoute::goods_sort::ResGoods_ExtEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::srchRoute::goods_sort::ResGoods_BizExtEntry_DoNotUse* Arena::CreateMaybeMessage< ::srchRoute::goods_sort::ResGoods_BizExtEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::srchRoute::goods_sort::ResGoods_BizExtEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::srchRoute::goods_sort::ResGoods* Arena::CreateMaybeMessage< ::srchRoute::goods_sort::ResGoods >(Arena* arena) {
  return Arena::CreateMessageInternal< ::srchRoute::goods_sort::ResGoods >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
