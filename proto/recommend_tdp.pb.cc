// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/recommend_tdp.proto

#include "proto/recommend_tdp.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fapi_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<8> scc_info_RecommendRequest_proto_2frecommend_5fapi_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fdmp_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_UserProfile_proto_2frecommend_5fdmp_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5ffeature_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<4> scc_info_Feature_proto_2frecommend_5ffeature_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5frank_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<3> scc_info_RankRequest_proto_2frecommend_5frank_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5ftdp_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_RecommendResponse_ExtraCtxFeatureEntry_DoNotUse_proto_2frecommend_5ftdp_2eproto;
namespace abc {
namespace recommend_plt {
namespace tdp {
class RecommendRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RecommendRequest> _instance;
} _RecommendRequest_default_instance_;
class RecommendResponse_ExtraCtxFeatureEntry_DoNotUseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RecommendResponse_ExtraCtxFeatureEntry_DoNotUse> _instance;
} _RecommendResponse_ExtraCtxFeatureEntry_DoNotUse_default_instance_;
class RecommendResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RecommendResponse> _instance;
} _RecommendResponse_default_instance_;
}  // namespace tdp
}  // namespace recommend_plt
}  // namespace abc
static void InitDefaultsscc_info_RecommendRequest_proto_2frecommend_5ftdp_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::tdp::_RecommendRequest_default_instance_;
    new (ptr) ::abc::recommend_plt::tdp::RecommendRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::tdp::RecommendRequest::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_RecommendRequest_proto_2frecommend_5ftdp_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsscc_info_RecommendRequest_proto_2frecommend_5ftdp_2eproto}, {
      &scc_info_RecommendRequest_proto_2frecommend_5fapi_2eproto.base,
      &scc_info_UserProfile_proto_2frecommend_5fdmp_2eproto.base,}};

static void InitDefaultsscc_info_RecommendResponse_proto_2frecommend_5ftdp_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::tdp::_RecommendResponse_default_instance_;
    new (ptr) ::abc::recommend_plt::tdp::RecommendResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::tdp::RecommendResponse::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_RecommendResponse_proto_2frecommend_5ftdp_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsscc_info_RecommendResponse_proto_2frecommend_5ftdp_2eproto}, {
      &scc_info_RankRequest_proto_2frecommend_5frank_2eproto.base,
      &scc_info_RecommendResponse_ExtraCtxFeatureEntry_DoNotUse_proto_2frecommend_5ftdp_2eproto.base,}};

static void InitDefaultsscc_info_RecommendResponse_ExtraCtxFeatureEntry_DoNotUse_proto_2frecommend_5ftdp_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::tdp::_RecommendResponse_ExtraCtxFeatureEntry_DoNotUse_default_instance_;
    new (ptr) ::abc::recommend_plt::tdp::RecommendResponse_ExtraCtxFeatureEntry_DoNotUse();
  }
  ::abc::recommend_plt::tdp::RecommendResponse_ExtraCtxFeatureEntry_DoNotUse::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_RecommendResponse_ExtraCtxFeatureEntry_DoNotUse_proto_2frecommend_5ftdp_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsscc_info_RecommendResponse_ExtraCtxFeatureEntry_DoNotUse_proto_2frecommend_5ftdp_2eproto}, {
      &scc_info_Feature_proto_2frecommend_5ffeature_2eproto.base,}};

static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_proto_2frecommend_5ftdp_2eproto[3];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_proto_2frecommend_5ftdp_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_proto_2frecommend_5ftdp_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_proto_2frecommend_5ftdp_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::tdp::RecommendRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::tdp::RecommendRequest, rec_trace_id_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::tdp::RecommendRequest, recommend_request_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::tdp::RecommendRequest, user_profile_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::tdp::RecommendResponse_ExtraCtxFeatureEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::tdp::RecommendResponse_ExtraCtxFeatureEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::tdp::RecommendResponse_ExtraCtxFeatureEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::tdp::RecommendResponse_ExtraCtxFeatureEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::tdp::RecommendResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::tdp::RecommendResponse, rec_trace_id_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::tdp::RecommendResponse, rank_request_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::tdp::RecommendResponse, error_code_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::tdp::RecommendResponse, extra_ctx_feature_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::abc::recommend_plt::tdp::RecommendRequest)},
  { 8, 15, sizeof(::abc::recommend_plt::tdp::RecommendResponse_ExtraCtxFeatureEntry_DoNotUse)},
  { 17, -1, sizeof(::abc::recommend_plt::tdp::RecommendResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::tdp::_RecommendRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::tdp::_RecommendResponse_ExtraCtxFeatureEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::tdp::_RecommendResponse_default_instance_),
};

const char descriptor_table_protodef_proto_2frecommend_5ftdp_2eproto[] =
  "\n\031proto/recommend_tdp.proto\022\025abc.recomme"
  "nd_plt.tdp\032\031proto/recommend_api.proto\032\034p"
  "roto/recommend_common.proto\032\031proto/recom"
  "mend_dmp.proto\032\032proto/recommend_rank.pro"
  "to\032\035proto/recommend_feature.proto\"\246\001\n\020Re"
  "commendRequest\022\024\n\014rec_trace_id\030\001 \001(\t\022B\n\021"
  "recommend_request\030\002 \001(\0132\'.abc.recommend_"
  "plt.api.RecommendRequest\0228\n\014user_profile"
  "\030\003 \001(\0132\".abc.recommend_plt.dmp.UserProfi"
  "le\"\317\002\n\021RecommendResponse\022\024\n\014rec_trace_id"
  "\030\001 \001(\t\0229\n\014rank_request\030\002 \001(\0132#.abc.recom"
  "mend_plt.rank.RankRequest\0227\n\nerror_code\030"
  "\003 \001(\0162#.abc.recommend_plt.common.ErrorCo"
  "de\022X\n\021extra_ctx_feature\030\004 \003(\0132=.abc.reco"
  "mmend_plt.tdp.RecommendResponse.ExtraCtx"
  "FeatureEntry\032V\n\024ExtraCtxFeatureEntry\022\013\n\003"
  "key\030\001 \001(\t\022-\n\005value\030\002 \001(\0132\036.abc.recommend"
  "_plt.fmp.Feature:\0028\0012v\n\020RecommendService"
  "\022b\n\013doRecommend\022\'.abc.recommend_plt.tdp."
  "RecommendRequest\032(.abc.recommend_plt.tdp"
  ".RecommendResponse\"\000b\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_proto_2frecommend_5ftdp_2eproto_deps[5] = {
  &::descriptor_table_proto_2frecommend_5fapi_2eproto,
  &::descriptor_table_proto_2frecommend_5fcommon_2eproto,
  &::descriptor_table_proto_2frecommend_5fdmp_2eproto,
  &::descriptor_table_proto_2frecommend_5ffeature_2eproto,
  &::descriptor_table_proto_2frecommend_5frank_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_proto_2frecommend_5ftdp_2eproto_sccs[3] = {
  &scc_info_RecommendRequest_proto_2frecommend_5ftdp_2eproto.base,
  &scc_info_RecommendResponse_proto_2frecommend_5ftdp_2eproto.base,
  &scc_info_RecommendResponse_ExtraCtxFeatureEntry_DoNotUse_proto_2frecommend_5ftdp_2eproto.base,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_proto_2frecommend_5ftdp_2eproto_once;
static bool descriptor_table_proto_2frecommend_5ftdp_2eproto_initialized = false;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2frecommend_5ftdp_2eproto = {
  &descriptor_table_proto_2frecommend_5ftdp_2eproto_initialized, descriptor_table_protodef_proto_2frecommend_5ftdp_2eproto, "proto/recommend_tdp.proto", 828,
  &descriptor_table_proto_2frecommend_5ftdp_2eproto_once, descriptor_table_proto_2frecommend_5ftdp_2eproto_sccs, descriptor_table_proto_2frecommend_5ftdp_2eproto_deps, 3, 5,
  schemas, file_default_instances, TableStruct_proto_2frecommend_5ftdp_2eproto::offsets,
  file_level_metadata_proto_2frecommend_5ftdp_2eproto, 3, file_level_enum_descriptors_proto_2frecommend_5ftdp_2eproto, file_level_service_descriptors_proto_2frecommend_5ftdp_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_proto_2frecommend_5ftdp_2eproto = (  ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_proto_2frecommend_5ftdp_2eproto), true);
namespace abc {
namespace recommend_plt {
namespace tdp {

// ===================================================================

void RecommendRequest::InitAsDefaultInstance() {
  ::abc::recommend_plt::tdp::_RecommendRequest_default_instance_._instance.get_mutable()->recommend_request_ = const_cast< ::abc::recommend_plt::api::RecommendRequest*>(
      ::abc::recommend_plt::api::RecommendRequest::internal_default_instance());
  ::abc::recommend_plt::tdp::_RecommendRequest_default_instance_._instance.get_mutable()->user_profile_ = const_cast< ::abc::recommend_plt::dmp::UserProfile*>(
      ::abc::recommend_plt::dmp::UserProfile::internal_default_instance());
}
class RecommendRequest::HasBitSetters {
 public:
  static const ::abc::recommend_plt::api::RecommendRequest& recommend_request(const RecommendRequest* msg);
  static const ::abc::recommend_plt::dmp::UserProfile& user_profile(const RecommendRequest* msg);
};

const ::abc::recommend_plt::api::RecommendRequest&
RecommendRequest::HasBitSetters::recommend_request(const RecommendRequest* msg) {
  return *msg->recommend_request_;
}
const ::abc::recommend_plt::dmp::UserProfile&
RecommendRequest::HasBitSetters::user_profile(const RecommendRequest* msg) {
  return *msg->user_profile_;
}
void RecommendRequest::clear_recommend_request() {
  if (GetArenaNoVirtual() == nullptr && recommend_request_ != nullptr) {
    delete recommend_request_;
  }
  recommend_request_ = nullptr;
}
void RecommendRequest::clear_user_profile() {
  if (GetArenaNoVirtual() == nullptr && user_profile_ != nullptr) {
    delete user_profile_;
  }
  user_profile_ = nullptr;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RecommendRequest::kRecTraceIdFieldNumber;
const int RecommendRequest::kRecommendRequestFieldNumber;
const int RecommendRequest::kUserProfileFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RecommendRequest::RecommendRequest()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.tdp.RecommendRequest)
}
RecommendRequest::RecommendRequest(const RecommendRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  rec_trace_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.rec_trace_id().size() > 0) {
    rec_trace_id_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.rec_trace_id_);
  }
  if (from.has_recommend_request()) {
    recommend_request_ = new ::abc::recommend_plt::api::RecommendRequest(*from.recommend_request_);
  } else {
    recommend_request_ = nullptr;
  }
  if (from.has_user_profile()) {
    user_profile_ = new ::abc::recommend_plt::dmp::UserProfile(*from.user_profile_);
  } else {
    user_profile_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.tdp.RecommendRequest)
}

void RecommendRequest::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RecommendRequest_proto_2frecommend_5ftdp_2eproto.base);
  rec_trace_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&recommend_request_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&user_profile_) -
      reinterpret_cast<char*>(&recommend_request_)) + sizeof(user_profile_));
}

RecommendRequest::~RecommendRequest() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.tdp.RecommendRequest)
  SharedDtor();
}

void RecommendRequest::SharedDtor() {
  rec_trace_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete recommend_request_;
  if (this != internal_default_instance()) delete user_profile_;
}

void RecommendRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RecommendRequest& RecommendRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RecommendRequest_proto_2frecommend_5ftdp_2eproto.base);
  return *internal_default_instance();
}


void RecommendRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.tdp.RecommendRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  rec_trace_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == nullptr && recommend_request_ != nullptr) {
    delete recommend_request_;
  }
  recommend_request_ = nullptr;
  if (GetArenaNoVirtual() == nullptr && user_profile_ != nullptr) {
    delete user_profile_;
  }
  user_profile_ = nullptr;
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* RecommendRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string rec_trace_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_rec_trace_id(), ptr, ctx, "abc.recommend_plt.tdp.RecommendRequest.rec_trace_id");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .abc.recommend_plt.api.RecommendRequest recommend_request = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(mutable_recommend_request(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .abc.recommend_plt.dmp.UserProfile user_profile = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(mutable_user_profile(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool RecommendRequest::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.tdp.RecommendRequest)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string rec_trace_id = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_rec_trace_id()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->rec_trace_id().data(), static_cast<int>(this->rec_trace_id().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.tdp.RecommendRequest.rec_trace_id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .abc.recommend_plt.api.RecommendRequest recommend_request = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_recommend_request()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .abc.recommend_plt.dmp.UserProfile user_profile = 3;
      case 3: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (26 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_user_profile()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.tdp.RecommendRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.tdp.RecommendRequest)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void RecommendRequest::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.tdp.RecommendRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string rec_trace_id = 1;
  if (this->rec_trace_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->rec_trace_id().data(), static_cast<int>(this->rec_trace_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.tdp.RecommendRequest.rec_trace_id");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->rec_trace_id(), output);
  }

  // .abc.recommend_plt.api.RecommendRequest recommend_request = 2;
  if (this->has_recommend_request()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, HasBitSetters::recommend_request(this), output);
  }

  // .abc.recommend_plt.dmp.UserProfile user_profile = 3;
  if (this->has_user_profile()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, HasBitSetters::user_profile(this), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.tdp.RecommendRequest)
}

::PROTOBUF_NAMESPACE_ID::uint8* RecommendRequest::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.tdp.RecommendRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string rec_trace_id = 1;
  if (this->rec_trace_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->rec_trace_id().data(), static_cast<int>(this->rec_trace_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.tdp.RecommendRequest.rec_trace_id");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        1, this->rec_trace_id(), target);
  }

  // .abc.recommend_plt.api.RecommendRequest recommend_request = 2;
  if (this->has_recommend_request()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, HasBitSetters::recommend_request(this), target);
  }

  // .abc.recommend_plt.dmp.UserProfile user_profile = 3;
  if (this->has_user_profile()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, HasBitSetters::user_profile(this), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.tdp.RecommendRequest)
  return target;
}

size_t RecommendRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.tdp.RecommendRequest)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string rec_trace_id = 1;
  if (this->rec_trace_id().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->rec_trace_id());
  }

  // .abc.recommend_plt.api.RecommendRequest recommend_request = 2;
  if (this->has_recommend_request()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *recommend_request_);
  }

  // .abc.recommend_plt.dmp.UserProfile user_profile = 3;
  if (this->has_user_profile()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *user_profile_);
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RecommendRequest::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.tdp.RecommendRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const RecommendRequest* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RecommendRequest>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.tdp.RecommendRequest)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.tdp.RecommendRequest)
    MergeFrom(*source);
  }
}

void RecommendRequest::MergeFrom(const RecommendRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.tdp.RecommendRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.rec_trace_id().size() > 0) {

    rec_trace_id_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.rec_trace_id_);
  }
  if (from.has_recommend_request()) {
    mutable_recommend_request()->::abc::recommend_plt::api::RecommendRequest::MergeFrom(from.recommend_request());
  }
  if (from.has_user_profile()) {
    mutable_user_profile()->::abc::recommend_plt::dmp::UserProfile::MergeFrom(from.user_profile());
  }
}

void RecommendRequest::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.tdp.RecommendRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RecommendRequest::CopyFrom(const RecommendRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.tdp.RecommendRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RecommendRequest::IsInitialized() const {
  return true;
}

void RecommendRequest::Swap(RecommendRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RecommendRequest::InternalSwap(RecommendRequest* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  rec_trace_id_.Swap(&other->rec_trace_id_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(recommend_request_, other->recommend_request_);
  swap(user_profile_, other->user_profile_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RecommendRequest::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

RecommendResponse_ExtraCtxFeatureEntry_DoNotUse::RecommendResponse_ExtraCtxFeatureEntry_DoNotUse() {}
RecommendResponse_ExtraCtxFeatureEntry_DoNotUse::RecommendResponse_ExtraCtxFeatureEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void RecommendResponse_ExtraCtxFeatureEntry_DoNotUse::MergeFrom(const RecommendResponse_ExtraCtxFeatureEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata RecommendResponse_ExtraCtxFeatureEntry_DoNotUse::GetMetadata() const {
  return GetMetadataStatic();
}
void RecommendResponse_ExtraCtxFeatureEntry_DoNotUse::MergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::Message& other) {
  ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom(other);
}


// ===================================================================

void RecommendResponse::InitAsDefaultInstance() {
  ::abc::recommend_plt::tdp::_RecommendResponse_default_instance_._instance.get_mutable()->rank_request_ = const_cast< ::abc::recommend_plt::rank::RankRequest*>(
      ::abc::recommend_plt::rank::RankRequest::internal_default_instance());
}
class RecommendResponse::HasBitSetters {
 public:
  static const ::abc::recommend_plt::rank::RankRequest& rank_request(const RecommendResponse* msg);
};

const ::abc::recommend_plt::rank::RankRequest&
RecommendResponse::HasBitSetters::rank_request(const RecommendResponse* msg) {
  return *msg->rank_request_;
}
void RecommendResponse::clear_rank_request() {
  if (GetArenaNoVirtual() == nullptr && rank_request_ != nullptr) {
    delete rank_request_;
  }
  rank_request_ = nullptr;
}
void RecommendResponse::clear_extra_ctx_feature() {
  extra_ctx_feature_.Clear();
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RecommendResponse::kRecTraceIdFieldNumber;
const int RecommendResponse::kRankRequestFieldNumber;
const int RecommendResponse::kErrorCodeFieldNumber;
const int RecommendResponse::kExtraCtxFeatureFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RecommendResponse::RecommendResponse()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.tdp.RecommendResponse)
}
RecommendResponse::RecommendResponse(const RecommendResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  extra_ctx_feature_.MergeFrom(from.extra_ctx_feature_);
  rec_trace_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.rec_trace_id().size() > 0) {
    rec_trace_id_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.rec_trace_id_);
  }
  if (from.has_rank_request()) {
    rank_request_ = new ::abc::recommend_plt::rank::RankRequest(*from.rank_request_);
  } else {
    rank_request_ = nullptr;
  }
  error_code_ = from.error_code_;
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.tdp.RecommendResponse)
}

void RecommendResponse::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RecommendResponse_proto_2frecommend_5ftdp_2eproto.base);
  rec_trace_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&rank_request_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&error_code_) -
      reinterpret_cast<char*>(&rank_request_)) + sizeof(error_code_));
}

RecommendResponse::~RecommendResponse() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.tdp.RecommendResponse)
  SharedDtor();
}

void RecommendResponse::SharedDtor() {
  rec_trace_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete rank_request_;
}

void RecommendResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RecommendResponse& RecommendResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RecommendResponse_proto_2frecommend_5ftdp_2eproto.base);
  return *internal_default_instance();
}


void RecommendResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.tdp.RecommendResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  extra_ctx_feature_.Clear();
  rec_trace_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == nullptr && rank_request_ != nullptr) {
    delete rank_request_;
  }
  rank_request_ = nullptr;
  error_code_ = 0;
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* RecommendResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string rec_trace_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_rec_trace_id(), ptr, ctx, "abc.recommend_plt.tdp.RecommendResponse.rec_trace_id");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .abc.recommend_plt.rank.RankRequest rank_request = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(mutable_rank_request(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .abc.recommend_plt.common.ErrorCode error_code = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
          set_error_code(static_cast<::abc::recommend_plt::common::ErrorCode>(val));
        } else goto handle_unusual;
        continue;
      // map<string, .abc.recommend_plt.fmp.Feature> extra_ctx_feature = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&extra_ctx_feature_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 34);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool RecommendResponse::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.tdp.RecommendResponse)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string rec_trace_id = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_rec_trace_id()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->rec_trace_id().data(), static_cast<int>(this->rec_trace_id().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.tdp.RecommendResponse.rec_trace_id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .abc.recommend_plt.rank.RankRequest rank_request = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_rank_request()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .abc.recommend_plt.common.ErrorCode error_code = 3;
      case 3: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (24 & 0xFF)) {
          int value = 0;
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   int, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_error_code(static_cast< ::abc::recommend_plt::common::ErrorCode >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, .abc.recommend_plt.fmp.Feature> extra_ctx_feature = 4;
      case 4: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (34 & 0xFF)) {
          RecommendResponse_ExtraCtxFeatureEntry_DoNotUse::Parser< ::PROTOBUF_NAMESPACE_ID::internal::MapField<
              RecommendResponse_ExtraCtxFeatureEntry_DoNotUse,
              std::string, ::abc::recommend_plt::fmp::Feature,
              ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
              ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature > > parser(&extra_ctx_feature_);
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.tdp.RecommendResponse.ExtraCtxFeatureEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.tdp.RecommendResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.tdp.RecommendResponse)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void RecommendResponse::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.tdp.RecommendResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string rec_trace_id = 1;
  if (this->rec_trace_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->rec_trace_id().data(), static_cast<int>(this->rec_trace_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.tdp.RecommendResponse.rec_trace_id");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->rec_trace_id(), output);
  }

  // .abc.recommend_plt.rank.RankRequest rank_request = 2;
  if (this->has_rank_request()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, HasBitSetters::rank_request(this), output);
  }

  // .abc.recommend_plt.common.ErrorCode error_code = 3;
  if (this->error_code() != 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnum(
      3, this->error_code(), output);
  }

  // map<string, .abc.recommend_plt.fmp.Feature> extra_ctx_feature = 4;
  if (!this->extra_ctx_feature().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.tdp.RecommendResponse.ExtraCtxFeatureEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->extra_ctx_feature().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->extra_ctx_feature().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >::const_iterator
          it = this->extra_ctx_feature().begin();
          it != this->extra_ctx_feature().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        RecommendResponse_ExtraCtxFeatureEntry_DoNotUse::MapEntryWrapper entry(nullptr, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second);
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(4, entry, output);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >::const_iterator
          it = this->extra_ctx_feature().begin();
          it != this->extra_ctx_feature().end(); ++it) {
        RecommendResponse_ExtraCtxFeatureEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(4, entry, output);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.tdp.RecommendResponse)
}

::PROTOBUF_NAMESPACE_ID::uint8* RecommendResponse::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.tdp.RecommendResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string rec_trace_id = 1;
  if (this->rec_trace_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->rec_trace_id().data(), static_cast<int>(this->rec_trace_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.tdp.RecommendResponse.rec_trace_id");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        1, this->rec_trace_id(), target);
  }

  // .abc.recommend_plt.rank.RankRequest rank_request = 2;
  if (this->has_rank_request()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, HasBitSetters::rank_request(this), target);
  }

  // .abc.recommend_plt.common.ErrorCode error_code = 3;
  if (this->error_code() != 0) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->error_code(), target);
  }

  // map<string, .abc.recommend_plt.fmp.Feature> extra_ctx_feature = 4;
  if (!this->extra_ctx_feature().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.tdp.RecommendResponse.ExtraCtxFeatureEntry.key");
      }
    };

    if (false &&
        this->extra_ctx_feature().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->extra_ctx_feature().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >::const_iterator
          it = this->extra_ctx_feature().begin();
          it != this->extra_ctx_feature().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        RecommendResponse_ExtraCtxFeatureEntry_DoNotUse::MapEntryWrapper entry(nullptr, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second);
        target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::InternalWriteMessageNoVirtualToArray(4, entry, target);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >::const_iterator
          it = this->extra_ctx_feature().begin();
          it != this->extra_ctx_feature().end(); ++it) {
        RecommendResponse_ExtraCtxFeatureEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
        target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::InternalWriteMessageNoVirtualToArray(4, entry, target);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.tdp.RecommendResponse)
  return target;
}

size_t RecommendResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.tdp.RecommendResponse)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, .abc.recommend_plt.fmp.Feature> extra_ctx_feature = 4;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->extra_ctx_feature_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >::const_iterator
      it = this->extra_ctx_feature().begin();
      it != this->extra_ctx_feature().end(); ++it) {
    RecommendResponse_ExtraCtxFeatureEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        MessageSizeNoVirtual(entry);
  }

  // string rec_trace_id = 1;
  if (this->rec_trace_id().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->rec_trace_id());
  }

  // .abc.recommend_plt.rank.RankRequest rank_request = 2;
  if (this->has_rank_request()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *rank_request_);
  }

  // .abc.recommend_plt.common.ErrorCode error_code = 3;
  if (this->error_code() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->error_code());
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RecommendResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.tdp.RecommendResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const RecommendResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RecommendResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.tdp.RecommendResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.tdp.RecommendResponse)
    MergeFrom(*source);
  }
}

void RecommendResponse::MergeFrom(const RecommendResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.tdp.RecommendResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  extra_ctx_feature_.MergeFrom(from.extra_ctx_feature_);
  if (from.rec_trace_id().size() > 0) {

    rec_trace_id_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.rec_trace_id_);
  }
  if (from.has_rank_request()) {
    mutable_rank_request()->::abc::recommend_plt::rank::RankRequest::MergeFrom(from.rank_request());
  }
  if (from.error_code() != 0) {
    set_error_code(from.error_code());
  }
}

void RecommendResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.tdp.RecommendResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RecommendResponse::CopyFrom(const RecommendResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.tdp.RecommendResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RecommendResponse::IsInitialized() const {
  return true;
}

void RecommendResponse::Swap(RecommendResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RecommendResponse::InternalSwap(RecommendResponse* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  extra_ctx_feature_.Swap(&other->extra_ctx_feature_);
  rec_trace_id_.Swap(&other->rec_trace_id_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(rank_request_, other->rank_request_);
  swap(error_code_, other->error_code_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RecommendResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tdp
}  // namespace recommend_plt
}  // namespace abc
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::tdp::RecommendRequest* Arena::CreateMaybeMessage< ::abc::recommend_plt::tdp::RecommendRequest >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::tdp::RecommendRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::tdp::RecommendResponse_ExtraCtxFeatureEntry_DoNotUse* Arena::CreateMaybeMessage< ::abc::recommend_plt::tdp::RecommendResponse_ExtraCtxFeatureEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::tdp::RecommendResponse_ExtraCtxFeatureEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::tdp::RecommendResponse* Arena::CreateMaybeMessage< ::abc::recommend_plt::tdp::RecommendResponse >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::tdp::RecommendResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
