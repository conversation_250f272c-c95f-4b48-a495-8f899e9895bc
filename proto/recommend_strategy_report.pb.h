// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/recommend_strategy_report.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_proto_2frecommend_5fstrategy_5freport_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_proto_2frecommend_5fstrategy_5freport_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3008000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3008000 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fstrategy_5freport_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_proto_2frecommend_5fstrategy_5freport_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[4]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2frecommend_5fstrategy_5freport_2eproto;
namespace abc {
namespace recommend_plt {
namespace strategy {
class RecStrategyReport;
class RecStrategyReportDefaultTypeInternal;
extern RecStrategyReportDefaultTypeInternal _RecStrategyReport_default_instance_;
class RecStrategyReport_RecServiceChange;
class RecStrategyReport_RecServiceChangeDefaultTypeInternal;
extern RecStrategyReport_RecServiceChangeDefaultTypeInternal _RecStrategyReport_RecServiceChange_default_instance_;
class RecStrategyReport_RecStrategyFea;
class RecStrategyReport_RecStrategyFeaDefaultTypeInternal;
extern RecStrategyReport_RecStrategyFeaDefaultTypeInternal _RecStrategyReport_RecStrategyFea_default_instance_;
class RecStrategyReport_RecStrategyMetric;
class RecStrategyReport_RecStrategyMetricDefaultTypeInternal;
extern RecStrategyReport_RecStrategyMetricDefaultTypeInternal _RecStrategyReport_RecStrategyMetric_default_instance_;
}  // namespace strategy
}  // namespace recommend_plt
}  // namespace abc
PROTOBUF_NAMESPACE_OPEN
template<> ::abc::recommend_plt::strategy::RecStrategyReport* Arena::CreateMaybeMessage<::abc::recommend_plt::strategy::RecStrategyReport>(Arena*);
template<> ::abc::recommend_plt::strategy::RecStrategyReport_RecServiceChange* Arena::CreateMaybeMessage<::abc::recommend_plt::strategy::RecStrategyReport_RecServiceChange>(Arena*);
template<> ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyFea* Arena::CreateMaybeMessage<::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyFea>(Arena*);
template<> ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyMetric* Arena::CreateMaybeMessage<::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyMetric>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace abc {
namespace recommend_plt {
namespace strategy {

// ===================================================================

class RecStrategyReport_RecStrategyFea :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea) */ {
 public:
  RecStrategyReport_RecStrategyFea();
  virtual ~RecStrategyReport_RecStrategyFea();

  RecStrategyReport_RecStrategyFea(const RecStrategyReport_RecStrategyFea& from);
  RecStrategyReport_RecStrategyFea(RecStrategyReport_RecStrategyFea&& from) noexcept
    : RecStrategyReport_RecStrategyFea() {
    *this = ::std::move(from);
  }

  inline RecStrategyReport_RecStrategyFea& operator=(const RecStrategyReport_RecStrategyFea& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecStrategyReport_RecStrategyFea& operator=(RecStrategyReport_RecStrategyFea&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RecStrategyReport_RecStrategyFea& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RecStrategyReport_RecStrategyFea* internal_default_instance() {
    return reinterpret_cast<const RecStrategyReport_RecStrategyFea*>(
               &_RecStrategyReport_RecStrategyFea_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(RecStrategyReport_RecStrategyFea* other);
  friend void swap(RecStrategyReport_RecStrategyFea& a, RecStrategyReport_RecStrategyFea& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RecStrategyReport_RecStrategyFea* New() const final {
    return CreateMaybeMessage<RecStrategyReport_RecStrategyFea>(nullptr);
  }

  RecStrategyReport_RecStrategyFea* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RecStrategyReport_RecStrategyFea>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RecStrategyReport_RecStrategyFea& from);
  void MergeFrom(const RecStrategyReport_RecStrategyFea& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecStrategyReport_RecStrategyFea* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fstrategy_5freport_2eproto);
    return ::descriptor_table_proto_2frecommend_5fstrategy_5freport_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string scene_id = 2;
  void clear_scene_id();
  static const int kSceneIdFieldNumber = 2;
  const std::string& scene_id() const;
  void set_scene_id(const std::string& value);
  void set_scene_id(std::string&& value);
  void set_scene_id(const char* value);
  void set_scene_id(const char* value, size_t size);
  std::string* mutable_scene_id();
  std::string* release_scene_id();
  void set_allocated_scene_id(std::string* scene_id);

  // string item_source = 3;
  void clear_item_source();
  static const int kItemSourceFieldNumber = 3;
  const std::string& item_source() const;
  void set_item_source(const std::string& value);
  void set_item_source(std::string&& value);
  void set_item_source(const char* value);
  void set_item_source(const char* value, size_t size);
  std::string* mutable_item_source();
  std::string* release_item_source();
  void set_allocated_item_source(std::string* item_source);

  // string step_id = 4;
  void clear_step_id();
  static const int kStepIdFieldNumber = 4;
  const std::string& step_id() const;
  void set_step_id(const std::string& value);
  void set_step_id(std::string&& value);
  void set_step_id(const char* value);
  void set_step_id(const char* value, size_t size);
  std::string* mutable_step_id();
  std::string* release_step_id();
  void set_allocated_step_id(std::string* step_id);

  // string strategy_id = 5;
  void clear_strategy_id();
  static const int kStrategyIdFieldNumber = 5;
  const std::string& strategy_id() const;
  void set_strategy_id(const std::string& value);
  void set_strategy_id(std::string&& value);
  void set_strategy_id(const char* value);
  void set_strategy_id(const char* value, size_t size);
  std::string* mutable_strategy_id();
  std::string* release_strategy_id();
  void set_allocated_strategy_id(std::string* strategy_id);

  // string item_fea = 6;
  void clear_item_fea();
  static const int kItemFeaFieldNumber = 6;
  const std::string& item_fea() const;
  void set_item_fea(const std::string& value);
  void set_item_fea(std::string&& value);
  void set_item_fea(const char* value);
  void set_item_fea(const char* value, size_t size);
  std::string* mutable_item_fea();
  std::string* release_item_fea();
  void set_allocated_item_fea(std::string* item_fea);

  // int32 count = 1;
  void clear_count();
  static const int kCountFieldNumber = 1;
  ::PROTOBUF_NAMESPACE_ID::int32 count() const;
  void set_count(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 user_fea = 7;
  void clear_user_fea();
  static const int kUserFeaFieldNumber = 7;
  ::PROTOBUF_NAMESPACE_ID::int32 user_fea() const;
  void set_user_fea(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr scene_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr item_source_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr step_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr strategy_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr item_fea_;
  ::PROTOBUF_NAMESPACE_ID::int32 count_;
  ::PROTOBUF_NAMESPACE_ID::int32 user_fea_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fstrategy_5freport_2eproto;
};
// -------------------------------------------------------------------

class RecStrategyReport_RecStrategyMetric :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric) */ {
 public:
  RecStrategyReport_RecStrategyMetric();
  virtual ~RecStrategyReport_RecStrategyMetric();

  RecStrategyReport_RecStrategyMetric(const RecStrategyReport_RecStrategyMetric& from);
  RecStrategyReport_RecStrategyMetric(RecStrategyReport_RecStrategyMetric&& from) noexcept
    : RecStrategyReport_RecStrategyMetric() {
    *this = ::std::move(from);
  }

  inline RecStrategyReport_RecStrategyMetric& operator=(const RecStrategyReport_RecStrategyMetric& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecStrategyReport_RecStrategyMetric& operator=(RecStrategyReport_RecStrategyMetric&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RecStrategyReport_RecStrategyMetric& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RecStrategyReport_RecStrategyMetric* internal_default_instance() {
    return reinterpret_cast<const RecStrategyReport_RecStrategyMetric*>(
               &_RecStrategyReport_RecStrategyMetric_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void Swap(RecStrategyReport_RecStrategyMetric* other);
  friend void swap(RecStrategyReport_RecStrategyMetric& a, RecStrategyReport_RecStrategyMetric& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RecStrategyReport_RecStrategyMetric* New() const final {
    return CreateMaybeMessage<RecStrategyReport_RecStrategyMetric>(nullptr);
  }

  RecStrategyReport_RecStrategyMetric* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RecStrategyReport_RecStrategyMetric>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RecStrategyReport_RecStrategyMetric& from);
  void MergeFrom(const RecStrategyReport_RecStrategyMetric& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecStrategyReport_RecStrategyMetric* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fstrategy_5freport_2eproto);
    return ::descriptor_table_proto_2frecommend_5fstrategy_5freport_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string scene_id = 1;
  void clear_scene_id();
  static const int kSceneIdFieldNumber = 1;
  const std::string& scene_id() const;
  void set_scene_id(const std::string& value);
  void set_scene_id(std::string&& value);
  void set_scene_id(const char* value);
  void set_scene_id(const char* value, size_t size);
  std::string* mutable_scene_id();
  std::string* release_scene_id();
  void set_allocated_scene_id(std::string* scene_id);

  // string item_source = 2;
  void clear_item_source();
  static const int kItemSourceFieldNumber = 2;
  const std::string& item_source() const;
  void set_item_source(const std::string& value);
  void set_item_source(std::string&& value);
  void set_item_source(const char* value);
  void set_item_source(const char* value, size_t size);
  std::string* mutable_item_source();
  std::string* release_item_source();
  void set_allocated_item_source(std::string* item_source);

  // string step_id = 3;
  void clear_step_id();
  static const int kStepIdFieldNumber = 3;
  const std::string& step_id() const;
  void set_step_id(const std::string& value);
  void set_step_id(std::string&& value);
  void set_step_id(const char* value);
  void set_step_id(const char* value, size_t size);
  std::string* mutable_step_id();
  std::string* release_step_id();
  void set_allocated_step_id(std::string* step_id);

  // string metric_name = 4;
  void clear_metric_name();
  static const int kMetricNameFieldNumber = 4;
  const std::string& metric_name() const;
  void set_metric_name(const std::string& value);
  void set_metric_name(std::string&& value);
  void set_metric_name(const char* value);
  void set_metric_name(const char* value, size_t size);
  std::string* mutable_metric_name();
  std::string* release_metric_name();
  void set_allocated_metric_name(std::string* metric_name);

  // string label = 5;
  void clear_label();
  static const int kLabelFieldNumber = 5;
  const std::string& label() const;
  void set_label(const std::string& value);
  void set_label(std::string&& value);
  void set_label(const char* value);
  void set_label(const char* value, size_t size);
  std::string* mutable_label();
  std::string* release_label();
  void set_allocated_label(std::string* label);

  // string service_name = 8;
  void clear_service_name();
  static const int kServiceNameFieldNumber = 8;
  const std::string& service_name() const;
  void set_service_name(const std::string& value);
  void set_service_name(std::string&& value);
  void set_service_name(const char* value);
  void set_service_name(const char* value, size_t size);
  std::string* mutable_service_name();
  std::string* release_service_name();
  void set_allocated_service_name(std::string* service_name);

  // string poskey = 10;
  void clear_poskey();
  static const int kPoskeyFieldNumber = 10;
  const std::string& poskey() const;
  void set_poskey(const std::string& value);
  void set_poskey(std::string&& value);
  void set_poskey(const char* value);
  void set_poskey(const char* value, size_t size);
  std::string* mutable_poskey();
  std::string* release_poskey();
  void set_allocated_poskey(std::string* poskey);

  // int64 count = 6;
  void clear_count();
  static const int kCountFieldNumber = 6;
  ::PROTOBUF_NAMESPACE_ID::int64 count() const;
  void set_count(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 sum = 7;
  void clear_sum();
  static const int kSumFieldNumber = 7;
  ::PROTOBUF_NAMESPACE_ID::int64 sum() const;
  void set_sum(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 ts = 9;
  void clear_ts();
  static const int kTsFieldNumber = 9;
  ::PROTOBUF_NAMESPACE_ID::int64 ts() const;
  void set_ts(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr scene_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr item_source_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr step_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr metric_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr label_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr service_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr poskey_;
  ::PROTOBUF_NAMESPACE_ID::int64 count_;
  ::PROTOBUF_NAMESPACE_ID::int64 sum_;
  ::PROTOBUF_NAMESPACE_ID::int64 ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fstrategy_5freport_2eproto;
};
// -------------------------------------------------------------------

class RecStrategyReport_RecServiceChange :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange) */ {
 public:
  RecStrategyReport_RecServiceChange();
  virtual ~RecStrategyReport_RecServiceChange();

  RecStrategyReport_RecServiceChange(const RecStrategyReport_RecServiceChange& from);
  RecStrategyReport_RecServiceChange(RecStrategyReport_RecServiceChange&& from) noexcept
    : RecStrategyReport_RecServiceChange() {
    *this = ::std::move(from);
  }

  inline RecStrategyReport_RecServiceChange& operator=(const RecStrategyReport_RecServiceChange& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecStrategyReport_RecServiceChange& operator=(RecStrategyReport_RecServiceChange&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RecStrategyReport_RecServiceChange& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RecStrategyReport_RecServiceChange* internal_default_instance() {
    return reinterpret_cast<const RecStrategyReport_RecServiceChange*>(
               &_RecStrategyReport_RecServiceChange_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void Swap(RecStrategyReport_RecServiceChange* other);
  friend void swap(RecStrategyReport_RecServiceChange& a, RecStrategyReport_RecServiceChange& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RecStrategyReport_RecServiceChange* New() const final {
    return CreateMaybeMessage<RecStrategyReport_RecServiceChange>(nullptr);
  }

  RecStrategyReport_RecServiceChange* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RecStrategyReport_RecServiceChange>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RecStrategyReport_RecServiceChange& from);
  void MergeFrom(const RecStrategyReport_RecServiceChange& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecStrategyReport_RecServiceChange* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fstrategy_5freport_2eproto);
    return ::descriptor_table_proto_2frecommend_5fstrategy_5freport_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string change_time = 1;
  void clear_change_time();
  static const int kChangeTimeFieldNumber = 1;
  const std::string& change_time() const;
  void set_change_time(const std::string& value);
  void set_change_time(std::string&& value);
  void set_change_time(const char* value);
  void set_change_time(const char* value, size_t size);
  std::string* mutable_change_time();
  std::string* release_change_time();
  void set_allocated_change_time(std::string* change_time);

  // string service = 2;
  void clear_service();
  static const int kServiceFieldNumber = 2;
  const std::string& service() const;
  void set_service(const std::string& value);
  void set_service(std::string&& value);
  void set_service(const char* value);
  void set_service(const char* value, size_t size);
  std::string* mutable_service();
  std::string* release_service();
  void set_allocated_service(std::string* service);

  // string before_change = 4;
  void clear_before_change();
  static const int kBeforeChangeFieldNumber = 4;
  const std::string& before_change() const;
  void set_before_change(const std::string& value);
  void set_before_change(std::string&& value);
  void set_before_change(const char* value);
  void set_before_change(const char* value, size_t size);
  std::string* mutable_before_change();
  std::string* release_before_change();
  void set_allocated_before_change(std::string* before_change);

  // string after_change = 5;
  void clear_after_change();
  static const int kAfterChangeFieldNumber = 5;
  const std::string& after_change() const;
  void set_after_change(const std::string& value);
  void set_after_change(std::string&& value);
  void set_after_change(const char* value);
  void set_after_change(const char* value, size_t size);
  std::string* mutable_after_change();
  std::string* release_after_change();
  void set_allocated_after_change(std::string* after_change);

  // string change_detail = 6;
  void clear_change_detail();
  static const int kChangeDetailFieldNumber = 6;
  const std::string& change_detail() const;
  void set_change_detail(const std::string& value);
  void set_change_detail(std::string&& value);
  void set_change_detail(const char* value);
  void set_change_detail(const char* value, size_t size);
  std::string* mutable_change_detail();
  std::string* release_change_detail();
  void set_allocated_change_detail(std::string* change_detail);

  // int32 change_type = 3;
  void clear_change_type();
  static const int kChangeTypeFieldNumber = 3;
  ::PROTOBUF_NAMESPACE_ID::int32 change_type() const;
  void set_change_type(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr change_time_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr service_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr before_change_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr after_change_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr change_detail_;
  ::PROTOBUF_NAMESPACE_ID::int32 change_type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fstrategy_5freport_2eproto;
};
// -------------------------------------------------------------------

class RecStrategyReport :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.strategy.RecStrategyReport) */ {
 public:
  RecStrategyReport();
  virtual ~RecStrategyReport();

  RecStrategyReport(const RecStrategyReport& from);
  RecStrategyReport(RecStrategyReport&& from) noexcept
    : RecStrategyReport() {
    *this = ::std::move(from);
  }

  inline RecStrategyReport& operator=(const RecStrategyReport& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecStrategyReport& operator=(RecStrategyReport&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RecStrategyReport& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RecStrategyReport* internal_default_instance() {
    return reinterpret_cast<const RecStrategyReport*>(
               &_RecStrategyReport_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void Swap(RecStrategyReport* other);
  friend void swap(RecStrategyReport& a, RecStrategyReport& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RecStrategyReport* New() const final {
    return CreateMaybeMessage<RecStrategyReport>(nullptr);
  }

  RecStrategyReport* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RecStrategyReport>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RecStrategyReport& from);
  void MergeFrom(const RecStrategyReport& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecStrategyReport* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.strategy.RecStrategyReport";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fstrategy_5freport_2eproto);
    return ::descriptor_table_proto_2frecommend_5fstrategy_5freport_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef RecStrategyReport_RecStrategyFea RecStrategyFea;
  typedef RecStrategyReport_RecStrategyMetric RecStrategyMetric;
  typedef RecStrategyReport_RecServiceChange RecServiceChange;

  // accessors -------------------------------------------------------

  // repeated .abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea rec_strategy_fea = 1;
  int rec_strategy_fea_size() const;
  void clear_rec_strategy_fea();
  static const int kRecStrategyFeaFieldNumber = 1;
  ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyFea* mutable_rec_strategy_fea(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyFea >*
      mutable_rec_strategy_fea();
  const ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyFea& rec_strategy_fea(int index) const;
  ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyFea* add_rec_strategy_fea();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyFea >&
      rec_strategy_fea() const;

  // repeated .abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric rec_strategy_metric = 2;
  int rec_strategy_metric_size() const;
  void clear_rec_strategy_metric();
  static const int kRecStrategyMetricFieldNumber = 2;
  ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyMetric* mutable_rec_strategy_metric(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyMetric >*
      mutable_rec_strategy_metric();
  const ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyMetric& rec_strategy_metric(int index) const;
  ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyMetric* add_rec_strategy_metric();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyMetric >&
      rec_strategy_metric() const;

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.strategy.RecStrategyReport)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyFea > rec_strategy_fea_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyMetric > rec_strategy_metric_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fstrategy_5freport_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// RecStrategyReport_RecStrategyFea

// int32 count = 1;
inline void RecStrategyReport_RecStrategyFea::clear_count() {
  count_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RecStrategyReport_RecStrategyFea::count() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.count)
  return count_;
}
inline void RecStrategyReport_RecStrategyFea::set_count(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  count_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.count)
}

// string scene_id = 2;
inline void RecStrategyReport_RecStrategyFea::clear_scene_id() {
  scene_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecStrategyReport_RecStrategyFea::scene_id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.scene_id)
  return scene_id_.GetNoArena();
}
inline void RecStrategyReport_RecStrategyFea::set_scene_id(const std::string& value) {
  
  scene_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.scene_id)
}
inline void RecStrategyReport_RecStrategyFea::set_scene_id(std::string&& value) {
  
  scene_id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.scene_id)
}
inline void RecStrategyReport_RecStrategyFea::set_scene_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  scene_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.scene_id)
}
inline void RecStrategyReport_RecStrategyFea::set_scene_id(const char* value, size_t size) {
  
  scene_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.scene_id)
}
inline std::string* RecStrategyReport_RecStrategyFea::mutable_scene_id() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.scene_id)
  return scene_id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecStrategyReport_RecStrategyFea::release_scene_id() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.scene_id)
  
  return scene_id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecStrategyReport_RecStrategyFea::set_allocated_scene_id(std::string* scene_id) {
  if (scene_id != nullptr) {
    
  } else {
    
  }
  scene_id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), scene_id);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.scene_id)
}

// string item_source = 3;
inline void RecStrategyReport_RecStrategyFea::clear_item_source() {
  item_source_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecStrategyReport_RecStrategyFea::item_source() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.item_source)
  return item_source_.GetNoArena();
}
inline void RecStrategyReport_RecStrategyFea::set_item_source(const std::string& value) {
  
  item_source_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.item_source)
}
inline void RecStrategyReport_RecStrategyFea::set_item_source(std::string&& value) {
  
  item_source_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.item_source)
}
inline void RecStrategyReport_RecStrategyFea::set_item_source(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  item_source_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.item_source)
}
inline void RecStrategyReport_RecStrategyFea::set_item_source(const char* value, size_t size) {
  
  item_source_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.item_source)
}
inline std::string* RecStrategyReport_RecStrategyFea::mutable_item_source() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.item_source)
  return item_source_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecStrategyReport_RecStrategyFea::release_item_source() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.item_source)
  
  return item_source_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecStrategyReport_RecStrategyFea::set_allocated_item_source(std::string* item_source) {
  if (item_source != nullptr) {
    
  } else {
    
  }
  item_source_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), item_source);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.item_source)
}

// string step_id = 4;
inline void RecStrategyReport_RecStrategyFea::clear_step_id() {
  step_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecStrategyReport_RecStrategyFea::step_id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.step_id)
  return step_id_.GetNoArena();
}
inline void RecStrategyReport_RecStrategyFea::set_step_id(const std::string& value) {
  
  step_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.step_id)
}
inline void RecStrategyReport_RecStrategyFea::set_step_id(std::string&& value) {
  
  step_id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.step_id)
}
inline void RecStrategyReport_RecStrategyFea::set_step_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  step_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.step_id)
}
inline void RecStrategyReport_RecStrategyFea::set_step_id(const char* value, size_t size) {
  
  step_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.step_id)
}
inline std::string* RecStrategyReport_RecStrategyFea::mutable_step_id() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.step_id)
  return step_id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecStrategyReport_RecStrategyFea::release_step_id() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.step_id)
  
  return step_id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecStrategyReport_RecStrategyFea::set_allocated_step_id(std::string* step_id) {
  if (step_id != nullptr) {
    
  } else {
    
  }
  step_id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), step_id);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.step_id)
}

// string strategy_id = 5;
inline void RecStrategyReport_RecStrategyFea::clear_strategy_id() {
  strategy_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecStrategyReport_RecStrategyFea::strategy_id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.strategy_id)
  return strategy_id_.GetNoArena();
}
inline void RecStrategyReport_RecStrategyFea::set_strategy_id(const std::string& value) {
  
  strategy_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.strategy_id)
}
inline void RecStrategyReport_RecStrategyFea::set_strategy_id(std::string&& value) {
  
  strategy_id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.strategy_id)
}
inline void RecStrategyReport_RecStrategyFea::set_strategy_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  strategy_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.strategy_id)
}
inline void RecStrategyReport_RecStrategyFea::set_strategy_id(const char* value, size_t size) {
  
  strategy_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.strategy_id)
}
inline std::string* RecStrategyReport_RecStrategyFea::mutable_strategy_id() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.strategy_id)
  return strategy_id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecStrategyReport_RecStrategyFea::release_strategy_id() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.strategy_id)
  
  return strategy_id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecStrategyReport_RecStrategyFea::set_allocated_strategy_id(std::string* strategy_id) {
  if (strategy_id != nullptr) {
    
  } else {
    
  }
  strategy_id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), strategy_id);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.strategy_id)
}

// string item_fea = 6;
inline void RecStrategyReport_RecStrategyFea::clear_item_fea() {
  item_fea_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecStrategyReport_RecStrategyFea::item_fea() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.item_fea)
  return item_fea_.GetNoArena();
}
inline void RecStrategyReport_RecStrategyFea::set_item_fea(const std::string& value) {
  
  item_fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.item_fea)
}
inline void RecStrategyReport_RecStrategyFea::set_item_fea(std::string&& value) {
  
  item_fea_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.item_fea)
}
inline void RecStrategyReport_RecStrategyFea::set_item_fea(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  item_fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.item_fea)
}
inline void RecStrategyReport_RecStrategyFea::set_item_fea(const char* value, size_t size) {
  
  item_fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.item_fea)
}
inline std::string* RecStrategyReport_RecStrategyFea::mutable_item_fea() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.item_fea)
  return item_fea_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecStrategyReport_RecStrategyFea::release_item_fea() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.item_fea)
  
  return item_fea_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecStrategyReport_RecStrategyFea::set_allocated_item_fea(std::string* item_fea) {
  if (item_fea != nullptr) {
    
  } else {
    
  }
  item_fea_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), item_fea);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.item_fea)
}

// int32 user_fea = 7;
inline void RecStrategyReport_RecStrategyFea::clear_user_fea() {
  user_fea_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RecStrategyReport_RecStrategyFea::user_fea() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.user_fea)
  return user_fea_;
}
inline void RecStrategyReport_RecStrategyFea::set_user_fea(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  user_fea_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea.user_fea)
}

// -------------------------------------------------------------------

// RecStrategyReport_RecStrategyMetric

// string scene_id = 1;
inline void RecStrategyReport_RecStrategyMetric::clear_scene_id() {
  scene_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecStrategyReport_RecStrategyMetric::scene_id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.scene_id)
  return scene_id_.GetNoArena();
}
inline void RecStrategyReport_RecStrategyMetric::set_scene_id(const std::string& value) {
  
  scene_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.scene_id)
}
inline void RecStrategyReport_RecStrategyMetric::set_scene_id(std::string&& value) {
  
  scene_id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.scene_id)
}
inline void RecStrategyReport_RecStrategyMetric::set_scene_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  scene_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.scene_id)
}
inline void RecStrategyReport_RecStrategyMetric::set_scene_id(const char* value, size_t size) {
  
  scene_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.scene_id)
}
inline std::string* RecStrategyReport_RecStrategyMetric::mutable_scene_id() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.scene_id)
  return scene_id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecStrategyReport_RecStrategyMetric::release_scene_id() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.scene_id)
  
  return scene_id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecStrategyReport_RecStrategyMetric::set_allocated_scene_id(std::string* scene_id) {
  if (scene_id != nullptr) {
    
  } else {
    
  }
  scene_id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), scene_id);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.scene_id)
}

// string item_source = 2;
inline void RecStrategyReport_RecStrategyMetric::clear_item_source() {
  item_source_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecStrategyReport_RecStrategyMetric::item_source() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.item_source)
  return item_source_.GetNoArena();
}
inline void RecStrategyReport_RecStrategyMetric::set_item_source(const std::string& value) {
  
  item_source_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.item_source)
}
inline void RecStrategyReport_RecStrategyMetric::set_item_source(std::string&& value) {
  
  item_source_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.item_source)
}
inline void RecStrategyReport_RecStrategyMetric::set_item_source(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  item_source_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.item_source)
}
inline void RecStrategyReport_RecStrategyMetric::set_item_source(const char* value, size_t size) {
  
  item_source_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.item_source)
}
inline std::string* RecStrategyReport_RecStrategyMetric::mutable_item_source() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.item_source)
  return item_source_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecStrategyReport_RecStrategyMetric::release_item_source() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.item_source)
  
  return item_source_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecStrategyReport_RecStrategyMetric::set_allocated_item_source(std::string* item_source) {
  if (item_source != nullptr) {
    
  } else {
    
  }
  item_source_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), item_source);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.item_source)
}

// string step_id = 3;
inline void RecStrategyReport_RecStrategyMetric::clear_step_id() {
  step_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecStrategyReport_RecStrategyMetric::step_id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.step_id)
  return step_id_.GetNoArena();
}
inline void RecStrategyReport_RecStrategyMetric::set_step_id(const std::string& value) {
  
  step_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.step_id)
}
inline void RecStrategyReport_RecStrategyMetric::set_step_id(std::string&& value) {
  
  step_id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.step_id)
}
inline void RecStrategyReport_RecStrategyMetric::set_step_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  step_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.step_id)
}
inline void RecStrategyReport_RecStrategyMetric::set_step_id(const char* value, size_t size) {
  
  step_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.step_id)
}
inline std::string* RecStrategyReport_RecStrategyMetric::mutable_step_id() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.step_id)
  return step_id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecStrategyReport_RecStrategyMetric::release_step_id() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.step_id)
  
  return step_id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecStrategyReport_RecStrategyMetric::set_allocated_step_id(std::string* step_id) {
  if (step_id != nullptr) {
    
  } else {
    
  }
  step_id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), step_id);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.step_id)
}

// string metric_name = 4;
inline void RecStrategyReport_RecStrategyMetric::clear_metric_name() {
  metric_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecStrategyReport_RecStrategyMetric::metric_name() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.metric_name)
  return metric_name_.GetNoArena();
}
inline void RecStrategyReport_RecStrategyMetric::set_metric_name(const std::string& value) {
  
  metric_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.metric_name)
}
inline void RecStrategyReport_RecStrategyMetric::set_metric_name(std::string&& value) {
  
  metric_name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.metric_name)
}
inline void RecStrategyReport_RecStrategyMetric::set_metric_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  metric_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.metric_name)
}
inline void RecStrategyReport_RecStrategyMetric::set_metric_name(const char* value, size_t size) {
  
  metric_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.metric_name)
}
inline std::string* RecStrategyReport_RecStrategyMetric::mutable_metric_name() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.metric_name)
  return metric_name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecStrategyReport_RecStrategyMetric::release_metric_name() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.metric_name)
  
  return metric_name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecStrategyReport_RecStrategyMetric::set_allocated_metric_name(std::string* metric_name) {
  if (metric_name != nullptr) {
    
  } else {
    
  }
  metric_name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), metric_name);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.metric_name)
}

// string label = 5;
inline void RecStrategyReport_RecStrategyMetric::clear_label() {
  label_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecStrategyReport_RecStrategyMetric::label() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.label)
  return label_.GetNoArena();
}
inline void RecStrategyReport_RecStrategyMetric::set_label(const std::string& value) {
  
  label_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.label)
}
inline void RecStrategyReport_RecStrategyMetric::set_label(std::string&& value) {
  
  label_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.label)
}
inline void RecStrategyReport_RecStrategyMetric::set_label(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  label_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.label)
}
inline void RecStrategyReport_RecStrategyMetric::set_label(const char* value, size_t size) {
  
  label_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.label)
}
inline std::string* RecStrategyReport_RecStrategyMetric::mutable_label() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.label)
  return label_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecStrategyReport_RecStrategyMetric::release_label() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.label)
  
  return label_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecStrategyReport_RecStrategyMetric::set_allocated_label(std::string* label) {
  if (label != nullptr) {
    
  } else {
    
  }
  label_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), label);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.label)
}

// int64 count = 6;
inline void RecStrategyReport_RecStrategyMetric::clear_count() {
  count_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RecStrategyReport_RecStrategyMetric::count() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.count)
  return count_;
}
inline void RecStrategyReport_RecStrategyMetric::set_count(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  count_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.count)
}

// int64 sum = 7;
inline void RecStrategyReport_RecStrategyMetric::clear_sum() {
  sum_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RecStrategyReport_RecStrategyMetric::sum() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.sum)
  return sum_;
}
inline void RecStrategyReport_RecStrategyMetric::set_sum(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  sum_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.sum)
}

// string service_name = 8;
inline void RecStrategyReport_RecStrategyMetric::clear_service_name() {
  service_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecStrategyReport_RecStrategyMetric::service_name() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.service_name)
  return service_name_.GetNoArena();
}
inline void RecStrategyReport_RecStrategyMetric::set_service_name(const std::string& value) {
  
  service_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.service_name)
}
inline void RecStrategyReport_RecStrategyMetric::set_service_name(std::string&& value) {
  
  service_name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.service_name)
}
inline void RecStrategyReport_RecStrategyMetric::set_service_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  service_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.service_name)
}
inline void RecStrategyReport_RecStrategyMetric::set_service_name(const char* value, size_t size) {
  
  service_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.service_name)
}
inline std::string* RecStrategyReport_RecStrategyMetric::mutable_service_name() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.service_name)
  return service_name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecStrategyReport_RecStrategyMetric::release_service_name() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.service_name)
  
  return service_name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecStrategyReport_RecStrategyMetric::set_allocated_service_name(std::string* service_name) {
  if (service_name != nullptr) {
    
  } else {
    
  }
  service_name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), service_name);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.service_name)
}

// int64 ts = 9;
inline void RecStrategyReport_RecStrategyMetric::clear_ts() {
  ts_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RecStrategyReport_RecStrategyMetric::ts() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.ts)
  return ts_;
}
inline void RecStrategyReport_RecStrategyMetric::set_ts(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  ts_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.ts)
}

// string poskey = 10;
inline void RecStrategyReport_RecStrategyMetric::clear_poskey() {
  poskey_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecStrategyReport_RecStrategyMetric::poskey() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.poskey)
  return poskey_.GetNoArena();
}
inline void RecStrategyReport_RecStrategyMetric::set_poskey(const std::string& value) {
  
  poskey_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.poskey)
}
inline void RecStrategyReport_RecStrategyMetric::set_poskey(std::string&& value) {
  
  poskey_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.poskey)
}
inline void RecStrategyReport_RecStrategyMetric::set_poskey(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  poskey_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.poskey)
}
inline void RecStrategyReport_RecStrategyMetric::set_poskey(const char* value, size_t size) {
  
  poskey_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.poskey)
}
inline std::string* RecStrategyReport_RecStrategyMetric::mutable_poskey() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.poskey)
  return poskey_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecStrategyReport_RecStrategyMetric::release_poskey() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.poskey)
  
  return poskey_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecStrategyReport_RecStrategyMetric::set_allocated_poskey(std::string* poskey) {
  if (poskey != nullptr) {
    
  } else {
    
  }
  poskey_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), poskey);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric.poskey)
}

// -------------------------------------------------------------------

// RecStrategyReport_RecServiceChange

// string change_time = 1;
inline void RecStrategyReport_RecServiceChange::clear_change_time() {
  change_time_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecStrategyReport_RecServiceChange::change_time() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.change_time)
  return change_time_.GetNoArena();
}
inline void RecStrategyReport_RecServiceChange::set_change_time(const std::string& value) {
  
  change_time_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.change_time)
}
inline void RecStrategyReport_RecServiceChange::set_change_time(std::string&& value) {
  
  change_time_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.change_time)
}
inline void RecStrategyReport_RecServiceChange::set_change_time(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  change_time_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.change_time)
}
inline void RecStrategyReport_RecServiceChange::set_change_time(const char* value, size_t size) {
  
  change_time_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.change_time)
}
inline std::string* RecStrategyReport_RecServiceChange::mutable_change_time() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.change_time)
  return change_time_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecStrategyReport_RecServiceChange::release_change_time() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.change_time)
  
  return change_time_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecStrategyReport_RecServiceChange::set_allocated_change_time(std::string* change_time) {
  if (change_time != nullptr) {
    
  } else {
    
  }
  change_time_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), change_time);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.change_time)
}

// string service = 2;
inline void RecStrategyReport_RecServiceChange::clear_service() {
  service_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecStrategyReport_RecServiceChange::service() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.service)
  return service_.GetNoArena();
}
inline void RecStrategyReport_RecServiceChange::set_service(const std::string& value) {
  
  service_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.service)
}
inline void RecStrategyReport_RecServiceChange::set_service(std::string&& value) {
  
  service_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.service)
}
inline void RecStrategyReport_RecServiceChange::set_service(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  service_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.service)
}
inline void RecStrategyReport_RecServiceChange::set_service(const char* value, size_t size) {
  
  service_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.service)
}
inline std::string* RecStrategyReport_RecServiceChange::mutable_service() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.service)
  return service_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecStrategyReport_RecServiceChange::release_service() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.service)
  
  return service_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecStrategyReport_RecServiceChange::set_allocated_service(std::string* service) {
  if (service != nullptr) {
    
  } else {
    
  }
  service_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), service);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.service)
}

// int32 change_type = 3;
inline void RecStrategyReport_RecServiceChange::clear_change_type() {
  change_type_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RecStrategyReport_RecServiceChange::change_type() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.change_type)
  return change_type_;
}
inline void RecStrategyReport_RecServiceChange::set_change_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  change_type_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.change_type)
}

// string before_change = 4;
inline void RecStrategyReport_RecServiceChange::clear_before_change() {
  before_change_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecStrategyReport_RecServiceChange::before_change() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.before_change)
  return before_change_.GetNoArena();
}
inline void RecStrategyReport_RecServiceChange::set_before_change(const std::string& value) {
  
  before_change_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.before_change)
}
inline void RecStrategyReport_RecServiceChange::set_before_change(std::string&& value) {
  
  before_change_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.before_change)
}
inline void RecStrategyReport_RecServiceChange::set_before_change(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  before_change_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.before_change)
}
inline void RecStrategyReport_RecServiceChange::set_before_change(const char* value, size_t size) {
  
  before_change_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.before_change)
}
inline std::string* RecStrategyReport_RecServiceChange::mutable_before_change() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.before_change)
  return before_change_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecStrategyReport_RecServiceChange::release_before_change() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.before_change)
  
  return before_change_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecStrategyReport_RecServiceChange::set_allocated_before_change(std::string* before_change) {
  if (before_change != nullptr) {
    
  } else {
    
  }
  before_change_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), before_change);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.before_change)
}

// string after_change = 5;
inline void RecStrategyReport_RecServiceChange::clear_after_change() {
  after_change_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecStrategyReport_RecServiceChange::after_change() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.after_change)
  return after_change_.GetNoArena();
}
inline void RecStrategyReport_RecServiceChange::set_after_change(const std::string& value) {
  
  after_change_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.after_change)
}
inline void RecStrategyReport_RecServiceChange::set_after_change(std::string&& value) {
  
  after_change_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.after_change)
}
inline void RecStrategyReport_RecServiceChange::set_after_change(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  after_change_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.after_change)
}
inline void RecStrategyReport_RecServiceChange::set_after_change(const char* value, size_t size) {
  
  after_change_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.after_change)
}
inline std::string* RecStrategyReport_RecServiceChange::mutable_after_change() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.after_change)
  return after_change_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecStrategyReport_RecServiceChange::release_after_change() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.after_change)
  
  return after_change_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecStrategyReport_RecServiceChange::set_allocated_after_change(std::string* after_change) {
  if (after_change != nullptr) {
    
  } else {
    
  }
  after_change_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), after_change);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.after_change)
}

// string change_detail = 6;
inline void RecStrategyReport_RecServiceChange::clear_change_detail() {
  change_detail_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecStrategyReport_RecServiceChange::change_detail() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.change_detail)
  return change_detail_.GetNoArena();
}
inline void RecStrategyReport_RecServiceChange::set_change_detail(const std::string& value) {
  
  change_detail_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.change_detail)
}
inline void RecStrategyReport_RecServiceChange::set_change_detail(std::string&& value) {
  
  change_detail_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.change_detail)
}
inline void RecStrategyReport_RecServiceChange::set_change_detail(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  change_detail_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.change_detail)
}
inline void RecStrategyReport_RecServiceChange::set_change_detail(const char* value, size_t size) {
  
  change_detail_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.change_detail)
}
inline std::string* RecStrategyReport_RecServiceChange::mutable_change_detail() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.change_detail)
  return change_detail_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecStrategyReport_RecServiceChange::release_change_detail() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.change_detail)
  
  return change_detail_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecStrategyReport_RecServiceChange::set_allocated_change_detail(std::string* change_detail) {
  if (change_detail != nullptr) {
    
  } else {
    
  }
  change_detail_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), change_detail);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.strategy.RecStrategyReport.RecServiceChange.change_detail)
}

// -------------------------------------------------------------------

// RecStrategyReport

// repeated .abc.recommend_plt.strategy.RecStrategyReport.RecStrategyFea rec_strategy_fea = 1;
inline int RecStrategyReport::rec_strategy_fea_size() const {
  return rec_strategy_fea_.size();
}
inline void RecStrategyReport::clear_rec_strategy_fea() {
  rec_strategy_fea_.Clear();
}
inline ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyFea* RecStrategyReport::mutable_rec_strategy_fea(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.RecStrategyReport.rec_strategy_fea)
  return rec_strategy_fea_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyFea >*
RecStrategyReport::mutable_rec_strategy_fea() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.strategy.RecStrategyReport.rec_strategy_fea)
  return &rec_strategy_fea_;
}
inline const ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyFea& RecStrategyReport::rec_strategy_fea(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.rec_strategy_fea)
  return rec_strategy_fea_.Get(index);
}
inline ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyFea* RecStrategyReport::add_rec_strategy_fea() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.strategy.RecStrategyReport.rec_strategy_fea)
  return rec_strategy_fea_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyFea >&
RecStrategyReport::rec_strategy_fea() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.strategy.RecStrategyReport.rec_strategy_fea)
  return rec_strategy_fea_;
}

// repeated .abc.recommend_plt.strategy.RecStrategyReport.RecStrategyMetric rec_strategy_metric = 2;
inline int RecStrategyReport::rec_strategy_metric_size() const {
  return rec_strategy_metric_.size();
}
inline void RecStrategyReport::clear_rec_strategy_metric() {
  rec_strategy_metric_.Clear();
}
inline ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyMetric* RecStrategyReport::mutable_rec_strategy_metric(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.strategy.RecStrategyReport.rec_strategy_metric)
  return rec_strategy_metric_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyMetric >*
RecStrategyReport::mutable_rec_strategy_metric() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.strategy.RecStrategyReport.rec_strategy_metric)
  return &rec_strategy_metric_;
}
inline const ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyMetric& RecStrategyReport::rec_strategy_metric(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.strategy.RecStrategyReport.rec_strategy_metric)
  return rec_strategy_metric_.Get(index);
}
inline ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyMetric* RecStrategyReport::add_rec_strategy_metric() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.strategy.RecStrategyReport.rec_strategy_metric)
  return rec_strategy_metric_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::strategy::RecStrategyReport_RecStrategyMetric >&
RecStrategyReport::rec_strategy_metric() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.strategy.RecStrategyReport.rec_strategy_metric)
  return rec_strategy_metric_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace strategy
}  // namespace recommend_plt
}  // namespace abc

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_proto_2frecommend_5fstrategy_5freport_2eproto
