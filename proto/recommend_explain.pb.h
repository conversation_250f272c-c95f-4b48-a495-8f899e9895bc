// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/recommend_explain.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_proto_2frecommend_5fexplain_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_proto_2frecommend_5fexplain_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3008000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3008000 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fexplain_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_proto_2frecommend_5fexplain_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[5]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2frecommend_5fexplain_2eproto;
namespace abc {
namespace recommend_plt {
namespace explain {
class Data;
class DataDefaultTypeInternal;
extern DataDefaultTypeInternal _Data_default_instance_;
class ExplainReport;
class ExplainReportDefaultTypeInternal;
extern ExplainReportDefaultTypeInternal _ExplainReport_default_instance_;
class ExplainReport_NodesTableEntry_DoNotUse;
class ExplainReport_NodesTableEntry_DoNotUseDefaultTypeInternal;
extern ExplainReport_NodesTableEntry_DoNotUseDefaultTypeInternal _ExplainReport_NodesTableEntry_DoNotUse_default_instance_;
class ExplainResponse;
class ExplainResponseDefaultTypeInternal;
extern ExplainResponseDefaultTypeInternal _ExplainResponse_default_instance_;
class Table;
class TableDefaultTypeInternal;
extern TableDefaultTypeInternal _Table_default_instance_;
}  // namespace explain
}  // namespace recommend_plt
}  // namespace abc
PROTOBUF_NAMESPACE_OPEN
template<> ::abc::recommend_plt::explain::Data* Arena::CreateMaybeMessage<::abc::recommend_plt::explain::Data>(Arena*);
template<> ::abc::recommend_plt::explain::ExplainReport* Arena::CreateMaybeMessage<::abc::recommend_plt::explain::ExplainReport>(Arena*);
template<> ::abc::recommend_plt::explain::ExplainReport_NodesTableEntry_DoNotUse* Arena::CreateMaybeMessage<::abc::recommend_plt::explain::ExplainReport_NodesTableEntry_DoNotUse>(Arena*);
template<> ::abc::recommend_plt::explain::ExplainResponse* Arena::CreateMaybeMessage<::abc::recommend_plt::explain::ExplainResponse>(Arena*);
template<> ::abc::recommend_plt::explain::Table* Arena::CreateMaybeMessage<::abc::recommend_plt::explain::Table>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace abc {
namespace recommend_plt {
namespace explain {

// ===================================================================

class Data :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.explain.Data) */ {
 public:
  Data();
  virtual ~Data();

  Data(const Data& from);
  Data(Data&& from) noexcept
    : Data() {
    *this = ::std::move(from);
  }

  inline Data& operator=(const Data& from) {
    CopyFrom(from);
    return *this;
  }
  inline Data& operator=(Data&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Data& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Data* internal_default_instance() {
    return reinterpret_cast<const Data*>(
               &_Data_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(Data* other);
  friend void swap(Data& a, Data& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Data* New() const final {
    return CreateMaybeMessage<Data>(nullptr);
  }

  Data* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Data>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Data& from);
  void MergeFrom(const Data& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Data* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.explain.Data";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fexplain_2eproto);
    return ::descriptor_table_proto_2frecommend_5fexplain_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int64 int_val = 1;
  int int_val_size() const;
  void clear_int_val();
  static const int kIntValFieldNumber = 1;
  ::PROTOBUF_NAMESPACE_ID::int64 int_val(int index) const;
  void set_int_val(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_int_val(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      int_val() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_int_val();

  // repeated float float_val = 2;
  int float_val_size() const;
  void clear_float_val();
  static const int kFloatValFieldNumber = 2;
  float float_val(int index) const;
  void set_float_val(int index, float value);
  void add_float_val(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      float_val() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_float_val();

  // repeated string str_val = 3;
  int str_val_size() const;
  void clear_str_val();
  static const int kStrValFieldNumber = 3;
  const std::string& str_val(int index) const;
  std::string* mutable_str_val(int index);
  void set_str_val(int index, const std::string& value);
  void set_str_val(int index, std::string&& value);
  void set_str_val(int index, const char* value);
  void set_str_val(int index, const char* value, size_t size);
  std::string* add_str_val();
  void add_str_val(const std::string& value);
  void add_str_val(std::string&& value);
  void add_str_val(const char* value);
  void add_str_val(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& str_val() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_str_val();

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.explain.Data)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > int_val_;
  mutable std::atomic<int> _int_val_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > float_val_;
  mutable std::atomic<int> _float_val_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> str_val_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fexplain_2eproto;
};
// -------------------------------------------------------------------

class Table :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.explain.Table) */ {
 public:
  Table();
  virtual ~Table();

  Table(const Table& from);
  Table(Table&& from) noexcept
    : Table() {
    *this = ::std::move(from);
  }

  inline Table& operator=(const Table& from) {
    CopyFrom(from);
    return *this;
  }
  inline Table& operator=(Table&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Table& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Table* internal_default_instance() {
    return reinterpret_cast<const Table*>(
               &_Table_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void Swap(Table* other);
  friend void swap(Table& a, Table& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Table* New() const final {
    return CreateMaybeMessage<Table>(nullptr);
  }

  Table* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Table>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Table& from);
  void MergeFrom(const Table& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Table* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.explain.Table";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fexplain_2eproto);
    return ::descriptor_table_proto_2frecommend_5fexplain_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string header = 1;
  int header_size() const;
  void clear_header();
  static const int kHeaderFieldNumber = 1;
  const std::string& header(int index) const;
  std::string* mutable_header(int index);
  void set_header(int index, const std::string& value);
  void set_header(int index, std::string&& value);
  void set_header(int index, const char* value);
  void set_header(int index, const char* value, size_t size);
  std::string* add_header();
  void add_header(const std::string& value);
  void add_header(std::string&& value);
  void add_header(const char* value);
  void add_header(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& header() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_header();

  // repeated .abc.recommend_plt.explain.Data rows = 2;
  int rows_size() const;
  void clear_rows();
  static const int kRowsFieldNumber = 2;
  ::abc::recommend_plt::explain::Data* mutable_rows(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::explain::Data >*
      mutable_rows();
  const ::abc::recommend_plt::explain::Data& rows(int index) const;
  ::abc::recommend_plt::explain::Data* add_rows();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::explain::Data >&
      rows() const;

  // repeated .abc.recommend_plt.explain.Data cols = 3;
  int cols_size() const;
  void clear_cols();
  static const int kColsFieldNumber = 3;
  ::abc::recommend_plt::explain::Data* mutable_cols(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::explain::Data >*
      mutable_cols();
  const ::abc::recommend_plt::explain::Data& cols(int index) const;
  ::abc::recommend_plt::explain::Data* add_cols();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::explain::Data >&
      cols() const;

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.explain.Table)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> header_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::explain::Data > rows_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::explain::Data > cols_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fexplain_2eproto;
};
// -------------------------------------------------------------------

class ExplainReport_NodesTableEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ExplainReport_NodesTableEntry_DoNotUse, 
    std::string, ::abc::recommend_plt::explain::Table,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ExplainReport_NodesTableEntry_DoNotUse, 
    std::string, ::abc::recommend_plt::explain::Table,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  ExplainReport_NodesTableEntry_DoNotUse();
  ExplainReport_NodesTableEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ExplainReport_NodesTableEntry_DoNotUse& other);
  static const ExplainReport_NodesTableEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ExplainReport_NodesTableEntry_DoNotUse*>(&_ExplainReport_NodesTableEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "abc.recommend_plt.explain.ExplainReport.NodesTableEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fexplain_2eproto);
    return ::descriptor_table_proto_2frecommend_5fexplain_2eproto.file_level_metadata[2];
  }

  public:
};

// -------------------------------------------------------------------

class ExplainReport :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.explain.ExplainReport) */ {
 public:
  ExplainReport();
  virtual ~ExplainReport();

  ExplainReport(const ExplainReport& from);
  ExplainReport(ExplainReport&& from) noexcept
    : ExplainReport() {
    *this = ::std::move(from);
  }

  inline ExplainReport& operator=(const ExplainReport& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExplainReport& operator=(ExplainReport&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ExplainReport& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExplainReport* internal_default_instance() {
    return reinterpret_cast<const ExplainReport*>(
               &_ExplainReport_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void Swap(ExplainReport* other);
  friend void swap(ExplainReport& a, ExplainReport& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ExplainReport* New() const final {
    return CreateMaybeMessage<ExplainReport>(nullptr);
  }

  ExplainReport* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ExplainReport>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ExplainReport& from);
  void MergeFrom(const ExplainReport& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExplainReport* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.explain.ExplainReport";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fexplain_2eproto);
    return ::descriptor_table_proto_2frecommend_5fexplain_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<string, .abc.recommend_plt.explain.Table> nodes_table = 1;
  int nodes_table_size() const;
  void clear_nodes_table();
  static const int kNodesTableFieldNumber = 1;
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::explain::Table >&
      nodes_table() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::explain::Table >*
      mutable_nodes_table();

  // string trace_id = 2;
  void clear_trace_id();
  static const int kTraceIdFieldNumber = 2;
  const std::string& trace_id() const;
  void set_trace_id(const std::string& value);
  void set_trace_id(std::string&& value);
  void set_trace_id(const char* value);
  void set_trace_id(const char* value, size_t size);
  std::string* mutable_trace_id();
  std::string* release_trace_id();
  void set_allocated_trace_id(std::string* trace_id);

  // string module = 4;
  void clear_module();
  static const int kModuleFieldNumber = 4;
  const std::string& module() const;
  void set_module(const std::string& value);
  void set_module(std::string&& value);
  void set_module(const char* value);
  void set_module(const char* value, size_t size);
  std::string* mutable_module();
  std::string* release_module();
  void set_allocated_module(std::string* module);

  // string reporter = 5;
  void clear_reporter();
  static const int kReporterFieldNumber = 5;
  const std::string& reporter() const;
  void set_reporter(const std::string& value);
  void set_reporter(std::string&& value);
  void set_reporter(const char* value);
  void set_reporter(const char* value, size_t size);
  std::string* mutable_reporter();
  std::string* release_reporter();
  void set_allocated_reporter(std::string* reporter);

  // string tag = 8;
  void clear_tag();
  static const int kTagFieldNumber = 8;
  const std::string& tag() const;
  void set_tag(const std::string& value);
  void set_tag(std::string&& value);
  void set_tag(const char* value);
  void set_tag(const char* value, size_t size);
  std::string* mutable_tag();
  std::string* release_tag();
  void set_allocated_tag(std::string* tag);

  // int32 item_source = 3;
  void clear_item_source();
  static const int kItemSourceFieldNumber = 3;
  ::PROTOBUF_NAMESPACE_ID::int32 item_source() const;
  void set_item_source(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 req_source = 6;
  void clear_req_source();
  static const int kReqSourceFieldNumber = 6;
  ::PROTOBUF_NAMESPACE_ID::int32 req_source() const;
  void set_req_source(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int64 expire_time = 7;
  void clear_expire_time();
  static const int kExpireTimeFieldNumber = 7;
  ::PROTOBUF_NAMESPACE_ID::int64 expire_time() const;
  void set_expire_time(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.explain.ExplainReport)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      ExplainReport_NodesTableEntry_DoNotUse,
      std::string, ::abc::recommend_plt::explain::Table,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > nodes_table_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr trace_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr module_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr reporter_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tag_;
  ::PROTOBUF_NAMESPACE_ID::int32 item_source_;
  ::PROTOBUF_NAMESPACE_ID::int32 req_source_;
  ::PROTOBUF_NAMESPACE_ID::int64 expire_time_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fexplain_2eproto;
};
// -------------------------------------------------------------------

class ExplainResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.explain.ExplainResponse) */ {
 public:
  ExplainResponse();
  virtual ~ExplainResponse();

  ExplainResponse(const ExplainResponse& from);
  ExplainResponse(ExplainResponse&& from) noexcept
    : ExplainResponse() {
    *this = ::std::move(from);
  }

  inline ExplainResponse& operator=(const ExplainResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExplainResponse& operator=(ExplainResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ExplainResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExplainResponse* internal_default_instance() {
    return reinterpret_cast<const ExplainResponse*>(
               &_ExplainResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void Swap(ExplainResponse* other);
  friend void swap(ExplainResponse& a, ExplainResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ExplainResponse* New() const final {
    return CreateMaybeMessage<ExplainResponse>(nullptr);
  }

  ExplainResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ExplainResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ExplainResponse& from);
  void MergeFrom(const ExplainResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExplainResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.explain.ExplainResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fexplain_2eproto);
    return ::descriptor_table_proto_2frecommend_5fexplain_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string trace_id = 2;
  void clear_trace_id();
  static const int kTraceIdFieldNumber = 2;
  const std::string& trace_id() const;
  void set_trace_id(const std::string& value);
  void set_trace_id(std::string&& value);
  void set_trace_id(const char* value);
  void set_trace_id(const char* value, size_t size);
  std::string* mutable_trace_id();
  std::string* release_trace_id();
  void set_allocated_trace_id(std::string* trace_id);

  // int32 error_code = 1;
  void clear_error_code();
  static const int kErrorCodeFieldNumber = 1;
  ::PROTOBUF_NAMESPACE_ID::int32 error_code() const;
  void set_error_code(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.explain.ExplainResponse)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr trace_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 error_code_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fexplain_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Data

// repeated int64 int_val = 1;
inline int Data::int_val_size() const {
  return int_val_.size();
}
inline void Data::clear_int_val() {
  int_val_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 Data::int_val(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.explain.Data.int_val)
  return int_val_.Get(index);
}
inline void Data::set_int_val(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  int_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.explain.Data.int_val)
}
inline void Data::add_int_val(::PROTOBUF_NAMESPACE_ID::int64 value) {
  int_val_.Add(value);
  // @@protoc_insertion_point(field_add:abc.recommend_plt.explain.Data.int_val)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
Data::int_val() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.explain.Data.int_val)
  return int_val_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
Data::mutable_int_val() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.explain.Data.int_val)
  return &int_val_;
}

// repeated float float_val = 2;
inline int Data::float_val_size() const {
  return float_val_.size();
}
inline void Data::clear_float_val() {
  float_val_.Clear();
}
inline float Data::float_val(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.explain.Data.float_val)
  return float_val_.Get(index);
}
inline void Data::set_float_val(int index, float value) {
  float_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.explain.Data.float_val)
}
inline void Data::add_float_val(float value) {
  float_val_.Add(value);
  // @@protoc_insertion_point(field_add:abc.recommend_plt.explain.Data.float_val)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
Data::float_val() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.explain.Data.float_val)
  return float_val_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
Data::mutable_float_val() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.explain.Data.float_val)
  return &float_val_;
}

// repeated string str_val = 3;
inline int Data::str_val_size() const {
  return str_val_.size();
}
inline void Data::clear_str_val() {
  str_val_.Clear();
}
inline const std::string& Data::str_val(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.explain.Data.str_val)
  return str_val_.Get(index);
}
inline std::string* Data::mutable_str_val(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.explain.Data.str_val)
  return str_val_.Mutable(index);
}
inline void Data::set_str_val(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:abc.recommend_plt.explain.Data.str_val)
  str_val_.Mutable(index)->assign(value);
}
inline void Data::set_str_val(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:abc.recommend_plt.explain.Data.str_val)
  str_val_.Mutable(index)->assign(std::move(value));
}
inline void Data::set_str_val(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  str_val_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.explain.Data.str_val)
}
inline void Data::set_str_val(int index, const char* value, size_t size) {
  str_val_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.explain.Data.str_val)
}
inline std::string* Data::add_str_val() {
  // @@protoc_insertion_point(field_add_mutable:abc.recommend_plt.explain.Data.str_val)
  return str_val_.Add();
}
inline void Data::add_str_val(const std::string& value) {
  str_val_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:abc.recommend_plt.explain.Data.str_val)
}
inline void Data::add_str_val(std::string&& value) {
  str_val_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:abc.recommend_plt.explain.Data.str_val)
}
inline void Data::add_str_val(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  str_val_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:abc.recommend_plt.explain.Data.str_val)
}
inline void Data::add_str_val(const char* value, size_t size) {
  str_val_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:abc.recommend_plt.explain.Data.str_val)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
Data::str_val() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.explain.Data.str_val)
  return str_val_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
Data::mutable_str_val() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.explain.Data.str_val)
  return &str_val_;
}

// -------------------------------------------------------------------

// Table

// repeated string header = 1;
inline int Table::header_size() const {
  return header_.size();
}
inline void Table::clear_header() {
  header_.Clear();
}
inline const std::string& Table::header(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.explain.Table.header)
  return header_.Get(index);
}
inline std::string* Table::mutable_header(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.explain.Table.header)
  return header_.Mutable(index);
}
inline void Table::set_header(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:abc.recommend_plt.explain.Table.header)
  header_.Mutable(index)->assign(value);
}
inline void Table::set_header(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:abc.recommend_plt.explain.Table.header)
  header_.Mutable(index)->assign(std::move(value));
}
inline void Table::set_header(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  header_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.explain.Table.header)
}
inline void Table::set_header(int index, const char* value, size_t size) {
  header_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.explain.Table.header)
}
inline std::string* Table::add_header() {
  // @@protoc_insertion_point(field_add_mutable:abc.recommend_plt.explain.Table.header)
  return header_.Add();
}
inline void Table::add_header(const std::string& value) {
  header_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:abc.recommend_plt.explain.Table.header)
}
inline void Table::add_header(std::string&& value) {
  header_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:abc.recommend_plt.explain.Table.header)
}
inline void Table::add_header(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  header_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:abc.recommend_plt.explain.Table.header)
}
inline void Table::add_header(const char* value, size_t size) {
  header_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:abc.recommend_plt.explain.Table.header)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
Table::header() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.explain.Table.header)
  return header_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
Table::mutable_header() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.explain.Table.header)
  return &header_;
}

// repeated .abc.recommend_plt.explain.Data rows = 2;
inline int Table::rows_size() const {
  return rows_.size();
}
inline void Table::clear_rows() {
  rows_.Clear();
}
inline ::abc::recommend_plt::explain::Data* Table::mutable_rows(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.explain.Table.rows)
  return rows_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::explain::Data >*
Table::mutable_rows() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.explain.Table.rows)
  return &rows_;
}
inline const ::abc::recommend_plt::explain::Data& Table::rows(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.explain.Table.rows)
  return rows_.Get(index);
}
inline ::abc::recommend_plt::explain::Data* Table::add_rows() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.explain.Table.rows)
  return rows_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::explain::Data >&
Table::rows() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.explain.Table.rows)
  return rows_;
}

// repeated .abc.recommend_plt.explain.Data cols = 3;
inline int Table::cols_size() const {
  return cols_.size();
}
inline void Table::clear_cols() {
  cols_.Clear();
}
inline ::abc::recommend_plt::explain::Data* Table::mutable_cols(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.explain.Table.cols)
  return cols_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::explain::Data >*
Table::mutable_cols() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.explain.Table.cols)
  return &cols_;
}
inline const ::abc::recommend_plt::explain::Data& Table::cols(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.explain.Table.cols)
  return cols_.Get(index);
}
inline ::abc::recommend_plt::explain::Data* Table::add_cols() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.explain.Table.cols)
  return cols_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::explain::Data >&
Table::cols() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.explain.Table.cols)
  return cols_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ExplainReport

// map<string, .abc.recommend_plt.explain.Table> nodes_table = 1;
inline int ExplainReport::nodes_table_size() const {
  return nodes_table_.size();
}
inline void ExplainReport::clear_nodes_table() {
  nodes_table_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::explain::Table >&
ExplainReport::nodes_table() const {
  // @@protoc_insertion_point(field_map:abc.recommend_plt.explain.ExplainReport.nodes_table)
  return nodes_table_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::explain::Table >*
ExplainReport::mutable_nodes_table() {
  // @@protoc_insertion_point(field_mutable_map:abc.recommend_plt.explain.ExplainReport.nodes_table)
  return nodes_table_.MutableMap();
}

// string trace_id = 2;
inline void ExplainReport::clear_trace_id() {
  trace_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ExplainReport::trace_id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.explain.ExplainReport.trace_id)
  return trace_id_.GetNoArena();
}
inline void ExplainReport::set_trace_id(const std::string& value) {
  
  trace_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.explain.ExplainReport.trace_id)
}
inline void ExplainReport::set_trace_id(std::string&& value) {
  
  trace_id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.explain.ExplainReport.trace_id)
}
inline void ExplainReport::set_trace_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  trace_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.explain.ExplainReport.trace_id)
}
inline void ExplainReport::set_trace_id(const char* value, size_t size) {
  
  trace_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.explain.ExplainReport.trace_id)
}
inline std::string* ExplainReport::mutable_trace_id() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.explain.ExplainReport.trace_id)
  return trace_id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ExplainReport::release_trace_id() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.explain.ExplainReport.trace_id)
  
  return trace_id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ExplainReport::set_allocated_trace_id(std::string* trace_id) {
  if (trace_id != nullptr) {
    
  } else {
    
  }
  trace_id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), trace_id);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.explain.ExplainReport.trace_id)
}

// int32 item_source = 3;
inline void ExplainReport::clear_item_source() {
  item_source_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ExplainReport::item_source() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.explain.ExplainReport.item_source)
  return item_source_;
}
inline void ExplainReport::set_item_source(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  item_source_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.explain.ExplainReport.item_source)
}

// string module = 4;
inline void ExplainReport::clear_module() {
  module_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ExplainReport::module() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.explain.ExplainReport.module)
  return module_.GetNoArena();
}
inline void ExplainReport::set_module(const std::string& value) {
  
  module_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.explain.ExplainReport.module)
}
inline void ExplainReport::set_module(std::string&& value) {
  
  module_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.explain.ExplainReport.module)
}
inline void ExplainReport::set_module(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  module_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.explain.ExplainReport.module)
}
inline void ExplainReport::set_module(const char* value, size_t size) {
  
  module_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.explain.ExplainReport.module)
}
inline std::string* ExplainReport::mutable_module() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.explain.ExplainReport.module)
  return module_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ExplainReport::release_module() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.explain.ExplainReport.module)
  
  return module_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ExplainReport::set_allocated_module(std::string* module) {
  if (module != nullptr) {
    
  } else {
    
  }
  module_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), module);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.explain.ExplainReport.module)
}

// string reporter = 5;
inline void ExplainReport::clear_reporter() {
  reporter_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ExplainReport::reporter() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.explain.ExplainReport.reporter)
  return reporter_.GetNoArena();
}
inline void ExplainReport::set_reporter(const std::string& value) {
  
  reporter_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.explain.ExplainReport.reporter)
}
inline void ExplainReport::set_reporter(std::string&& value) {
  
  reporter_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.explain.ExplainReport.reporter)
}
inline void ExplainReport::set_reporter(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  reporter_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.explain.ExplainReport.reporter)
}
inline void ExplainReport::set_reporter(const char* value, size_t size) {
  
  reporter_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.explain.ExplainReport.reporter)
}
inline std::string* ExplainReport::mutable_reporter() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.explain.ExplainReport.reporter)
  return reporter_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ExplainReport::release_reporter() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.explain.ExplainReport.reporter)
  
  return reporter_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ExplainReport::set_allocated_reporter(std::string* reporter) {
  if (reporter != nullptr) {
    
  } else {
    
  }
  reporter_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), reporter);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.explain.ExplainReport.reporter)
}

// int32 req_source = 6;
inline void ExplainReport::clear_req_source() {
  req_source_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ExplainReport::req_source() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.explain.ExplainReport.req_source)
  return req_source_;
}
inline void ExplainReport::set_req_source(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  req_source_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.explain.ExplainReport.req_source)
}

// int64 expire_time = 7;
inline void ExplainReport::clear_expire_time() {
  expire_time_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ExplainReport::expire_time() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.explain.ExplainReport.expire_time)
  return expire_time_;
}
inline void ExplainReport::set_expire_time(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  expire_time_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.explain.ExplainReport.expire_time)
}

// string tag = 8;
inline void ExplainReport::clear_tag() {
  tag_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ExplainReport::tag() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.explain.ExplainReport.tag)
  return tag_.GetNoArena();
}
inline void ExplainReport::set_tag(const std::string& value) {
  
  tag_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.explain.ExplainReport.tag)
}
inline void ExplainReport::set_tag(std::string&& value) {
  
  tag_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.explain.ExplainReport.tag)
}
inline void ExplainReport::set_tag(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  tag_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.explain.ExplainReport.tag)
}
inline void ExplainReport::set_tag(const char* value, size_t size) {
  
  tag_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.explain.ExplainReport.tag)
}
inline std::string* ExplainReport::mutable_tag() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.explain.ExplainReport.tag)
  return tag_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ExplainReport::release_tag() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.explain.ExplainReport.tag)
  
  return tag_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ExplainReport::set_allocated_tag(std::string* tag) {
  if (tag != nullptr) {
    
  } else {
    
  }
  tag_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), tag);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.explain.ExplainReport.tag)
}

// -------------------------------------------------------------------

// ExplainResponse

// int32 error_code = 1;
inline void ExplainResponse::clear_error_code() {
  error_code_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ExplainResponse::error_code() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.explain.ExplainResponse.error_code)
  return error_code_;
}
inline void ExplainResponse::set_error_code(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  error_code_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.explain.ExplainResponse.error_code)
}

// string trace_id = 2;
inline void ExplainResponse::clear_trace_id() {
  trace_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ExplainResponse::trace_id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.explain.ExplainResponse.trace_id)
  return trace_id_.GetNoArena();
}
inline void ExplainResponse::set_trace_id(const std::string& value) {
  
  trace_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.explain.ExplainResponse.trace_id)
}
inline void ExplainResponse::set_trace_id(std::string&& value) {
  
  trace_id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.explain.ExplainResponse.trace_id)
}
inline void ExplainResponse::set_trace_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  trace_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.explain.ExplainResponse.trace_id)
}
inline void ExplainResponse::set_trace_id(const char* value, size_t size) {
  
  trace_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.explain.ExplainResponse.trace_id)
}
inline std::string* ExplainResponse::mutable_trace_id() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.explain.ExplainResponse.trace_id)
  return trace_id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ExplainResponse::release_trace_id() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.explain.ExplainResponse.trace_id)
  
  return trace_id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ExplainResponse::set_allocated_trace_id(std::string* trace_id) {
  if (trace_id != nullptr) {
    
  } else {
    
  }
  trace_id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), trace_id);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.explain.ExplainResponse.trace_id)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace explain
}  // namespace recommend_plt
}  // namespace abc

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_proto_2frecommend_5fexplain_2eproto
