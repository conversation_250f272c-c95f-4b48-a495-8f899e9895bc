// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/recommend_model.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_proto_2frecommend_5fmodel_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_proto_2frecommend_5fmodel_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3008000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3008000 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "proto/recommend_common.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fmodel_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_proto_2frecommend_5fmodel_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[33]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2frecommend_5fmodel_2eproto;
namespace abc {
namespace recommend_plt {
namespace model {
class CateRelationKey;
class CateRelationKeyDefaultTypeInternal;
extern CateRelationKeyDefaultTypeInternal _CateRelationKey_default_instance_;
class CateRelationVal;
class CateRelationValDefaultTypeInternal;
extern CateRelationValDefaultTypeInternal _CateRelationVal_default_instance_;
class FeatureMappingInfo;
class FeatureMappingInfoDefaultTypeInternal;
extern FeatureMappingInfoDefaultTypeInternal _FeatureMappingInfo_default_instance_;
class FeatureMappingKey;
class FeatureMappingKeyDefaultTypeInternal;
extern FeatureMappingKeyDefaultTypeInternal _FeatureMappingKey_default_instance_;
class FeatureMappingModel;
class FeatureMappingModelDefaultTypeInternal;
extern FeatureMappingModelDefaultTypeInternal _FeatureMappingModel_default_instance_;
class FeatureMappingMultiVal;
class FeatureMappingMultiValDefaultTypeInternal;
extern FeatureMappingMultiValDefaultTypeInternal _FeatureMappingMultiVal_default_instance_;
class FeatureMappingVal;
class FeatureMappingValDefaultTypeInternal;
extern FeatureMappingValDefaultTypeInternal _FeatureMappingVal_default_instance_;
class ItemRelationInfo;
class ItemRelationInfoDefaultTypeInternal;
extern ItemRelationInfoDefaultTypeInternal _ItemRelationInfo_default_instance_;
class ItemRelationKey;
class ItemRelationKeyDefaultTypeInternal;
extern ItemRelationKeyDefaultTypeInternal _ItemRelationKey_default_instance_;
class ItemRelationModel;
class ItemRelationModelDefaultTypeInternal;
extern ItemRelationModelDefaultTypeInternal _ItemRelationModel_default_instance_;
class ItemRelationMultiVal;
class ItemRelationMultiValDefaultTypeInternal;
extern ItemRelationMultiValDefaultTypeInternal _ItemRelationMultiVal_default_instance_;
class ItemRelationVal;
class ItemRelationValDefaultTypeInternal;
extern ItemRelationValDefaultTypeInternal _ItemRelationVal_default_instance_;
class ItemTagsInfo;
class ItemTagsInfoDefaultTypeInternal;
extern ItemTagsInfoDefaultTypeInternal _ItemTagsInfo_default_instance_;
class ItemTagsKey;
class ItemTagsKeyDefaultTypeInternal;
extern ItemTagsKeyDefaultTypeInternal _ItemTagsKey_default_instance_;
class ItemTagsModel;
class ItemTagsModelDefaultTypeInternal;
extern ItemTagsModelDefaultTypeInternal _ItemTagsModel_default_instance_;
class ItemTagsVal;
class ItemTagsValDefaultTypeInternal;
extern ItemTagsValDefaultTypeInternal _ItemTagsVal_default_instance_;
class ItemTagsVal_Tag;
class ItemTagsVal_TagDefaultTypeInternal;
extern ItemTagsVal_TagDefaultTypeInternal _ItemTagsVal_Tag_default_instance_;
class RecList;
class RecListDefaultTypeInternal;
extern RecListDefaultTypeInternal _RecList_default_instance_;
class RecListInfo;
class RecListInfoDefaultTypeInternal;
extern RecListInfoDefaultTypeInternal _RecListInfo_default_instance_;
class RecListKey;
class RecListKeyDefaultTypeInternal;
extern RecListKeyDefaultTypeInternal _RecListKey_default_instance_;
class RecListKey_Dimension;
class RecListKey_DimensionDefaultTypeInternal;
extern RecListKey_DimensionDefaultTypeInternal _RecListKey_Dimension_default_instance_;
class RecListModel;
class RecListModelDefaultTypeInternal;
extern RecListModelDefaultTypeInternal _RecListModel_default_instance_;
class RecListVal;
class RecListValDefaultTypeInternal;
extern RecListValDefaultTypeInternal _RecListVal_default_instance_;
class RecListVal_Item;
class RecListVal_ItemDefaultTypeInternal;
extern RecListVal_ItemDefaultTypeInternal _RecListVal_Item_default_instance_;
class RecList_Dimension;
class RecList_DimensionDefaultTypeInternal;
extern RecList_DimensionDefaultTypeInternal _RecList_Dimension_default_instance_;
class RecList_Item;
class RecList_ItemDefaultTypeInternal;
extern RecList_ItemDefaultTypeInternal _RecList_Item_default_instance_;
class StatRateInfo;
class StatRateInfoDefaultTypeInternal;
extern StatRateInfoDefaultTypeInternal _StatRateInfo_default_instance_;
class StatRateKey;
class StatRateKeyDefaultTypeInternal;
extern StatRateKeyDefaultTypeInternal _StatRateKey_default_instance_;
class StatRateKey_Dimension;
class StatRateKey_DimensionDefaultTypeInternal;
extern StatRateKey_DimensionDefaultTypeInternal _StatRateKey_Dimension_default_instance_;
class StatRateModel;
class StatRateModelDefaultTypeInternal;
extern StatRateModelDefaultTypeInternal _StatRateModel_default_instance_;
class StatRateVal;
class StatRateValDefaultTypeInternal;
extern StatRateValDefaultTypeInternal _StatRateVal_default_instance_;
class StatRateVal_Metric;
class StatRateVal_MetricDefaultTypeInternal;
extern StatRateVal_MetricDefaultTypeInternal _StatRateVal_Metric_default_instance_;
class TransformFeaVal;
class TransformFeaValDefaultTypeInternal;
extern TransformFeaValDefaultTypeInternal _TransformFeaVal_default_instance_;
}  // namespace model
}  // namespace recommend_plt
}  // namespace abc
PROTOBUF_NAMESPACE_OPEN
template<> ::abc::recommend_plt::model::CateRelationKey* Arena::CreateMaybeMessage<::abc::recommend_plt::model::CateRelationKey>(Arena*);
template<> ::abc::recommend_plt::model::CateRelationVal* Arena::CreateMaybeMessage<::abc::recommend_plt::model::CateRelationVal>(Arena*);
template<> ::abc::recommend_plt::model::FeatureMappingInfo* Arena::CreateMaybeMessage<::abc::recommend_plt::model::FeatureMappingInfo>(Arena*);
template<> ::abc::recommend_plt::model::FeatureMappingKey* Arena::CreateMaybeMessage<::abc::recommend_plt::model::FeatureMappingKey>(Arena*);
template<> ::abc::recommend_plt::model::FeatureMappingModel* Arena::CreateMaybeMessage<::abc::recommend_plt::model::FeatureMappingModel>(Arena*);
template<> ::abc::recommend_plt::model::FeatureMappingMultiVal* Arena::CreateMaybeMessage<::abc::recommend_plt::model::FeatureMappingMultiVal>(Arena*);
template<> ::abc::recommend_plt::model::FeatureMappingVal* Arena::CreateMaybeMessage<::abc::recommend_plt::model::FeatureMappingVal>(Arena*);
template<> ::abc::recommend_plt::model::ItemRelationInfo* Arena::CreateMaybeMessage<::abc::recommend_plt::model::ItemRelationInfo>(Arena*);
template<> ::abc::recommend_plt::model::ItemRelationKey* Arena::CreateMaybeMessage<::abc::recommend_plt::model::ItemRelationKey>(Arena*);
template<> ::abc::recommend_plt::model::ItemRelationModel* Arena::CreateMaybeMessage<::abc::recommend_plt::model::ItemRelationModel>(Arena*);
template<> ::abc::recommend_plt::model::ItemRelationMultiVal* Arena::CreateMaybeMessage<::abc::recommend_plt::model::ItemRelationMultiVal>(Arena*);
template<> ::abc::recommend_plt::model::ItemRelationVal* Arena::CreateMaybeMessage<::abc::recommend_plt::model::ItemRelationVal>(Arena*);
template<> ::abc::recommend_plt::model::ItemTagsInfo* Arena::CreateMaybeMessage<::abc::recommend_plt::model::ItemTagsInfo>(Arena*);
template<> ::abc::recommend_plt::model::ItemTagsKey* Arena::CreateMaybeMessage<::abc::recommend_plt::model::ItemTagsKey>(Arena*);
template<> ::abc::recommend_plt::model::ItemTagsModel* Arena::CreateMaybeMessage<::abc::recommend_plt::model::ItemTagsModel>(Arena*);
template<> ::abc::recommend_plt::model::ItemTagsVal* Arena::CreateMaybeMessage<::abc::recommend_plt::model::ItemTagsVal>(Arena*);
template<> ::abc::recommend_plt::model::ItemTagsVal_Tag* Arena::CreateMaybeMessage<::abc::recommend_plt::model::ItemTagsVal_Tag>(Arena*);
template<> ::abc::recommend_plt::model::RecList* Arena::CreateMaybeMessage<::abc::recommend_plt::model::RecList>(Arena*);
template<> ::abc::recommend_plt::model::RecListInfo* Arena::CreateMaybeMessage<::abc::recommend_plt::model::RecListInfo>(Arena*);
template<> ::abc::recommend_plt::model::RecListKey* Arena::CreateMaybeMessage<::abc::recommend_plt::model::RecListKey>(Arena*);
template<> ::abc::recommend_plt::model::RecListKey_Dimension* Arena::CreateMaybeMessage<::abc::recommend_plt::model::RecListKey_Dimension>(Arena*);
template<> ::abc::recommend_plt::model::RecListModel* Arena::CreateMaybeMessage<::abc::recommend_plt::model::RecListModel>(Arena*);
template<> ::abc::recommend_plt::model::RecListVal* Arena::CreateMaybeMessage<::abc::recommend_plt::model::RecListVal>(Arena*);
template<> ::abc::recommend_plt::model::RecListVal_Item* Arena::CreateMaybeMessage<::abc::recommend_plt::model::RecListVal_Item>(Arena*);
template<> ::abc::recommend_plt::model::RecList_Dimension* Arena::CreateMaybeMessage<::abc::recommend_plt::model::RecList_Dimension>(Arena*);
template<> ::abc::recommend_plt::model::RecList_Item* Arena::CreateMaybeMessage<::abc::recommend_plt::model::RecList_Item>(Arena*);
template<> ::abc::recommend_plt::model::StatRateInfo* Arena::CreateMaybeMessage<::abc::recommend_plt::model::StatRateInfo>(Arena*);
template<> ::abc::recommend_plt::model::StatRateKey* Arena::CreateMaybeMessage<::abc::recommend_plt::model::StatRateKey>(Arena*);
template<> ::abc::recommend_plt::model::StatRateKey_Dimension* Arena::CreateMaybeMessage<::abc::recommend_plt::model::StatRateKey_Dimension>(Arena*);
template<> ::abc::recommend_plt::model::StatRateModel* Arena::CreateMaybeMessage<::abc::recommend_plt::model::StatRateModel>(Arena*);
template<> ::abc::recommend_plt::model::StatRateVal* Arena::CreateMaybeMessage<::abc::recommend_plt::model::StatRateVal>(Arena*);
template<> ::abc::recommend_plt::model::StatRateVal_Metric* Arena::CreateMaybeMessage<::abc::recommend_plt::model::StatRateVal_Metric>(Arena*);
template<> ::abc::recommend_plt::model::TransformFeaVal* Arena::CreateMaybeMessage<::abc::recommend_plt::model::TransformFeaVal>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace abc {
namespace recommend_plt {
namespace model {

// ===================================================================

class ItemTagsKey :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.ItemTagsKey) */ {
 public:
  ItemTagsKey();
  virtual ~ItemTagsKey();

  ItemTagsKey(const ItemTagsKey& from);
  ItemTagsKey(ItemTagsKey&& from) noexcept
    : ItemTagsKey() {
    *this = ::std::move(from);
  }

  inline ItemTagsKey& operator=(const ItemTagsKey& from) {
    CopyFrom(from);
    return *this;
  }
  inline ItemTagsKey& operator=(ItemTagsKey&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ItemTagsKey& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ItemTagsKey* internal_default_instance() {
    return reinterpret_cast<const ItemTagsKey*>(
               &_ItemTagsKey_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(ItemTagsKey* other);
  friend void swap(ItemTagsKey& a, ItemTagsKey& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ItemTagsKey* New() const final {
    return CreateMaybeMessage<ItemTagsKey>(nullptr);
  }

  ItemTagsKey* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ItemTagsKey>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ItemTagsKey& from);
  void MergeFrom(const ItemTagsKey& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ItemTagsKey* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.ItemTagsKey";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string pool = 1;
  void clear_pool();
  static const int kPoolFieldNumber = 1;
  const std::string& pool() const;
  void set_pool(const std::string& value);
  void set_pool(std::string&& value);
  void set_pool(const char* value);
  void set_pool(const char* value, size_t size);
  std::string* mutable_pool();
  std::string* release_pool();
  void set_allocated_pool(std::string* pool);

  // string id = 2;
  void clear_id();
  static const int kIdFieldNumber = 2;
  const std::string& id() const;
  void set_id(const std::string& value);
  void set_id(std::string&& value);
  void set_id(const char* value);
  void set_id(const char* value, size_t size);
  std::string* mutable_id();
  std::string* release_id();
  void set_allocated_id(std::string* id);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemTagsKey)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr pool_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class ItemTagsVal_Tag :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.ItemTagsVal.Tag) */ {
 public:
  ItemTagsVal_Tag();
  virtual ~ItemTagsVal_Tag();

  ItemTagsVal_Tag(const ItemTagsVal_Tag& from);
  ItemTagsVal_Tag(ItemTagsVal_Tag&& from) noexcept
    : ItemTagsVal_Tag() {
    *this = ::std::move(from);
  }

  inline ItemTagsVal_Tag& operator=(const ItemTagsVal_Tag& from) {
    CopyFrom(from);
    return *this;
  }
  inline ItemTagsVal_Tag& operator=(ItemTagsVal_Tag&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ItemTagsVal_Tag& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ItemTagsVal_Tag* internal_default_instance() {
    return reinterpret_cast<const ItemTagsVal_Tag*>(
               &_ItemTagsVal_Tag_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void Swap(ItemTagsVal_Tag* other);
  friend void swap(ItemTagsVal_Tag& a, ItemTagsVal_Tag& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ItemTagsVal_Tag* New() const final {
    return CreateMaybeMessage<ItemTagsVal_Tag>(nullptr);
  }

  ItemTagsVal_Tag* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ItemTagsVal_Tag>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ItemTagsVal_Tag& from);
  void MergeFrom(const ItemTagsVal_Tag& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ItemTagsVal_Tag* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.ItemTagsVal.Tag";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int64 tag_vals = 3;
  int tag_vals_size() const;
  void clear_tag_vals();
  static const int kTagValsFieldNumber = 3;
  ::PROTOBUF_NAMESPACE_ID::int64 tag_vals(int index) const;
  void set_tag_vals(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_tag_vals(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      tag_vals() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_tag_vals();

  // int64 tag_val = 2;
  void clear_tag_val();
  static const int kTagValFieldNumber = 2;
  ::PROTOBUF_NAMESPACE_ID::int64 tag_val() const;
  void set_tag_val(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int32 tag_id = 1;
  void clear_tag_id();
  static const int kTagIdFieldNumber = 1;
  ::PROTOBUF_NAMESPACE_ID::int32 tag_id() const;
  void set_tag_id(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemTagsVal.Tag)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > tag_vals_;
  mutable std::atomic<int> _tag_vals_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 tag_val_;
  ::PROTOBUF_NAMESPACE_ID::int32 tag_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class ItemTagsVal :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.ItemTagsVal) */ {
 public:
  ItemTagsVal();
  virtual ~ItemTagsVal();

  ItemTagsVal(const ItemTagsVal& from);
  ItemTagsVal(ItemTagsVal&& from) noexcept
    : ItemTagsVal() {
    *this = ::std::move(from);
  }

  inline ItemTagsVal& operator=(const ItemTagsVal& from) {
    CopyFrom(from);
    return *this;
  }
  inline ItemTagsVal& operator=(ItemTagsVal&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ItemTagsVal& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ItemTagsVal* internal_default_instance() {
    return reinterpret_cast<const ItemTagsVal*>(
               &_ItemTagsVal_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void Swap(ItemTagsVal* other);
  friend void swap(ItemTagsVal& a, ItemTagsVal& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ItemTagsVal* New() const final {
    return CreateMaybeMessage<ItemTagsVal>(nullptr);
  }

  ItemTagsVal* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ItemTagsVal>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ItemTagsVal& from);
  void MergeFrom(const ItemTagsVal& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ItemTagsVal* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.ItemTagsVal";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef ItemTagsVal_Tag Tag;

  // accessors -------------------------------------------------------

  // repeated .abc.recommend_plt.model.ItemTagsVal.Tag tags = 1;
  int tags_size() const;
  void clear_tags();
  static const int kTagsFieldNumber = 1;
  ::abc::recommend_plt::model::ItemTagsVal_Tag* mutable_tags(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::ItemTagsVal_Tag >*
      mutable_tags();
  const ::abc::recommend_plt::model::ItemTagsVal_Tag& tags(int index) const;
  ::abc::recommend_plt::model::ItemTagsVal_Tag* add_tags();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::ItemTagsVal_Tag >&
      tags() const;

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemTagsVal)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::ItemTagsVal_Tag > tags_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class ItemTagsInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.ItemTagsInfo) */ {
 public:
  ItemTagsInfo();
  virtual ~ItemTagsInfo();

  ItemTagsInfo(const ItemTagsInfo& from);
  ItemTagsInfo(ItemTagsInfo&& from) noexcept
    : ItemTagsInfo() {
    *this = ::std::move(from);
  }

  inline ItemTagsInfo& operator=(const ItemTagsInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline ItemTagsInfo& operator=(ItemTagsInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ItemTagsInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ItemTagsInfo* internal_default_instance() {
    return reinterpret_cast<const ItemTagsInfo*>(
               &_ItemTagsInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void Swap(ItemTagsInfo* other);
  friend void swap(ItemTagsInfo& a, ItemTagsInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ItemTagsInfo* New() const final {
    return CreateMaybeMessage<ItemTagsInfo>(nullptr);
  }

  ItemTagsInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ItemTagsInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ItemTagsInfo& from);
  void MergeFrom(const ItemTagsInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ItemTagsInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.ItemTagsInfo";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .abc.recommend_plt.model.ItemTagsKey key = 1;
  bool has_key() const;
  void clear_key();
  static const int kKeyFieldNumber = 1;
  const ::abc::recommend_plt::model::ItemTagsKey& key() const;
  ::abc::recommend_plt::model::ItemTagsKey* release_key();
  ::abc::recommend_plt::model::ItemTagsKey* mutable_key();
  void set_allocated_key(::abc::recommend_plt::model::ItemTagsKey* key);

  // .abc.recommend_plt.model.ItemTagsVal val = 2;
  bool has_val() const;
  void clear_val();
  static const int kValFieldNumber = 2;
  const ::abc::recommend_plt::model::ItemTagsVal& val() const;
  ::abc::recommend_plt::model::ItemTagsVal* release_val();
  ::abc::recommend_plt::model::ItemTagsVal* mutable_val();
  void set_allocated_val(::abc::recommend_plt::model::ItemTagsVal* val);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemTagsInfo)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::abc::recommend_plt::model::ItemTagsKey* key_;
  ::abc::recommend_plt::model::ItemTagsVal* val_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class ItemTagsModel :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.ItemTagsModel) */ {
 public:
  ItemTagsModel();
  virtual ~ItemTagsModel();

  ItemTagsModel(const ItemTagsModel& from);
  ItemTagsModel(ItemTagsModel&& from) noexcept
    : ItemTagsModel() {
    *this = ::std::move(from);
  }

  inline ItemTagsModel& operator=(const ItemTagsModel& from) {
    CopyFrom(from);
    return *this;
  }
  inline ItemTagsModel& operator=(ItemTagsModel&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ItemTagsModel& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ItemTagsModel* internal_default_instance() {
    return reinterpret_cast<const ItemTagsModel*>(
               &_ItemTagsModel_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void Swap(ItemTagsModel* other);
  friend void swap(ItemTagsModel& a, ItemTagsModel& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ItemTagsModel* New() const final {
    return CreateMaybeMessage<ItemTagsModel>(nullptr);
  }

  ItemTagsModel* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ItemTagsModel>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ItemTagsModel& from);
  void MergeFrom(const ItemTagsModel& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ItemTagsModel* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.ItemTagsModel";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .abc.recommend_plt.model.ItemTagsInfo infos = 2;
  int infos_size() const;
  void clear_infos();
  static const int kInfosFieldNumber = 2;
  ::abc::recommend_plt::model::ItemTagsInfo* mutable_infos(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::ItemTagsInfo >*
      mutable_infos();
  const ::abc::recommend_plt::model::ItemTagsInfo& infos(int index) const;
  ::abc::recommend_plt::model::ItemTagsInfo* add_infos();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::ItemTagsInfo >&
      infos() const;

  // int32 bussiness_id = 1;
  void clear_bussiness_id();
  static const int kBussinessIdFieldNumber = 1;
  ::PROTOBUF_NAMESPACE_ID::int32 bussiness_id() const;
  void set_bussiness_id(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemTagsModel)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::ItemTagsInfo > infos_;
  ::PROTOBUF_NAMESPACE_ID::int32 bussiness_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class TransformFeaVal :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.TransformFeaVal) */ {
 public:
  TransformFeaVal();
  virtual ~TransformFeaVal();

  TransformFeaVal(const TransformFeaVal& from);
  TransformFeaVal(TransformFeaVal&& from) noexcept
    : TransformFeaVal() {
    *this = ::std::move(from);
  }

  inline TransformFeaVal& operator=(const TransformFeaVal& from) {
    CopyFrom(from);
    return *this;
  }
  inline TransformFeaVal& operator=(TransformFeaVal&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TransformFeaVal& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TransformFeaVal* internal_default_instance() {
    return reinterpret_cast<const TransformFeaVal*>(
               &_TransformFeaVal_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void Swap(TransformFeaVal* other);
  friend void swap(TransformFeaVal& a, TransformFeaVal& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TransformFeaVal* New() const final {
    return CreateMaybeMessage<TransformFeaVal>(nullptr);
  }

  TransformFeaVal* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TransformFeaVal>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TransformFeaVal& from);
  void MergeFrom(const TransformFeaVal& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TransformFeaVal* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.TransformFeaVal";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string raw_fea = 1;
  void clear_raw_fea();
  static const int kRawFeaFieldNumber = 1;
  const std::string& raw_fea() const;
  void set_raw_fea(const std::string& value);
  void set_raw_fea(std::string&& value);
  void set_raw_fea(const char* value);
  void set_raw_fea(const char* value, size_t size);
  std::string* mutable_raw_fea();
  std::string* release_raw_fea();
  void set_allocated_raw_fea(std::string* raw_fea);

  // string raw_val = 2;
  void clear_raw_val();
  static const int kRawValFieldNumber = 2;
  const std::string& raw_val() const;
  void set_raw_val(const std::string& value);
  void set_raw_val(std::string&& value);
  void set_raw_val(const char* value);
  void set_raw_val(const char* value, size_t size);
  std::string* mutable_raw_val();
  std::string* release_raw_val();
  void set_allocated_raw_val(std::string* raw_val);

  // string trans_fea = 3;
  void clear_trans_fea();
  static const int kTransFeaFieldNumber = 3;
  const std::string& trans_fea() const;
  void set_trans_fea(const std::string& value);
  void set_trans_fea(std::string&& value);
  void set_trans_fea(const char* value);
  void set_trans_fea(const char* value, size_t size);
  std::string* mutable_trans_fea();
  std::string* release_trans_fea();
  void set_allocated_trans_fea(std::string* trans_fea);

  // string trans_val = 4;
  void clear_trans_val();
  static const int kTransValFieldNumber = 4;
  const std::string& trans_val() const;
  void set_trans_val(const std::string& value);
  void set_trans_val(std::string&& value);
  void set_trans_val(const char* value);
  void set_trans_val(const char* value, size_t size);
  std::string* mutable_trans_val();
  std::string* release_trans_val();
  void set_allocated_trans_val(std::string* trans_val);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.TransformFeaVal)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr raw_fea_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr raw_val_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr trans_fea_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr trans_val_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class RecList_Dimension :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.RecList.Dimension) */ {
 public:
  RecList_Dimension();
  virtual ~RecList_Dimension();

  RecList_Dimension(const RecList_Dimension& from);
  RecList_Dimension(RecList_Dimension&& from) noexcept
    : RecList_Dimension() {
    *this = ::std::move(from);
  }

  inline RecList_Dimension& operator=(const RecList_Dimension& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecList_Dimension& operator=(RecList_Dimension&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RecList_Dimension& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RecList_Dimension* internal_default_instance() {
    return reinterpret_cast<const RecList_Dimension*>(
               &_RecList_Dimension_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  void Swap(RecList_Dimension* other);
  friend void swap(RecList_Dimension& a, RecList_Dimension& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RecList_Dimension* New() const final {
    return CreateMaybeMessage<RecList_Dimension>(nullptr);
  }

  RecList_Dimension* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RecList_Dimension>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RecList_Dimension& from);
  void MergeFrom(const RecList_Dimension& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecList_Dimension* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.RecList.Dimension";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string fea = 1;
  void clear_fea();
  static const int kFeaFieldNumber = 1;
  const std::string& fea() const;
  void set_fea(const std::string& value);
  void set_fea(std::string&& value);
  void set_fea(const char* value);
  void set_fea(const char* value, size_t size);
  std::string* mutable_fea();
  std::string* release_fea();
  void set_allocated_fea(std::string* fea);

  // string val = 2;
  void clear_val();
  static const int kValFieldNumber = 2;
  const std::string& val() const;
  void set_val(const std::string& value);
  void set_val(std::string&& value);
  void set_val(const char* value);
  void set_val(const char* value, size_t size);
  std::string* mutable_val();
  std::string* release_val();
  void set_allocated_val(std::string* val);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecList.Dimension)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr fea_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr val_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class RecList_Item :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.RecList.Item) */ {
 public:
  RecList_Item();
  virtual ~RecList_Item();

  RecList_Item(const RecList_Item& from);
  RecList_Item(RecList_Item&& from) noexcept
    : RecList_Item() {
    *this = ::std::move(from);
  }

  inline RecList_Item& operator=(const RecList_Item& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecList_Item& operator=(RecList_Item&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RecList_Item& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RecList_Item* internal_default_instance() {
    return reinterpret_cast<const RecList_Item*>(
               &_RecList_Item_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  void Swap(RecList_Item* other);
  friend void swap(RecList_Item& a, RecList_Item& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RecList_Item* New() const final {
    return CreateMaybeMessage<RecList_Item>(nullptr);
  }

  RecList_Item* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RecList_Item>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RecList_Item& from);
  void MergeFrom(const RecList_Item& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecList_Item* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.RecList.Item";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string id = 1;
  void clear_id();
  static const int kIdFieldNumber = 1;
  const std::string& id() const;
  void set_id(const std::string& value);
  void set_id(std::string&& value);
  void set_id(const char* value);
  void set_id(const char* value, size_t size);
  std::string* mutable_id();
  std::string* release_id();
  void set_allocated_id(std::string* id);

  // float score = 2;
  void clear_score();
  static const int kScoreFieldNumber = 2;
  float score() const;
  void set_score(float value);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecList.Item)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  float score_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class RecList :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.RecList) */ {
 public:
  RecList();
  virtual ~RecList();

  RecList(const RecList& from);
  RecList(RecList&& from) noexcept
    : RecList() {
    *this = ::std::move(from);
  }

  inline RecList& operator=(const RecList& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecList& operator=(RecList&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RecList& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RecList* internal_default_instance() {
    return reinterpret_cast<const RecList*>(
               &_RecList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  void Swap(RecList* other);
  friend void swap(RecList& a, RecList& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RecList* New() const final {
    return CreateMaybeMessage<RecList>(nullptr);
  }

  RecList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RecList>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RecList& from);
  void MergeFrom(const RecList& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecList* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.RecList";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef RecList_Dimension Dimension;
  typedef RecList_Item Item;

  // accessors -------------------------------------------------------

  // repeated .abc.recommend_plt.model.RecList.Dimension dimensions = 1;
  int dimensions_size() const;
  void clear_dimensions();
  static const int kDimensionsFieldNumber = 1;
  ::abc::recommend_plt::model::RecList_Dimension* mutable_dimensions(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecList_Dimension >*
      mutable_dimensions();
  const ::abc::recommend_plt::model::RecList_Dimension& dimensions(int index) const;
  ::abc::recommend_plt::model::RecList_Dimension* add_dimensions();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecList_Dimension >&
      dimensions() const;

  // repeated .abc.recommend_plt.model.RecList.Item items = 2;
  int items_size() const;
  void clear_items();
  static const int kItemsFieldNumber = 2;
  ::abc::recommend_plt::model::RecList_Item* mutable_items(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecList_Item >*
      mutable_items();
  const ::abc::recommend_plt::model::RecList_Item& items(int index) const;
  ::abc::recommend_plt::model::RecList_Item* add_items();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecList_Item >&
      items() const;

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecList)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecList_Dimension > dimensions_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecList_Item > items_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class RecListKey_Dimension :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.RecListKey.Dimension) */ {
 public:
  RecListKey_Dimension();
  virtual ~RecListKey_Dimension();

  RecListKey_Dimension(const RecListKey_Dimension& from);
  RecListKey_Dimension(RecListKey_Dimension&& from) noexcept
    : RecListKey_Dimension() {
    *this = ::std::move(from);
  }

  inline RecListKey_Dimension& operator=(const RecListKey_Dimension& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecListKey_Dimension& operator=(RecListKey_Dimension&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RecListKey_Dimension& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RecListKey_Dimension* internal_default_instance() {
    return reinterpret_cast<const RecListKey_Dimension*>(
               &_RecListKey_Dimension_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  void Swap(RecListKey_Dimension* other);
  friend void swap(RecListKey_Dimension& a, RecListKey_Dimension& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RecListKey_Dimension* New() const final {
    return CreateMaybeMessage<RecListKey_Dimension>(nullptr);
  }

  RecListKey_Dimension* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RecListKey_Dimension>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RecListKey_Dimension& from);
  void MergeFrom(const RecListKey_Dimension& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecListKey_Dimension* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.RecListKey.Dimension";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string fea = 1;
  void clear_fea();
  static const int kFeaFieldNumber = 1;
  const std::string& fea() const;
  void set_fea(const std::string& value);
  void set_fea(std::string&& value);
  void set_fea(const char* value);
  void set_fea(const char* value, size_t size);
  std::string* mutable_fea();
  std::string* release_fea();
  void set_allocated_fea(std::string* fea);

  // string val = 2;
  void clear_val();
  static const int kValFieldNumber = 2;
  const std::string& val() const;
  void set_val(const std::string& value);
  void set_val(std::string&& value);
  void set_val(const char* value);
  void set_val(const char* value, size_t size);
  std::string* mutable_val();
  std::string* release_val();
  void set_allocated_val(std::string* val);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecListKey.Dimension)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr fea_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr val_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class RecListKey :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.RecListKey) */ {
 public:
  RecListKey();
  virtual ~RecListKey();

  RecListKey(const RecListKey& from);
  RecListKey(RecListKey&& from) noexcept
    : RecListKey() {
    *this = ::std::move(from);
  }

  inline RecListKey& operator=(const RecListKey& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecListKey& operator=(RecListKey&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RecListKey& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RecListKey* internal_default_instance() {
    return reinterpret_cast<const RecListKey*>(
               &_RecListKey_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  void Swap(RecListKey* other);
  friend void swap(RecListKey& a, RecListKey& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RecListKey* New() const final {
    return CreateMaybeMessage<RecListKey>(nullptr);
  }

  RecListKey* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RecListKey>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RecListKey& from);
  void MergeFrom(const RecListKey& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecListKey* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.RecListKey";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef RecListKey_Dimension Dimension;

  // accessors -------------------------------------------------------

  // repeated .abc.recommend_plt.model.RecListKey.Dimension dimensions = 1;
  int dimensions_size() const;
  void clear_dimensions();
  static const int kDimensionsFieldNumber = 1;
  ::abc::recommend_plt::model::RecListKey_Dimension* mutable_dimensions(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecListKey_Dimension >*
      mutable_dimensions();
  const ::abc::recommend_plt::model::RecListKey_Dimension& dimensions(int index) const;
  ::abc::recommend_plt::model::RecListKey_Dimension* add_dimensions();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecListKey_Dimension >&
      dimensions() const;

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecListKey)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecListKey_Dimension > dimensions_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class RecListVal_Item :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.RecListVal.Item) */ {
 public:
  RecListVal_Item();
  virtual ~RecListVal_Item();

  RecListVal_Item(const RecListVal_Item& from);
  RecListVal_Item(RecListVal_Item&& from) noexcept
    : RecListVal_Item() {
    *this = ::std::move(from);
  }

  inline RecListVal_Item& operator=(const RecListVal_Item& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecListVal_Item& operator=(RecListVal_Item&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RecListVal_Item& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RecListVal_Item* internal_default_instance() {
    return reinterpret_cast<const RecListVal_Item*>(
               &_RecListVal_Item_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  void Swap(RecListVal_Item* other);
  friend void swap(RecListVal_Item& a, RecListVal_Item& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RecListVal_Item* New() const final {
    return CreateMaybeMessage<RecListVal_Item>(nullptr);
  }

  RecListVal_Item* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RecListVal_Item>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RecListVal_Item& from);
  void MergeFrom(const RecListVal_Item& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecListVal_Item* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.RecListVal.Item";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string id = 1;
  void clear_id();
  static const int kIdFieldNumber = 1;
  const std::string& id() const;
  void set_id(const std::string& value);
  void set_id(std::string&& value);
  void set_id(const char* value);
  void set_id(const char* value, size_t size);
  std::string* mutable_id();
  std::string* release_id();
  void set_allocated_id(std::string* id);

  // float score = 2;
  void clear_score();
  static const int kScoreFieldNumber = 2;
  float score() const;
  void set_score(float value);

  // int32 cate = 3;
  void clear_cate();
  static const int kCateFieldNumber = 3;
  ::PROTOBUF_NAMESPACE_ID::int32 cate() const;
  void set_cate(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int64 parent_id = 4;
  void clear_parent_id();
  static const int kParentIdFieldNumber = 4;
  ::PROTOBUF_NAMESPACE_ID::int64 parent_id() const;
  void set_parent_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecListVal.Item)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  float score_;
  ::PROTOBUF_NAMESPACE_ID::int32 cate_;
  ::PROTOBUF_NAMESPACE_ID::int64 parent_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class RecListVal :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.RecListVal) */ {
 public:
  RecListVal();
  virtual ~RecListVal();

  RecListVal(const RecListVal& from);
  RecListVal(RecListVal&& from) noexcept
    : RecListVal() {
    *this = ::std::move(from);
  }

  inline RecListVal& operator=(const RecListVal& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecListVal& operator=(RecListVal&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RecListVal& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RecListVal* internal_default_instance() {
    return reinterpret_cast<const RecListVal*>(
               &_RecListVal_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  void Swap(RecListVal* other);
  friend void swap(RecListVal& a, RecListVal& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RecListVal* New() const final {
    return CreateMaybeMessage<RecListVal>(nullptr);
  }

  RecListVal* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RecListVal>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RecListVal& from);
  void MergeFrom(const RecListVal& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecListVal* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.RecListVal";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef RecListVal_Item Item;

  // accessors -------------------------------------------------------

  // repeated .abc.recommend_plt.model.RecListVal.Item items = 2;
  int items_size() const;
  void clear_items();
  static const int kItemsFieldNumber = 2;
  ::abc::recommend_plt::model::RecListVal_Item* mutable_items(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecListVal_Item >*
      mutable_items();
  const ::abc::recommend_plt::model::RecListVal_Item& items(int index) const;
  ::abc::recommend_plt::model::RecListVal_Item* add_items();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecListVal_Item >&
      items() const;

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecListVal)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecListVal_Item > items_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class RecListInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.RecListInfo) */ {
 public:
  RecListInfo();
  virtual ~RecListInfo();

  RecListInfo(const RecListInfo& from);
  RecListInfo(RecListInfo&& from) noexcept
    : RecListInfo() {
    *this = ::std::move(from);
  }

  inline RecListInfo& operator=(const RecListInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecListInfo& operator=(RecListInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RecListInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RecListInfo* internal_default_instance() {
    return reinterpret_cast<const RecListInfo*>(
               &_RecListInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  void Swap(RecListInfo* other);
  friend void swap(RecListInfo& a, RecListInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RecListInfo* New() const final {
    return CreateMaybeMessage<RecListInfo>(nullptr);
  }

  RecListInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RecListInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RecListInfo& from);
  void MergeFrom(const RecListInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecListInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.RecListInfo";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .abc.recommend_plt.model.RecListKey key = 1;
  bool has_key() const;
  void clear_key();
  static const int kKeyFieldNumber = 1;
  const ::abc::recommend_plt::model::RecListKey& key() const;
  ::abc::recommend_plt::model::RecListKey* release_key();
  ::abc::recommend_plt::model::RecListKey* mutable_key();
  void set_allocated_key(::abc::recommend_plt::model::RecListKey* key);

  // .abc.recommend_plt.model.RecListVal val = 2;
  bool has_val() const;
  void clear_val();
  static const int kValFieldNumber = 2;
  const ::abc::recommend_plt::model::RecListVal& val() const;
  ::abc::recommend_plt::model::RecListVal* release_val();
  ::abc::recommend_plt::model::RecListVal* mutable_val();
  void set_allocated_val(::abc::recommend_plt::model::RecListVal* val);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecListInfo)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::abc::recommend_plt::model::RecListKey* key_;
  ::abc::recommend_plt::model::RecListVal* val_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class RecListModel :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.RecListModel) */ {
 public:
  RecListModel();
  virtual ~RecListModel();

  RecListModel(const RecListModel& from);
  RecListModel(RecListModel&& from) noexcept
    : RecListModel() {
    *this = ::std::move(from);
  }

  inline RecListModel& operator=(const RecListModel& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecListModel& operator=(RecListModel&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RecListModel& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RecListModel* internal_default_instance() {
    return reinterpret_cast<const RecListModel*>(
               &_RecListModel_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  void Swap(RecListModel* other);
  friend void swap(RecListModel& a, RecListModel& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RecListModel* New() const final {
    return CreateMaybeMessage<RecListModel>(nullptr);
  }

  RecListModel* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RecListModel>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RecListModel& from);
  void MergeFrom(const RecListModel& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecListModel* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.RecListModel";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .abc.recommend_plt.model.RecList rec_lists = 3;
  int rec_lists_size() const;
  void clear_rec_lists();
  static const int kRecListsFieldNumber = 3;
  ::abc::recommend_plt::model::RecList* mutable_rec_lists(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecList >*
      mutable_rec_lists();
  const ::abc::recommend_plt::model::RecList& rec_lists(int index) const;
  ::abc::recommend_plt::model::RecList* add_rec_lists();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecList >&
      rec_lists() const;

  // repeated .abc.recommend_plt.model.TransformFeaVal trans_fea_vals = 4;
  int trans_fea_vals_size() const;
  void clear_trans_fea_vals();
  static const int kTransFeaValsFieldNumber = 4;
  ::abc::recommend_plt::model::TransformFeaVal* mutable_trans_fea_vals(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::TransformFeaVal >*
      mutable_trans_fea_vals();
  const ::abc::recommend_plt::model::TransformFeaVal& trans_fea_vals(int index) const;
  ::abc::recommend_plt::model::TransformFeaVal* add_trans_fea_vals();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::TransformFeaVal >&
      trans_fea_vals() const;

  // repeated .abc.recommend_plt.model.RecListInfo rec_list_infos = 5;
  int rec_list_infos_size() const;
  void clear_rec_list_infos();
  static const int kRecListInfosFieldNumber = 5;
  ::abc::recommend_plt::model::RecListInfo* mutable_rec_list_infos(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecListInfo >*
      mutable_rec_list_infos();
  const ::abc::recommend_plt::model::RecListInfo& rec_list_infos(int index) const;
  ::abc::recommend_plt::model::RecListInfo* add_rec_list_infos();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecListInfo >&
      rec_list_infos() const;

  // string model_id = 2;
  void clear_model_id();
  static const int kModelIdFieldNumber = 2;
  const std::string& model_id() const;
  void set_model_id(const std::string& value);
  void set_model_id(std::string&& value);
  void set_model_id(const char* value);
  void set_model_id(const char* value, size_t size);
  std::string* mutable_model_id();
  std::string* release_model_id();
  void set_allocated_model_id(std::string* model_id);

  // uint32 bussiness_id = 1;
  void clear_bussiness_id();
  static const int kBussinessIdFieldNumber = 1;
  ::PROTOBUF_NAMESPACE_ID::uint32 bussiness_id() const;
  void set_bussiness_id(::PROTOBUF_NAMESPACE_ID::uint32 value);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.RecListModel)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecList > rec_lists_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::TransformFeaVal > trans_fea_vals_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecListInfo > rec_list_infos_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_id_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bussiness_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class FeatureMappingKey :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.FeatureMappingKey) */ {
 public:
  FeatureMappingKey();
  virtual ~FeatureMappingKey();

  FeatureMappingKey(const FeatureMappingKey& from);
  FeatureMappingKey(FeatureMappingKey&& from) noexcept
    : FeatureMappingKey() {
    *this = ::std::move(from);
  }

  inline FeatureMappingKey& operator=(const FeatureMappingKey& from) {
    CopyFrom(from);
    return *this;
  }
  inline FeatureMappingKey& operator=(FeatureMappingKey&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const FeatureMappingKey& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FeatureMappingKey* internal_default_instance() {
    return reinterpret_cast<const FeatureMappingKey*>(
               &_FeatureMappingKey_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  void Swap(FeatureMappingKey* other);
  friend void swap(FeatureMappingKey& a, FeatureMappingKey& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline FeatureMappingKey* New() const final {
    return CreateMaybeMessage<FeatureMappingKey>(nullptr);
  }

  FeatureMappingKey* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FeatureMappingKey>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const FeatureMappingKey& from);
  void MergeFrom(const FeatureMappingKey& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FeatureMappingKey* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.FeatureMappingKey";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string raw_fea = 1;
  void clear_raw_fea();
  static const int kRawFeaFieldNumber = 1;
  const std::string& raw_fea() const;
  void set_raw_fea(const std::string& value);
  void set_raw_fea(std::string&& value);
  void set_raw_fea(const char* value);
  void set_raw_fea(const char* value, size_t size);
  std::string* mutable_raw_fea();
  std::string* release_raw_fea();
  void set_allocated_raw_fea(std::string* raw_fea);

  // string raw_val = 2;
  void clear_raw_val();
  static const int kRawValFieldNumber = 2;
  const std::string& raw_val() const;
  void set_raw_val(const std::string& value);
  void set_raw_val(std::string&& value);
  void set_raw_val(const char* value);
  void set_raw_val(const char* value, size_t size);
  std::string* mutable_raw_val();
  std::string* release_raw_val();
  void set_allocated_raw_val(std::string* raw_val);

  // string site_id = 3;
  void clear_site_id();
  static const int kSiteIdFieldNumber = 3;
  const std::string& site_id() const;
  void set_site_id(const std::string& value);
  void set_site_id(std::string&& value);
  void set_site_id(const char* value);
  void set_site_id(const char* value, size_t size);
  std::string* mutable_site_id();
  std::string* release_site_id();
  void set_allocated_site_id(std::string* site_id);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.FeatureMappingKey)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr raw_fea_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr raw_val_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr site_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class FeatureMappingVal :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.FeatureMappingVal) */ {
 public:
  FeatureMappingVal();
  virtual ~FeatureMappingVal();

  FeatureMappingVal(const FeatureMappingVal& from);
  FeatureMappingVal(FeatureMappingVal&& from) noexcept
    : FeatureMappingVal() {
    *this = ::std::move(from);
  }

  inline FeatureMappingVal& operator=(const FeatureMappingVal& from) {
    CopyFrom(from);
    return *this;
  }
  inline FeatureMappingVal& operator=(FeatureMappingVal&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const FeatureMappingVal& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FeatureMappingVal* internal_default_instance() {
    return reinterpret_cast<const FeatureMappingVal*>(
               &_FeatureMappingVal_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  void Swap(FeatureMappingVal* other);
  friend void swap(FeatureMappingVal& a, FeatureMappingVal& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline FeatureMappingVal* New() const final {
    return CreateMaybeMessage<FeatureMappingVal>(nullptr);
  }

  FeatureMappingVal* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FeatureMappingVal>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const FeatureMappingVal& from);
  void MergeFrom(const FeatureMappingVal& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FeatureMappingVal* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.FeatureMappingVal";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string map_fea = 1;
  void clear_map_fea();
  static const int kMapFeaFieldNumber = 1;
  const std::string& map_fea() const;
  void set_map_fea(const std::string& value);
  void set_map_fea(std::string&& value);
  void set_map_fea(const char* value);
  void set_map_fea(const char* value, size_t size);
  std::string* mutable_map_fea();
  std::string* release_map_fea();
  void set_allocated_map_fea(std::string* map_fea);

  // string map_val = 2;
  void clear_map_val();
  static const int kMapValFieldNumber = 2;
  const std::string& map_val() const;
  void set_map_val(const std::string& value);
  void set_map_val(std::string&& value);
  void set_map_val(const char* value);
  void set_map_val(const char* value, size_t size);
  std::string* mutable_map_val();
  std::string* release_map_val();
  void set_allocated_map_val(std::string* map_val);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.FeatureMappingVal)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr map_fea_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr map_val_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class FeatureMappingMultiVal :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.FeatureMappingMultiVal) */ {
 public:
  FeatureMappingMultiVal();
  virtual ~FeatureMappingMultiVal();

  FeatureMappingMultiVal(const FeatureMappingMultiVal& from);
  FeatureMappingMultiVal(FeatureMappingMultiVal&& from) noexcept
    : FeatureMappingMultiVal() {
    *this = ::std::move(from);
  }

  inline FeatureMappingMultiVal& operator=(const FeatureMappingMultiVal& from) {
    CopyFrom(from);
    return *this;
  }
  inline FeatureMappingMultiVal& operator=(FeatureMappingMultiVal&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const FeatureMappingMultiVal& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FeatureMappingMultiVal* internal_default_instance() {
    return reinterpret_cast<const FeatureMappingMultiVal*>(
               &_FeatureMappingMultiVal_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  void Swap(FeatureMappingMultiVal* other);
  friend void swap(FeatureMappingMultiVal& a, FeatureMappingMultiVal& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline FeatureMappingMultiVal* New() const final {
    return CreateMaybeMessage<FeatureMappingMultiVal>(nullptr);
  }

  FeatureMappingMultiVal* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FeatureMappingMultiVal>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const FeatureMappingMultiVal& from);
  void MergeFrom(const FeatureMappingMultiVal& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FeatureMappingMultiVal* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.FeatureMappingMultiVal";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .abc.recommend_plt.model.FeatureMappingVal vals = 1;
  int vals_size() const;
  void clear_vals();
  static const int kValsFieldNumber = 1;
  ::abc::recommend_plt::model::FeatureMappingVal* mutable_vals(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::FeatureMappingVal >*
      mutable_vals();
  const ::abc::recommend_plt::model::FeatureMappingVal& vals(int index) const;
  ::abc::recommend_plt::model::FeatureMappingVal* add_vals();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::FeatureMappingVal >&
      vals() const;

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.FeatureMappingMultiVal)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::FeatureMappingVal > vals_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class FeatureMappingInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.FeatureMappingInfo) */ {
 public:
  FeatureMappingInfo();
  virtual ~FeatureMappingInfo();

  FeatureMappingInfo(const FeatureMappingInfo& from);
  FeatureMappingInfo(FeatureMappingInfo&& from) noexcept
    : FeatureMappingInfo() {
    *this = ::std::move(from);
  }

  inline FeatureMappingInfo& operator=(const FeatureMappingInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline FeatureMappingInfo& operator=(FeatureMappingInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const FeatureMappingInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FeatureMappingInfo* internal_default_instance() {
    return reinterpret_cast<const FeatureMappingInfo*>(
               &_FeatureMappingInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  void Swap(FeatureMappingInfo* other);
  friend void swap(FeatureMappingInfo& a, FeatureMappingInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline FeatureMappingInfo* New() const final {
    return CreateMaybeMessage<FeatureMappingInfo>(nullptr);
  }

  FeatureMappingInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FeatureMappingInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const FeatureMappingInfo& from);
  void MergeFrom(const FeatureMappingInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FeatureMappingInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.FeatureMappingInfo";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .abc.recommend_plt.model.FeatureMappingKey key = 1;
  bool has_key() const;
  void clear_key();
  static const int kKeyFieldNumber = 1;
  const ::abc::recommend_plt::model::FeatureMappingKey& key() const;
  ::abc::recommend_plt::model::FeatureMappingKey* release_key();
  ::abc::recommend_plt::model::FeatureMappingKey* mutable_key();
  void set_allocated_key(::abc::recommend_plt::model::FeatureMappingKey* key);

  // .abc.recommend_plt.model.FeatureMappingVal val = 2;
  bool has_val() const;
  void clear_val();
  static const int kValFieldNumber = 2;
  const ::abc::recommend_plt::model::FeatureMappingVal& val() const;
  ::abc::recommend_plt::model::FeatureMappingVal* release_val();
  ::abc::recommend_plt::model::FeatureMappingVal* mutable_val();
  void set_allocated_val(::abc::recommend_plt::model::FeatureMappingVal* val);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.FeatureMappingInfo)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::abc::recommend_plt::model::FeatureMappingKey* key_;
  ::abc::recommend_plt::model::FeatureMappingVal* val_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class FeatureMappingModel :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.FeatureMappingModel) */ {
 public:
  FeatureMappingModel();
  virtual ~FeatureMappingModel();

  FeatureMappingModel(const FeatureMappingModel& from);
  FeatureMappingModel(FeatureMappingModel&& from) noexcept
    : FeatureMappingModel() {
    *this = ::std::move(from);
  }

  inline FeatureMappingModel& operator=(const FeatureMappingModel& from) {
    CopyFrom(from);
    return *this;
  }
  inline FeatureMappingModel& operator=(FeatureMappingModel&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const FeatureMappingModel& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FeatureMappingModel* internal_default_instance() {
    return reinterpret_cast<const FeatureMappingModel*>(
               &_FeatureMappingModel_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  void Swap(FeatureMappingModel* other);
  friend void swap(FeatureMappingModel& a, FeatureMappingModel& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline FeatureMappingModel* New() const final {
    return CreateMaybeMessage<FeatureMappingModel>(nullptr);
  }

  FeatureMappingModel* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FeatureMappingModel>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const FeatureMappingModel& from);
  void MergeFrom(const FeatureMappingModel& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FeatureMappingModel* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.FeatureMappingModel";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .abc.recommend_plt.model.FeatureMappingInfo infos = 2;
  int infos_size() const;
  void clear_infos();
  static const int kInfosFieldNumber = 2;
  ::abc::recommend_plt::model::FeatureMappingInfo* mutable_infos(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::FeatureMappingInfo >*
      mutable_infos();
  const ::abc::recommend_plt::model::FeatureMappingInfo& infos(int index) const;
  ::abc::recommend_plt::model::FeatureMappingInfo* add_infos();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::FeatureMappingInfo >&
      infos() const;

  // uint32 bussiness_id = 1;
  void clear_bussiness_id();
  static const int kBussinessIdFieldNumber = 1;
  ::PROTOBUF_NAMESPACE_ID::uint32 bussiness_id() const;
  void set_bussiness_id(::PROTOBUF_NAMESPACE_ID::uint32 value);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.FeatureMappingModel)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::FeatureMappingInfo > infos_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bussiness_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class StatRateKey_Dimension :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.StatRateKey.Dimension) */ {
 public:
  StatRateKey_Dimension();
  virtual ~StatRateKey_Dimension();

  StatRateKey_Dimension(const StatRateKey_Dimension& from);
  StatRateKey_Dimension(StatRateKey_Dimension&& from) noexcept
    : StatRateKey_Dimension() {
    *this = ::std::move(from);
  }

  inline StatRateKey_Dimension& operator=(const StatRateKey_Dimension& from) {
    CopyFrom(from);
    return *this;
  }
  inline StatRateKey_Dimension& operator=(StatRateKey_Dimension&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const StatRateKey_Dimension& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StatRateKey_Dimension* internal_default_instance() {
    return reinterpret_cast<const StatRateKey_Dimension*>(
               &_StatRateKey_Dimension_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  void Swap(StatRateKey_Dimension* other);
  friend void swap(StatRateKey_Dimension& a, StatRateKey_Dimension& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline StatRateKey_Dimension* New() const final {
    return CreateMaybeMessage<StatRateKey_Dimension>(nullptr);
  }

  StatRateKey_Dimension* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StatRateKey_Dimension>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const StatRateKey_Dimension& from);
  void MergeFrom(const StatRateKey_Dimension& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StatRateKey_Dimension* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.StatRateKey.Dimension";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string fea = 1;
  void clear_fea();
  static const int kFeaFieldNumber = 1;
  const std::string& fea() const;
  void set_fea(const std::string& value);
  void set_fea(std::string&& value);
  void set_fea(const char* value);
  void set_fea(const char* value, size_t size);
  std::string* mutable_fea();
  std::string* release_fea();
  void set_allocated_fea(std::string* fea);

  // string val = 2;
  void clear_val();
  static const int kValFieldNumber = 2;
  const std::string& val() const;
  void set_val(const std::string& value);
  void set_val(std::string&& value);
  void set_val(const char* value);
  void set_val(const char* value, size_t size);
  std::string* mutable_val();
  std::string* release_val();
  void set_allocated_val(std::string* val);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.StatRateKey.Dimension)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr fea_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr val_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class StatRateKey :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.StatRateKey) */ {
 public:
  StatRateKey();
  virtual ~StatRateKey();

  StatRateKey(const StatRateKey& from);
  StatRateKey(StatRateKey&& from) noexcept
    : StatRateKey() {
    *this = ::std::move(from);
  }

  inline StatRateKey& operator=(const StatRateKey& from) {
    CopyFrom(from);
    return *this;
  }
  inline StatRateKey& operator=(StatRateKey&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const StatRateKey& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StatRateKey* internal_default_instance() {
    return reinterpret_cast<const StatRateKey*>(
               &_StatRateKey_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  void Swap(StatRateKey* other);
  friend void swap(StatRateKey& a, StatRateKey& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline StatRateKey* New() const final {
    return CreateMaybeMessage<StatRateKey>(nullptr);
  }

  StatRateKey* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StatRateKey>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const StatRateKey& from);
  void MergeFrom(const StatRateKey& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StatRateKey* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.StatRateKey";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef StatRateKey_Dimension Dimension;

  // accessors -------------------------------------------------------

  // repeated .abc.recommend_plt.model.StatRateKey.Dimension dimensions = 1;
  int dimensions_size() const;
  void clear_dimensions();
  static const int kDimensionsFieldNumber = 1;
  ::abc::recommend_plt::model::StatRateKey_Dimension* mutable_dimensions(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::StatRateKey_Dimension >*
      mutable_dimensions();
  const ::abc::recommend_plt::model::StatRateKey_Dimension& dimensions(int index) const;
  ::abc::recommend_plt::model::StatRateKey_Dimension* add_dimensions();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::StatRateKey_Dimension >&
      dimensions() const;

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.StatRateKey)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::StatRateKey_Dimension > dimensions_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class StatRateVal_Metric :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.StatRateVal.Metric) */ {
 public:
  StatRateVal_Metric();
  virtual ~StatRateVal_Metric();

  StatRateVal_Metric(const StatRateVal_Metric& from);
  StatRateVal_Metric(StatRateVal_Metric&& from) noexcept
    : StatRateVal_Metric() {
    *this = ::std::move(from);
  }

  inline StatRateVal_Metric& operator=(const StatRateVal_Metric& from) {
    CopyFrom(from);
    return *this;
  }
  inline StatRateVal_Metric& operator=(StatRateVal_Metric&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const StatRateVal_Metric& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StatRateVal_Metric* internal_default_instance() {
    return reinterpret_cast<const StatRateVal_Metric*>(
               &_StatRateVal_Metric_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  void Swap(StatRateVal_Metric* other);
  friend void swap(StatRateVal_Metric& a, StatRateVal_Metric& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline StatRateVal_Metric* New() const final {
    return CreateMaybeMessage<StatRateVal_Metric>(nullptr);
  }

  StatRateVal_Metric* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StatRateVal_Metric>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const StatRateVal_Metric& from);
  void MergeFrom(const StatRateVal_Metric& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StatRateVal_Metric* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.StatRateVal.Metric";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int32 type = 1;
  void clear_type();
  static const int kTypeFieldNumber = 1;
  ::PROTOBUF_NAMESPACE_ID::int32 type() const;
  void set_type(::PROTOBUF_NAMESPACE_ID::int32 value);

  // float val = 2;
  void clear_val();
  static const int kValFieldNumber = 2;
  float val() const;
  void set_val(float value);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.StatRateVal.Metric)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::int32 type_;
  float val_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class StatRateVal :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.StatRateVal) */ {
 public:
  StatRateVal();
  virtual ~StatRateVal();

  StatRateVal(const StatRateVal& from);
  StatRateVal(StatRateVal&& from) noexcept
    : StatRateVal() {
    *this = ::std::move(from);
  }

  inline StatRateVal& operator=(const StatRateVal& from) {
    CopyFrom(from);
    return *this;
  }
  inline StatRateVal& operator=(StatRateVal&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const StatRateVal& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StatRateVal* internal_default_instance() {
    return reinterpret_cast<const StatRateVal*>(
               &_StatRateVal_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  void Swap(StatRateVal* other);
  friend void swap(StatRateVal& a, StatRateVal& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline StatRateVal* New() const final {
    return CreateMaybeMessage<StatRateVal>(nullptr);
  }

  StatRateVal* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StatRateVal>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const StatRateVal& from);
  void MergeFrom(const StatRateVal& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StatRateVal* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.StatRateVal";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef StatRateVal_Metric Metric;

  // accessors -------------------------------------------------------

  // repeated .abc.recommend_plt.model.StatRateVal.Metric metrics = 1;
  int metrics_size() const;
  void clear_metrics();
  static const int kMetricsFieldNumber = 1;
  ::abc::recommend_plt::model::StatRateVal_Metric* mutable_metrics(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::StatRateVal_Metric >*
      mutable_metrics();
  const ::abc::recommend_plt::model::StatRateVal_Metric& metrics(int index) const;
  ::abc::recommend_plt::model::StatRateVal_Metric* add_metrics();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::StatRateVal_Metric >&
      metrics() const;

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.StatRateVal)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::StatRateVal_Metric > metrics_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class StatRateInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.StatRateInfo) */ {
 public:
  StatRateInfo();
  virtual ~StatRateInfo();

  StatRateInfo(const StatRateInfo& from);
  StatRateInfo(StatRateInfo&& from) noexcept
    : StatRateInfo() {
    *this = ::std::move(from);
  }

  inline StatRateInfo& operator=(const StatRateInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline StatRateInfo& operator=(StatRateInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const StatRateInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StatRateInfo* internal_default_instance() {
    return reinterpret_cast<const StatRateInfo*>(
               &_StatRateInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    24;

  void Swap(StatRateInfo* other);
  friend void swap(StatRateInfo& a, StatRateInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline StatRateInfo* New() const final {
    return CreateMaybeMessage<StatRateInfo>(nullptr);
  }

  StatRateInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StatRateInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const StatRateInfo& from);
  void MergeFrom(const StatRateInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StatRateInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.StatRateInfo";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .abc.recommend_plt.model.StatRateVal val = 2;
  bool has_val() const;
  void clear_val();
  static const int kValFieldNumber = 2;
  const ::abc::recommend_plt::model::StatRateVal& val() const;
  ::abc::recommend_plt::model::StatRateVal* release_val();
  ::abc::recommend_plt::model::StatRateVal* mutable_val();
  void set_allocated_val(::abc::recommend_plt::model::StatRateVal* val);

  // uint64 key = 1;
  void clear_key();
  static const int kKeyFieldNumber = 1;
  ::PROTOBUF_NAMESPACE_ID::uint64 key() const;
  void set_key(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.StatRateInfo)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::abc::recommend_plt::model::StatRateVal* val_;
  ::PROTOBUF_NAMESPACE_ID::uint64 key_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class StatRateModel :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.StatRateModel) */ {
 public:
  StatRateModel();
  virtual ~StatRateModel();

  StatRateModel(const StatRateModel& from);
  StatRateModel(StatRateModel&& from) noexcept
    : StatRateModel() {
    *this = ::std::move(from);
  }

  inline StatRateModel& operator=(const StatRateModel& from) {
    CopyFrom(from);
    return *this;
  }
  inline StatRateModel& operator=(StatRateModel&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const StatRateModel& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StatRateModel* internal_default_instance() {
    return reinterpret_cast<const StatRateModel*>(
               &_StatRateModel_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    25;

  void Swap(StatRateModel* other);
  friend void swap(StatRateModel& a, StatRateModel& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline StatRateModel* New() const final {
    return CreateMaybeMessage<StatRateModel>(nullptr);
  }

  StatRateModel* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StatRateModel>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const StatRateModel& from);
  void MergeFrom(const StatRateModel& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StatRateModel* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.StatRateModel";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .abc.recommend_plt.model.StatRateInfo infos = 2;
  int infos_size() const;
  void clear_infos();
  static const int kInfosFieldNumber = 2;
  ::abc::recommend_plt::model::StatRateInfo* mutable_infos(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::StatRateInfo >*
      mutable_infos();
  const ::abc::recommend_plt::model::StatRateInfo& infos(int index) const;
  ::abc::recommend_plt::model::StatRateInfo* add_infos();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::StatRateInfo >&
      infos() const;

  // uint32 bussiness_id = 1;
  void clear_bussiness_id();
  static const int kBussinessIdFieldNumber = 1;
  ::PROTOBUF_NAMESPACE_ID::uint32 bussiness_id() const;
  void set_bussiness_id(::PROTOBUF_NAMESPACE_ID::uint32 value);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.StatRateModel)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::StatRateInfo > infos_;
  ::PROTOBUF_NAMESPACE_ID::uint32 bussiness_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class CateRelationKey :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.CateRelationKey) */ {
 public:
  CateRelationKey();
  virtual ~CateRelationKey();

  CateRelationKey(const CateRelationKey& from);
  CateRelationKey(CateRelationKey&& from) noexcept
    : CateRelationKey() {
    *this = ::std::move(from);
  }

  inline CateRelationKey& operator=(const CateRelationKey& from) {
    CopyFrom(from);
    return *this;
  }
  inline CateRelationKey& operator=(CateRelationKey&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CateRelationKey& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CateRelationKey* internal_default_instance() {
    return reinterpret_cast<const CateRelationKey*>(
               &_CateRelationKey_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    26;

  void Swap(CateRelationKey* other);
  friend void swap(CateRelationKey& a, CateRelationKey& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CateRelationKey* New() const final {
    return CreateMaybeMessage<CateRelationKey>(nullptr);
  }

  CateRelationKey* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CateRelationKey>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CateRelationKey& from);
  void MergeFrom(const CateRelationKey& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CateRelationKey* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.CateRelationKey";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string site_uid = 1;
  void clear_site_uid();
  static const int kSiteUidFieldNumber = 1;
  const std::string& site_uid() const;
  void set_site_uid(const std::string& value);
  void set_site_uid(std::string&& value);
  void set_site_uid(const char* value);
  void set_site_uid(const char* value, size_t size);
  std::string* mutable_site_uid();
  std::string* release_site_uid();
  void set_allocated_site_uid(std::string* site_uid);

  // string category_id = 2;
  void clear_category_id();
  static const int kCategoryIdFieldNumber = 2;
  const std::string& category_id() const;
  void set_category_id(const std::string& value);
  void set_category_id(std::string&& value);
  void set_category_id(const char* value);
  void set_category_id(const char* value, size_t size);
  std::string* mutable_category_id();
  std::string* release_category_id();
  void set_allocated_category_id(std::string* category_id);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.CateRelationKey)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr site_uid_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr category_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class CateRelationVal :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.CateRelationVal) */ {
 public:
  CateRelationVal();
  virtual ~CateRelationVal();

  CateRelationVal(const CateRelationVal& from);
  CateRelationVal(CateRelationVal&& from) noexcept
    : CateRelationVal() {
    *this = ::std::move(from);
  }

  inline CateRelationVal& operator=(const CateRelationVal& from) {
    CopyFrom(from);
    return *this;
  }
  inline CateRelationVal& operator=(CateRelationVal&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CateRelationVal& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CateRelationVal* internal_default_instance() {
    return reinterpret_cast<const CateRelationVal*>(
               &_CateRelationVal_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    27;

  void Swap(CateRelationVal* other);
  friend void swap(CateRelationVal& a, CateRelationVal& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CateRelationVal* New() const final {
    return CreateMaybeMessage<CateRelationVal>(nullptr);
  }

  CateRelationVal* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CateRelationVal>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CateRelationVal& from);
  void MergeFrom(const CateRelationVal& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CateRelationVal* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.CateRelationVal";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .abc.recommend_plt.common.CateRelation infos = 1;
  int infos_size() const;
  void clear_infos();
  static const int kInfosFieldNumber = 1;
  ::abc::recommend_plt::common::CateRelation* mutable_infos(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::common::CateRelation >*
      mutable_infos();
  const ::abc::recommend_plt::common::CateRelation& infos(int index) const;
  ::abc::recommend_plt::common::CateRelation* add_infos();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::common::CateRelation >&
      infos() const;

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.CateRelationVal)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::common::CateRelation > infos_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class ItemRelationKey :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.ItemRelationKey) */ {
 public:
  ItemRelationKey();
  virtual ~ItemRelationKey();

  ItemRelationKey(const ItemRelationKey& from);
  ItemRelationKey(ItemRelationKey&& from) noexcept
    : ItemRelationKey() {
    *this = ::std::move(from);
  }

  inline ItemRelationKey& operator=(const ItemRelationKey& from) {
    CopyFrom(from);
    return *this;
  }
  inline ItemRelationKey& operator=(ItemRelationKey&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ItemRelationKey& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ItemRelationKey* internal_default_instance() {
    return reinterpret_cast<const ItemRelationKey*>(
               &_ItemRelationKey_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    28;

  void Swap(ItemRelationKey* other);
  friend void swap(ItemRelationKey& a, ItemRelationKey& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ItemRelationKey* New() const final {
    return CreateMaybeMessage<ItemRelationKey>(nullptr);
  }

  ItemRelationKey* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ItemRelationKey>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ItemRelationKey& from);
  void MergeFrom(const ItemRelationKey& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ItemRelationKey* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.ItemRelationKey";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string item_ids = 1;
  void clear_item_ids();
  static const int kItemIdsFieldNumber = 1;
  const std::string& item_ids() const;
  void set_item_ids(const std::string& value);
  void set_item_ids(std::string&& value);
  void set_item_ids(const char* value);
  void set_item_ids(const char* value, size_t size);
  std::string* mutable_item_ids();
  std::string* release_item_ids();
  void set_allocated_item_ids(std::string* item_ids);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemRelationKey)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr item_ids_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class ItemRelationVal :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.ItemRelationVal) */ {
 public:
  ItemRelationVal();
  virtual ~ItemRelationVal();

  ItemRelationVal(const ItemRelationVal& from);
  ItemRelationVal(ItemRelationVal&& from) noexcept
    : ItemRelationVal() {
    *this = ::std::move(from);
  }

  inline ItemRelationVal& operator=(const ItemRelationVal& from) {
    CopyFrom(from);
    return *this;
  }
  inline ItemRelationVal& operator=(ItemRelationVal&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ItemRelationVal& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ItemRelationVal* internal_default_instance() {
    return reinterpret_cast<const ItemRelationVal*>(
               &_ItemRelationVal_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    29;

  void Swap(ItemRelationVal* other);
  friend void swap(ItemRelationVal& a, ItemRelationVal& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ItemRelationVal* New() const final {
    return CreateMaybeMessage<ItemRelationVal>(nullptr);
  }

  ItemRelationVal* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ItemRelationVal>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ItemRelationVal& from);
  void MergeFrom(const ItemRelationVal& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ItemRelationVal* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.ItemRelationVal";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 item_id = 1;
  void clear_item_id();
  static const int kItemIdFieldNumber = 1;
  ::PROTOBUF_NAMESPACE_ID::int64 item_id() const;
  void set_item_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // float score = 2;
  void clear_score();
  static const int kScoreFieldNumber = 2;
  float score() const;
  void set_score(float value);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemRelationVal)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::int64 item_id_;
  float score_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class ItemRelationMultiVal :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.ItemRelationMultiVal) */ {
 public:
  ItemRelationMultiVal();
  virtual ~ItemRelationMultiVal();

  ItemRelationMultiVal(const ItemRelationMultiVal& from);
  ItemRelationMultiVal(ItemRelationMultiVal&& from) noexcept
    : ItemRelationMultiVal() {
    *this = ::std::move(from);
  }

  inline ItemRelationMultiVal& operator=(const ItemRelationMultiVal& from) {
    CopyFrom(from);
    return *this;
  }
  inline ItemRelationMultiVal& operator=(ItemRelationMultiVal&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ItemRelationMultiVal& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ItemRelationMultiVal* internal_default_instance() {
    return reinterpret_cast<const ItemRelationMultiVal*>(
               &_ItemRelationMultiVal_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    30;

  void Swap(ItemRelationMultiVal* other);
  friend void swap(ItemRelationMultiVal& a, ItemRelationMultiVal& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ItemRelationMultiVal* New() const final {
    return CreateMaybeMessage<ItemRelationMultiVal>(nullptr);
  }

  ItemRelationMultiVal* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ItemRelationMultiVal>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ItemRelationMultiVal& from);
  void MergeFrom(const ItemRelationMultiVal& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ItemRelationMultiVal* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.ItemRelationMultiVal";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .abc.recommend_plt.model.ItemRelationVal vals = 1;
  int vals_size() const;
  void clear_vals();
  static const int kValsFieldNumber = 1;
  ::abc::recommend_plt::model::ItemRelationVal* mutable_vals(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::ItemRelationVal >*
      mutable_vals();
  const ::abc::recommend_plt::model::ItemRelationVal& vals(int index) const;
  ::abc::recommend_plt::model::ItemRelationVal* add_vals();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::ItemRelationVal >&
      vals() const;

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemRelationMultiVal)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::ItemRelationVal > vals_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class ItemRelationInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.ItemRelationInfo) */ {
 public:
  ItemRelationInfo();
  virtual ~ItemRelationInfo();

  ItemRelationInfo(const ItemRelationInfo& from);
  ItemRelationInfo(ItemRelationInfo&& from) noexcept
    : ItemRelationInfo() {
    *this = ::std::move(from);
  }

  inline ItemRelationInfo& operator=(const ItemRelationInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline ItemRelationInfo& operator=(ItemRelationInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ItemRelationInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ItemRelationInfo* internal_default_instance() {
    return reinterpret_cast<const ItemRelationInfo*>(
               &_ItemRelationInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    31;

  void Swap(ItemRelationInfo* other);
  friend void swap(ItemRelationInfo& a, ItemRelationInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ItemRelationInfo* New() const final {
    return CreateMaybeMessage<ItemRelationInfo>(nullptr);
  }

  ItemRelationInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ItemRelationInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ItemRelationInfo& from);
  void MergeFrom(const ItemRelationInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ItemRelationInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.ItemRelationInfo";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .abc.recommend_plt.model.ItemRelationKey key = 1;
  bool has_key() const;
  void clear_key();
  static const int kKeyFieldNumber = 1;
  const ::abc::recommend_plt::model::ItemRelationKey& key() const;
  ::abc::recommend_plt::model::ItemRelationKey* release_key();
  ::abc::recommend_plt::model::ItemRelationKey* mutable_key();
  void set_allocated_key(::abc::recommend_plt::model::ItemRelationKey* key);

  // .abc.recommend_plt.model.ItemRelationMultiVal val = 2;
  bool has_val() const;
  void clear_val();
  static const int kValFieldNumber = 2;
  const ::abc::recommend_plt::model::ItemRelationMultiVal& val() const;
  ::abc::recommend_plt::model::ItemRelationMultiVal* release_val();
  ::abc::recommend_plt::model::ItemRelationMultiVal* mutable_val();
  void set_allocated_val(::abc::recommend_plt::model::ItemRelationMultiVal* val);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemRelationInfo)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::abc::recommend_plt::model::ItemRelationKey* key_;
  ::abc::recommend_plt::model::ItemRelationMultiVal* val_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// -------------------------------------------------------------------

class ItemRelationModel :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.model.ItemRelationModel) */ {
 public:
  ItemRelationModel();
  virtual ~ItemRelationModel();

  ItemRelationModel(const ItemRelationModel& from);
  ItemRelationModel(ItemRelationModel&& from) noexcept
    : ItemRelationModel() {
    *this = ::std::move(from);
  }

  inline ItemRelationModel& operator=(const ItemRelationModel& from) {
    CopyFrom(from);
    return *this;
  }
  inline ItemRelationModel& operator=(ItemRelationModel&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ItemRelationModel& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ItemRelationModel* internal_default_instance() {
    return reinterpret_cast<const ItemRelationModel*>(
               &_ItemRelationModel_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    32;

  void Swap(ItemRelationModel* other);
  friend void swap(ItemRelationModel& a, ItemRelationModel& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ItemRelationModel* New() const final {
    return CreateMaybeMessage<ItemRelationModel>(nullptr);
  }

  ItemRelationModel* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ItemRelationModel>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ItemRelationModel& from);
  void MergeFrom(const ItemRelationModel& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ItemRelationModel* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.model.ItemRelationModel";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fmodel_2eproto);
    return ::descriptor_table_proto_2frecommend_5fmodel_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .abc.recommend_plt.model.ItemRelationInfo infos = 1;
  int infos_size() const;
  void clear_infos();
  static const int kInfosFieldNumber = 1;
  ::abc::recommend_plt::model::ItemRelationInfo* mutable_infos(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::ItemRelationInfo >*
      mutable_infos();
  const ::abc::recommend_plt::model::ItemRelationInfo& infos(int index) const;
  ::abc::recommend_plt::model::ItemRelationInfo* add_infos();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::ItemRelationInfo >&
      infos() const;

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.model.ItemRelationModel)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::ItemRelationInfo > infos_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fmodel_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ItemTagsKey

// string pool = 1;
inline void ItemTagsKey::clear_pool() {
  pool_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ItemTagsKey::pool() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.ItemTagsKey.pool)
  return pool_.GetNoArena();
}
inline void ItemTagsKey::set_pool(const std::string& value) {
  
  pool_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.ItemTagsKey.pool)
}
inline void ItemTagsKey::set_pool(std::string&& value) {
  
  pool_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.ItemTagsKey.pool)
}
inline void ItemTagsKey::set_pool(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  pool_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.ItemTagsKey.pool)
}
inline void ItemTagsKey::set_pool(const char* value, size_t size) {
  
  pool_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.ItemTagsKey.pool)
}
inline std::string* ItemTagsKey::mutable_pool() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.ItemTagsKey.pool)
  return pool_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ItemTagsKey::release_pool() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.ItemTagsKey.pool)
  
  return pool_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ItemTagsKey::set_allocated_pool(std::string* pool) {
  if (pool != nullptr) {
    
  } else {
    
  }
  pool_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), pool);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.ItemTagsKey.pool)
}

// string id = 2;
inline void ItemTagsKey::clear_id() {
  id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ItemTagsKey::id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.ItemTagsKey.id)
  return id_.GetNoArena();
}
inline void ItemTagsKey::set_id(const std::string& value) {
  
  id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.ItemTagsKey.id)
}
inline void ItemTagsKey::set_id(std::string&& value) {
  
  id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.ItemTagsKey.id)
}
inline void ItemTagsKey::set_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.ItemTagsKey.id)
}
inline void ItemTagsKey::set_id(const char* value, size_t size) {
  
  id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.ItemTagsKey.id)
}
inline std::string* ItemTagsKey::mutable_id() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.ItemTagsKey.id)
  return id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ItemTagsKey::release_id() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.ItemTagsKey.id)
  
  return id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ItemTagsKey::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.ItemTagsKey.id)
}

// -------------------------------------------------------------------

// ItemTagsVal_Tag

// int32 tag_id = 1;
inline void ItemTagsVal_Tag::clear_tag_id() {
  tag_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ItemTagsVal_Tag::tag_id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.ItemTagsVal.Tag.tag_id)
  return tag_id_;
}
inline void ItemTagsVal_Tag::set_tag_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  tag_id_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.ItemTagsVal.Tag.tag_id)
}

// int64 tag_val = 2;
inline void ItemTagsVal_Tag::clear_tag_val() {
  tag_val_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ItemTagsVal_Tag::tag_val() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.ItemTagsVal.Tag.tag_val)
  return tag_val_;
}
inline void ItemTagsVal_Tag::set_tag_val(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  tag_val_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.ItemTagsVal.Tag.tag_val)
}

// repeated int64 tag_vals = 3;
inline int ItemTagsVal_Tag::tag_vals_size() const {
  return tag_vals_.size();
}
inline void ItemTagsVal_Tag::clear_tag_vals() {
  tag_vals_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ItemTagsVal_Tag::tag_vals(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.ItemTagsVal.Tag.tag_vals)
  return tag_vals_.Get(index);
}
inline void ItemTagsVal_Tag::set_tag_vals(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  tag_vals_.Set(index, value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.ItemTagsVal.Tag.tag_vals)
}
inline void ItemTagsVal_Tag::add_tag_vals(::PROTOBUF_NAMESPACE_ID::int64 value) {
  tag_vals_.Add(value);
  // @@protoc_insertion_point(field_add:abc.recommend_plt.model.ItemTagsVal.Tag.tag_vals)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
ItemTagsVal_Tag::tag_vals() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.model.ItemTagsVal.Tag.tag_vals)
  return tag_vals_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
ItemTagsVal_Tag::mutable_tag_vals() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.model.ItemTagsVal.Tag.tag_vals)
  return &tag_vals_;
}

// -------------------------------------------------------------------

// ItemTagsVal

// repeated .abc.recommend_plt.model.ItemTagsVal.Tag tags = 1;
inline int ItemTagsVal::tags_size() const {
  return tags_.size();
}
inline void ItemTagsVal::clear_tags() {
  tags_.Clear();
}
inline ::abc::recommend_plt::model::ItemTagsVal_Tag* ItemTagsVal::mutable_tags(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.ItemTagsVal.tags)
  return tags_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::ItemTagsVal_Tag >*
ItemTagsVal::mutable_tags() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.model.ItemTagsVal.tags)
  return &tags_;
}
inline const ::abc::recommend_plt::model::ItemTagsVal_Tag& ItemTagsVal::tags(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.ItemTagsVal.tags)
  return tags_.Get(index);
}
inline ::abc::recommend_plt::model::ItemTagsVal_Tag* ItemTagsVal::add_tags() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.model.ItemTagsVal.tags)
  return tags_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::ItemTagsVal_Tag >&
ItemTagsVal::tags() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.model.ItemTagsVal.tags)
  return tags_;
}

// -------------------------------------------------------------------

// ItemTagsInfo

// .abc.recommend_plt.model.ItemTagsKey key = 1;
inline bool ItemTagsInfo::has_key() const {
  return this != internal_default_instance() && key_ != nullptr;
}
inline void ItemTagsInfo::clear_key() {
  if (GetArenaNoVirtual() == nullptr && key_ != nullptr) {
    delete key_;
  }
  key_ = nullptr;
}
inline const ::abc::recommend_plt::model::ItemTagsKey& ItemTagsInfo::key() const {
  const ::abc::recommend_plt::model::ItemTagsKey* p = key_;
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.ItemTagsInfo.key)
  return p != nullptr ? *p : *reinterpret_cast<const ::abc::recommend_plt::model::ItemTagsKey*>(
      &::abc::recommend_plt::model::_ItemTagsKey_default_instance_);
}
inline ::abc::recommend_plt::model::ItemTagsKey* ItemTagsInfo::release_key() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.ItemTagsInfo.key)
  
  ::abc::recommend_plt::model::ItemTagsKey* temp = key_;
  key_ = nullptr;
  return temp;
}
inline ::abc::recommend_plt::model::ItemTagsKey* ItemTagsInfo::mutable_key() {
  
  if (key_ == nullptr) {
    auto* p = CreateMaybeMessage<::abc::recommend_plt::model::ItemTagsKey>(GetArenaNoVirtual());
    key_ = p;
  }
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.ItemTagsInfo.key)
  return key_;
}
inline void ItemTagsInfo::set_allocated_key(::abc::recommend_plt::model::ItemTagsKey* key) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete key_;
  }
  if (key) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      key = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, key, submessage_arena);
    }
    
  } else {
    
  }
  key_ = key;
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.ItemTagsInfo.key)
}

// .abc.recommend_plt.model.ItemTagsVal val = 2;
inline bool ItemTagsInfo::has_val() const {
  return this != internal_default_instance() && val_ != nullptr;
}
inline void ItemTagsInfo::clear_val() {
  if (GetArenaNoVirtual() == nullptr && val_ != nullptr) {
    delete val_;
  }
  val_ = nullptr;
}
inline const ::abc::recommend_plt::model::ItemTagsVal& ItemTagsInfo::val() const {
  const ::abc::recommend_plt::model::ItemTagsVal* p = val_;
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.ItemTagsInfo.val)
  return p != nullptr ? *p : *reinterpret_cast<const ::abc::recommend_plt::model::ItemTagsVal*>(
      &::abc::recommend_plt::model::_ItemTagsVal_default_instance_);
}
inline ::abc::recommend_plt::model::ItemTagsVal* ItemTagsInfo::release_val() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.ItemTagsInfo.val)
  
  ::abc::recommend_plt::model::ItemTagsVal* temp = val_;
  val_ = nullptr;
  return temp;
}
inline ::abc::recommend_plt::model::ItemTagsVal* ItemTagsInfo::mutable_val() {
  
  if (val_ == nullptr) {
    auto* p = CreateMaybeMessage<::abc::recommend_plt::model::ItemTagsVal>(GetArenaNoVirtual());
    val_ = p;
  }
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.ItemTagsInfo.val)
  return val_;
}
inline void ItemTagsInfo::set_allocated_val(::abc::recommend_plt::model::ItemTagsVal* val) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete val_;
  }
  if (val) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      val = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, val, submessage_arena);
    }
    
  } else {
    
  }
  val_ = val;
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.ItemTagsInfo.val)
}

// -------------------------------------------------------------------

// ItemTagsModel

// int32 bussiness_id = 1;
inline void ItemTagsModel::clear_bussiness_id() {
  bussiness_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ItemTagsModel::bussiness_id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.ItemTagsModel.bussiness_id)
  return bussiness_id_;
}
inline void ItemTagsModel::set_bussiness_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  bussiness_id_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.ItemTagsModel.bussiness_id)
}

// repeated .abc.recommend_plt.model.ItemTagsInfo infos = 2;
inline int ItemTagsModel::infos_size() const {
  return infos_.size();
}
inline void ItemTagsModel::clear_infos() {
  infos_.Clear();
}
inline ::abc::recommend_plt::model::ItemTagsInfo* ItemTagsModel::mutable_infos(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.ItemTagsModel.infos)
  return infos_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::ItemTagsInfo >*
ItemTagsModel::mutable_infos() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.model.ItemTagsModel.infos)
  return &infos_;
}
inline const ::abc::recommend_plt::model::ItemTagsInfo& ItemTagsModel::infos(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.ItemTagsModel.infos)
  return infos_.Get(index);
}
inline ::abc::recommend_plt::model::ItemTagsInfo* ItemTagsModel::add_infos() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.model.ItemTagsModel.infos)
  return infos_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::ItemTagsInfo >&
ItemTagsModel::infos() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.model.ItemTagsModel.infos)
  return infos_;
}

// -------------------------------------------------------------------

// TransformFeaVal

// string raw_fea = 1;
inline void TransformFeaVal::clear_raw_fea() {
  raw_fea_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& TransformFeaVal::raw_fea() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.TransformFeaVal.raw_fea)
  return raw_fea_.GetNoArena();
}
inline void TransformFeaVal::set_raw_fea(const std::string& value) {
  
  raw_fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.TransformFeaVal.raw_fea)
}
inline void TransformFeaVal::set_raw_fea(std::string&& value) {
  
  raw_fea_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.TransformFeaVal.raw_fea)
}
inline void TransformFeaVal::set_raw_fea(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  raw_fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.TransformFeaVal.raw_fea)
}
inline void TransformFeaVal::set_raw_fea(const char* value, size_t size) {
  
  raw_fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.TransformFeaVal.raw_fea)
}
inline std::string* TransformFeaVal::mutable_raw_fea() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.TransformFeaVal.raw_fea)
  return raw_fea_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* TransformFeaVal::release_raw_fea() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.TransformFeaVal.raw_fea)
  
  return raw_fea_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void TransformFeaVal::set_allocated_raw_fea(std::string* raw_fea) {
  if (raw_fea != nullptr) {
    
  } else {
    
  }
  raw_fea_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), raw_fea);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.TransformFeaVal.raw_fea)
}

// string raw_val = 2;
inline void TransformFeaVal::clear_raw_val() {
  raw_val_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& TransformFeaVal::raw_val() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.TransformFeaVal.raw_val)
  return raw_val_.GetNoArena();
}
inline void TransformFeaVal::set_raw_val(const std::string& value) {
  
  raw_val_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.TransformFeaVal.raw_val)
}
inline void TransformFeaVal::set_raw_val(std::string&& value) {
  
  raw_val_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.TransformFeaVal.raw_val)
}
inline void TransformFeaVal::set_raw_val(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  raw_val_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.TransformFeaVal.raw_val)
}
inline void TransformFeaVal::set_raw_val(const char* value, size_t size) {
  
  raw_val_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.TransformFeaVal.raw_val)
}
inline std::string* TransformFeaVal::mutable_raw_val() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.TransformFeaVal.raw_val)
  return raw_val_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* TransformFeaVal::release_raw_val() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.TransformFeaVal.raw_val)
  
  return raw_val_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void TransformFeaVal::set_allocated_raw_val(std::string* raw_val) {
  if (raw_val != nullptr) {
    
  } else {
    
  }
  raw_val_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), raw_val);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.TransformFeaVal.raw_val)
}

// string trans_fea = 3;
inline void TransformFeaVal::clear_trans_fea() {
  trans_fea_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& TransformFeaVal::trans_fea() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.TransformFeaVal.trans_fea)
  return trans_fea_.GetNoArena();
}
inline void TransformFeaVal::set_trans_fea(const std::string& value) {
  
  trans_fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.TransformFeaVal.trans_fea)
}
inline void TransformFeaVal::set_trans_fea(std::string&& value) {
  
  trans_fea_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.TransformFeaVal.trans_fea)
}
inline void TransformFeaVal::set_trans_fea(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  trans_fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.TransformFeaVal.trans_fea)
}
inline void TransformFeaVal::set_trans_fea(const char* value, size_t size) {
  
  trans_fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.TransformFeaVal.trans_fea)
}
inline std::string* TransformFeaVal::mutable_trans_fea() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.TransformFeaVal.trans_fea)
  return trans_fea_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* TransformFeaVal::release_trans_fea() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.TransformFeaVal.trans_fea)
  
  return trans_fea_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void TransformFeaVal::set_allocated_trans_fea(std::string* trans_fea) {
  if (trans_fea != nullptr) {
    
  } else {
    
  }
  trans_fea_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), trans_fea);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.TransformFeaVal.trans_fea)
}

// string trans_val = 4;
inline void TransformFeaVal::clear_trans_val() {
  trans_val_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& TransformFeaVal::trans_val() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.TransformFeaVal.trans_val)
  return trans_val_.GetNoArena();
}
inline void TransformFeaVal::set_trans_val(const std::string& value) {
  
  trans_val_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.TransformFeaVal.trans_val)
}
inline void TransformFeaVal::set_trans_val(std::string&& value) {
  
  trans_val_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.TransformFeaVal.trans_val)
}
inline void TransformFeaVal::set_trans_val(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  trans_val_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.TransformFeaVal.trans_val)
}
inline void TransformFeaVal::set_trans_val(const char* value, size_t size) {
  
  trans_val_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.TransformFeaVal.trans_val)
}
inline std::string* TransformFeaVal::mutable_trans_val() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.TransformFeaVal.trans_val)
  return trans_val_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* TransformFeaVal::release_trans_val() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.TransformFeaVal.trans_val)
  
  return trans_val_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void TransformFeaVal::set_allocated_trans_val(std::string* trans_val) {
  if (trans_val != nullptr) {
    
  } else {
    
  }
  trans_val_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), trans_val);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.TransformFeaVal.trans_val)
}

// -------------------------------------------------------------------

// RecList_Dimension

// string fea = 1;
inline void RecList_Dimension::clear_fea() {
  fea_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecList_Dimension::fea() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.RecList.Dimension.fea)
  return fea_.GetNoArena();
}
inline void RecList_Dimension::set_fea(const std::string& value) {
  
  fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.RecList.Dimension.fea)
}
inline void RecList_Dimension::set_fea(std::string&& value) {
  
  fea_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.RecList.Dimension.fea)
}
inline void RecList_Dimension::set_fea(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.RecList.Dimension.fea)
}
inline void RecList_Dimension::set_fea(const char* value, size_t size) {
  
  fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.RecList.Dimension.fea)
}
inline std::string* RecList_Dimension::mutable_fea() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.RecList.Dimension.fea)
  return fea_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecList_Dimension::release_fea() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.RecList.Dimension.fea)
  
  return fea_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecList_Dimension::set_allocated_fea(std::string* fea) {
  if (fea != nullptr) {
    
  } else {
    
  }
  fea_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), fea);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.RecList.Dimension.fea)
}

// string val = 2;
inline void RecList_Dimension::clear_val() {
  val_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecList_Dimension::val() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.RecList.Dimension.val)
  return val_.GetNoArena();
}
inline void RecList_Dimension::set_val(const std::string& value) {
  
  val_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.RecList.Dimension.val)
}
inline void RecList_Dimension::set_val(std::string&& value) {
  
  val_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.RecList.Dimension.val)
}
inline void RecList_Dimension::set_val(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  val_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.RecList.Dimension.val)
}
inline void RecList_Dimension::set_val(const char* value, size_t size) {
  
  val_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.RecList.Dimension.val)
}
inline std::string* RecList_Dimension::mutable_val() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.RecList.Dimension.val)
  return val_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecList_Dimension::release_val() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.RecList.Dimension.val)
  
  return val_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecList_Dimension::set_allocated_val(std::string* val) {
  if (val != nullptr) {
    
  } else {
    
  }
  val_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), val);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.RecList.Dimension.val)
}

// -------------------------------------------------------------------

// RecList_Item

// string id = 1;
inline void RecList_Item::clear_id() {
  id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecList_Item::id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.RecList.Item.id)
  return id_.GetNoArena();
}
inline void RecList_Item::set_id(const std::string& value) {
  
  id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.RecList.Item.id)
}
inline void RecList_Item::set_id(std::string&& value) {
  
  id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.RecList.Item.id)
}
inline void RecList_Item::set_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.RecList.Item.id)
}
inline void RecList_Item::set_id(const char* value, size_t size) {
  
  id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.RecList.Item.id)
}
inline std::string* RecList_Item::mutable_id() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.RecList.Item.id)
  return id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecList_Item::release_id() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.RecList.Item.id)
  
  return id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecList_Item::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.RecList.Item.id)
}

// float score = 2;
inline void RecList_Item::clear_score() {
  score_ = 0;
}
inline float RecList_Item::score() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.RecList.Item.score)
  return score_;
}
inline void RecList_Item::set_score(float value) {
  
  score_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.RecList.Item.score)
}

// -------------------------------------------------------------------

// RecList

// repeated .abc.recommend_plt.model.RecList.Dimension dimensions = 1;
inline int RecList::dimensions_size() const {
  return dimensions_.size();
}
inline void RecList::clear_dimensions() {
  dimensions_.Clear();
}
inline ::abc::recommend_plt::model::RecList_Dimension* RecList::mutable_dimensions(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.RecList.dimensions)
  return dimensions_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecList_Dimension >*
RecList::mutable_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.model.RecList.dimensions)
  return &dimensions_;
}
inline const ::abc::recommend_plt::model::RecList_Dimension& RecList::dimensions(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.RecList.dimensions)
  return dimensions_.Get(index);
}
inline ::abc::recommend_plt::model::RecList_Dimension* RecList::add_dimensions() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.model.RecList.dimensions)
  return dimensions_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecList_Dimension >&
RecList::dimensions() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.model.RecList.dimensions)
  return dimensions_;
}

// repeated .abc.recommend_plt.model.RecList.Item items = 2;
inline int RecList::items_size() const {
  return items_.size();
}
inline void RecList::clear_items() {
  items_.Clear();
}
inline ::abc::recommend_plt::model::RecList_Item* RecList::mutable_items(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.RecList.items)
  return items_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecList_Item >*
RecList::mutable_items() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.model.RecList.items)
  return &items_;
}
inline const ::abc::recommend_plt::model::RecList_Item& RecList::items(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.RecList.items)
  return items_.Get(index);
}
inline ::abc::recommend_plt::model::RecList_Item* RecList::add_items() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.model.RecList.items)
  return items_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecList_Item >&
RecList::items() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.model.RecList.items)
  return items_;
}

// -------------------------------------------------------------------

// RecListKey_Dimension

// string fea = 1;
inline void RecListKey_Dimension::clear_fea() {
  fea_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecListKey_Dimension::fea() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.RecListKey.Dimension.fea)
  return fea_.GetNoArena();
}
inline void RecListKey_Dimension::set_fea(const std::string& value) {
  
  fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.RecListKey.Dimension.fea)
}
inline void RecListKey_Dimension::set_fea(std::string&& value) {
  
  fea_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.RecListKey.Dimension.fea)
}
inline void RecListKey_Dimension::set_fea(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.RecListKey.Dimension.fea)
}
inline void RecListKey_Dimension::set_fea(const char* value, size_t size) {
  
  fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.RecListKey.Dimension.fea)
}
inline std::string* RecListKey_Dimension::mutable_fea() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.RecListKey.Dimension.fea)
  return fea_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecListKey_Dimension::release_fea() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.RecListKey.Dimension.fea)
  
  return fea_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecListKey_Dimension::set_allocated_fea(std::string* fea) {
  if (fea != nullptr) {
    
  } else {
    
  }
  fea_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), fea);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.RecListKey.Dimension.fea)
}

// string val = 2;
inline void RecListKey_Dimension::clear_val() {
  val_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecListKey_Dimension::val() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.RecListKey.Dimension.val)
  return val_.GetNoArena();
}
inline void RecListKey_Dimension::set_val(const std::string& value) {
  
  val_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.RecListKey.Dimension.val)
}
inline void RecListKey_Dimension::set_val(std::string&& value) {
  
  val_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.RecListKey.Dimension.val)
}
inline void RecListKey_Dimension::set_val(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  val_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.RecListKey.Dimension.val)
}
inline void RecListKey_Dimension::set_val(const char* value, size_t size) {
  
  val_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.RecListKey.Dimension.val)
}
inline std::string* RecListKey_Dimension::mutable_val() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.RecListKey.Dimension.val)
  return val_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecListKey_Dimension::release_val() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.RecListKey.Dimension.val)
  
  return val_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecListKey_Dimension::set_allocated_val(std::string* val) {
  if (val != nullptr) {
    
  } else {
    
  }
  val_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), val);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.RecListKey.Dimension.val)
}

// -------------------------------------------------------------------

// RecListKey

// repeated .abc.recommend_plt.model.RecListKey.Dimension dimensions = 1;
inline int RecListKey::dimensions_size() const {
  return dimensions_.size();
}
inline void RecListKey::clear_dimensions() {
  dimensions_.Clear();
}
inline ::abc::recommend_plt::model::RecListKey_Dimension* RecListKey::mutable_dimensions(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.RecListKey.dimensions)
  return dimensions_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecListKey_Dimension >*
RecListKey::mutable_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.model.RecListKey.dimensions)
  return &dimensions_;
}
inline const ::abc::recommend_plt::model::RecListKey_Dimension& RecListKey::dimensions(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.RecListKey.dimensions)
  return dimensions_.Get(index);
}
inline ::abc::recommend_plt::model::RecListKey_Dimension* RecListKey::add_dimensions() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.model.RecListKey.dimensions)
  return dimensions_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecListKey_Dimension >&
RecListKey::dimensions() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.model.RecListKey.dimensions)
  return dimensions_;
}

// -------------------------------------------------------------------

// RecListVal_Item

// string id = 1;
inline void RecListVal_Item::clear_id() {
  id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecListVal_Item::id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.RecListVal.Item.id)
  return id_.GetNoArena();
}
inline void RecListVal_Item::set_id(const std::string& value) {
  
  id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.RecListVal.Item.id)
}
inline void RecListVal_Item::set_id(std::string&& value) {
  
  id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.RecListVal.Item.id)
}
inline void RecListVal_Item::set_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.RecListVal.Item.id)
}
inline void RecListVal_Item::set_id(const char* value, size_t size) {
  
  id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.RecListVal.Item.id)
}
inline std::string* RecListVal_Item::mutable_id() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.RecListVal.Item.id)
  return id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecListVal_Item::release_id() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.RecListVal.Item.id)
  
  return id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecListVal_Item::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.RecListVal.Item.id)
}

// float score = 2;
inline void RecListVal_Item::clear_score() {
  score_ = 0;
}
inline float RecListVal_Item::score() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.RecListVal.Item.score)
  return score_;
}
inline void RecListVal_Item::set_score(float value) {
  
  score_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.RecListVal.Item.score)
}

// int32 cate = 3;
inline void RecListVal_Item::clear_cate() {
  cate_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RecListVal_Item::cate() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.RecListVal.Item.cate)
  return cate_;
}
inline void RecListVal_Item::set_cate(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  cate_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.RecListVal.Item.cate)
}

// int64 parent_id = 4;
inline void RecListVal_Item::clear_parent_id() {
  parent_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RecListVal_Item::parent_id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.RecListVal.Item.parent_id)
  return parent_id_;
}
inline void RecListVal_Item::set_parent_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  parent_id_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.RecListVal.Item.parent_id)
}

// -------------------------------------------------------------------

// RecListVal

// repeated .abc.recommend_plt.model.RecListVal.Item items = 2;
inline int RecListVal::items_size() const {
  return items_.size();
}
inline void RecListVal::clear_items() {
  items_.Clear();
}
inline ::abc::recommend_plt::model::RecListVal_Item* RecListVal::mutable_items(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.RecListVal.items)
  return items_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecListVal_Item >*
RecListVal::mutable_items() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.model.RecListVal.items)
  return &items_;
}
inline const ::abc::recommend_plt::model::RecListVal_Item& RecListVal::items(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.RecListVal.items)
  return items_.Get(index);
}
inline ::abc::recommend_plt::model::RecListVal_Item* RecListVal::add_items() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.model.RecListVal.items)
  return items_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecListVal_Item >&
RecListVal::items() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.model.RecListVal.items)
  return items_;
}

// -------------------------------------------------------------------

// RecListInfo

// .abc.recommend_plt.model.RecListKey key = 1;
inline bool RecListInfo::has_key() const {
  return this != internal_default_instance() && key_ != nullptr;
}
inline void RecListInfo::clear_key() {
  if (GetArenaNoVirtual() == nullptr && key_ != nullptr) {
    delete key_;
  }
  key_ = nullptr;
}
inline const ::abc::recommend_plt::model::RecListKey& RecListInfo::key() const {
  const ::abc::recommend_plt::model::RecListKey* p = key_;
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.RecListInfo.key)
  return p != nullptr ? *p : *reinterpret_cast<const ::abc::recommend_plt::model::RecListKey*>(
      &::abc::recommend_plt::model::_RecListKey_default_instance_);
}
inline ::abc::recommend_plt::model::RecListKey* RecListInfo::release_key() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.RecListInfo.key)
  
  ::abc::recommend_plt::model::RecListKey* temp = key_;
  key_ = nullptr;
  return temp;
}
inline ::abc::recommend_plt::model::RecListKey* RecListInfo::mutable_key() {
  
  if (key_ == nullptr) {
    auto* p = CreateMaybeMessage<::abc::recommend_plt::model::RecListKey>(GetArenaNoVirtual());
    key_ = p;
  }
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.RecListInfo.key)
  return key_;
}
inline void RecListInfo::set_allocated_key(::abc::recommend_plt::model::RecListKey* key) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete key_;
  }
  if (key) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      key = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, key, submessage_arena);
    }
    
  } else {
    
  }
  key_ = key;
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.RecListInfo.key)
}

// .abc.recommend_plt.model.RecListVal val = 2;
inline bool RecListInfo::has_val() const {
  return this != internal_default_instance() && val_ != nullptr;
}
inline void RecListInfo::clear_val() {
  if (GetArenaNoVirtual() == nullptr && val_ != nullptr) {
    delete val_;
  }
  val_ = nullptr;
}
inline const ::abc::recommend_plt::model::RecListVal& RecListInfo::val() const {
  const ::abc::recommend_plt::model::RecListVal* p = val_;
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.RecListInfo.val)
  return p != nullptr ? *p : *reinterpret_cast<const ::abc::recommend_plt::model::RecListVal*>(
      &::abc::recommend_plt::model::_RecListVal_default_instance_);
}
inline ::abc::recommend_plt::model::RecListVal* RecListInfo::release_val() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.RecListInfo.val)
  
  ::abc::recommend_plt::model::RecListVal* temp = val_;
  val_ = nullptr;
  return temp;
}
inline ::abc::recommend_plt::model::RecListVal* RecListInfo::mutable_val() {
  
  if (val_ == nullptr) {
    auto* p = CreateMaybeMessage<::abc::recommend_plt::model::RecListVal>(GetArenaNoVirtual());
    val_ = p;
  }
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.RecListInfo.val)
  return val_;
}
inline void RecListInfo::set_allocated_val(::abc::recommend_plt::model::RecListVal* val) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete val_;
  }
  if (val) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      val = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, val, submessage_arena);
    }
    
  } else {
    
  }
  val_ = val;
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.RecListInfo.val)
}

// -------------------------------------------------------------------

// RecListModel

// uint32 bussiness_id = 1;
inline void RecListModel::clear_bussiness_id() {
  bussiness_id_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RecListModel::bussiness_id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.RecListModel.bussiness_id)
  return bussiness_id_;
}
inline void RecListModel::set_bussiness_id(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bussiness_id_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.RecListModel.bussiness_id)
}

// string model_id = 2;
inline void RecListModel::clear_model_id() {
  model_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& RecListModel::model_id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.RecListModel.model_id)
  return model_id_.GetNoArena();
}
inline void RecListModel::set_model_id(const std::string& value) {
  
  model_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.RecListModel.model_id)
}
inline void RecListModel::set_model_id(std::string&& value) {
  
  model_id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.RecListModel.model_id)
}
inline void RecListModel::set_model_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  model_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.RecListModel.model_id)
}
inline void RecListModel::set_model_id(const char* value, size_t size) {
  
  model_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.RecListModel.model_id)
}
inline std::string* RecListModel::mutable_model_id() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.RecListModel.model_id)
  return model_id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* RecListModel::release_model_id() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.RecListModel.model_id)
  
  return model_id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void RecListModel::set_allocated_model_id(std::string* model_id) {
  if (model_id != nullptr) {
    
  } else {
    
  }
  model_id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_id);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.RecListModel.model_id)
}

// repeated .abc.recommend_plt.model.RecList rec_lists = 3;
inline int RecListModel::rec_lists_size() const {
  return rec_lists_.size();
}
inline void RecListModel::clear_rec_lists() {
  rec_lists_.Clear();
}
inline ::abc::recommend_plt::model::RecList* RecListModel::mutable_rec_lists(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.RecListModel.rec_lists)
  return rec_lists_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecList >*
RecListModel::mutable_rec_lists() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.model.RecListModel.rec_lists)
  return &rec_lists_;
}
inline const ::abc::recommend_plt::model::RecList& RecListModel::rec_lists(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.RecListModel.rec_lists)
  return rec_lists_.Get(index);
}
inline ::abc::recommend_plt::model::RecList* RecListModel::add_rec_lists() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.model.RecListModel.rec_lists)
  return rec_lists_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecList >&
RecListModel::rec_lists() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.model.RecListModel.rec_lists)
  return rec_lists_;
}

// repeated .abc.recommend_plt.model.TransformFeaVal trans_fea_vals = 4;
inline int RecListModel::trans_fea_vals_size() const {
  return trans_fea_vals_.size();
}
inline void RecListModel::clear_trans_fea_vals() {
  trans_fea_vals_.Clear();
}
inline ::abc::recommend_plt::model::TransformFeaVal* RecListModel::mutable_trans_fea_vals(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.RecListModel.trans_fea_vals)
  return trans_fea_vals_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::TransformFeaVal >*
RecListModel::mutable_trans_fea_vals() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.model.RecListModel.trans_fea_vals)
  return &trans_fea_vals_;
}
inline const ::abc::recommend_plt::model::TransformFeaVal& RecListModel::trans_fea_vals(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.RecListModel.trans_fea_vals)
  return trans_fea_vals_.Get(index);
}
inline ::abc::recommend_plt::model::TransformFeaVal* RecListModel::add_trans_fea_vals() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.model.RecListModel.trans_fea_vals)
  return trans_fea_vals_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::TransformFeaVal >&
RecListModel::trans_fea_vals() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.model.RecListModel.trans_fea_vals)
  return trans_fea_vals_;
}

// repeated .abc.recommend_plt.model.RecListInfo rec_list_infos = 5;
inline int RecListModel::rec_list_infos_size() const {
  return rec_list_infos_.size();
}
inline void RecListModel::clear_rec_list_infos() {
  rec_list_infos_.Clear();
}
inline ::abc::recommend_plt::model::RecListInfo* RecListModel::mutable_rec_list_infos(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.RecListModel.rec_list_infos)
  return rec_list_infos_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecListInfo >*
RecListModel::mutable_rec_list_infos() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.model.RecListModel.rec_list_infos)
  return &rec_list_infos_;
}
inline const ::abc::recommend_plt::model::RecListInfo& RecListModel::rec_list_infos(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.RecListModel.rec_list_infos)
  return rec_list_infos_.Get(index);
}
inline ::abc::recommend_plt::model::RecListInfo* RecListModel::add_rec_list_infos() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.model.RecListModel.rec_list_infos)
  return rec_list_infos_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::RecListInfo >&
RecListModel::rec_list_infos() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.model.RecListModel.rec_list_infos)
  return rec_list_infos_;
}

// -------------------------------------------------------------------

// FeatureMappingKey

// string raw_fea = 1;
inline void FeatureMappingKey::clear_raw_fea() {
  raw_fea_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& FeatureMappingKey::raw_fea() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.FeatureMappingKey.raw_fea)
  return raw_fea_.GetNoArena();
}
inline void FeatureMappingKey::set_raw_fea(const std::string& value) {
  
  raw_fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.FeatureMappingKey.raw_fea)
}
inline void FeatureMappingKey::set_raw_fea(std::string&& value) {
  
  raw_fea_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.FeatureMappingKey.raw_fea)
}
inline void FeatureMappingKey::set_raw_fea(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  raw_fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.FeatureMappingKey.raw_fea)
}
inline void FeatureMappingKey::set_raw_fea(const char* value, size_t size) {
  
  raw_fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.FeatureMappingKey.raw_fea)
}
inline std::string* FeatureMappingKey::mutable_raw_fea() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.FeatureMappingKey.raw_fea)
  return raw_fea_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* FeatureMappingKey::release_raw_fea() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.FeatureMappingKey.raw_fea)
  
  return raw_fea_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void FeatureMappingKey::set_allocated_raw_fea(std::string* raw_fea) {
  if (raw_fea != nullptr) {
    
  } else {
    
  }
  raw_fea_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), raw_fea);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.FeatureMappingKey.raw_fea)
}

// string raw_val = 2;
inline void FeatureMappingKey::clear_raw_val() {
  raw_val_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& FeatureMappingKey::raw_val() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.FeatureMappingKey.raw_val)
  return raw_val_.GetNoArena();
}
inline void FeatureMappingKey::set_raw_val(const std::string& value) {
  
  raw_val_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.FeatureMappingKey.raw_val)
}
inline void FeatureMappingKey::set_raw_val(std::string&& value) {
  
  raw_val_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.FeatureMappingKey.raw_val)
}
inline void FeatureMappingKey::set_raw_val(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  raw_val_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.FeatureMappingKey.raw_val)
}
inline void FeatureMappingKey::set_raw_val(const char* value, size_t size) {
  
  raw_val_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.FeatureMappingKey.raw_val)
}
inline std::string* FeatureMappingKey::mutable_raw_val() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.FeatureMappingKey.raw_val)
  return raw_val_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* FeatureMappingKey::release_raw_val() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.FeatureMappingKey.raw_val)
  
  return raw_val_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void FeatureMappingKey::set_allocated_raw_val(std::string* raw_val) {
  if (raw_val != nullptr) {
    
  } else {
    
  }
  raw_val_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), raw_val);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.FeatureMappingKey.raw_val)
}

// string site_id = 3;
inline void FeatureMappingKey::clear_site_id() {
  site_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& FeatureMappingKey::site_id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.FeatureMappingKey.site_id)
  return site_id_.GetNoArena();
}
inline void FeatureMappingKey::set_site_id(const std::string& value) {
  
  site_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.FeatureMappingKey.site_id)
}
inline void FeatureMappingKey::set_site_id(std::string&& value) {
  
  site_id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.FeatureMappingKey.site_id)
}
inline void FeatureMappingKey::set_site_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  site_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.FeatureMappingKey.site_id)
}
inline void FeatureMappingKey::set_site_id(const char* value, size_t size) {
  
  site_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.FeatureMappingKey.site_id)
}
inline std::string* FeatureMappingKey::mutable_site_id() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.FeatureMappingKey.site_id)
  return site_id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* FeatureMappingKey::release_site_id() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.FeatureMappingKey.site_id)
  
  return site_id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void FeatureMappingKey::set_allocated_site_id(std::string* site_id) {
  if (site_id != nullptr) {
    
  } else {
    
  }
  site_id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), site_id);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.FeatureMappingKey.site_id)
}

// -------------------------------------------------------------------

// FeatureMappingVal

// string map_fea = 1;
inline void FeatureMappingVal::clear_map_fea() {
  map_fea_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& FeatureMappingVal::map_fea() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.FeatureMappingVal.map_fea)
  return map_fea_.GetNoArena();
}
inline void FeatureMappingVal::set_map_fea(const std::string& value) {
  
  map_fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.FeatureMappingVal.map_fea)
}
inline void FeatureMappingVal::set_map_fea(std::string&& value) {
  
  map_fea_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.FeatureMappingVal.map_fea)
}
inline void FeatureMappingVal::set_map_fea(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  map_fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.FeatureMappingVal.map_fea)
}
inline void FeatureMappingVal::set_map_fea(const char* value, size_t size) {
  
  map_fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.FeatureMappingVal.map_fea)
}
inline std::string* FeatureMappingVal::mutable_map_fea() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.FeatureMappingVal.map_fea)
  return map_fea_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* FeatureMappingVal::release_map_fea() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.FeatureMappingVal.map_fea)
  
  return map_fea_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void FeatureMappingVal::set_allocated_map_fea(std::string* map_fea) {
  if (map_fea != nullptr) {
    
  } else {
    
  }
  map_fea_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), map_fea);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.FeatureMappingVal.map_fea)
}

// string map_val = 2;
inline void FeatureMappingVal::clear_map_val() {
  map_val_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& FeatureMappingVal::map_val() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.FeatureMappingVal.map_val)
  return map_val_.GetNoArena();
}
inline void FeatureMappingVal::set_map_val(const std::string& value) {
  
  map_val_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.FeatureMappingVal.map_val)
}
inline void FeatureMappingVal::set_map_val(std::string&& value) {
  
  map_val_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.FeatureMappingVal.map_val)
}
inline void FeatureMappingVal::set_map_val(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  map_val_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.FeatureMappingVal.map_val)
}
inline void FeatureMappingVal::set_map_val(const char* value, size_t size) {
  
  map_val_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.FeatureMappingVal.map_val)
}
inline std::string* FeatureMappingVal::mutable_map_val() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.FeatureMappingVal.map_val)
  return map_val_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* FeatureMappingVal::release_map_val() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.FeatureMappingVal.map_val)
  
  return map_val_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void FeatureMappingVal::set_allocated_map_val(std::string* map_val) {
  if (map_val != nullptr) {
    
  } else {
    
  }
  map_val_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), map_val);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.FeatureMappingVal.map_val)
}

// -------------------------------------------------------------------

// FeatureMappingMultiVal

// repeated .abc.recommend_plt.model.FeatureMappingVal vals = 1;
inline int FeatureMappingMultiVal::vals_size() const {
  return vals_.size();
}
inline void FeatureMappingMultiVal::clear_vals() {
  vals_.Clear();
}
inline ::abc::recommend_plt::model::FeatureMappingVal* FeatureMappingMultiVal::mutable_vals(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.FeatureMappingMultiVal.vals)
  return vals_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::FeatureMappingVal >*
FeatureMappingMultiVal::mutable_vals() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.model.FeatureMappingMultiVal.vals)
  return &vals_;
}
inline const ::abc::recommend_plt::model::FeatureMappingVal& FeatureMappingMultiVal::vals(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.FeatureMappingMultiVal.vals)
  return vals_.Get(index);
}
inline ::abc::recommend_plt::model::FeatureMappingVal* FeatureMappingMultiVal::add_vals() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.model.FeatureMappingMultiVal.vals)
  return vals_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::FeatureMappingVal >&
FeatureMappingMultiVal::vals() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.model.FeatureMappingMultiVal.vals)
  return vals_;
}

// -------------------------------------------------------------------

// FeatureMappingInfo

// .abc.recommend_plt.model.FeatureMappingKey key = 1;
inline bool FeatureMappingInfo::has_key() const {
  return this != internal_default_instance() && key_ != nullptr;
}
inline void FeatureMappingInfo::clear_key() {
  if (GetArenaNoVirtual() == nullptr && key_ != nullptr) {
    delete key_;
  }
  key_ = nullptr;
}
inline const ::abc::recommend_plt::model::FeatureMappingKey& FeatureMappingInfo::key() const {
  const ::abc::recommend_plt::model::FeatureMappingKey* p = key_;
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.FeatureMappingInfo.key)
  return p != nullptr ? *p : *reinterpret_cast<const ::abc::recommend_plt::model::FeatureMappingKey*>(
      &::abc::recommend_plt::model::_FeatureMappingKey_default_instance_);
}
inline ::abc::recommend_plt::model::FeatureMappingKey* FeatureMappingInfo::release_key() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.FeatureMappingInfo.key)
  
  ::abc::recommend_plt::model::FeatureMappingKey* temp = key_;
  key_ = nullptr;
  return temp;
}
inline ::abc::recommend_plt::model::FeatureMappingKey* FeatureMappingInfo::mutable_key() {
  
  if (key_ == nullptr) {
    auto* p = CreateMaybeMessage<::abc::recommend_plt::model::FeatureMappingKey>(GetArenaNoVirtual());
    key_ = p;
  }
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.FeatureMappingInfo.key)
  return key_;
}
inline void FeatureMappingInfo::set_allocated_key(::abc::recommend_plt::model::FeatureMappingKey* key) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete key_;
  }
  if (key) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      key = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, key, submessage_arena);
    }
    
  } else {
    
  }
  key_ = key;
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.FeatureMappingInfo.key)
}

// .abc.recommend_plt.model.FeatureMappingVal val = 2;
inline bool FeatureMappingInfo::has_val() const {
  return this != internal_default_instance() && val_ != nullptr;
}
inline void FeatureMappingInfo::clear_val() {
  if (GetArenaNoVirtual() == nullptr && val_ != nullptr) {
    delete val_;
  }
  val_ = nullptr;
}
inline const ::abc::recommend_plt::model::FeatureMappingVal& FeatureMappingInfo::val() const {
  const ::abc::recommend_plt::model::FeatureMappingVal* p = val_;
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.FeatureMappingInfo.val)
  return p != nullptr ? *p : *reinterpret_cast<const ::abc::recommend_plt::model::FeatureMappingVal*>(
      &::abc::recommend_plt::model::_FeatureMappingVal_default_instance_);
}
inline ::abc::recommend_plt::model::FeatureMappingVal* FeatureMappingInfo::release_val() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.FeatureMappingInfo.val)
  
  ::abc::recommend_plt::model::FeatureMappingVal* temp = val_;
  val_ = nullptr;
  return temp;
}
inline ::abc::recommend_plt::model::FeatureMappingVal* FeatureMappingInfo::mutable_val() {
  
  if (val_ == nullptr) {
    auto* p = CreateMaybeMessage<::abc::recommend_plt::model::FeatureMappingVal>(GetArenaNoVirtual());
    val_ = p;
  }
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.FeatureMappingInfo.val)
  return val_;
}
inline void FeatureMappingInfo::set_allocated_val(::abc::recommend_plt::model::FeatureMappingVal* val) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete val_;
  }
  if (val) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      val = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, val, submessage_arena);
    }
    
  } else {
    
  }
  val_ = val;
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.FeatureMappingInfo.val)
}

// -------------------------------------------------------------------

// FeatureMappingModel

// uint32 bussiness_id = 1;
inline void FeatureMappingModel::clear_bussiness_id() {
  bussiness_id_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 FeatureMappingModel::bussiness_id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.FeatureMappingModel.bussiness_id)
  return bussiness_id_;
}
inline void FeatureMappingModel::set_bussiness_id(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bussiness_id_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.FeatureMappingModel.bussiness_id)
}

// repeated .abc.recommend_plt.model.FeatureMappingInfo infos = 2;
inline int FeatureMappingModel::infos_size() const {
  return infos_.size();
}
inline void FeatureMappingModel::clear_infos() {
  infos_.Clear();
}
inline ::abc::recommend_plt::model::FeatureMappingInfo* FeatureMappingModel::mutable_infos(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.FeatureMappingModel.infos)
  return infos_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::FeatureMappingInfo >*
FeatureMappingModel::mutable_infos() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.model.FeatureMappingModel.infos)
  return &infos_;
}
inline const ::abc::recommend_plt::model::FeatureMappingInfo& FeatureMappingModel::infos(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.FeatureMappingModel.infos)
  return infos_.Get(index);
}
inline ::abc::recommend_plt::model::FeatureMappingInfo* FeatureMappingModel::add_infos() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.model.FeatureMappingModel.infos)
  return infos_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::FeatureMappingInfo >&
FeatureMappingModel::infos() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.model.FeatureMappingModel.infos)
  return infos_;
}

// -------------------------------------------------------------------

// StatRateKey_Dimension

// string fea = 1;
inline void StatRateKey_Dimension::clear_fea() {
  fea_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& StatRateKey_Dimension::fea() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.StatRateKey.Dimension.fea)
  return fea_.GetNoArena();
}
inline void StatRateKey_Dimension::set_fea(const std::string& value) {
  
  fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.StatRateKey.Dimension.fea)
}
inline void StatRateKey_Dimension::set_fea(std::string&& value) {
  
  fea_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.StatRateKey.Dimension.fea)
}
inline void StatRateKey_Dimension::set_fea(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.StatRateKey.Dimension.fea)
}
inline void StatRateKey_Dimension::set_fea(const char* value, size_t size) {
  
  fea_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.StatRateKey.Dimension.fea)
}
inline std::string* StatRateKey_Dimension::mutable_fea() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.StatRateKey.Dimension.fea)
  return fea_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* StatRateKey_Dimension::release_fea() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.StatRateKey.Dimension.fea)
  
  return fea_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void StatRateKey_Dimension::set_allocated_fea(std::string* fea) {
  if (fea != nullptr) {
    
  } else {
    
  }
  fea_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), fea);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.StatRateKey.Dimension.fea)
}

// string val = 2;
inline void StatRateKey_Dimension::clear_val() {
  val_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& StatRateKey_Dimension::val() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.StatRateKey.Dimension.val)
  return val_.GetNoArena();
}
inline void StatRateKey_Dimension::set_val(const std::string& value) {
  
  val_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.StatRateKey.Dimension.val)
}
inline void StatRateKey_Dimension::set_val(std::string&& value) {
  
  val_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.StatRateKey.Dimension.val)
}
inline void StatRateKey_Dimension::set_val(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  val_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.StatRateKey.Dimension.val)
}
inline void StatRateKey_Dimension::set_val(const char* value, size_t size) {
  
  val_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.StatRateKey.Dimension.val)
}
inline std::string* StatRateKey_Dimension::mutable_val() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.StatRateKey.Dimension.val)
  return val_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* StatRateKey_Dimension::release_val() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.StatRateKey.Dimension.val)
  
  return val_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void StatRateKey_Dimension::set_allocated_val(std::string* val) {
  if (val != nullptr) {
    
  } else {
    
  }
  val_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), val);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.StatRateKey.Dimension.val)
}

// -------------------------------------------------------------------

// StatRateKey

// repeated .abc.recommend_plt.model.StatRateKey.Dimension dimensions = 1;
inline int StatRateKey::dimensions_size() const {
  return dimensions_.size();
}
inline void StatRateKey::clear_dimensions() {
  dimensions_.Clear();
}
inline ::abc::recommend_plt::model::StatRateKey_Dimension* StatRateKey::mutable_dimensions(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.StatRateKey.dimensions)
  return dimensions_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::StatRateKey_Dimension >*
StatRateKey::mutable_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.model.StatRateKey.dimensions)
  return &dimensions_;
}
inline const ::abc::recommend_plt::model::StatRateKey_Dimension& StatRateKey::dimensions(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.StatRateKey.dimensions)
  return dimensions_.Get(index);
}
inline ::abc::recommend_plt::model::StatRateKey_Dimension* StatRateKey::add_dimensions() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.model.StatRateKey.dimensions)
  return dimensions_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::StatRateKey_Dimension >&
StatRateKey::dimensions() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.model.StatRateKey.dimensions)
  return dimensions_;
}

// -------------------------------------------------------------------

// StatRateVal_Metric

// int32 type = 1;
inline void StatRateVal_Metric::clear_type() {
  type_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 StatRateVal_Metric::type() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.StatRateVal.Metric.type)
  return type_;
}
inline void StatRateVal_Metric::set_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.StatRateVal.Metric.type)
}

// float val = 2;
inline void StatRateVal_Metric::clear_val() {
  val_ = 0;
}
inline float StatRateVal_Metric::val() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.StatRateVal.Metric.val)
  return val_;
}
inline void StatRateVal_Metric::set_val(float value) {
  
  val_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.StatRateVal.Metric.val)
}

// -------------------------------------------------------------------

// StatRateVal

// repeated .abc.recommend_plt.model.StatRateVal.Metric metrics = 1;
inline int StatRateVal::metrics_size() const {
  return metrics_.size();
}
inline void StatRateVal::clear_metrics() {
  metrics_.Clear();
}
inline ::abc::recommend_plt::model::StatRateVal_Metric* StatRateVal::mutable_metrics(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.StatRateVal.metrics)
  return metrics_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::StatRateVal_Metric >*
StatRateVal::mutable_metrics() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.model.StatRateVal.metrics)
  return &metrics_;
}
inline const ::abc::recommend_plt::model::StatRateVal_Metric& StatRateVal::metrics(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.StatRateVal.metrics)
  return metrics_.Get(index);
}
inline ::abc::recommend_plt::model::StatRateVal_Metric* StatRateVal::add_metrics() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.model.StatRateVal.metrics)
  return metrics_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::StatRateVal_Metric >&
StatRateVal::metrics() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.model.StatRateVal.metrics)
  return metrics_;
}

// -------------------------------------------------------------------

// StatRateInfo

// uint64 key = 1;
inline void StatRateInfo::clear_key() {
  key_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 StatRateInfo::key() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.StatRateInfo.key)
  return key_;
}
inline void StatRateInfo::set_key(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  key_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.StatRateInfo.key)
}

// .abc.recommend_plt.model.StatRateVal val = 2;
inline bool StatRateInfo::has_val() const {
  return this != internal_default_instance() && val_ != nullptr;
}
inline void StatRateInfo::clear_val() {
  if (GetArenaNoVirtual() == nullptr && val_ != nullptr) {
    delete val_;
  }
  val_ = nullptr;
}
inline const ::abc::recommend_plt::model::StatRateVal& StatRateInfo::val() const {
  const ::abc::recommend_plt::model::StatRateVal* p = val_;
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.StatRateInfo.val)
  return p != nullptr ? *p : *reinterpret_cast<const ::abc::recommend_plt::model::StatRateVal*>(
      &::abc::recommend_plt::model::_StatRateVal_default_instance_);
}
inline ::abc::recommend_plt::model::StatRateVal* StatRateInfo::release_val() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.StatRateInfo.val)
  
  ::abc::recommend_plt::model::StatRateVal* temp = val_;
  val_ = nullptr;
  return temp;
}
inline ::abc::recommend_plt::model::StatRateVal* StatRateInfo::mutable_val() {
  
  if (val_ == nullptr) {
    auto* p = CreateMaybeMessage<::abc::recommend_plt::model::StatRateVal>(GetArenaNoVirtual());
    val_ = p;
  }
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.StatRateInfo.val)
  return val_;
}
inline void StatRateInfo::set_allocated_val(::abc::recommend_plt::model::StatRateVal* val) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete val_;
  }
  if (val) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      val = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, val, submessage_arena);
    }
    
  } else {
    
  }
  val_ = val;
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.StatRateInfo.val)
}

// -------------------------------------------------------------------

// StatRateModel

// uint32 bussiness_id = 1;
inline void StatRateModel::clear_bussiness_id() {
  bussiness_id_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 StatRateModel::bussiness_id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.StatRateModel.bussiness_id)
  return bussiness_id_;
}
inline void StatRateModel::set_bussiness_id(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  bussiness_id_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.StatRateModel.bussiness_id)
}

// repeated .abc.recommend_plt.model.StatRateInfo infos = 2;
inline int StatRateModel::infos_size() const {
  return infos_.size();
}
inline void StatRateModel::clear_infos() {
  infos_.Clear();
}
inline ::abc::recommend_plt::model::StatRateInfo* StatRateModel::mutable_infos(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.StatRateModel.infos)
  return infos_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::StatRateInfo >*
StatRateModel::mutable_infos() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.model.StatRateModel.infos)
  return &infos_;
}
inline const ::abc::recommend_plt::model::StatRateInfo& StatRateModel::infos(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.StatRateModel.infos)
  return infos_.Get(index);
}
inline ::abc::recommend_plt::model::StatRateInfo* StatRateModel::add_infos() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.model.StatRateModel.infos)
  return infos_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::StatRateInfo >&
StatRateModel::infos() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.model.StatRateModel.infos)
  return infos_;
}

// -------------------------------------------------------------------

// CateRelationKey

// string site_uid = 1;
inline void CateRelationKey::clear_site_uid() {
  site_uid_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& CateRelationKey::site_uid() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.CateRelationKey.site_uid)
  return site_uid_.GetNoArena();
}
inline void CateRelationKey::set_site_uid(const std::string& value) {
  
  site_uid_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.CateRelationKey.site_uid)
}
inline void CateRelationKey::set_site_uid(std::string&& value) {
  
  site_uid_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.CateRelationKey.site_uid)
}
inline void CateRelationKey::set_site_uid(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  site_uid_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.CateRelationKey.site_uid)
}
inline void CateRelationKey::set_site_uid(const char* value, size_t size) {
  
  site_uid_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.CateRelationKey.site_uid)
}
inline std::string* CateRelationKey::mutable_site_uid() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.CateRelationKey.site_uid)
  return site_uid_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* CateRelationKey::release_site_uid() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.CateRelationKey.site_uid)
  
  return site_uid_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void CateRelationKey::set_allocated_site_uid(std::string* site_uid) {
  if (site_uid != nullptr) {
    
  } else {
    
  }
  site_uid_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), site_uid);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.CateRelationKey.site_uid)
}

// string category_id = 2;
inline void CateRelationKey::clear_category_id() {
  category_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& CateRelationKey::category_id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.CateRelationKey.category_id)
  return category_id_.GetNoArena();
}
inline void CateRelationKey::set_category_id(const std::string& value) {
  
  category_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.CateRelationKey.category_id)
}
inline void CateRelationKey::set_category_id(std::string&& value) {
  
  category_id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.CateRelationKey.category_id)
}
inline void CateRelationKey::set_category_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  category_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.CateRelationKey.category_id)
}
inline void CateRelationKey::set_category_id(const char* value, size_t size) {
  
  category_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.CateRelationKey.category_id)
}
inline std::string* CateRelationKey::mutable_category_id() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.CateRelationKey.category_id)
  return category_id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* CateRelationKey::release_category_id() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.CateRelationKey.category_id)
  
  return category_id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void CateRelationKey::set_allocated_category_id(std::string* category_id) {
  if (category_id != nullptr) {
    
  } else {
    
  }
  category_id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), category_id);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.CateRelationKey.category_id)
}

// -------------------------------------------------------------------

// CateRelationVal

// repeated .abc.recommend_plt.common.CateRelation infos = 1;
inline int CateRelationVal::infos_size() const {
  return infos_.size();
}
inline ::abc::recommend_plt::common::CateRelation* CateRelationVal::mutable_infos(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.CateRelationVal.infos)
  return infos_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::common::CateRelation >*
CateRelationVal::mutable_infos() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.model.CateRelationVal.infos)
  return &infos_;
}
inline const ::abc::recommend_plt::common::CateRelation& CateRelationVal::infos(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.CateRelationVal.infos)
  return infos_.Get(index);
}
inline ::abc::recommend_plt::common::CateRelation* CateRelationVal::add_infos() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.model.CateRelationVal.infos)
  return infos_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::common::CateRelation >&
CateRelationVal::infos() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.model.CateRelationVal.infos)
  return infos_;
}

// -------------------------------------------------------------------

// ItemRelationKey

// string item_ids = 1;
inline void ItemRelationKey::clear_item_ids() {
  item_ids_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ItemRelationKey::item_ids() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.ItemRelationKey.item_ids)
  return item_ids_.GetNoArena();
}
inline void ItemRelationKey::set_item_ids(const std::string& value) {
  
  item_ids_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.ItemRelationKey.item_ids)
}
inline void ItemRelationKey::set_item_ids(std::string&& value) {
  
  item_ids_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.model.ItemRelationKey.item_ids)
}
inline void ItemRelationKey::set_item_ids(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  item_ids_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.model.ItemRelationKey.item_ids)
}
inline void ItemRelationKey::set_item_ids(const char* value, size_t size) {
  
  item_ids_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.model.ItemRelationKey.item_ids)
}
inline std::string* ItemRelationKey::mutable_item_ids() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.ItemRelationKey.item_ids)
  return item_ids_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ItemRelationKey::release_item_ids() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.ItemRelationKey.item_ids)
  
  return item_ids_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ItemRelationKey::set_allocated_item_ids(std::string* item_ids) {
  if (item_ids != nullptr) {
    
  } else {
    
  }
  item_ids_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), item_ids);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.ItemRelationKey.item_ids)
}

// -------------------------------------------------------------------

// ItemRelationVal

// int64 item_id = 1;
inline void ItemRelationVal::clear_item_id() {
  item_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ItemRelationVal::item_id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.ItemRelationVal.item_id)
  return item_id_;
}
inline void ItemRelationVal::set_item_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  item_id_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.ItemRelationVal.item_id)
}

// float score = 2;
inline void ItemRelationVal::clear_score() {
  score_ = 0;
}
inline float ItemRelationVal::score() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.ItemRelationVal.score)
  return score_;
}
inline void ItemRelationVal::set_score(float value) {
  
  score_ = value;
  // @@protoc_insertion_point(field_set:abc.recommend_plt.model.ItemRelationVal.score)
}

// -------------------------------------------------------------------

// ItemRelationMultiVal

// repeated .abc.recommend_plt.model.ItemRelationVal vals = 1;
inline int ItemRelationMultiVal::vals_size() const {
  return vals_.size();
}
inline void ItemRelationMultiVal::clear_vals() {
  vals_.Clear();
}
inline ::abc::recommend_plt::model::ItemRelationVal* ItemRelationMultiVal::mutable_vals(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.ItemRelationMultiVal.vals)
  return vals_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::ItemRelationVal >*
ItemRelationMultiVal::mutable_vals() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.model.ItemRelationMultiVal.vals)
  return &vals_;
}
inline const ::abc::recommend_plt::model::ItemRelationVal& ItemRelationMultiVal::vals(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.ItemRelationMultiVal.vals)
  return vals_.Get(index);
}
inline ::abc::recommend_plt::model::ItemRelationVal* ItemRelationMultiVal::add_vals() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.model.ItemRelationMultiVal.vals)
  return vals_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::ItemRelationVal >&
ItemRelationMultiVal::vals() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.model.ItemRelationMultiVal.vals)
  return vals_;
}

// -------------------------------------------------------------------

// ItemRelationInfo

// .abc.recommend_plt.model.ItemRelationKey key = 1;
inline bool ItemRelationInfo::has_key() const {
  return this != internal_default_instance() && key_ != nullptr;
}
inline void ItemRelationInfo::clear_key() {
  if (GetArenaNoVirtual() == nullptr && key_ != nullptr) {
    delete key_;
  }
  key_ = nullptr;
}
inline const ::abc::recommend_plt::model::ItemRelationKey& ItemRelationInfo::key() const {
  const ::abc::recommend_plt::model::ItemRelationKey* p = key_;
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.ItemRelationInfo.key)
  return p != nullptr ? *p : *reinterpret_cast<const ::abc::recommend_plt::model::ItemRelationKey*>(
      &::abc::recommend_plt::model::_ItemRelationKey_default_instance_);
}
inline ::abc::recommend_plt::model::ItemRelationKey* ItemRelationInfo::release_key() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.ItemRelationInfo.key)
  
  ::abc::recommend_plt::model::ItemRelationKey* temp = key_;
  key_ = nullptr;
  return temp;
}
inline ::abc::recommend_plt::model::ItemRelationKey* ItemRelationInfo::mutable_key() {
  
  if (key_ == nullptr) {
    auto* p = CreateMaybeMessage<::abc::recommend_plt::model::ItemRelationKey>(GetArenaNoVirtual());
    key_ = p;
  }
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.ItemRelationInfo.key)
  return key_;
}
inline void ItemRelationInfo::set_allocated_key(::abc::recommend_plt::model::ItemRelationKey* key) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete key_;
  }
  if (key) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      key = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, key, submessage_arena);
    }
    
  } else {
    
  }
  key_ = key;
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.ItemRelationInfo.key)
}

// .abc.recommend_plt.model.ItemRelationMultiVal val = 2;
inline bool ItemRelationInfo::has_val() const {
  return this != internal_default_instance() && val_ != nullptr;
}
inline void ItemRelationInfo::clear_val() {
  if (GetArenaNoVirtual() == nullptr && val_ != nullptr) {
    delete val_;
  }
  val_ = nullptr;
}
inline const ::abc::recommend_plt::model::ItemRelationMultiVal& ItemRelationInfo::val() const {
  const ::abc::recommend_plt::model::ItemRelationMultiVal* p = val_;
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.ItemRelationInfo.val)
  return p != nullptr ? *p : *reinterpret_cast<const ::abc::recommend_plt::model::ItemRelationMultiVal*>(
      &::abc::recommend_plt::model::_ItemRelationMultiVal_default_instance_);
}
inline ::abc::recommend_plt::model::ItemRelationMultiVal* ItemRelationInfo::release_val() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.model.ItemRelationInfo.val)
  
  ::abc::recommend_plt::model::ItemRelationMultiVal* temp = val_;
  val_ = nullptr;
  return temp;
}
inline ::abc::recommend_plt::model::ItemRelationMultiVal* ItemRelationInfo::mutable_val() {
  
  if (val_ == nullptr) {
    auto* p = CreateMaybeMessage<::abc::recommend_plt::model::ItemRelationMultiVal>(GetArenaNoVirtual());
    val_ = p;
  }
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.ItemRelationInfo.val)
  return val_;
}
inline void ItemRelationInfo::set_allocated_val(::abc::recommend_plt::model::ItemRelationMultiVal* val) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete val_;
  }
  if (val) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      val = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, val, submessage_arena);
    }
    
  } else {
    
  }
  val_ = val;
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.model.ItemRelationInfo.val)
}

// -------------------------------------------------------------------

// ItemRelationModel

// repeated .abc.recommend_plt.model.ItemRelationInfo infos = 1;
inline int ItemRelationModel::infos_size() const {
  return infos_.size();
}
inline void ItemRelationModel::clear_infos() {
  infos_.Clear();
}
inline ::abc::recommend_plt::model::ItemRelationInfo* ItemRelationModel::mutable_infos(int index) {
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.model.ItemRelationModel.infos)
  return infos_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::ItemRelationInfo >*
ItemRelationModel::mutable_infos() {
  // @@protoc_insertion_point(field_mutable_list:abc.recommend_plt.model.ItemRelationModel.infos)
  return &infos_;
}
inline const ::abc::recommend_plt::model::ItemRelationInfo& ItemRelationModel::infos(int index) const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.model.ItemRelationModel.infos)
  return infos_.Get(index);
}
inline ::abc::recommend_plt::model::ItemRelationInfo* ItemRelationModel::add_infos() {
  // @@protoc_insertion_point(field_add:abc.recommend_plt.model.ItemRelationModel.infos)
  return infos_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::model::ItemRelationInfo >&
ItemRelationModel::infos() const {
  // @@protoc_insertion_point(field_list:abc.recommend_plt.model.ItemRelationModel.infos)
  return infos_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace recommend_plt
}  // namespace abc

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_proto_2frecommend_5fmodel_2eproto
