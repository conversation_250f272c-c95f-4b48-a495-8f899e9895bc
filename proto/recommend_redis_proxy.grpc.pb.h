// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: proto/recommend_redis_proxy.proto
#ifndef GRPC_proto_2frecommend_5fredis_5fproxy_2eproto__INCLUDED
#define GRPC_proto_2frecommend_5fredis_5fproxy_2eproto__INCLUDED

#include "proto/recommend_redis_proxy.pb.h"

#include <functional>
#include <grpc/impl/codegen/port_platform.h>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace abc {
namespace redis {
namespace proxy {

// rpc 服务
class RedisProxy final {
 public:
  static constexpr char const* service_full_name() {
    return "abc.redis.proxy.RedisProxy";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status MGet(::grpc::ClientContext* context, const ::abc::redis::proxy::RedisProxyRequest& request, ::abc::redis::proxy::RedisProxyResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::abc::redis::proxy::RedisProxyResponse>> AsyncMGet(::grpc::ClientContext* context, const ::abc::redis::proxy::RedisProxyRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::abc::redis::proxy::RedisProxyResponse>>(AsyncMGetRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::abc::redis::proxy::RedisProxyResponse>> PrepareAsyncMGet(::grpc::ClientContext* context, const ::abc::redis::proxy::RedisProxyRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::abc::redis::proxy::RedisProxyResponse>>(PrepareAsyncMGetRaw(context, request, cq));
    }
    class experimental_async_interface {
     public:
      virtual ~experimental_async_interface() {}
      virtual void MGet(::grpc::ClientContext* context, const ::abc::redis::proxy::RedisProxyRequest* request, ::abc::redis::proxy::RedisProxyResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void MGet(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::abc::redis::proxy::RedisProxyResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void MGet(::grpc::ClientContext* context, const ::abc::redis::proxy::RedisProxyRequest* request, ::abc::redis::proxy::RedisProxyResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void MGet(::grpc::ClientContext* context, const ::abc::redis::proxy::RedisProxyRequest* request, ::abc::redis::proxy::RedisProxyResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void MGet(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::abc::redis::proxy::RedisProxyResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void MGet(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::abc::redis::proxy::RedisProxyResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
    };
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    typedef class experimental_async_interface async_interface;
    #endif
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    async_interface* async() { return experimental_async(); }
    #endif
    virtual class experimental_async_interface* experimental_async() { return nullptr; }
  private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::abc::redis::proxy::RedisProxyResponse>* AsyncMGetRaw(::grpc::ClientContext* context, const ::abc::redis::proxy::RedisProxyRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::abc::redis::proxy::RedisProxyResponse>* PrepareAsyncMGetRaw(::grpc::ClientContext* context, const ::abc::redis::proxy::RedisProxyRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel);
    ::grpc::Status MGet(::grpc::ClientContext* context, const ::abc::redis::proxy::RedisProxyRequest& request, ::abc::redis::proxy::RedisProxyResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::abc::redis::proxy::RedisProxyResponse>> AsyncMGet(::grpc::ClientContext* context, const ::abc::redis::proxy::RedisProxyRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::abc::redis::proxy::RedisProxyResponse>>(AsyncMGetRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::abc::redis::proxy::RedisProxyResponse>> PrepareAsyncMGet(::grpc::ClientContext* context, const ::abc::redis::proxy::RedisProxyRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::abc::redis::proxy::RedisProxyResponse>>(PrepareAsyncMGetRaw(context, request, cq));
    }
    class experimental_async final :
      public StubInterface::experimental_async_interface {
     public:
      void MGet(::grpc::ClientContext* context, const ::abc::redis::proxy::RedisProxyRequest* request, ::abc::redis::proxy::RedisProxyResponse* response, std::function<void(::grpc::Status)>) override;
      void MGet(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::abc::redis::proxy::RedisProxyResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void MGet(::grpc::ClientContext* context, const ::abc::redis::proxy::RedisProxyRequest* request, ::abc::redis::proxy::RedisProxyResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void MGet(::grpc::ClientContext* context, const ::abc::redis::proxy::RedisProxyRequest* request, ::abc::redis::proxy::RedisProxyResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void MGet(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::abc::redis::proxy::RedisProxyResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void MGet(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::abc::redis::proxy::RedisProxyResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
     private:
      friend class Stub;
      explicit experimental_async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class experimental_async_interface* experimental_async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class experimental_async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::abc::redis::proxy::RedisProxyResponse>* AsyncMGetRaw(::grpc::ClientContext* context, const ::abc::redis::proxy::RedisProxyRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::abc::redis::proxy::RedisProxyResponse>* PrepareAsyncMGetRaw(::grpc::ClientContext* context, const ::abc::redis::proxy::RedisProxyRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_MGet_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status MGet(::grpc::ServerContext* context, const ::abc::redis::proxy::RedisProxyRequest* request, ::abc::redis::proxy::RedisProxyResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_MGet : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_MGet() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_MGet() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MGet(::grpc::ServerContext* /*context*/, const ::abc::redis::proxy::RedisProxyRequest* /*request*/, ::abc::redis::proxy::RedisProxyResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestMGet(::grpc::ServerContext* context, ::abc::redis::proxy::RedisProxyRequest* request, ::grpc::ServerAsyncResponseWriter< ::abc::redis::proxy::RedisProxyResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_MGet<Service > AsyncService;
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_MGet : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_MGet() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(0,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::abc::redis::proxy::RedisProxyRequest, ::abc::redis::proxy::RedisProxyResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::abc::redis::proxy::RedisProxyRequest* request, ::abc::redis::proxy::RedisProxyResponse* response) { return this->MGet(context, request, response); }));}
    void SetMessageAllocatorFor_MGet(
        ::grpc::experimental::MessageAllocator< ::abc::redis::proxy::RedisProxyRequest, ::abc::redis::proxy::RedisProxyResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(0);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::abc::redis::proxy::RedisProxyRequest, ::abc::redis::proxy::RedisProxyResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_MGet() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MGet(::grpc::ServerContext* /*context*/, const ::abc::redis::proxy::RedisProxyRequest* /*request*/, ::abc::redis::proxy::RedisProxyResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* MGet(
      ::grpc::CallbackServerContext* /*context*/, const ::abc::redis::proxy::RedisProxyRequest* /*request*/, ::abc::redis::proxy::RedisProxyResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* MGet(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::abc::redis::proxy::RedisProxyRequest* /*request*/, ::abc::redis::proxy::RedisProxyResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
  typedef ExperimentalWithCallbackMethod_MGet<Service > CallbackService;
  #endif

  typedef ExperimentalWithCallbackMethod_MGet<Service > ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_MGet : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_MGet() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_MGet() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MGet(::grpc::ServerContext* /*context*/, const ::abc::redis::proxy::RedisProxyRequest* /*request*/, ::abc::redis::proxy::RedisProxyResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_MGet : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_MGet() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_MGet() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MGet(::grpc::ServerContext* /*context*/, const ::abc::redis::proxy::RedisProxyRequest* /*request*/, ::abc::redis::proxy::RedisProxyResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestMGet(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_MGet : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_MGet() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(0,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->MGet(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_MGet() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MGet(::grpc::ServerContext* /*context*/, const ::abc::redis::proxy::RedisProxyRequest* /*request*/, ::abc::redis::proxy::RedisProxyResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* MGet(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* MGet(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_MGet : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_MGet() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::abc::redis::proxy::RedisProxyRequest, ::abc::redis::proxy::RedisProxyResponse>(
            [this](::grpc_impl::ServerContext* context,
                   ::grpc_impl::ServerUnaryStreamer<
                     ::abc::redis::proxy::RedisProxyRequest, ::abc::redis::proxy::RedisProxyResponse>* streamer) {
                       return this->StreamedMGet(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_MGet() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status MGet(::grpc::ServerContext* /*context*/, const ::abc::redis::proxy::RedisProxyRequest* /*request*/, ::abc::redis::proxy::RedisProxyResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedMGet(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::abc::redis::proxy::RedisProxyRequest,::abc::redis::proxy::RedisProxyResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_MGet<Service > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_MGet<Service > StreamedService;
};

}  // namespace proxy
}  // namespace redis
}  // namespace abc


#endif  // GRPC_proto_2frecommend_5fredis_5fproxy_2eproto__INCLUDED
