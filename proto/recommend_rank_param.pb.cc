// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/recommend_rank_param.proto

#include "proto/recommend_rank_param.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5frank_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_CrossMixParam_proto_2frecommend_5frank_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5frank_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_CrossMixParam_Factor_proto_2frecommend_5frank_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5frank_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_DiversityParam_proto_2frecommend_5frank_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5frank_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_DslMixParam_proto_2frecommend_5frank_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5frank_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_DslScoringParam_proto_2frecommend_5frank_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5frank_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_HotScoringParam_proto_2frecommend_5frank_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5frank_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_ItemFeatureDiversityParam_proto_2frecommend_5frank_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5frank_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_ItemFeatureDiversityParam_Factor_proto_2frecommend_5frank_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5frank_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_MixParam_proto_2frecommend_5frank_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5frank_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<3> scc_info_RankStrategy_proto_2frecommend_5frank_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5frank_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_RankStrategyConfig_RankStrategyConfEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5frank_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_ScoreName_proto_2frecommend_5frank_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5frank_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<4> scc_info_ScoringParam_proto_2frecommend_5frank_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5frank_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<3> scc_info_TFScoringParam_proto_2frecommend_5frank_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5frank_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_TFScoringParam_FeaSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5frank_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_TFScoringParam_ModelSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5frank_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_TwinTowerScoringParam_proto_2frecommend_5frank_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5frank_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5frank_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto;
namespace abc {
namespace recommend_plt {
namespace rank {
class ScoreNameDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ScoreName> _instance;
} _ScoreName_default_instance_;
class TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse> _instance;
} _TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse_default_instance_;
class TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse> _instance;
} _TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse_default_instance_;
class TwinTowerScoringParamDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TwinTowerScoringParam> _instance;
} _TwinTowerScoringParam_default_instance_;
class HotScoringParamDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<HotScoringParam> _instance;
} _HotScoringParam_default_instance_;
class TFScoringParam_ModelSiteUidMapEntry_DoNotUseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TFScoringParam_ModelSiteUidMapEntry_DoNotUse> _instance;
} _TFScoringParam_ModelSiteUidMapEntry_DoNotUse_default_instance_;
class TFScoringParam_FeaSiteUidMapEntry_DoNotUseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TFScoringParam_FeaSiteUidMapEntry_DoNotUse> _instance;
} _TFScoringParam_FeaSiteUidMapEntry_DoNotUse_default_instance_;
class TFScoringParamDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TFScoringParam> _instance;
} _TFScoringParam_default_instance_;
class DslScoringParamDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<DslScoringParam> _instance;
} _DslScoringParam_default_instance_;
class ScoringParamDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ScoringParam> _instance;
} _ScoringParam_default_instance_;
class CrossMixParam_FactorDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<CrossMixParam_Factor> _instance;
} _CrossMixParam_Factor_default_instance_;
class CrossMixParamDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<CrossMixParam> _instance;
} _CrossMixParam_default_instance_;
class DslMixParamDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<DslMixParam> _instance;
} _DslMixParam_default_instance_;
class MixParamDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<MixParam> _instance;
} _MixParam_default_instance_;
class ItemFeatureDiversityParam_FactorDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ItemFeatureDiversityParam_Factor> _instance;
} _ItemFeatureDiversityParam_Factor_default_instance_;
class ItemFeatureDiversityParamDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ItemFeatureDiversityParam> _instance;
} _ItemFeatureDiversityParam_default_instance_;
class DiversityParamDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<DiversityParam> _instance;
} _DiversityParam_default_instance_;
class RankStrategyDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RankStrategy> _instance;
} _RankStrategy_default_instance_;
class RankStrategyConfig_RankStrategyConfEntry_DoNotUseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RankStrategyConfig_RankStrategyConfEntry_DoNotUse> _instance;
} _RankStrategyConfig_RankStrategyConfEntry_DoNotUse_default_instance_;
class RankStrategyConfigDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RankStrategyConfig> _instance;
} _RankStrategyConfig_default_instance_;
}  // namespace rank
}  // namespace recommend_plt
}  // namespace abc
static void InitDefaultsscc_info_CrossMixParam_proto_2frecommend_5frank_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::rank::_CrossMixParam_default_instance_;
    new (ptr) ::abc::recommend_plt::rank::CrossMixParam();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::rank::CrossMixParam::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_CrossMixParam_proto_2frecommend_5frank_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsscc_info_CrossMixParam_proto_2frecommend_5frank_5fparam_2eproto}, {
      &scc_info_CrossMixParam_Factor_proto_2frecommend_5frank_5fparam_2eproto.base,}};

static void InitDefaultsscc_info_CrossMixParam_Factor_proto_2frecommend_5frank_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::rank::_CrossMixParam_Factor_default_instance_;
    new (ptr) ::abc::recommend_plt::rank::CrossMixParam_Factor();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::rank::CrossMixParam_Factor::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_CrossMixParam_Factor_proto_2frecommend_5frank_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsscc_info_CrossMixParam_Factor_proto_2frecommend_5frank_5fparam_2eproto}, {}};

static void InitDefaultsscc_info_DiversityParam_proto_2frecommend_5frank_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::rank::_DiversityParam_default_instance_;
    new (ptr) ::abc::recommend_plt::rank::DiversityParam();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::rank::DiversityParam::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_DiversityParam_proto_2frecommend_5frank_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsscc_info_DiversityParam_proto_2frecommend_5frank_5fparam_2eproto}, {
      &scc_info_ItemFeatureDiversityParam_proto_2frecommend_5frank_5fparam_2eproto.base,}};

static void InitDefaultsscc_info_DslMixParam_proto_2frecommend_5frank_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::rank::_DslMixParam_default_instance_;
    new (ptr) ::abc::recommend_plt::rank::DslMixParam();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::rank::DslMixParam::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_DslMixParam_proto_2frecommend_5frank_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsscc_info_DslMixParam_proto_2frecommend_5frank_5fparam_2eproto}, {}};

static void InitDefaultsscc_info_DslScoringParam_proto_2frecommend_5frank_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::rank::_DslScoringParam_default_instance_;
    new (ptr) ::abc::recommend_plt::rank::DslScoringParam();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::rank::DslScoringParam::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_DslScoringParam_proto_2frecommend_5frank_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsscc_info_DslScoringParam_proto_2frecommend_5frank_5fparam_2eproto}, {}};

static void InitDefaultsscc_info_HotScoringParam_proto_2frecommend_5frank_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::rank::_HotScoringParam_default_instance_;
    new (ptr) ::abc::recommend_plt::rank::HotScoringParam();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::rank::HotScoringParam::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_HotScoringParam_proto_2frecommend_5frank_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsscc_info_HotScoringParam_proto_2frecommend_5frank_5fparam_2eproto}, {}};

static void InitDefaultsscc_info_ItemFeatureDiversityParam_proto_2frecommend_5frank_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::rank::_ItemFeatureDiversityParam_default_instance_;
    new (ptr) ::abc::recommend_plt::rank::ItemFeatureDiversityParam();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::rank::ItemFeatureDiversityParam::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_ItemFeatureDiversityParam_proto_2frecommend_5frank_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsscc_info_ItemFeatureDiversityParam_proto_2frecommend_5frank_5fparam_2eproto}, {
      &scc_info_ItemFeatureDiversityParam_Factor_proto_2frecommend_5frank_5fparam_2eproto.base,}};

static void InitDefaultsscc_info_ItemFeatureDiversityParam_Factor_proto_2frecommend_5frank_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::rank::_ItemFeatureDiversityParam_Factor_default_instance_;
    new (ptr) ::abc::recommend_plt::rank::ItemFeatureDiversityParam_Factor();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::rank::ItemFeatureDiversityParam_Factor::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_ItemFeatureDiversityParam_Factor_proto_2frecommend_5frank_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsscc_info_ItemFeatureDiversityParam_Factor_proto_2frecommend_5frank_5fparam_2eproto}, {}};

static void InitDefaultsscc_info_MixParam_proto_2frecommend_5frank_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::rank::_MixParam_default_instance_;
    new (ptr) ::abc::recommend_plt::rank::MixParam();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::rank::MixParam::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_MixParam_proto_2frecommend_5frank_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsscc_info_MixParam_proto_2frecommend_5frank_5fparam_2eproto}, {
      &scc_info_CrossMixParam_proto_2frecommend_5frank_5fparam_2eproto.base,
      &scc_info_DslMixParam_proto_2frecommend_5frank_5fparam_2eproto.base,}};

static void InitDefaultsscc_info_RankStrategy_proto_2frecommend_5frank_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::rank::_RankStrategy_default_instance_;
    new (ptr) ::abc::recommend_plt::rank::RankStrategy();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::rank::RankStrategy::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<3> scc_info_RankStrategy_proto_2frecommend_5frank_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 3, InitDefaultsscc_info_RankStrategy_proto_2frecommend_5frank_5fparam_2eproto}, {
      &scc_info_ScoringParam_proto_2frecommend_5frank_5fparam_2eproto.base,
      &scc_info_MixParam_proto_2frecommend_5frank_5fparam_2eproto.base,
      &scc_info_DiversityParam_proto_2frecommend_5frank_5fparam_2eproto.base,}};

static void InitDefaultsscc_info_RankStrategyConfig_proto_2frecommend_5frank_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::rank::_RankStrategyConfig_default_instance_;
    new (ptr) ::abc::recommend_plt::rank::RankStrategyConfig();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::rank::RankStrategyConfig::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_RankStrategyConfig_proto_2frecommend_5frank_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsscc_info_RankStrategyConfig_proto_2frecommend_5frank_5fparam_2eproto}, {
      &scc_info_RankStrategyConfig_RankStrategyConfEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto.base,}};

static void InitDefaultsscc_info_RankStrategyConfig_RankStrategyConfEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::rank::_RankStrategyConfig_RankStrategyConfEntry_DoNotUse_default_instance_;
    new (ptr) ::abc::recommend_plt::rank::RankStrategyConfig_RankStrategyConfEntry_DoNotUse();
  }
  ::abc::recommend_plt::rank::RankStrategyConfig_RankStrategyConfEntry_DoNotUse::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_RankStrategyConfig_RankStrategyConfEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsscc_info_RankStrategyConfig_RankStrategyConfEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto}, {
      &scc_info_RankStrategy_proto_2frecommend_5frank_5fparam_2eproto.base,}};

static void InitDefaultsscc_info_ScoreName_proto_2frecommend_5frank_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::rank::_ScoreName_default_instance_;
    new (ptr) ::abc::recommend_plt::rank::ScoreName();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::rank::ScoreName::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_ScoreName_proto_2frecommend_5frank_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsscc_info_ScoreName_proto_2frecommend_5frank_5fparam_2eproto}, {}};

static void InitDefaultsscc_info_ScoringParam_proto_2frecommend_5frank_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::rank::_ScoringParam_default_instance_;
    new (ptr) ::abc::recommend_plt::rank::ScoringParam();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::rank::ScoringParam::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<4> scc_info_ScoringParam_proto_2frecommend_5frank_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 4, InitDefaultsscc_info_ScoringParam_proto_2frecommend_5frank_5fparam_2eproto}, {
      &scc_info_TwinTowerScoringParam_proto_2frecommend_5frank_5fparam_2eproto.base,
      &scc_info_HotScoringParam_proto_2frecommend_5frank_5fparam_2eproto.base,
      &scc_info_TFScoringParam_proto_2frecommend_5frank_5fparam_2eproto.base,
      &scc_info_DslScoringParam_proto_2frecommend_5frank_5fparam_2eproto.base,}};

static void InitDefaultsscc_info_TFScoringParam_proto_2frecommend_5frank_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::rank::_TFScoringParam_default_instance_;
    new (ptr) ::abc::recommend_plt::rank::TFScoringParam();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::rank::TFScoringParam::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<3> scc_info_TFScoringParam_proto_2frecommend_5frank_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 3, InitDefaultsscc_info_TFScoringParam_proto_2frecommend_5frank_5fparam_2eproto}, {
      &scc_info_ScoreName_proto_2frecommend_5frank_5fparam_2eproto.base,
      &scc_info_TFScoringParam_ModelSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto.base,
      &scc_info_TFScoringParam_FeaSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto.base,}};

static void InitDefaultsscc_info_TFScoringParam_FeaSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::rank::_TFScoringParam_FeaSiteUidMapEntry_DoNotUse_default_instance_;
    new (ptr) ::abc::recommend_plt::rank::TFScoringParam_FeaSiteUidMapEntry_DoNotUse();
  }
  ::abc::recommend_plt::rank::TFScoringParam_FeaSiteUidMapEntry_DoNotUse::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_TFScoringParam_FeaSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsscc_info_TFScoringParam_FeaSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto}, {}};

static void InitDefaultsscc_info_TFScoringParam_ModelSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::rank::_TFScoringParam_ModelSiteUidMapEntry_DoNotUse_default_instance_;
    new (ptr) ::abc::recommend_plt::rank::TFScoringParam_ModelSiteUidMapEntry_DoNotUse();
  }
  ::abc::recommend_plt::rank::TFScoringParam_ModelSiteUidMapEntry_DoNotUse::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_TFScoringParam_ModelSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsscc_info_TFScoringParam_ModelSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto}, {}};

static void InitDefaultsscc_info_TwinTowerScoringParam_proto_2frecommend_5frank_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::rank::_TwinTowerScoringParam_default_instance_;
    new (ptr) ::abc::recommend_plt::rank::TwinTowerScoringParam();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::rank::TwinTowerScoringParam::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_TwinTowerScoringParam_proto_2frecommend_5frank_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsscc_info_TwinTowerScoringParam_proto_2frecommend_5frank_5fparam_2eproto}, {
      &scc_info_TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto.base,
      &scc_info_TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto.base,}};

static void InitDefaultsscc_info_TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::rank::_TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse_default_instance_;
    new (ptr) ::abc::recommend_plt::rank::TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse();
  }
  ::abc::recommend_plt::rank::TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsscc_info_TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto}, {}};

static void InitDefaultsscc_info_TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::rank::_TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse_default_instance_;
    new (ptr) ::abc::recommend_plt::rank::TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse();
  }
  ::abc::recommend_plt::rank::TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsscc_info_TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto}, {}};

static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_proto_2frecommend_5frank_5fparam_2eproto[20];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_proto_2frecommend_5frank_5fparam_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_proto_2frecommend_5frank_5fparam_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_proto_2frecommend_5frank_5fparam_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::ScoreName, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::ScoreName, model_output_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::ScoreName, output_score_feature_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TwinTowerScoringParam, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TwinTowerScoringParam, user_emb_model_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TwinTowerScoringParam, user_emb_model_output_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TwinTowerScoringParam, item_emb_feature_group_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TwinTowerScoringParam, item_emb_feature_name_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TwinTowerScoringParam, vector_size_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TwinTowerScoringParam, function_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TwinTowerScoringParam, output_score_feature_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TwinTowerScoringParam, model_site_uid_map_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TwinTowerScoringParam, fea_site_uid_map_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::HotScoringParam, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::HotScoringParam, feature_group_name_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::HotScoringParam, feature_name_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::HotScoringParam, output_score_feature_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::HotScoringParam, default_item_id_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::HotScoringParam, default_feature_group_name_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::HotScoringParam, default_feature_name_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TFScoringParam_ModelSiteUidMapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TFScoringParam_ModelSiteUidMapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TFScoringParam_ModelSiteUidMapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TFScoringParam_ModelSiteUidMapEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TFScoringParam_FeaSiteUidMapEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TFScoringParam_FeaSiteUidMapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TFScoringParam_FeaSiteUidMapEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TFScoringParam_FeaSiteUidMapEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TFScoringParam, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TFScoringParam, model_id_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TFScoringParam, model_site_uid_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TFScoringParam, fea_site_uid_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TFScoringParam, score_name_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TFScoringParam, model_site_uid_map_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TFScoringParam, fea_site_uid_map_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::TFScoringParam, predict_type_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::DslScoringParam, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::DslScoringParam, dsl_feature_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::DslScoringParam, step_feature_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::DslScoringParam, output_score_feature_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::ScoringParam, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::ScoringParam, twin_tower_scoring_param_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::ScoringParam, hot_scoring_param_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::ScoringParam, tf_scoring_param_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::ScoringParam, dsl_scoring_param_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::CrossMixParam_Factor, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::CrossMixParam_Factor, score_feature_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::CrossMixParam_Factor, cnt_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::CrossMixParam, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::CrossMixParam, factor_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::DslMixParam, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::DslMixParam, mix_score_feature_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::DslMixParam, step_feature_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::MixParam, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::MixParam, cross_mix_param_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::MixParam, dsl_mix_param_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::ItemFeatureDiversityParam_Factor, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::ItemFeatureDiversityParam_Factor, feature_val_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::ItemFeatureDiversityParam_Factor, cnt_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::ItemFeatureDiversityParam, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::ItemFeatureDiversityParam, feature_from_dsl_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::ItemFeatureDiversityParam, factor_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::DiversityParam, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::DiversityParam, item_feature_diversity_param_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::RankStrategy, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::RankStrategy, scoring_param_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::RankStrategy, mix_param_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::RankStrategy, diversity_param_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::RankStrategy, name_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::RankStrategyConfig_RankStrategyConfEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::RankStrategyConfig_RankStrategyConfEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::RankStrategyConfig_RankStrategyConfEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::RankStrategyConfig_RankStrategyConfEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::RankStrategyConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::rank::RankStrategyConfig, rank_strategy_conf_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::abc::recommend_plt::rank::ScoreName)},
  { 7, 14, sizeof(::abc::recommend_plt::rank::TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse)},
  { 16, 23, sizeof(::abc::recommend_plt::rank::TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse)},
  { 25, -1, sizeof(::abc::recommend_plt::rank::TwinTowerScoringParam)},
  { 39, -1, sizeof(::abc::recommend_plt::rank::HotScoringParam)},
  { 50, 57, sizeof(::abc::recommend_plt::rank::TFScoringParam_ModelSiteUidMapEntry_DoNotUse)},
  { 59, 66, sizeof(::abc::recommend_plt::rank::TFScoringParam_FeaSiteUidMapEntry_DoNotUse)},
  { 68, -1, sizeof(::abc::recommend_plt::rank::TFScoringParam)},
  { 80, -1, sizeof(::abc::recommend_plt::rank::DslScoringParam)},
  { 88, -1, sizeof(::abc::recommend_plt::rank::ScoringParam)},
  { 97, -1, sizeof(::abc::recommend_plt::rank::CrossMixParam_Factor)},
  { 104, -1, sizeof(::abc::recommend_plt::rank::CrossMixParam)},
  { 110, -1, sizeof(::abc::recommend_plt::rank::DslMixParam)},
  { 117, -1, sizeof(::abc::recommend_plt::rank::MixParam)},
  { 124, -1, sizeof(::abc::recommend_plt::rank::ItemFeatureDiversityParam_Factor)},
  { 131, -1, sizeof(::abc::recommend_plt::rank::ItemFeatureDiversityParam)},
  { 138, -1, sizeof(::abc::recommend_plt::rank::DiversityParam)},
  { 144, -1, sizeof(::abc::recommend_plt::rank::RankStrategy)},
  { 153, 160, sizeof(::abc::recommend_plt::rank::RankStrategyConfig_RankStrategyConfEntry_DoNotUse)},
  { 162, -1, sizeof(::abc::recommend_plt::rank::RankStrategyConfig)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::rank::_ScoreName_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::rank::_TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::rank::_TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::rank::_TwinTowerScoringParam_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::rank::_HotScoringParam_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::rank::_TFScoringParam_ModelSiteUidMapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::rank::_TFScoringParam_FeaSiteUidMapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::rank::_TFScoringParam_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::rank::_DslScoringParam_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::rank::_ScoringParam_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::rank::_CrossMixParam_Factor_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::rank::_CrossMixParam_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::rank::_DslMixParam_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::rank::_MixParam_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::rank::_ItemFeatureDiversityParam_Factor_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::rank::_ItemFeatureDiversityParam_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::rank::_DiversityParam_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::rank::_RankStrategy_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::rank::_RankStrategyConfig_RankStrategyConfEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::rank::_RankStrategyConfig_default_instance_),
};

const char descriptor_table_protodef_proto_2frecommend_5frank_5fparam_2eproto[] =
  "\n proto/recommend_rank_param.proto\022\026abc."
  "recommend_plt.rank\"\?\n\tScoreName\022\024\n\014model"
  "_output\030\001 \001(\t\022\034\n\024output_score_feature\030\002 "
  "\001(\t\"\351\004\n\025TwinTowerScoringParam\022\026\n\016user_em"
  "b_model\030\001 \001(\t\022\035\n\025user_emb_model_output\030\002"
  " \001(\t\022\036\n\026item_emb_feature_group\030\003 \001(\t\022\035\n\025"
  "item_emb_feature_name\030\004 \001(\t\022\023\n\013vector_si"
  "ze\030\005 \001(\005\022H\n\010function\030\006 \001(\01626.abc.recomme"
  "nd_plt.rank.TwinTowerScoringParam.FUNCTI"
  "ON\022\034\n\024output_score_feature\030\007 \001(\t\022^\n\022mode"
  "l_site_uid_map\030\010 \003(\0132B.abc.recommend_plt"
  ".rank.TwinTowerScoringParam.ModelSiteUid"
  "MapEntry\022Z\n\020fea_site_uid_map\030\t \003(\0132@.abc"
  ".recommend_plt.rank.TwinTowerScoringPara"
  "m.FeaSiteUidMapEntry\0326\n\024ModelSiteUidMapE"
  "ntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\0324\n\022"
  "FeaSiteUidMapEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value"
  "\030\002 \001(\t:\0028\001\"3\n\010FUNCTION\022\013\n\007UNKNOWN\020\000\022\n\n\006C"
  "OSINE\020\001\022\016\n\nDOTPRODUCT\020\002\"\274\001\n\017HotScoringPa"
  "ram\022\032\n\022feature_group_name\030\001 \001(\t\022\024\n\014featu"
  "re_name\030\002 \001(\t\022\034\n\024output_score_feature\030\003 "
  "\001(\t\022\027\n\017default_item_id\030\004 \001(\t\022\"\n\032default_"
  "feature_group_name\030\005 \001(\t\022\034\n\024default_feat"
  "ure_name\030\006 \001(\t\"\271\003\n\016TFScoringParam\022\020\n\010mod"
  "el_id\030\001 \001(\t\022\026\n\016model_site_uid\030\002 \001(\t\022\024\n\014f"
  "ea_site_uid\030\003 \001(\t\0225\n\nscore_name\030\004 \003(\0132!."
  "abc.recommend_plt.rank.ScoreName\022W\n\022mode"
  "l_site_uid_map\030\005 \003(\0132;.abc.recommend_plt"
  ".rank.TFScoringParam.ModelSiteUidMapEntr"
  "y\022S\n\020fea_site_uid_map\030\006 \003(\01329.abc.recomm"
  "end_plt.rank.TFScoringParam.FeaSiteUidMa"
  "pEntry\022\024\n\014predict_type\030\007 \001(\005\0326\n\024ModelSit"
  "eUidMapEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t"
  ":\0028\001\0324\n\022FeaSiteUidMapEntry\022\013\n\003key\030\001 \001(\t\022"
  "\r\n\005value\030\002 \001(\t:\0028\001\"Z\n\017DslScoringParam\022\023\n"
  "\013dsl_feature\030\001 \001(\t\022\024\n\014step_feature\030\002 \003(\t"
  "\022\034\n\024output_score_feature\030\003 \001(\t\"\251\002\n\014Scori"
  "ngParam\022O\n\030twin_tower_scoring_param\030\001 \001("
  "\0132-.abc.recommend_plt.rank.TwinTowerScor"
  "ingParam\022B\n\021hot_scoring_param\030\002 \001(\0132\'.ab"
  "c.recommend_plt.rank.HotScoringParam\022@\n\020"
  "tf_scoring_param\030\003 \001(\0132&.abc.recommend_p"
  "lt.rank.TFScoringParam\022B\n\021dsl_scoring_pa"
  "ram\030\004 \001(\0132\'.abc.recommend_plt.rank.DslSc"
  "oringParam\"{\n\rCrossMixParam\022<\n\006factor\030\001 "
  "\003(\0132,.abc.recommend_plt.rank.CrossMixPar"
  "am.Factor\032,\n\006Factor\022\025\n\rscore_feature\030\001 \001"
  "(\t\022\013\n\003cnt\030\002 \001(\005\">\n\013DslMixParam\022\031\n\021mix_sc"
  "ore_feature\030\001 \001(\t\022\024\n\014step_feature\030\002 \003(\t\""
  "\206\001\n\010MixParam\022>\n\017cross_mix_param\030\001 \001(\0132%."
  "abc.recommend_plt.rank.CrossMixParam\022:\n\r"
  "dsl_mix_param\030\002 \001(\0132#.abc.recommend_plt."
  "rank.DslMixParam\"\253\001\n\031ItemFeatureDiversit"
  "yParam\022\030\n\020feature_from_dsl\030\001 \001(\t\022H\n\006fact"
  "or\030\002 \003(\01328.abc.recommend_plt.rank.ItemFe"
  "atureDiversityParam.Factor\032*\n\006Factor\022\023\n\013"
  "feature_val\030\001 \001(\005\022\013\n\003cnt\030\002 \001(\005\"i\n\016Divers"
  "ityParam\022W\n\034item_feature_diversity_param"
  "\030\001 \001(\01321.abc.recommend_plt.rank.ItemFeat"
  "ureDiversityParam\"\317\001\n\014RankStrategy\022;\n\rsc"
  "oring_param\030\001 \003(\0132$.abc.recommend_plt.ra"
  "nk.ScoringParam\0223\n\tmix_param\030\002 \001(\0132 .abc"
  ".recommend_plt.rank.MixParam\022\?\n\017diversit"
  "y_param\030\003 \001(\0132&.abc.recommend_plt.rank.D"
  "iversityParam\022\014\n\004name\030\004 \001(\t\"\321\001\n\022RankStra"
  "tegyConfig\022\\\n\022rank_strategy_conf\030\001 \003(\0132@"
  ".abc.recommend_plt.rank.RankStrategyConf"
  "ig.RankStrategyConfEntry\032]\n\025RankStrategy"
  "ConfEntry\022\013\n\003key\030\001 \001(\t\0223\n\005value\030\002 \001(\0132$."
  "abc.recommend_plt.rank.RankStrategy:\0028\001b"
  "\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_proto_2frecommend_5frank_5fparam_2eproto_deps[1] = {
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_proto_2frecommend_5frank_5fparam_2eproto_sccs[20] = {
  &scc_info_CrossMixParam_proto_2frecommend_5frank_5fparam_2eproto.base,
  &scc_info_CrossMixParam_Factor_proto_2frecommend_5frank_5fparam_2eproto.base,
  &scc_info_DiversityParam_proto_2frecommend_5frank_5fparam_2eproto.base,
  &scc_info_DslMixParam_proto_2frecommend_5frank_5fparam_2eproto.base,
  &scc_info_DslScoringParam_proto_2frecommend_5frank_5fparam_2eproto.base,
  &scc_info_HotScoringParam_proto_2frecommend_5frank_5fparam_2eproto.base,
  &scc_info_ItemFeatureDiversityParam_proto_2frecommend_5frank_5fparam_2eproto.base,
  &scc_info_ItemFeatureDiversityParam_Factor_proto_2frecommend_5frank_5fparam_2eproto.base,
  &scc_info_MixParam_proto_2frecommend_5frank_5fparam_2eproto.base,
  &scc_info_RankStrategy_proto_2frecommend_5frank_5fparam_2eproto.base,
  &scc_info_RankStrategyConfig_proto_2frecommend_5frank_5fparam_2eproto.base,
  &scc_info_RankStrategyConfig_RankStrategyConfEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto.base,
  &scc_info_ScoreName_proto_2frecommend_5frank_5fparam_2eproto.base,
  &scc_info_ScoringParam_proto_2frecommend_5frank_5fparam_2eproto.base,
  &scc_info_TFScoringParam_proto_2frecommend_5frank_5fparam_2eproto.base,
  &scc_info_TFScoringParam_FeaSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto.base,
  &scc_info_TFScoringParam_ModelSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto.base,
  &scc_info_TwinTowerScoringParam_proto_2frecommend_5frank_5fparam_2eproto.base,
  &scc_info_TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto.base,
  &scc_info_TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse_proto_2frecommend_5frank_5fparam_2eproto.base,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_proto_2frecommend_5frank_5fparam_2eproto_once;
static bool descriptor_table_proto_2frecommend_5frank_5fparam_2eproto_initialized = false;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2frecommend_5frank_5fparam_2eproto = {
  &descriptor_table_proto_2frecommend_5frank_5fparam_2eproto_initialized, descriptor_table_protodef_proto_2frecommend_5frank_5fparam_2eproto, "proto/recommend_rank_param.proto", 2807,
  &descriptor_table_proto_2frecommend_5frank_5fparam_2eproto_once, descriptor_table_proto_2frecommend_5frank_5fparam_2eproto_sccs, descriptor_table_proto_2frecommend_5frank_5fparam_2eproto_deps, 20, 0,
  schemas, file_default_instances, TableStruct_proto_2frecommend_5frank_5fparam_2eproto::offsets,
  file_level_metadata_proto_2frecommend_5frank_5fparam_2eproto, 20, file_level_enum_descriptors_proto_2frecommend_5frank_5fparam_2eproto, file_level_service_descriptors_proto_2frecommend_5frank_5fparam_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_proto_2frecommend_5frank_5fparam_2eproto = (  ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_proto_2frecommend_5frank_5fparam_2eproto), true);
namespace abc {
namespace recommend_plt {
namespace rank {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TwinTowerScoringParam_FUNCTION_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_proto_2frecommend_5frank_5fparam_2eproto);
  return file_level_enum_descriptors_proto_2frecommend_5frank_5fparam_2eproto[0];
}
bool TwinTowerScoringParam_FUNCTION_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr TwinTowerScoringParam_FUNCTION TwinTowerScoringParam::UNKNOWN;
constexpr TwinTowerScoringParam_FUNCTION TwinTowerScoringParam::COSINE;
constexpr TwinTowerScoringParam_FUNCTION TwinTowerScoringParam::DOTPRODUCT;
constexpr TwinTowerScoringParam_FUNCTION TwinTowerScoringParam::FUNCTION_MIN;
constexpr TwinTowerScoringParam_FUNCTION TwinTowerScoringParam::FUNCTION_MAX;
constexpr int TwinTowerScoringParam::FUNCTION_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)

// ===================================================================

void ScoreName::InitAsDefaultInstance() {
}
class ScoreName::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ScoreName::kModelOutputFieldNumber;
const int ScoreName::kOutputScoreFeatureFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ScoreName::ScoreName()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.rank.ScoreName)
}
ScoreName::ScoreName(const ScoreName& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  model_output_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.model_output().size() > 0) {
    model_output_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.model_output_);
  }
  output_score_feature_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.output_score_feature().size() > 0) {
    output_score_feature_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.output_score_feature_);
  }
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.rank.ScoreName)
}

void ScoreName::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ScoreName_proto_2frecommend_5frank_5fparam_2eproto.base);
  model_output_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  output_score_feature_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

ScoreName::~ScoreName() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.rank.ScoreName)
  SharedDtor();
}

void ScoreName::SharedDtor() {
  model_output_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  output_score_feature_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ScoreName::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ScoreName& ScoreName::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ScoreName_proto_2frecommend_5frank_5fparam_2eproto.base);
  return *internal_default_instance();
}


void ScoreName::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.rank.ScoreName)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  model_output_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  output_score_feature_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* ScoreName::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string model_output = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_model_output(), ptr, ctx, "abc.recommend_plt.rank.ScoreName.model_output");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string output_score_feature = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_output_score_feature(), ptr, ctx, "abc.recommend_plt.rank.ScoreName.output_score_feature");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool ScoreName::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.rank.ScoreName)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string model_output = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_model_output()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->model_output().data(), static_cast<int>(this->model_output().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.ScoreName.model_output"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string output_score_feature = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_output_score_feature()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->output_score_feature().data(), static_cast<int>(this->output_score_feature().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.ScoreName.output_score_feature"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.rank.ScoreName)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.rank.ScoreName)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void ScoreName::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.rank.ScoreName)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string model_output = 1;
  if (this->model_output().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->model_output().data(), static_cast<int>(this->model_output().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.ScoreName.model_output");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->model_output(), output);
  }

  // string output_score_feature = 2;
  if (this->output_score_feature().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->output_score_feature().data(), static_cast<int>(this->output_score_feature().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.ScoreName.output_score_feature");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->output_score_feature(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.rank.ScoreName)
}

::PROTOBUF_NAMESPACE_ID::uint8* ScoreName::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.rank.ScoreName)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string model_output = 1;
  if (this->model_output().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->model_output().data(), static_cast<int>(this->model_output().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.ScoreName.model_output");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        1, this->model_output(), target);
  }

  // string output_score_feature = 2;
  if (this->output_score_feature().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->output_score_feature().data(), static_cast<int>(this->output_score_feature().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.ScoreName.output_score_feature");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        2, this->output_score_feature(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.rank.ScoreName)
  return target;
}

size_t ScoreName::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.rank.ScoreName)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string model_output = 1;
  if (this->model_output().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->model_output());
  }

  // string output_score_feature = 2;
  if (this->output_score_feature().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->output_score_feature());
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ScoreName::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.rank.ScoreName)
  GOOGLE_DCHECK_NE(&from, this);
  const ScoreName* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ScoreName>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.rank.ScoreName)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.rank.ScoreName)
    MergeFrom(*source);
  }
}

void ScoreName::MergeFrom(const ScoreName& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.rank.ScoreName)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.model_output().size() > 0) {

    model_output_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.model_output_);
  }
  if (from.output_score_feature().size() > 0) {

    output_score_feature_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.output_score_feature_);
  }
}

void ScoreName::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.rank.ScoreName)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ScoreName::CopyFrom(const ScoreName& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.rank.ScoreName)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ScoreName::IsInitialized() const {
  return true;
}

void ScoreName::Swap(ScoreName* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ScoreName::InternalSwap(ScoreName* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  model_output_.Swap(&other->model_output_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  output_score_feature_.Swap(&other->output_score_feature_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
}

::PROTOBUF_NAMESPACE_ID::Metadata ScoreName::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse::TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse() {}
TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse::TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse::MergeFrom(const TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse::GetMetadata() const {
  return GetMetadataStatic();
}
void TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse::MergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::Message& other) {
  ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom(other);
}


// ===================================================================

TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse::TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse() {}
TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse::TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse::MergeFrom(const TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse::GetMetadata() const {
  return GetMetadataStatic();
}
void TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse::MergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::Message& other) {
  ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom(other);
}


// ===================================================================

void TwinTowerScoringParam::InitAsDefaultInstance() {
}
class TwinTowerScoringParam::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TwinTowerScoringParam::kUserEmbModelFieldNumber;
const int TwinTowerScoringParam::kUserEmbModelOutputFieldNumber;
const int TwinTowerScoringParam::kItemEmbFeatureGroupFieldNumber;
const int TwinTowerScoringParam::kItemEmbFeatureNameFieldNumber;
const int TwinTowerScoringParam::kVectorSizeFieldNumber;
const int TwinTowerScoringParam::kFunctionFieldNumber;
const int TwinTowerScoringParam::kOutputScoreFeatureFieldNumber;
const int TwinTowerScoringParam::kModelSiteUidMapFieldNumber;
const int TwinTowerScoringParam::kFeaSiteUidMapFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TwinTowerScoringParam::TwinTowerScoringParam()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.rank.TwinTowerScoringParam)
}
TwinTowerScoringParam::TwinTowerScoringParam(const TwinTowerScoringParam& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  model_site_uid_map_.MergeFrom(from.model_site_uid_map_);
  fea_site_uid_map_.MergeFrom(from.fea_site_uid_map_);
  user_emb_model_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.user_emb_model().size() > 0) {
    user_emb_model_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.user_emb_model_);
  }
  user_emb_model_output_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.user_emb_model_output().size() > 0) {
    user_emb_model_output_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.user_emb_model_output_);
  }
  item_emb_feature_group_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.item_emb_feature_group().size() > 0) {
    item_emb_feature_group_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.item_emb_feature_group_);
  }
  item_emb_feature_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.item_emb_feature_name().size() > 0) {
    item_emb_feature_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.item_emb_feature_name_);
  }
  output_score_feature_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.output_score_feature().size() > 0) {
    output_score_feature_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.output_score_feature_);
  }
  ::memcpy(&vector_size_, &from.vector_size_,
    static_cast<size_t>(reinterpret_cast<char*>(&function_) -
    reinterpret_cast<char*>(&vector_size_)) + sizeof(function_));
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.rank.TwinTowerScoringParam)
}

void TwinTowerScoringParam::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TwinTowerScoringParam_proto_2frecommend_5frank_5fparam_2eproto.base);
  user_emb_model_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  user_emb_model_output_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  item_emb_feature_group_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  item_emb_feature_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  output_score_feature_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&vector_size_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&function_) -
      reinterpret_cast<char*>(&vector_size_)) + sizeof(function_));
}

TwinTowerScoringParam::~TwinTowerScoringParam() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.rank.TwinTowerScoringParam)
  SharedDtor();
}

void TwinTowerScoringParam::SharedDtor() {
  user_emb_model_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  user_emb_model_output_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  item_emb_feature_group_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  item_emb_feature_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  output_score_feature_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void TwinTowerScoringParam::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TwinTowerScoringParam& TwinTowerScoringParam::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TwinTowerScoringParam_proto_2frecommend_5frank_5fparam_2eproto.base);
  return *internal_default_instance();
}


void TwinTowerScoringParam::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.rank.TwinTowerScoringParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  model_site_uid_map_.Clear();
  fea_site_uid_map_.Clear();
  user_emb_model_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  user_emb_model_output_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  item_emb_feature_group_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  item_emb_feature_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  output_score_feature_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&vector_size_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&function_) -
      reinterpret_cast<char*>(&vector_size_)) + sizeof(function_));
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* TwinTowerScoringParam::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string user_emb_model = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_user_emb_model(), ptr, ctx, "abc.recommend_plt.rank.TwinTowerScoringParam.user_emb_model");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string user_emb_model_output = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_user_emb_model_output(), ptr, ctx, "abc.recommend_plt.rank.TwinTowerScoringParam.user_emb_model_output");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string item_emb_feature_group = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_item_emb_feature_group(), ptr, ctx, "abc.recommend_plt.rank.TwinTowerScoringParam.item_emb_feature_group");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string item_emb_feature_name = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_item_emb_feature_name(), ptr, ctx, "abc.recommend_plt.rank.TwinTowerScoringParam.item_emb_feature_name");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 vector_size = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          vector_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .abc.recommend_plt.rank.TwinTowerScoringParam.FUNCTION function = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
          set_function(static_cast<::abc::recommend_plt::rank::TwinTowerScoringParam_FUNCTION>(val));
        } else goto handle_unusual;
        continue;
      // string output_score_feature = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_output_score_feature(), ptr, ctx, "abc.recommend_plt.rank.TwinTowerScoringParam.output_score_feature");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // map<string, string> model_site_uid_map = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&model_site_uid_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 66);
        } else goto handle_unusual;
        continue;
      // map<string, string> fea_site_uid_map = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&fea_site_uid_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 74);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool TwinTowerScoringParam::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.rank.TwinTowerScoringParam)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string user_emb_model = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_user_emb_model()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->user_emb_model().data(), static_cast<int>(this->user_emb_model().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.TwinTowerScoringParam.user_emb_model"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string user_emb_model_output = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_user_emb_model_output()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->user_emb_model_output().data(), static_cast<int>(this->user_emb_model_output().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.TwinTowerScoringParam.user_emb_model_output"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string item_emb_feature_group = 3;
      case 3: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (26 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_item_emb_feature_group()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->item_emb_feature_group().data(), static_cast<int>(this->item_emb_feature_group().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.TwinTowerScoringParam.item_emb_feature_group"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string item_emb_feature_name = 4;
      case 4: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (34 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_item_emb_feature_name()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->item_emb_feature_name().data(), static_cast<int>(this->item_emb_feature_name().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.TwinTowerScoringParam.item_emb_feature_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 vector_size = 5;
      case 5: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (40 & 0xFF)) {

          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32>(
                 input, &vector_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .abc.recommend_plt.rank.TwinTowerScoringParam.FUNCTION function = 6;
      case 6: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (48 & 0xFF)) {
          int value = 0;
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   int, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_function(static_cast< ::abc::recommend_plt::rank::TwinTowerScoringParam_FUNCTION >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string output_score_feature = 7;
      case 7: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (58 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_output_score_feature()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->output_score_feature().data(), static_cast<int>(this->output_score_feature().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.TwinTowerScoringParam.output_score_feature"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, string> model_site_uid_map = 8;
      case 8: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (66 & 0xFF)) {
          TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse::Parser< ::PROTOBUF_NAMESPACE_ID::internal::MapField<
              TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse,
              std::string, std::string,
              ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
              ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string > > parser(&model_site_uid_map_);
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.TwinTowerScoringParam.ModelSiteUidMapEntry.key"));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), static_cast<int>(parser.value().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.TwinTowerScoringParam.ModelSiteUidMapEntry.value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, string> fea_site_uid_map = 9;
      case 9: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (74 & 0xFF)) {
          TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse::Parser< ::PROTOBUF_NAMESPACE_ID::internal::MapField<
              TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse,
              std::string, std::string,
              ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
              ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string > > parser(&fea_site_uid_map_);
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.TwinTowerScoringParam.FeaSiteUidMapEntry.key"));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), static_cast<int>(parser.value().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.TwinTowerScoringParam.FeaSiteUidMapEntry.value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.rank.TwinTowerScoringParam)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.rank.TwinTowerScoringParam)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void TwinTowerScoringParam::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.rank.TwinTowerScoringParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string user_emb_model = 1;
  if (this->user_emb_model().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->user_emb_model().data(), static_cast<int>(this->user_emb_model().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.TwinTowerScoringParam.user_emb_model");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->user_emb_model(), output);
  }

  // string user_emb_model_output = 2;
  if (this->user_emb_model_output().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->user_emb_model_output().data(), static_cast<int>(this->user_emb_model_output().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.TwinTowerScoringParam.user_emb_model_output");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->user_emb_model_output(), output);
  }

  // string item_emb_feature_group = 3;
  if (this->item_emb_feature_group().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->item_emb_feature_group().data(), static_cast<int>(this->item_emb_feature_group().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.TwinTowerScoringParam.item_emb_feature_group");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->item_emb_feature_group(), output);
  }

  // string item_emb_feature_name = 4;
  if (this->item_emb_feature_name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->item_emb_feature_name().data(), static_cast<int>(this->item_emb_feature_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.TwinTowerScoringParam.item_emb_feature_name");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->item_emb_feature_name(), output);
  }

  // int32 vector_size = 5;
  if (this->vector_size() != 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32(5, this->vector_size(), output);
  }

  // .abc.recommend_plt.rank.TwinTowerScoringParam.FUNCTION function = 6;
  if (this->function() != 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnum(
      6, this->function(), output);
  }

  // string output_score_feature = 7;
  if (this->output_score_feature().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->output_score_feature().data(), static_cast<int>(this->output_score_feature().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.TwinTowerScoringParam.output_score_feature");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->output_score_feature(), output);
  }

  // map<string, string> model_site_uid_map = 8;
  if (!this->model_site_uid_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.rank.TwinTowerScoringParam.ModelSiteUidMapEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.rank.TwinTowerScoringParam.ModelSiteUidMapEntry.value");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->model_site_uid_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->model_site_uid_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->model_site_uid_map().begin();
          it != this->model_site_uid_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse::MapEntryWrapper entry(nullptr, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second);
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(8, entry, output);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->model_site_uid_map().begin();
          it != this->model_site_uid_map().end(); ++it) {
        TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(8, entry, output);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // map<string, string> fea_site_uid_map = 9;
  if (!this->fea_site_uid_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.rank.TwinTowerScoringParam.FeaSiteUidMapEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.rank.TwinTowerScoringParam.FeaSiteUidMapEntry.value");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->fea_site_uid_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->fea_site_uid_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->fea_site_uid_map().begin();
          it != this->fea_site_uid_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse::MapEntryWrapper entry(nullptr, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second);
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(9, entry, output);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->fea_site_uid_map().begin();
          it != this->fea_site_uid_map().end(); ++it) {
        TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(9, entry, output);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.rank.TwinTowerScoringParam)
}

::PROTOBUF_NAMESPACE_ID::uint8* TwinTowerScoringParam::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.rank.TwinTowerScoringParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string user_emb_model = 1;
  if (this->user_emb_model().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->user_emb_model().data(), static_cast<int>(this->user_emb_model().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.TwinTowerScoringParam.user_emb_model");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        1, this->user_emb_model(), target);
  }

  // string user_emb_model_output = 2;
  if (this->user_emb_model_output().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->user_emb_model_output().data(), static_cast<int>(this->user_emb_model_output().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.TwinTowerScoringParam.user_emb_model_output");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        2, this->user_emb_model_output(), target);
  }

  // string item_emb_feature_group = 3;
  if (this->item_emb_feature_group().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->item_emb_feature_group().data(), static_cast<int>(this->item_emb_feature_group().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.TwinTowerScoringParam.item_emb_feature_group");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        3, this->item_emb_feature_group(), target);
  }

  // string item_emb_feature_name = 4;
  if (this->item_emb_feature_name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->item_emb_feature_name().data(), static_cast<int>(this->item_emb_feature_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.TwinTowerScoringParam.item_emb_feature_name");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        4, this->item_emb_feature_name(), target);
  }

  // int32 vector_size = 5;
  if (this->vector_size() != 0) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(5, this->vector_size(), target);
  }

  // .abc.recommend_plt.rank.TwinTowerScoringParam.FUNCTION function = 6;
  if (this->function() != 0) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      6, this->function(), target);
  }

  // string output_score_feature = 7;
  if (this->output_score_feature().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->output_score_feature().data(), static_cast<int>(this->output_score_feature().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.TwinTowerScoringParam.output_score_feature");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        7, this->output_score_feature(), target);
  }

  // map<string, string> model_site_uid_map = 8;
  if (!this->model_site_uid_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.rank.TwinTowerScoringParam.ModelSiteUidMapEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.rank.TwinTowerScoringParam.ModelSiteUidMapEntry.value");
      }
    };

    if (false &&
        this->model_site_uid_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->model_site_uid_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->model_site_uid_map().begin();
          it != this->model_site_uid_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse::MapEntryWrapper entry(nullptr, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second);
        target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::InternalWriteMessageNoVirtualToArray(8, entry, target);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->model_site_uid_map().begin();
          it != this->model_site_uid_map().end(); ++it) {
        TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
        target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::InternalWriteMessageNoVirtualToArray(8, entry, target);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // map<string, string> fea_site_uid_map = 9;
  if (!this->fea_site_uid_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.rank.TwinTowerScoringParam.FeaSiteUidMapEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.rank.TwinTowerScoringParam.FeaSiteUidMapEntry.value");
      }
    };

    if (false &&
        this->fea_site_uid_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->fea_site_uid_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->fea_site_uid_map().begin();
          it != this->fea_site_uid_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse::MapEntryWrapper entry(nullptr, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second);
        target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::InternalWriteMessageNoVirtualToArray(9, entry, target);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->fea_site_uid_map().begin();
          it != this->fea_site_uid_map().end(); ++it) {
        TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
        target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::InternalWriteMessageNoVirtualToArray(9, entry, target);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.rank.TwinTowerScoringParam)
  return target;
}

size_t TwinTowerScoringParam::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.rank.TwinTowerScoringParam)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, string> model_site_uid_map = 8;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->model_site_uid_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->model_site_uid_map().begin();
      it != this->model_site_uid_map().end(); ++it) {
    TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        MessageSizeNoVirtual(entry);
  }

  // map<string, string> fea_site_uid_map = 9;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->fea_site_uid_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->fea_site_uid_map().begin();
      it != this->fea_site_uid_map().end(); ++it) {
    TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        MessageSizeNoVirtual(entry);
  }

  // string user_emb_model = 1;
  if (this->user_emb_model().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->user_emb_model());
  }

  // string user_emb_model_output = 2;
  if (this->user_emb_model_output().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->user_emb_model_output());
  }

  // string item_emb_feature_group = 3;
  if (this->item_emb_feature_group().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->item_emb_feature_group());
  }

  // string item_emb_feature_name = 4;
  if (this->item_emb_feature_name().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->item_emb_feature_name());
  }

  // string output_score_feature = 7;
  if (this->output_score_feature().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->output_score_feature());
  }

  // int32 vector_size = 5;
  if (this->vector_size() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->vector_size());
  }

  // .abc.recommend_plt.rank.TwinTowerScoringParam.FUNCTION function = 6;
  if (this->function() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->function());
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TwinTowerScoringParam::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.rank.TwinTowerScoringParam)
  GOOGLE_DCHECK_NE(&from, this);
  const TwinTowerScoringParam* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TwinTowerScoringParam>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.rank.TwinTowerScoringParam)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.rank.TwinTowerScoringParam)
    MergeFrom(*source);
  }
}

void TwinTowerScoringParam::MergeFrom(const TwinTowerScoringParam& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.rank.TwinTowerScoringParam)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  model_site_uid_map_.MergeFrom(from.model_site_uid_map_);
  fea_site_uid_map_.MergeFrom(from.fea_site_uid_map_);
  if (from.user_emb_model().size() > 0) {

    user_emb_model_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.user_emb_model_);
  }
  if (from.user_emb_model_output().size() > 0) {

    user_emb_model_output_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.user_emb_model_output_);
  }
  if (from.item_emb_feature_group().size() > 0) {

    item_emb_feature_group_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.item_emb_feature_group_);
  }
  if (from.item_emb_feature_name().size() > 0) {

    item_emb_feature_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.item_emb_feature_name_);
  }
  if (from.output_score_feature().size() > 0) {

    output_score_feature_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.output_score_feature_);
  }
  if (from.vector_size() != 0) {
    set_vector_size(from.vector_size());
  }
  if (from.function() != 0) {
    set_function(from.function());
  }
}

void TwinTowerScoringParam::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.rank.TwinTowerScoringParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TwinTowerScoringParam::CopyFrom(const TwinTowerScoringParam& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.rank.TwinTowerScoringParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TwinTowerScoringParam::IsInitialized() const {
  return true;
}

void TwinTowerScoringParam::Swap(TwinTowerScoringParam* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TwinTowerScoringParam::InternalSwap(TwinTowerScoringParam* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  model_site_uid_map_.Swap(&other->model_site_uid_map_);
  fea_site_uid_map_.Swap(&other->fea_site_uid_map_);
  user_emb_model_.Swap(&other->user_emb_model_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  user_emb_model_output_.Swap(&other->user_emb_model_output_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  item_emb_feature_group_.Swap(&other->item_emb_feature_group_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  item_emb_feature_name_.Swap(&other->item_emb_feature_name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  output_score_feature_.Swap(&other->output_score_feature_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(vector_size_, other->vector_size_);
  swap(function_, other->function_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TwinTowerScoringParam::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void HotScoringParam::InitAsDefaultInstance() {
}
class HotScoringParam::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int HotScoringParam::kFeatureGroupNameFieldNumber;
const int HotScoringParam::kFeatureNameFieldNumber;
const int HotScoringParam::kOutputScoreFeatureFieldNumber;
const int HotScoringParam::kDefaultItemIdFieldNumber;
const int HotScoringParam::kDefaultFeatureGroupNameFieldNumber;
const int HotScoringParam::kDefaultFeatureNameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

HotScoringParam::HotScoringParam()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.rank.HotScoringParam)
}
HotScoringParam::HotScoringParam(const HotScoringParam& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  feature_group_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.feature_group_name().size() > 0) {
    feature_group_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.feature_group_name_);
  }
  feature_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.feature_name().size() > 0) {
    feature_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.feature_name_);
  }
  output_score_feature_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.output_score_feature().size() > 0) {
    output_score_feature_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.output_score_feature_);
  }
  default_item_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.default_item_id().size() > 0) {
    default_item_id_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.default_item_id_);
  }
  default_feature_group_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.default_feature_group_name().size() > 0) {
    default_feature_group_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.default_feature_group_name_);
  }
  default_feature_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.default_feature_name().size() > 0) {
    default_feature_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.default_feature_name_);
  }
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.rank.HotScoringParam)
}

void HotScoringParam::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_HotScoringParam_proto_2frecommend_5frank_5fparam_2eproto.base);
  feature_group_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  feature_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  output_score_feature_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  default_item_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  default_feature_group_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  default_feature_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

HotScoringParam::~HotScoringParam() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.rank.HotScoringParam)
  SharedDtor();
}

void HotScoringParam::SharedDtor() {
  feature_group_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  feature_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  output_score_feature_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  default_item_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  default_feature_group_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  default_feature_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void HotScoringParam::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const HotScoringParam& HotScoringParam::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_HotScoringParam_proto_2frecommend_5frank_5fparam_2eproto.base);
  return *internal_default_instance();
}


void HotScoringParam::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.rank.HotScoringParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  feature_group_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  feature_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  output_score_feature_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  default_item_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  default_feature_group_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  default_feature_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* HotScoringParam::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string feature_group_name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_feature_group_name(), ptr, ctx, "abc.recommend_plt.rank.HotScoringParam.feature_group_name");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string feature_name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_feature_name(), ptr, ctx, "abc.recommend_plt.rank.HotScoringParam.feature_name");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string output_score_feature = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_output_score_feature(), ptr, ctx, "abc.recommend_plt.rank.HotScoringParam.output_score_feature");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string default_item_id = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_default_item_id(), ptr, ctx, "abc.recommend_plt.rank.HotScoringParam.default_item_id");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string default_feature_group_name = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_default_feature_group_name(), ptr, ctx, "abc.recommend_plt.rank.HotScoringParam.default_feature_group_name");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string default_feature_name = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_default_feature_name(), ptr, ctx, "abc.recommend_plt.rank.HotScoringParam.default_feature_name");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool HotScoringParam::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.rank.HotScoringParam)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string feature_group_name = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_feature_group_name()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->feature_group_name().data(), static_cast<int>(this->feature_group_name().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.HotScoringParam.feature_group_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string feature_name = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_feature_name()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->feature_name().data(), static_cast<int>(this->feature_name().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.HotScoringParam.feature_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string output_score_feature = 3;
      case 3: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (26 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_output_score_feature()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->output_score_feature().data(), static_cast<int>(this->output_score_feature().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.HotScoringParam.output_score_feature"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string default_item_id = 4;
      case 4: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (34 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_default_item_id()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->default_item_id().data(), static_cast<int>(this->default_item_id().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.HotScoringParam.default_item_id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string default_feature_group_name = 5;
      case 5: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (42 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_default_feature_group_name()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->default_feature_group_name().data(), static_cast<int>(this->default_feature_group_name().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.HotScoringParam.default_feature_group_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string default_feature_name = 6;
      case 6: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (50 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_default_feature_name()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->default_feature_name().data(), static_cast<int>(this->default_feature_name().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.HotScoringParam.default_feature_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.rank.HotScoringParam)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.rank.HotScoringParam)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void HotScoringParam::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.rank.HotScoringParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string feature_group_name = 1;
  if (this->feature_group_name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->feature_group_name().data(), static_cast<int>(this->feature_group_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.HotScoringParam.feature_group_name");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->feature_group_name(), output);
  }

  // string feature_name = 2;
  if (this->feature_name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->feature_name().data(), static_cast<int>(this->feature_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.HotScoringParam.feature_name");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->feature_name(), output);
  }

  // string output_score_feature = 3;
  if (this->output_score_feature().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->output_score_feature().data(), static_cast<int>(this->output_score_feature().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.HotScoringParam.output_score_feature");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->output_score_feature(), output);
  }

  // string default_item_id = 4;
  if (this->default_item_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->default_item_id().data(), static_cast<int>(this->default_item_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.HotScoringParam.default_item_id");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->default_item_id(), output);
  }

  // string default_feature_group_name = 5;
  if (this->default_feature_group_name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->default_feature_group_name().data(), static_cast<int>(this->default_feature_group_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.HotScoringParam.default_feature_group_name");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->default_feature_group_name(), output);
  }

  // string default_feature_name = 6;
  if (this->default_feature_name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->default_feature_name().data(), static_cast<int>(this->default_feature_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.HotScoringParam.default_feature_name");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->default_feature_name(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.rank.HotScoringParam)
}

::PROTOBUF_NAMESPACE_ID::uint8* HotScoringParam::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.rank.HotScoringParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string feature_group_name = 1;
  if (this->feature_group_name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->feature_group_name().data(), static_cast<int>(this->feature_group_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.HotScoringParam.feature_group_name");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        1, this->feature_group_name(), target);
  }

  // string feature_name = 2;
  if (this->feature_name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->feature_name().data(), static_cast<int>(this->feature_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.HotScoringParam.feature_name");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        2, this->feature_name(), target);
  }

  // string output_score_feature = 3;
  if (this->output_score_feature().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->output_score_feature().data(), static_cast<int>(this->output_score_feature().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.HotScoringParam.output_score_feature");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        3, this->output_score_feature(), target);
  }

  // string default_item_id = 4;
  if (this->default_item_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->default_item_id().data(), static_cast<int>(this->default_item_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.HotScoringParam.default_item_id");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        4, this->default_item_id(), target);
  }

  // string default_feature_group_name = 5;
  if (this->default_feature_group_name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->default_feature_group_name().data(), static_cast<int>(this->default_feature_group_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.HotScoringParam.default_feature_group_name");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        5, this->default_feature_group_name(), target);
  }

  // string default_feature_name = 6;
  if (this->default_feature_name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->default_feature_name().data(), static_cast<int>(this->default_feature_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.HotScoringParam.default_feature_name");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        6, this->default_feature_name(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.rank.HotScoringParam)
  return target;
}

size_t HotScoringParam::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.rank.HotScoringParam)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string feature_group_name = 1;
  if (this->feature_group_name().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->feature_group_name());
  }

  // string feature_name = 2;
  if (this->feature_name().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->feature_name());
  }

  // string output_score_feature = 3;
  if (this->output_score_feature().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->output_score_feature());
  }

  // string default_item_id = 4;
  if (this->default_item_id().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->default_item_id());
  }

  // string default_feature_group_name = 5;
  if (this->default_feature_group_name().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->default_feature_group_name());
  }

  // string default_feature_name = 6;
  if (this->default_feature_name().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->default_feature_name());
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void HotScoringParam::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.rank.HotScoringParam)
  GOOGLE_DCHECK_NE(&from, this);
  const HotScoringParam* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<HotScoringParam>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.rank.HotScoringParam)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.rank.HotScoringParam)
    MergeFrom(*source);
  }
}

void HotScoringParam::MergeFrom(const HotScoringParam& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.rank.HotScoringParam)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.feature_group_name().size() > 0) {

    feature_group_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.feature_group_name_);
  }
  if (from.feature_name().size() > 0) {

    feature_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.feature_name_);
  }
  if (from.output_score_feature().size() > 0) {

    output_score_feature_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.output_score_feature_);
  }
  if (from.default_item_id().size() > 0) {

    default_item_id_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.default_item_id_);
  }
  if (from.default_feature_group_name().size() > 0) {

    default_feature_group_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.default_feature_group_name_);
  }
  if (from.default_feature_name().size() > 0) {

    default_feature_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.default_feature_name_);
  }
}

void HotScoringParam::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.rank.HotScoringParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void HotScoringParam::CopyFrom(const HotScoringParam& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.rank.HotScoringParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HotScoringParam::IsInitialized() const {
  return true;
}

void HotScoringParam::Swap(HotScoringParam* other) {
  if (other == this) return;
  InternalSwap(other);
}
void HotScoringParam::InternalSwap(HotScoringParam* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  feature_group_name_.Swap(&other->feature_group_name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  feature_name_.Swap(&other->feature_name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  output_score_feature_.Swap(&other->output_score_feature_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  default_item_id_.Swap(&other->default_item_id_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  default_feature_group_name_.Swap(&other->default_feature_group_name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  default_feature_name_.Swap(&other->default_feature_name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
}

::PROTOBUF_NAMESPACE_ID::Metadata HotScoringParam::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

TFScoringParam_ModelSiteUidMapEntry_DoNotUse::TFScoringParam_ModelSiteUidMapEntry_DoNotUse() {}
TFScoringParam_ModelSiteUidMapEntry_DoNotUse::TFScoringParam_ModelSiteUidMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TFScoringParam_ModelSiteUidMapEntry_DoNotUse::MergeFrom(const TFScoringParam_ModelSiteUidMapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TFScoringParam_ModelSiteUidMapEntry_DoNotUse::GetMetadata() const {
  return GetMetadataStatic();
}
void TFScoringParam_ModelSiteUidMapEntry_DoNotUse::MergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::Message& other) {
  ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom(other);
}


// ===================================================================

TFScoringParam_FeaSiteUidMapEntry_DoNotUse::TFScoringParam_FeaSiteUidMapEntry_DoNotUse() {}
TFScoringParam_FeaSiteUidMapEntry_DoNotUse::TFScoringParam_FeaSiteUidMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TFScoringParam_FeaSiteUidMapEntry_DoNotUse::MergeFrom(const TFScoringParam_FeaSiteUidMapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TFScoringParam_FeaSiteUidMapEntry_DoNotUse::GetMetadata() const {
  return GetMetadataStatic();
}
void TFScoringParam_FeaSiteUidMapEntry_DoNotUse::MergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::Message& other) {
  ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom(other);
}


// ===================================================================

void TFScoringParam::InitAsDefaultInstance() {
}
class TFScoringParam::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TFScoringParam::kModelIdFieldNumber;
const int TFScoringParam::kModelSiteUidFieldNumber;
const int TFScoringParam::kFeaSiteUidFieldNumber;
const int TFScoringParam::kScoreNameFieldNumber;
const int TFScoringParam::kModelSiteUidMapFieldNumber;
const int TFScoringParam::kFeaSiteUidMapFieldNumber;
const int TFScoringParam::kPredictTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TFScoringParam::TFScoringParam()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.rank.TFScoringParam)
}
TFScoringParam::TFScoringParam(const TFScoringParam& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      score_name_(from.score_name_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  model_site_uid_map_.MergeFrom(from.model_site_uid_map_);
  fea_site_uid_map_.MergeFrom(from.fea_site_uid_map_);
  model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.model_id().size() > 0) {
    model_id_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.model_id_);
  }
  model_site_uid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.model_site_uid().size() > 0) {
    model_site_uid_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.model_site_uid_);
  }
  fea_site_uid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.fea_site_uid().size() > 0) {
    fea_site_uid_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.fea_site_uid_);
  }
  predict_type_ = from.predict_type_;
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.rank.TFScoringParam)
}

void TFScoringParam::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TFScoringParam_proto_2frecommend_5frank_5fparam_2eproto.base);
  model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_site_uid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  fea_site_uid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  predict_type_ = 0;
}

TFScoringParam::~TFScoringParam() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.rank.TFScoringParam)
  SharedDtor();
}

void TFScoringParam::SharedDtor() {
  model_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_site_uid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  fea_site_uid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void TFScoringParam::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TFScoringParam& TFScoringParam::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TFScoringParam_proto_2frecommend_5frank_5fparam_2eproto.base);
  return *internal_default_instance();
}


void TFScoringParam::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.rank.TFScoringParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  score_name_.Clear();
  model_site_uid_map_.Clear();
  fea_site_uid_map_.Clear();
  model_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_site_uid_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  fea_site_uid_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  predict_type_ = 0;
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* TFScoringParam::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string model_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_model_id(), ptr, ctx, "abc.recommend_plt.rank.TFScoringParam.model_id");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string model_site_uid = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_model_site_uid(), ptr, ctx, "abc.recommend_plt.rank.TFScoringParam.model_site_uid");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string fea_site_uid = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_fea_site_uid(), ptr, ctx, "abc.recommend_plt.rank.TFScoringParam.fea_site_uid");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .abc.recommend_plt.rank.ScoreName score_name = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(add_score_name(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 34);
        } else goto handle_unusual;
        continue;
      // map<string, string> model_site_uid_map = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&model_site_uid_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 42);
        } else goto handle_unusual;
        continue;
      // map<string, string> fea_site_uid_map = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&fea_site_uid_map_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 50);
        } else goto handle_unusual;
        continue;
      // int32 predict_type = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          predict_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool TFScoringParam::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.rank.TFScoringParam)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string model_id = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_model_id()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->model_id().data(), static_cast<int>(this->model_id().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.TFScoringParam.model_id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string model_site_uid = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_model_site_uid()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->model_site_uid().data(), static_cast<int>(this->model_site_uid().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.TFScoringParam.model_site_uid"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string fea_site_uid = 3;
      case 3: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (26 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_fea_site_uid()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->fea_site_uid().data(), static_cast<int>(this->fea_site_uid().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.TFScoringParam.fea_site_uid"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .abc.recommend_plt.rank.ScoreName score_name = 4;
      case 4: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (34 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
                input, add_score_name()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, string> model_site_uid_map = 5;
      case 5: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (42 & 0xFF)) {
          TFScoringParam_ModelSiteUidMapEntry_DoNotUse::Parser< ::PROTOBUF_NAMESPACE_ID::internal::MapField<
              TFScoringParam_ModelSiteUidMapEntry_DoNotUse,
              std::string, std::string,
              ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
              ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string > > parser(&model_site_uid_map_);
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.TFScoringParam.ModelSiteUidMapEntry.key"));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), static_cast<int>(parser.value().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.TFScoringParam.ModelSiteUidMapEntry.value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, string> fea_site_uid_map = 6;
      case 6: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (50 & 0xFF)) {
          TFScoringParam_FeaSiteUidMapEntry_DoNotUse::Parser< ::PROTOBUF_NAMESPACE_ID::internal::MapField<
              TFScoringParam_FeaSiteUidMapEntry_DoNotUse,
              std::string, std::string,
              ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
              ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string > > parser(&fea_site_uid_map_);
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.TFScoringParam.FeaSiteUidMapEntry.key"));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), static_cast<int>(parser.value().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.TFScoringParam.FeaSiteUidMapEntry.value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 predict_type = 7;
      case 7: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (56 & 0xFF)) {

          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32>(
                 input, &predict_type_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.rank.TFScoringParam)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.rank.TFScoringParam)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void TFScoringParam::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.rank.TFScoringParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string model_id = 1;
  if (this->model_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->model_id().data(), static_cast<int>(this->model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.TFScoringParam.model_id");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->model_id(), output);
  }

  // string model_site_uid = 2;
  if (this->model_site_uid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->model_site_uid().data(), static_cast<int>(this->model_site_uid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.TFScoringParam.model_site_uid");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->model_site_uid(), output);
  }

  // string fea_site_uid = 3;
  if (this->fea_site_uid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->fea_site_uid().data(), static_cast<int>(this->fea_site_uid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.TFScoringParam.fea_site_uid");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->fea_site_uid(), output);
  }

  // repeated .abc.recommend_plt.rank.ScoreName score_name = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->score_name_size()); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      4,
      this->score_name(static_cast<int>(i)),
      output);
  }

  // map<string, string> model_site_uid_map = 5;
  if (!this->model_site_uid_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.rank.TFScoringParam.ModelSiteUidMapEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.rank.TFScoringParam.ModelSiteUidMapEntry.value");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->model_site_uid_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->model_site_uid_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->model_site_uid_map().begin();
          it != this->model_site_uid_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        TFScoringParam_ModelSiteUidMapEntry_DoNotUse::MapEntryWrapper entry(nullptr, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second);
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(5, entry, output);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->model_site_uid_map().begin();
          it != this->model_site_uid_map().end(); ++it) {
        TFScoringParam_ModelSiteUidMapEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(5, entry, output);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // map<string, string> fea_site_uid_map = 6;
  if (!this->fea_site_uid_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.rank.TFScoringParam.FeaSiteUidMapEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.rank.TFScoringParam.FeaSiteUidMapEntry.value");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->fea_site_uid_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->fea_site_uid_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->fea_site_uid_map().begin();
          it != this->fea_site_uid_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        TFScoringParam_FeaSiteUidMapEntry_DoNotUse::MapEntryWrapper entry(nullptr, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second);
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(6, entry, output);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->fea_site_uid_map().begin();
          it != this->fea_site_uid_map().end(); ++it) {
        TFScoringParam_FeaSiteUidMapEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(6, entry, output);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // int32 predict_type = 7;
  if (this->predict_type() != 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32(7, this->predict_type(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.rank.TFScoringParam)
}

::PROTOBUF_NAMESPACE_ID::uint8* TFScoringParam::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.rank.TFScoringParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string model_id = 1;
  if (this->model_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->model_id().data(), static_cast<int>(this->model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.TFScoringParam.model_id");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        1, this->model_id(), target);
  }

  // string model_site_uid = 2;
  if (this->model_site_uid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->model_site_uid().data(), static_cast<int>(this->model_site_uid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.TFScoringParam.model_site_uid");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        2, this->model_site_uid(), target);
  }

  // string fea_site_uid = 3;
  if (this->fea_site_uid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->fea_site_uid().data(), static_cast<int>(this->fea_site_uid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.TFScoringParam.fea_site_uid");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        3, this->fea_site_uid(), target);
  }

  // repeated .abc.recommend_plt.rank.ScoreName score_name = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->score_name_size()); i < n; i++) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->score_name(static_cast<int>(i)), target);
  }

  // map<string, string> model_site_uid_map = 5;
  if (!this->model_site_uid_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.rank.TFScoringParam.ModelSiteUidMapEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.rank.TFScoringParam.ModelSiteUidMapEntry.value");
      }
    };

    if (false &&
        this->model_site_uid_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->model_site_uid_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->model_site_uid_map().begin();
          it != this->model_site_uid_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        TFScoringParam_ModelSiteUidMapEntry_DoNotUse::MapEntryWrapper entry(nullptr, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second);
        target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::InternalWriteMessageNoVirtualToArray(5, entry, target);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->model_site_uid_map().begin();
          it != this->model_site_uid_map().end(); ++it) {
        TFScoringParam_ModelSiteUidMapEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
        target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::InternalWriteMessageNoVirtualToArray(5, entry, target);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // map<string, string> fea_site_uid_map = 6;
  if (!this->fea_site_uid_map().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.rank.TFScoringParam.FeaSiteUidMapEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.rank.TFScoringParam.FeaSiteUidMapEntry.value");
      }
    };

    if (false &&
        this->fea_site_uid_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->fea_site_uid_map().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->fea_site_uid_map().begin();
          it != this->fea_site_uid_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        TFScoringParam_FeaSiteUidMapEntry_DoNotUse::MapEntryWrapper entry(nullptr, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second);
        target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::InternalWriteMessageNoVirtualToArray(6, entry, target);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->fea_site_uid_map().begin();
          it != this->fea_site_uid_map().end(); ++it) {
        TFScoringParam_FeaSiteUidMapEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
        target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::InternalWriteMessageNoVirtualToArray(6, entry, target);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // int32 predict_type = 7;
  if (this->predict_type() != 0) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(7, this->predict_type(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.rank.TFScoringParam)
  return target;
}

size_t TFScoringParam::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.rank.TFScoringParam)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.rank.ScoreName score_name = 4;
  {
    unsigned int count = static_cast<unsigned int>(this->score_name_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          this->score_name(static_cast<int>(i)));
    }
  }

  // map<string, string> model_site_uid_map = 5;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->model_site_uid_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->model_site_uid_map().begin();
      it != this->model_site_uid_map().end(); ++it) {
    TFScoringParam_ModelSiteUidMapEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        MessageSizeNoVirtual(entry);
  }

  // map<string, string> fea_site_uid_map = 6;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->fea_site_uid_map_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->fea_site_uid_map().begin();
      it != this->fea_site_uid_map().end(); ++it) {
    TFScoringParam_FeaSiteUidMapEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        MessageSizeNoVirtual(entry);
  }

  // string model_id = 1;
  if (this->model_id().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->model_id());
  }

  // string model_site_uid = 2;
  if (this->model_site_uid().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->model_site_uid());
  }

  // string fea_site_uid = 3;
  if (this->fea_site_uid().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->fea_site_uid());
  }

  // int32 predict_type = 7;
  if (this->predict_type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->predict_type());
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TFScoringParam::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.rank.TFScoringParam)
  GOOGLE_DCHECK_NE(&from, this);
  const TFScoringParam* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TFScoringParam>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.rank.TFScoringParam)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.rank.TFScoringParam)
    MergeFrom(*source);
  }
}

void TFScoringParam::MergeFrom(const TFScoringParam& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.rank.TFScoringParam)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  score_name_.MergeFrom(from.score_name_);
  model_site_uid_map_.MergeFrom(from.model_site_uid_map_);
  fea_site_uid_map_.MergeFrom(from.fea_site_uid_map_);
  if (from.model_id().size() > 0) {

    model_id_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.model_id_);
  }
  if (from.model_site_uid().size() > 0) {

    model_site_uid_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.model_site_uid_);
  }
  if (from.fea_site_uid().size() > 0) {

    fea_site_uid_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.fea_site_uid_);
  }
  if (from.predict_type() != 0) {
    set_predict_type(from.predict_type());
  }
}

void TFScoringParam::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.rank.TFScoringParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TFScoringParam::CopyFrom(const TFScoringParam& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.rank.TFScoringParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TFScoringParam::IsInitialized() const {
  return true;
}

void TFScoringParam::Swap(TFScoringParam* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TFScoringParam::InternalSwap(TFScoringParam* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  CastToBase(&score_name_)->InternalSwap(CastToBase(&other->score_name_));
  model_site_uid_map_.Swap(&other->model_site_uid_map_);
  fea_site_uid_map_.Swap(&other->fea_site_uid_map_);
  model_id_.Swap(&other->model_id_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  model_site_uid_.Swap(&other->model_site_uid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  fea_site_uid_.Swap(&other->fea_site_uid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(predict_type_, other->predict_type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TFScoringParam::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void DslScoringParam::InitAsDefaultInstance() {
}
class DslScoringParam::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DslScoringParam::kDslFeatureFieldNumber;
const int DslScoringParam::kStepFeatureFieldNumber;
const int DslScoringParam::kOutputScoreFeatureFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DslScoringParam::DslScoringParam()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.rank.DslScoringParam)
}
DslScoringParam::DslScoringParam(const DslScoringParam& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      step_feature_(from.step_feature_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  dsl_feature_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.dsl_feature().size() > 0) {
    dsl_feature_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.dsl_feature_);
  }
  output_score_feature_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.output_score_feature().size() > 0) {
    output_score_feature_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.output_score_feature_);
  }
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.rank.DslScoringParam)
}

void DslScoringParam::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_DslScoringParam_proto_2frecommend_5frank_5fparam_2eproto.base);
  dsl_feature_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  output_score_feature_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

DslScoringParam::~DslScoringParam() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.rank.DslScoringParam)
  SharedDtor();
}

void DslScoringParam::SharedDtor() {
  dsl_feature_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  output_score_feature_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DslScoringParam::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const DslScoringParam& DslScoringParam::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_DslScoringParam_proto_2frecommend_5frank_5fparam_2eproto.base);
  return *internal_default_instance();
}


void DslScoringParam::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.rank.DslScoringParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  step_feature_.Clear();
  dsl_feature_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  output_score_feature_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* DslScoringParam::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string dsl_feature = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_dsl_feature(), ptr, ctx, "abc.recommend_plt.rank.DslScoringParam.dsl_feature");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated string step_feature = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(add_step_feature(), ptr, ctx, "abc.recommend_plt.rank.DslScoringParam.step_feature");
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 18);
        } else goto handle_unusual;
        continue;
      // string output_score_feature = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_output_score_feature(), ptr, ctx, "abc.recommend_plt.rank.DslScoringParam.output_score_feature");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool DslScoringParam::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.rank.DslScoringParam)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string dsl_feature = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_dsl_feature()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->dsl_feature().data(), static_cast<int>(this->dsl_feature().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.DslScoringParam.dsl_feature"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string step_feature = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->add_step_feature()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->step_feature(this->step_feature_size() - 1).data(),
            static_cast<int>(this->step_feature(this->step_feature_size() - 1).length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.DslScoringParam.step_feature"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string output_score_feature = 3;
      case 3: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (26 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_output_score_feature()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->output_score_feature().data(), static_cast<int>(this->output_score_feature().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.DslScoringParam.output_score_feature"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.rank.DslScoringParam)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.rank.DslScoringParam)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void DslScoringParam::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.rank.DslScoringParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string dsl_feature = 1;
  if (this->dsl_feature().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->dsl_feature().data(), static_cast<int>(this->dsl_feature().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.DslScoringParam.dsl_feature");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->dsl_feature(), output);
  }

  // repeated string step_feature = 2;
  for (int i = 0, n = this->step_feature_size(); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->step_feature(i).data(), static_cast<int>(this->step_feature(i).length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.DslScoringParam.step_feature");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteString(
      2, this->step_feature(i), output);
  }

  // string output_score_feature = 3;
  if (this->output_score_feature().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->output_score_feature().data(), static_cast<int>(this->output_score_feature().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.DslScoringParam.output_score_feature");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->output_score_feature(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.rank.DslScoringParam)
}

::PROTOBUF_NAMESPACE_ID::uint8* DslScoringParam::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.rank.DslScoringParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string dsl_feature = 1;
  if (this->dsl_feature().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->dsl_feature().data(), static_cast<int>(this->dsl_feature().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.DslScoringParam.dsl_feature");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        1, this->dsl_feature(), target);
  }

  // repeated string step_feature = 2;
  for (int i = 0, n = this->step_feature_size(); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->step_feature(i).data(), static_cast<int>(this->step_feature(i).length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.DslScoringParam.step_feature");
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      WriteStringToArray(2, this->step_feature(i), target);
  }

  // string output_score_feature = 3;
  if (this->output_score_feature().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->output_score_feature().data(), static_cast<int>(this->output_score_feature().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.DslScoringParam.output_score_feature");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        3, this->output_score_feature(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.rank.DslScoringParam)
  return target;
}

size_t DslScoringParam::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.rank.DslScoringParam)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string step_feature = 2;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->step_feature_size());
  for (int i = 0, n = this->step_feature_size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      this->step_feature(i));
  }

  // string dsl_feature = 1;
  if (this->dsl_feature().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->dsl_feature());
  }

  // string output_score_feature = 3;
  if (this->output_score_feature().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->output_score_feature());
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DslScoringParam::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.rank.DslScoringParam)
  GOOGLE_DCHECK_NE(&from, this);
  const DslScoringParam* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<DslScoringParam>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.rank.DslScoringParam)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.rank.DslScoringParam)
    MergeFrom(*source);
  }
}

void DslScoringParam::MergeFrom(const DslScoringParam& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.rank.DslScoringParam)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  step_feature_.MergeFrom(from.step_feature_);
  if (from.dsl_feature().size() > 0) {

    dsl_feature_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.dsl_feature_);
  }
  if (from.output_score_feature().size() > 0) {

    output_score_feature_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.output_score_feature_);
  }
}

void DslScoringParam::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.rank.DslScoringParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DslScoringParam::CopyFrom(const DslScoringParam& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.rank.DslScoringParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DslScoringParam::IsInitialized() const {
  return true;
}

void DslScoringParam::Swap(DslScoringParam* other) {
  if (other == this) return;
  InternalSwap(other);
}
void DslScoringParam::InternalSwap(DslScoringParam* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  step_feature_.InternalSwap(CastToBase(&other->step_feature_));
  dsl_feature_.Swap(&other->dsl_feature_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  output_score_feature_.Swap(&other->output_score_feature_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
}

::PROTOBUF_NAMESPACE_ID::Metadata DslScoringParam::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void ScoringParam::InitAsDefaultInstance() {
  ::abc::recommend_plt::rank::_ScoringParam_default_instance_._instance.get_mutable()->twin_tower_scoring_param_ = const_cast< ::abc::recommend_plt::rank::TwinTowerScoringParam*>(
      ::abc::recommend_plt::rank::TwinTowerScoringParam::internal_default_instance());
  ::abc::recommend_plt::rank::_ScoringParam_default_instance_._instance.get_mutable()->hot_scoring_param_ = const_cast< ::abc::recommend_plt::rank::HotScoringParam*>(
      ::abc::recommend_plt::rank::HotScoringParam::internal_default_instance());
  ::abc::recommend_plt::rank::_ScoringParam_default_instance_._instance.get_mutable()->tf_scoring_param_ = const_cast< ::abc::recommend_plt::rank::TFScoringParam*>(
      ::abc::recommend_plt::rank::TFScoringParam::internal_default_instance());
  ::abc::recommend_plt::rank::_ScoringParam_default_instance_._instance.get_mutable()->dsl_scoring_param_ = const_cast< ::abc::recommend_plt::rank::DslScoringParam*>(
      ::abc::recommend_plt::rank::DslScoringParam::internal_default_instance());
}
class ScoringParam::HasBitSetters {
 public:
  static const ::abc::recommend_plt::rank::TwinTowerScoringParam& twin_tower_scoring_param(const ScoringParam* msg);
  static const ::abc::recommend_plt::rank::HotScoringParam& hot_scoring_param(const ScoringParam* msg);
  static const ::abc::recommend_plt::rank::TFScoringParam& tf_scoring_param(const ScoringParam* msg);
  static const ::abc::recommend_plt::rank::DslScoringParam& dsl_scoring_param(const ScoringParam* msg);
};

const ::abc::recommend_plt::rank::TwinTowerScoringParam&
ScoringParam::HasBitSetters::twin_tower_scoring_param(const ScoringParam* msg) {
  return *msg->twin_tower_scoring_param_;
}
const ::abc::recommend_plt::rank::HotScoringParam&
ScoringParam::HasBitSetters::hot_scoring_param(const ScoringParam* msg) {
  return *msg->hot_scoring_param_;
}
const ::abc::recommend_plt::rank::TFScoringParam&
ScoringParam::HasBitSetters::tf_scoring_param(const ScoringParam* msg) {
  return *msg->tf_scoring_param_;
}
const ::abc::recommend_plt::rank::DslScoringParam&
ScoringParam::HasBitSetters::dsl_scoring_param(const ScoringParam* msg) {
  return *msg->dsl_scoring_param_;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ScoringParam::kTwinTowerScoringParamFieldNumber;
const int ScoringParam::kHotScoringParamFieldNumber;
const int ScoringParam::kTfScoringParamFieldNumber;
const int ScoringParam::kDslScoringParamFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ScoringParam::ScoringParam()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.rank.ScoringParam)
}
ScoringParam::ScoringParam(const ScoringParam& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_twin_tower_scoring_param()) {
    twin_tower_scoring_param_ = new ::abc::recommend_plt::rank::TwinTowerScoringParam(*from.twin_tower_scoring_param_);
  } else {
    twin_tower_scoring_param_ = nullptr;
  }
  if (from.has_hot_scoring_param()) {
    hot_scoring_param_ = new ::abc::recommend_plt::rank::HotScoringParam(*from.hot_scoring_param_);
  } else {
    hot_scoring_param_ = nullptr;
  }
  if (from.has_tf_scoring_param()) {
    tf_scoring_param_ = new ::abc::recommend_plt::rank::TFScoringParam(*from.tf_scoring_param_);
  } else {
    tf_scoring_param_ = nullptr;
  }
  if (from.has_dsl_scoring_param()) {
    dsl_scoring_param_ = new ::abc::recommend_plt::rank::DslScoringParam(*from.dsl_scoring_param_);
  } else {
    dsl_scoring_param_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.rank.ScoringParam)
}

void ScoringParam::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ScoringParam_proto_2frecommend_5frank_5fparam_2eproto.base);
  ::memset(&twin_tower_scoring_param_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&dsl_scoring_param_) -
      reinterpret_cast<char*>(&twin_tower_scoring_param_)) + sizeof(dsl_scoring_param_));
}

ScoringParam::~ScoringParam() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.rank.ScoringParam)
  SharedDtor();
}

void ScoringParam::SharedDtor() {
  if (this != internal_default_instance()) delete twin_tower_scoring_param_;
  if (this != internal_default_instance()) delete hot_scoring_param_;
  if (this != internal_default_instance()) delete tf_scoring_param_;
  if (this != internal_default_instance()) delete dsl_scoring_param_;
}

void ScoringParam::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ScoringParam& ScoringParam::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ScoringParam_proto_2frecommend_5frank_5fparam_2eproto.base);
  return *internal_default_instance();
}


void ScoringParam::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.rank.ScoringParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == nullptr && twin_tower_scoring_param_ != nullptr) {
    delete twin_tower_scoring_param_;
  }
  twin_tower_scoring_param_ = nullptr;
  if (GetArenaNoVirtual() == nullptr && hot_scoring_param_ != nullptr) {
    delete hot_scoring_param_;
  }
  hot_scoring_param_ = nullptr;
  if (GetArenaNoVirtual() == nullptr && tf_scoring_param_ != nullptr) {
    delete tf_scoring_param_;
  }
  tf_scoring_param_ = nullptr;
  if (GetArenaNoVirtual() == nullptr && dsl_scoring_param_ != nullptr) {
    delete dsl_scoring_param_;
  }
  dsl_scoring_param_ = nullptr;
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* ScoringParam::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .abc.recommend_plt.rank.TwinTowerScoringParam twin_tower_scoring_param = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(mutable_twin_tower_scoring_param(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .abc.recommend_plt.rank.HotScoringParam hot_scoring_param = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(mutable_hot_scoring_param(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .abc.recommend_plt.rank.TFScoringParam tf_scoring_param = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(mutable_tf_scoring_param(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .abc.recommend_plt.rank.DslScoringParam dsl_scoring_param = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(mutable_dsl_scoring_param(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool ScoringParam::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.rank.ScoringParam)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .abc.recommend_plt.rank.TwinTowerScoringParam twin_tower_scoring_param = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_twin_tower_scoring_param()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .abc.recommend_plt.rank.HotScoringParam hot_scoring_param = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_hot_scoring_param()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .abc.recommend_plt.rank.TFScoringParam tf_scoring_param = 3;
      case 3: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (26 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_tf_scoring_param()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .abc.recommend_plt.rank.DslScoringParam dsl_scoring_param = 4;
      case 4: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (34 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_dsl_scoring_param()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.rank.ScoringParam)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.rank.ScoringParam)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void ScoringParam::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.rank.ScoringParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .abc.recommend_plt.rank.TwinTowerScoringParam twin_tower_scoring_param = 1;
  if (this->has_twin_tower_scoring_param()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, HasBitSetters::twin_tower_scoring_param(this), output);
  }

  // .abc.recommend_plt.rank.HotScoringParam hot_scoring_param = 2;
  if (this->has_hot_scoring_param()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, HasBitSetters::hot_scoring_param(this), output);
  }

  // .abc.recommend_plt.rank.TFScoringParam tf_scoring_param = 3;
  if (this->has_tf_scoring_param()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, HasBitSetters::tf_scoring_param(this), output);
  }

  // .abc.recommend_plt.rank.DslScoringParam dsl_scoring_param = 4;
  if (this->has_dsl_scoring_param()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, HasBitSetters::dsl_scoring_param(this), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.rank.ScoringParam)
}

::PROTOBUF_NAMESPACE_ID::uint8* ScoringParam::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.rank.ScoringParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .abc.recommend_plt.rank.TwinTowerScoringParam twin_tower_scoring_param = 1;
  if (this->has_twin_tower_scoring_param()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, HasBitSetters::twin_tower_scoring_param(this), target);
  }

  // .abc.recommend_plt.rank.HotScoringParam hot_scoring_param = 2;
  if (this->has_hot_scoring_param()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, HasBitSetters::hot_scoring_param(this), target);
  }

  // .abc.recommend_plt.rank.TFScoringParam tf_scoring_param = 3;
  if (this->has_tf_scoring_param()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, HasBitSetters::tf_scoring_param(this), target);
  }

  // .abc.recommend_plt.rank.DslScoringParam dsl_scoring_param = 4;
  if (this->has_dsl_scoring_param()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, HasBitSetters::dsl_scoring_param(this), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.rank.ScoringParam)
  return target;
}

size_t ScoringParam::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.rank.ScoringParam)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .abc.recommend_plt.rank.TwinTowerScoringParam twin_tower_scoring_param = 1;
  if (this->has_twin_tower_scoring_param()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *twin_tower_scoring_param_);
  }

  // .abc.recommend_plt.rank.HotScoringParam hot_scoring_param = 2;
  if (this->has_hot_scoring_param()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *hot_scoring_param_);
  }

  // .abc.recommend_plt.rank.TFScoringParam tf_scoring_param = 3;
  if (this->has_tf_scoring_param()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *tf_scoring_param_);
  }

  // .abc.recommend_plt.rank.DslScoringParam dsl_scoring_param = 4;
  if (this->has_dsl_scoring_param()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *dsl_scoring_param_);
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ScoringParam::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.rank.ScoringParam)
  GOOGLE_DCHECK_NE(&from, this);
  const ScoringParam* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ScoringParam>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.rank.ScoringParam)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.rank.ScoringParam)
    MergeFrom(*source);
  }
}

void ScoringParam::MergeFrom(const ScoringParam& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.rank.ScoringParam)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_twin_tower_scoring_param()) {
    mutable_twin_tower_scoring_param()->::abc::recommend_plt::rank::TwinTowerScoringParam::MergeFrom(from.twin_tower_scoring_param());
  }
  if (from.has_hot_scoring_param()) {
    mutable_hot_scoring_param()->::abc::recommend_plt::rank::HotScoringParam::MergeFrom(from.hot_scoring_param());
  }
  if (from.has_tf_scoring_param()) {
    mutable_tf_scoring_param()->::abc::recommend_plt::rank::TFScoringParam::MergeFrom(from.tf_scoring_param());
  }
  if (from.has_dsl_scoring_param()) {
    mutable_dsl_scoring_param()->::abc::recommend_plt::rank::DslScoringParam::MergeFrom(from.dsl_scoring_param());
  }
}

void ScoringParam::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.rank.ScoringParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ScoringParam::CopyFrom(const ScoringParam& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.rank.ScoringParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ScoringParam::IsInitialized() const {
  return true;
}

void ScoringParam::Swap(ScoringParam* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ScoringParam::InternalSwap(ScoringParam* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(twin_tower_scoring_param_, other->twin_tower_scoring_param_);
  swap(hot_scoring_param_, other->hot_scoring_param_);
  swap(tf_scoring_param_, other->tf_scoring_param_);
  swap(dsl_scoring_param_, other->dsl_scoring_param_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ScoringParam::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void CrossMixParam_Factor::InitAsDefaultInstance() {
}
class CrossMixParam_Factor::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CrossMixParam_Factor::kScoreFeatureFieldNumber;
const int CrossMixParam_Factor::kCntFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CrossMixParam_Factor::CrossMixParam_Factor()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.rank.CrossMixParam.Factor)
}
CrossMixParam_Factor::CrossMixParam_Factor(const CrossMixParam_Factor& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  score_feature_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.score_feature().size() > 0) {
    score_feature_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.score_feature_);
  }
  cnt_ = from.cnt_;
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.rank.CrossMixParam.Factor)
}

void CrossMixParam_Factor::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_CrossMixParam_Factor_proto_2frecommend_5frank_5fparam_2eproto.base);
  score_feature_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  cnt_ = 0;
}

CrossMixParam_Factor::~CrossMixParam_Factor() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.rank.CrossMixParam.Factor)
  SharedDtor();
}

void CrossMixParam_Factor::SharedDtor() {
  score_feature_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CrossMixParam_Factor::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const CrossMixParam_Factor& CrossMixParam_Factor::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_CrossMixParam_Factor_proto_2frecommend_5frank_5fparam_2eproto.base);
  return *internal_default_instance();
}


void CrossMixParam_Factor::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.rank.CrossMixParam.Factor)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  score_feature_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  cnt_ = 0;
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* CrossMixParam_Factor::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string score_feature = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_score_feature(), ptr, ctx, "abc.recommend_plt.rank.CrossMixParam.Factor.score_feature");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 cnt = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          cnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool CrossMixParam_Factor::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.rank.CrossMixParam.Factor)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string score_feature = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_score_feature()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->score_feature().data(), static_cast<int>(this->score_feature().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.CrossMixParam.Factor.score_feature"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 cnt = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (16 & 0xFF)) {

          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32>(
                 input, &cnt_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.rank.CrossMixParam.Factor)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.rank.CrossMixParam.Factor)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void CrossMixParam_Factor::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.rank.CrossMixParam.Factor)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string score_feature = 1;
  if (this->score_feature().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->score_feature().data(), static_cast<int>(this->score_feature().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.CrossMixParam.Factor.score_feature");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->score_feature(), output);
  }

  // int32 cnt = 2;
  if (this->cnt() != 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32(2, this->cnt(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.rank.CrossMixParam.Factor)
}

::PROTOBUF_NAMESPACE_ID::uint8* CrossMixParam_Factor::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.rank.CrossMixParam.Factor)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string score_feature = 1;
  if (this->score_feature().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->score_feature().data(), static_cast<int>(this->score_feature().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.CrossMixParam.Factor.score_feature");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        1, this->score_feature(), target);
  }

  // int32 cnt = 2;
  if (this->cnt() != 0) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->cnt(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.rank.CrossMixParam.Factor)
  return target;
}

size_t CrossMixParam_Factor::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.rank.CrossMixParam.Factor)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string score_feature = 1;
  if (this->score_feature().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->score_feature());
  }

  // int32 cnt = 2;
  if (this->cnt() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->cnt());
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CrossMixParam_Factor::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.rank.CrossMixParam.Factor)
  GOOGLE_DCHECK_NE(&from, this);
  const CrossMixParam_Factor* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<CrossMixParam_Factor>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.rank.CrossMixParam.Factor)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.rank.CrossMixParam.Factor)
    MergeFrom(*source);
  }
}

void CrossMixParam_Factor::MergeFrom(const CrossMixParam_Factor& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.rank.CrossMixParam.Factor)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.score_feature().size() > 0) {

    score_feature_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.score_feature_);
  }
  if (from.cnt() != 0) {
    set_cnt(from.cnt());
  }
}

void CrossMixParam_Factor::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.rank.CrossMixParam.Factor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CrossMixParam_Factor::CopyFrom(const CrossMixParam_Factor& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.rank.CrossMixParam.Factor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CrossMixParam_Factor::IsInitialized() const {
  return true;
}

void CrossMixParam_Factor::Swap(CrossMixParam_Factor* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CrossMixParam_Factor::InternalSwap(CrossMixParam_Factor* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  score_feature_.Swap(&other->score_feature_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(cnt_, other->cnt_);
}

::PROTOBUF_NAMESPACE_ID::Metadata CrossMixParam_Factor::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void CrossMixParam::InitAsDefaultInstance() {
}
class CrossMixParam::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CrossMixParam::kFactorFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CrossMixParam::CrossMixParam()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.rank.CrossMixParam)
}
CrossMixParam::CrossMixParam(const CrossMixParam& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      factor_(from.factor_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.rank.CrossMixParam)
}

void CrossMixParam::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_CrossMixParam_proto_2frecommend_5frank_5fparam_2eproto.base);
}

CrossMixParam::~CrossMixParam() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.rank.CrossMixParam)
  SharedDtor();
}

void CrossMixParam::SharedDtor() {
}

void CrossMixParam::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const CrossMixParam& CrossMixParam::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_CrossMixParam_proto_2frecommend_5frank_5fparam_2eproto.base);
  return *internal_default_instance();
}


void CrossMixParam::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.rank.CrossMixParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  factor_.Clear();
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* CrossMixParam::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .abc.recommend_plt.rank.CrossMixParam.Factor factor = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(add_factor(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 10);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool CrossMixParam::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.rank.CrossMixParam)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .abc.recommend_plt.rank.CrossMixParam.Factor factor = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
                input, add_factor()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.rank.CrossMixParam)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.rank.CrossMixParam)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void CrossMixParam::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.rank.CrossMixParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.rank.CrossMixParam.Factor factor = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->factor_size()); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->factor(static_cast<int>(i)),
      output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.rank.CrossMixParam)
}

::PROTOBUF_NAMESPACE_ID::uint8* CrossMixParam::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.rank.CrossMixParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.rank.CrossMixParam.Factor factor = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->factor_size()); i < n; i++) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->factor(static_cast<int>(i)), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.rank.CrossMixParam)
  return target;
}

size_t CrossMixParam::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.rank.CrossMixParam)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.rank.CrossMixParam.Factor factor = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->factor_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          this->factor(static_cast<int>(i)));
    }
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CrossMixParam::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.rank.CrossMixParam)
  GOOGLE_DCHECK_NE(&from, this);
  const CrossMixParam* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<CrossMixParam>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.rank.CrossMixParam)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.rank.CrossMixParam)
    MergeFrom(*source);
  }
}

void CrossMixParam::MergeFrom(const CrossMixParam& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.rank.CrossMixParam)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  factor_.MergeFrom(from.factor_);
}

void CrossMixParam::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.rank.CrossMixParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CrossMixParam::CopyFrom(const CrossMixParam& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.rank.CrossMixParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CrossMixParam::IsInitialized() const {
  return true;
}

void CrossMixParam::Swap(CrossMixParam* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CrossMixParam::InternalSwap(CrossMixParam* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  CastToBase(&factor_)->InternalSwap(CastToBase(&other->factor_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CrossMixParam::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void DslMixParam::InitAsDefaultInstance() {
}
class DslMixParam::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DslMixParam::kMixScoreFeatureFieldNumber;
const int DslMixParam::kStepFeatureFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DslMixParam::DslMixParam()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.rank.DslMixParam)
}
DslMixParam::DslMixParam(const DslMixParam& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      step_feature_(from.step_feature_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  mix_score_feature_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.mix_score_feature().size() > 0) {
    mix_score_feature_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.mix_score_feature_);
  }
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.rank.DslMixParam)
}

void DslMixParam::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_DslMixParam_proto_2frecommend_5frank_5fparam_2eproto.base);
  mix_score_feature_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

DslMixParam::~DslMixParam() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.rank.DslMixParam)
  SharedDtor();
}

void DslMixParam::SharedDtor() {
  mix_score_feature_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DslMixParam::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const DslMixParam& DslMixParam::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_DslMixParam_proto_2frecommend_5frank_5fparam_2eproto.base);
  return *internal_default_instance();
}


void DslMixParam::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.rank.DslMixParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  step_feature_.Clear();
  mix_score_feature_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* DslMixParam::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string mix_score_feature = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_mix_score_feature(), ptr, ctx, "abc.recommend_plt.rank.DslMixParam.mix_score_feature");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated string step_feature = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(add_step_feature(), ptr, ctx, "abc.recommend_plt.rank.DslMixParam.step_feature");
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 18);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool DslMixParam::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.rank.DslMixParam)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string mix_score_feature = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_mix_score_feature()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->mix_score_feature().data(), static_cast<int>(this->mix_score_feature().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.DslMixParam.mix_score_feature"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string step_feature = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->add_step_feature()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->step_feature(this->step_feature_size() - 1).data(),
            static_cast<int>(this->step_feature(this->step_feature_size() - 1).length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.DslMixParam.step_feature"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.rank.DslMixParam)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.rank.DslMixParam)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void DslMixParam::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.rank.DslMixParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string mix_score_feature = 1;
  if (this->mix_score_feature().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->mix_score_feature().data(), static_cast<int>(this->mix_score_feature().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.DslMixParam.mix_score_feature");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->mix_score_feature(), output);
  }

  // repeated string step_feature = 2;
  for (int i = 0, n = this->step_feature_size(); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->step_feature(i).data(), static_cast<int>(this->step_feature(i).length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.DslMixParam.step_feature");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteString(
      2, this->step_feature(i), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.rank.DslMixParam)
}

::PROTOBUF_NAMESPACE_ID::uint8* DslMixParam::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.rank.DslMixParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string mix_score_feature = 1;
  if (this->mix_score_feature().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->mix_score_feature().data(), static_cast<int>(this->mix_score_feature().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.DslMixParam.mix_score_feature");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        1, this->mix_score_feature(), target);
  }

  // repeated string step_feature = 2;
  for (int i = 0, n = this->step_feature_size(); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->step_feature(i).data(), static_cast<int>(this->step_feature(i).length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.DslMixParam.step_feature");
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      WriteStringToArray(2, this->step_feature(i), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.rank.DslMixParam)
  return target;
}

size_t DslMixParam::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.rank.DslMixParam)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string step_feature = 2;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->step_feature_size());
  for (int i = 0, n = this->step_feature_size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      this->step_feature(i));
  }

  // string mix_score_feature = 1;
  if (this->mix_score_feature().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->mix_score_feature());
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DslMixParam::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.rank.DslMixParam)
  GOOGLE_DCHECK_NE(&from, this);
  const DslMixParam* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<DslMixParam>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.rank.DslMixParam)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.rank.DslMixParam)
    MergeFrom(*source);
  }
}

void DslMixParam::MergeFrom(const DslMixParam& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.rank.DslMixParam)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  step_feature_.MergeFrom(from.step_feature_);
  if (from.mix_score_feature().size() > 0) {

    mix_score_feature_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.mix_score_feature_);
  }
}

void DslMixParam::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.rank.DslMixParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DslMixParam::CopyFrom(const DslMixParam& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.rank.DslMixParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DslMixParam::IsInitialized() const {
  return true;
}

void DslMixParam::Swap(DslMixParam* other) {
  if (other == this) return;
  InternalSwap(other);
}
void DslMixParam::InternalSwap(DslMixParam* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  step_feature_.InternalSwap(CastToBase(&other->step_feature_));
  mix_score_feature_.Swap(&other->mix_score_feature_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
}

::PROTOBUF_NAMESPACE_ID::Metadata DslMixParam::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void MixParam::InitAsDefaultInstance() {
  ::abc::recommend_plt::rank::_MixParam_default_instance_._instance.get_mutable()->cross_mix_param_ = const_cast< ::abc::recommend_plt::rank::CrossMixParam*>(
      ::abc::recommend_plt::rank::CrossMixParam::internal_default_instance());
  ::abc::recommend_plt::rank::_MixParam_default_instance_._instance.get_mutable()->dsl_mix_param_ = const_cast< ::abc::recommend_plt::rank::DslMixParam*>(
      ::abc::recommend_plt::rank::DslMixParam::internal_default_instance());
}
class MixParam::HasBitSetters {
 public:
  static const ::abc::recommend_plt::rank::CrossMixParam& cross_mix_param(const MixParam* msg);
  static const ::abc::recommend_plt::rank::DslMixParam& dsl_mix_param(const MixParam* msg);
};

const ::abc::recommend_plt::rank::CrossMixParam&
MixParam::HasBitSetters::cross_mix_param(const MixParam* msg) {
  return *msg->cross_mix_param_;
}
const ::abc::recommend_plt::rank::DslMixParam&
MixParam::HasBitSetters::dsl_mix_param(const MixParam* msg) {
  return *msg->dsl_mix_param_;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MixParam::kCrossMixParamFieldNumber;
const int MixParam::kDslMixParamFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MixParam::MixParam()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.rank.MixParam)
}
MixParam::MixParam(const MixParam& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_cross_mix_param()) {
    cross_mix_param_ = new ::abc::recommend_plt::rank::CrossMixParam(*from.cross_mix_param_);
  } else {
    cross_mix_param_ = nullptr;
  }
  if (from.has_dsl_mix_param()) {
    dsl_mix_param_ = new ::abc::recommend_plt::rank::DslMixParam(*from.dsl_mix_param_);
  } else {
    dsl_mix_param_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.rank.MixParam)
}

void MixParam::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_MixParam_proto_2frecommend_5frank_5fparam_2eproto.base);
  ::memset(&cross_mix_param_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&dsl_mix_param_) -
      reinterpret_cast<char*>(&cross_mix_param_)) + sizeof(dsl_mix_param_));
}

MixParam::~MixParam() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.rank.MixParam)
  SharedDtor();
}

void MixParam::SharedDtor() {
  if (this != internal_default_instance()) delete cross_mix_param_;
  if (this != internal_default_instance()) delete dsl_mix_param_;
}

void MixParam::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const MixParam& MixParam::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_MixParam_proto_2frecommend_5frank_5fparam_2eproto.base);
  return *internal_default_instance();
}


void MixParam::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.rank.MixParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == nullptr && cross_mix_param_ != nullptr) {
    delete cross_mix_param_;
  }
  cross_mix_param_ = nullptr;
  if (GetArenaNoVirtual() == nullptr && dsl_mix_param_ != nullptr) {
    delete dsl_mix_param_;
  }
  dsl_mix_param_ = nullptr;
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* MixParam::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .abc.recommend_plt.rank.CrossMixParam cross_mix_param = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(mutable_cross_mix_param(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .abc.recommend_plt.rank.DslMixParam dsl_mix_param = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(mutable_dsl_mix_param(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool MixParam::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.rank.MixParam)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .abc.recommend_plt.rank.CrossMixParam cross_mix_param = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_cross_mix_param()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .abc.recommend_plt.rank.DslMixParam dsl_mix_param = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_dsl_mix_param()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.rank.MixParam)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.rank.MixParam)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void MixParam::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.rank.MixParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .abc.recommend_plt.rank.CrossMixParam cross_mix_param = 1;
  if (this->has_cross_mix_param()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, HasBitSetters::cross_mix_param(this), output);
  }

  // .abc.recommend_plt.rank.DslMixParam dsl_mix_param = 2;
  if (this->has_dsl_mix_param()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, HasBitSetters::dsl_mix_param(this), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.rank.MixParam)
}

::PROTOBUF_NAMESPACE_ID::uint8* MixParam::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.rank.MixParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .abc.recommend_plt.rank.CrossMixParam cross_mix_param = 1;
  if (this->has_cross_mix_param()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, HasBitSetters::cross_mix_param(this), target);
  }

  // .abc.recommend_plt.rank.DslMixParam dsl_mix_param = 2;
  if (this->has_dsl_mix_param()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, HasBitSetters::dsl_mix_param(this), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.rank.MixParam)
  return target;
}

size_t MixParam::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.rank.MixParam)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .abc.recommend_plt.rank.CrossMixParam cross_mix_param = 1;
  if (this->has_cross_mix_param()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *cross_mix_param_);
  }

  // .abc.recommend_plt.rank.DslMixParam dsl_mix_param = 2;
  if (this->has_dsl_mix_param()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *dsl_mix_param_);
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MixParam::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.rank.MixParam)
  GOOGLE_DCHECK_NE(&from, this);
  const MixParam* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<MixParam>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.rank.MixParam)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.rank.MixParam)
    MergeFrom(*source);
  }
}

void MixParam::MergeFrom(const MixParam& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.rank.MixParam)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_cross_mix_param()) {
    mutable_cross_mix_param()->::abc::recommend_plt::rank::CrossMixParam::MergeFrom(from.cross_mix_param());
  }
  if (from.has_dsl_mix_param()) {
    mutable_dsl_mix_param()->::abc::recommend_plt::rank::DslMixParam::MergeFrom(from.dsl_mix_param());
  }
}

void MixParam::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.rank.MixParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MixParam::CopyFrom(const MixParam& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.rank.MixParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MixParam::IsInitialized() const {
  return true;
}

void MixParam::Swap(MixParam* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MixParam::InternalSwap(MixParam* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(cross_mix_param_, other->cross_mix_param_);
  swap(dsl_mix_param_, other->dsl_mix_param_);
}

::PROTOBUF_NAMESPACE_ID::Metadata MixParam::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void ItemFeatureDiversityParam_Factor::InitAsDefaultInstance() {
}
class ItemFeatureDiversityParam_Factor::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ItemFeatureDiversityParam_Factor::kFeatureValFieldNumber;
const int ItemFeatureDiversityParam_Factor::kCntFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ItemFeatureDiversityParam_Factor::ItemFeatureDiversityParam_Factor()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor)
}
ItemFeatureDiversityParam_Factor::ItemFeatureDiversityParam_Factor(const ItemFeatureDiversityParam_Factor& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&feature_val_, &from.feature_val_,
    static_cast<size_t>(reinterpret_cast<char*>(&cnt_) -
    reinterpret_cast<char*>(&feature_val_)) + sizeof(cnt_));
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor)
}

void ItemFeatureDiversityParam_Factor::SharedCtor() {
  ::memset(&feature_val_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&cnt_) -
      reinterpret_cast<char*>(&feature_val_)) + sizeof(cnt_));
}

ItemFeatureDiversityParam_Factor::~ItemFeatureDiversityParam_Factor() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor)
  SharedDtor();
}

void ItemFeatureDiversityParam_Factor::SharedDtor() {
}

void ItemFeatureDiversityParam_Factor::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ItemFeatureDiversityParam_Factor& ItemFeatureDiversityParam_Factor::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ItemFeatureDiversityParam_Factor_proto_2frecommend_5frank_5fparam_2eproto.base);
  return *internal_default_instance();
}


void ItemFeatureDiversityParam_Factor::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&feature_val_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&cnt_) -
      reinterpret_cast<char*>(&feature_val_)) + sizeof(cnt_));
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* ItemFeatureDiversityParam_Factor::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // int32 feature_val = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          feature_val_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 cnt = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          cnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool ItemFeatureDiversityParam_Factor::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 feature_val = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (8 & 0xFF)) {

          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32>(
                 input, &feature_val_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 cnt = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (16 & 0xFF)) {

          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32>(
                 input, &cnt_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void ItemFeatureDiversityParam_Factor::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 feature_val = 1;
  if (this->feature_val() != 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32(1, this->feature_val(), output);
  }

  // int32 cnt = 2;
  if (this->cnt() != 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32(2, this->cnt(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor)
}

::PROTOBUF_NAMESPACE_ID::uint8* ItemFeatureDiversityParam_Factor::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 feature_val = 1;
  if (this->feature_val() != 0) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->feature_val(), target);
  }

  // int32 cnt = 2;
  if (this->cnt() != 0) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->cnt(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor)
  return target;
}

size_t ItemFeatureDiversityParam_Factor::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 feature_val = 1;
  if (this->feature_val() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->feature_val());
  }

  // int32 cnt = 2;
  if (this->cnt() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->cnt());
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ItemFeatureDiversityParam_Factor::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor)
  GOOGLE_DCHECK_NE(&from, this);
  const ItemFeatureDiversityParam_Factor* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ItemFeatureDiversityParam_Factor>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor)
    MergeFrom(*source);
  }
}

void ItemFeatureDiversityParam_Factor::MergeFrom(const ItemFeatureDiversityParam_Factor& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.feature_val() != 0) {
    set_feature_val(from.feature_val());
  }
  if (from.cnt() != 0) {
    set_cnt(from.cnt());
  }
}

void ItemFeatureDiversityParam_Factor::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ItemFeatureDiversityParam_Factor::CopyFrom(const ItemFeatureDiversityParam_Factor& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ItemFeatureDiversityParam_Factor::IsInitialized() const {
  return true;
}

void ItemFeatureDiversityParam_Factor::Swap(ItemFeatureDiversityParam_Factor* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ItemFeatureDiversityParam_Factor::InternalSwap(ItemFeatureDiversityParam_Factor* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(feature_val_, other->feature_val_);
  swap(cnt_, other->cnt_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ItemFeatureDiversityParam_Factor::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void ItemFeatureDiversityParam::InitAsDefaultInstance() {
}
class ItemFeatureDiversityParam::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ItemFeatureDiversityParam::kFeatureFromDslFieldNumber;
const int ItemFeatureDiversityParam::kFactorFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ItemFeatureDiversityParam::ItemFeatureDiversityParam()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.rank.ItemFeatureDiversityParam)
}
ItemFeatureDiversityParam::ItemFeatureDiversityParam(const ItemFeatureDiversityParam& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      factor_(from.factor_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  feature_from_dsl_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.feature_from_dsl().size() > 0) {
    feature_from_dsl_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.feature_from_dsl_);
  }
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.rank.ItemFeatureDiversityParam)
}

void ItemFeatureDiversityParam::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ItemFeatureDiversityParam_proto_2frecommend_5frank_5fparam_2eproto.base);
  feature_from_dsl_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

ItemFeatureDiversityParam::~ItemFeatureDiversityParam() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.rank.ItemFeatureDiversityParam)
  SharedDtor();
}

void ItemFeatureDiversityParam::SharedDtor() {
  feature_from_dsl_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ItemFeatureDiversityParam::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ItemFeatureDiversityParam& ItemFeatureDiversityParam::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ItemFeatureDiversityParam_proto_2frecommend_5frank_5fparam_2eproto.base);
  return *internal_default_instance();
}


void ItemFeatureDiversityParam::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.rank.ItemFeatureDiversityParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  factor_.Clear();
  feature_from_dsl_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* ItemFeatureDiversityParam::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string feature_from_dsl = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_feature_from_dsl(), ptr, ctx, "abc.recommend_plt.rank.ItemFeatureDiversityParam.feature_from_dsl");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor factor = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(add_factor(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 18);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool ItemFeatureDiversityParam::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.rank.ItemFeatureDiversityParam)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string feature_from_dsl = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_feature_from_dsl()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->feature_from_dsl().data(), static_cast<int>(this->feature_from_dsl().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.ItemFeatureDiversityParam.feature_from_dsl"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor factor = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
                input, add_factor()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.rank.ItemFeatureDiversityParam)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.rank.ItemFeatureDiversityParam)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void ItemFeatureDiversityParam::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.rank.ItemFeatureDiversityParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string feature_from_dsl = 1;
  if (this->feature_from_dsl().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->feature_from_dsl().data(), static_cast<int>(this->feature_from_dsl().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.ItemFeatureDiversityParam.feature_from_dsl");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->feature_from_dsl(), output);
  }

  // repeated .abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor factor = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->factor_size()); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      2,
      this->factor(static_cast<int>(i)),
      output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.rank.ItemFeatureDiversityParam)
}

::PROTOBUF_NAMESPACE_ID::uint8* ItemFeatureDiversityParam::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.rank.ItemFeatureDiversityParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string feature_from_dsl = 1;
  if (this->feature_from_dsl().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->feature_from_dsl().data(), static_cast<int>(this->feature_from_dsl().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.ItemFeatureDiversityParam.feature_from_dsl");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        1, this->feature_from_dsl(), target);
  }

  // repeated .abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor factor = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->factor_size()); i < n; i++) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->factor(static_cast<int>(i)), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.rank.ItemFeatureDiversityParam)
  return target;
}

size_t ItemFeatureDiversityParam::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.rank.ItemFeatureDiversityParam)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.rank.ItemFeatureDiversityParam.Factor factor = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->factor_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          this->factor(static_cast<int>(i)));
    }
  }

  // string feature_from_dsl = 1;
  if (this->feature_from_dsl().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->feature_from_dsl());
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ItemFeatureDiversityParam::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.rank.ItemFeatureDiversityParam)
  GOOGLE_DCHECK_NE(&from, this);
  const ItemFeatureDiversityParam* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ItemFeatureDiversityParam>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.rank.ItemFeatureDiversityParam)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.rank.ItemFeatureDiversityParam)
    MergeFrom(*source);
  }
}

void ItemFeatureDiversityParam::MergeFrom(const ItemFeatureDiversityParam& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.rank.ItemFeatureDiversityParam)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  factor_.MergeFrom(from.factor_);
  if (from.feature_from_dsl().size() > 0) {

    feature_from_dsl_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.feature_from_dsl_);
  }
}

void ItemFeatureDiversityParam::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.rank.ItemFeatureDiversityParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ItemFeatureDiversityParam::CopyFrom(const ItemFeatureDiversityParam& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.rank.ItemFeatureDiversityParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ItemFeatureDiversityParam::IsInitialized() const {
  return true;
}

void ItemFeatureDiversityParam::Swap(ItemFeatureDiversityParam* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ItemFeatureDiversityParam::InternalSwap(ItemFeatureDiversityParam* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  CastToBase(&factor_)->InternalSwap(CastToBase(&other->factor_));
  feature_from_dsl_.Swap(&other->feature_from_dsl_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
}

::PROTOBUF_NAMESPACE_ID::Metadata ItemFeatureDiversityParam::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void DiversityParam::InitAsDefaultInstance() {
  ::abc::recommend_plt::rank::_DiversityParam_default_instance_._instance.get_mutable()->item_feature_diversity_param_ = const_cast< ::abc::recommend_plt::rank::ItemFeatureDiversityParam*>(
      ::abc::recommend_plt::rank::ItemFeatureDiversityParam::internal_default_instance());
}
class DiversityParam::HasBitSetters {
 public:
  static const ::abc::recommend_plt::rank::ItemFeatureDiversityParam& item_feature_diversity_param(const DiversityParam* msg);
};

const ::abc::recommend_plt::rank::ItemFeatureDiversityParam&
DiversityParam::HasBitSetters::item_feature_diversity_param(const DiversityParam* msg) {
  return *msg->item_feature_diversity_param_;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DiversityParam::kItemFeatureDiversityParamFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DiversityParam::DiversityParam()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.rank.DiversityParam)
}
DiversityParam::DiversityParam(const DiversityParam& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_item_feature_diversity_param()) {
    item_feature_diversity_param_ = new ::abc::recommend_plt::rank::ItemFeatureDiversityParam(*from.item_feature_diversity_param_);
  } else {
    item_feature_diversity_param_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.rank.DiversityParam)
}

void DiversityParam::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_DiversityParam_proto_2frecommend_5frank_5fparam_2eproto.base);
  item_feature_diversity_param_ = nullptr;
}

DiversityParam::~DiversityParam() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.rank.DiversityParam)
  SharedDtor();
}

void DiversityParam::SharedDtor() {
  if (this != internal_default_instance()) delete item_feature_diversity_param_;
}

void DiversityParam::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const DiversityParam& DiversityParam::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_DiversityParam_proto_2frecommend_5frank_5fparam_2eproto.base);
  return *internal_default_instance();
}


void DiversityParam::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.rank.DiversityParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == nullptr && item_feature_diversity_param_ != nullptr) {
    delete item_feature_diversity_param_;
  }
  item_feature_diversity_param_ = nullptr;
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* DiversityParam::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .abc.recommend_plt.rank.ItemFeatureDiversityParam item_feature_diversity_param = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(mutable_item_feature_diversity_param(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool DiversityParam::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.rank.DiversityParam)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .abc.recommend_plt.rank.ItemFeatureDiversityParam item_feature_diversity_param = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_item_feature_diversity_param()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.rank.DiversityParam)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.rank.DiversityParam)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void DiversityParam::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.rank.DiversityParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .abc.recommend_plt.rank.ItemFeatureDiversityParam item_feature_diversity_param = 1;
  if (this->has_item_feature_diversity_param()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, HasBitSetters::item_feature_diversity_param(this), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.rank.DiversityParam)
}

::PROTOBUF_NAMESPACE_ID::uint8* DiversityParam::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.rank.DiversityParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .abc.recommend_plt.rank.ItemFeatureDiversityParam item_feature_diversity_param = 1;
  if (this->has_item_feature_diversity_param()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, HasBitSetters::item_feature_diversity_param(this), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.rank.DiversityParam)
  return target;
}

size_t DiversityParam::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.rank.DiversityParam)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .abc.recommend_plt.rank.ItemFeatureDiversityParam item_feature_diversity_param = 1;
  if (this->has_item_feature_diversity_param()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *item_feature_diversity_param_);
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DiversityParam::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.rank.DiversityParam)
  GOOGLE_DCHECK_NE(&from, this);
  const DiversityParam* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<DiversityParam>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.rank.DiversityParam)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.rank.DiversityParam)
    MergeFrom(*source);
  }
}

void DiversityParam::MergeFrom(const DiversityParam& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.rank.DiversityParam)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_item_feature_diversity_param()) {
    mutable_item_feature_diversity_param()->::abc::recommend_plt::rank::ItemFeatureDiversityParam::MergeFrom(from.item_feature_diversity_param());
  }
}

void DiversityParam::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.rank.DiversityParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DiversityParam::CopyFrom(const DiversityParam& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.rank.DiversityParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DiversityParam::IsInitialized() const {
  return true;
}

void DiversityParam::Swap(DiversityParam* other) {
  if (other == this) return;
  InternalSwap(other);
}
void DiversityParam::InternalSwap(DiversityParam* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(item_feature_diversity_param_, other->item_feature_diversity_param_);
}

::PROTOBUF_NAMESPACE_ID::Metadata DiversityParam::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void RankStrategy::InitAsDefaultInstance() {
  ::abc::recommend_plt::rank::_RankStrategy_default_instance_._instance.get_mutable()->mix_param_ = const_cast< ::abc::recommend_plt::rank::MixParam*>(
      ::abc::recommend_plt::rank::MixParam::internal_default_instance());
  ::abc::recommend_plt::rank::_RankStrategy_default_instance_._instance.get_mutable()->diversity_param_ = const_cast< ::abc::recommend_plt::rank::DiversityParam*>(
      ::abc::recommend_plt::rank::DiversityParam::internal_default_instance());
}
class RankStrategy::HasBitSetters {
 public:
  static const ::abc::recommend_plt::rank::MixParam& mix_param(const RankStrategy* msg);
  static const ::abc::recommend_plt::rank::DiversityParam& diversity_param(const RankStrategy* msg);
};

const ::abc::recommend_plt::rank::MixParam&
RankStrategy::HasBitSetters::mix_param(const RankStrategy* msg) {
  return *msg->mix_param_;
}
const ::abc::recommend_plt::rank::DiversityParam&
RankStrategy::HasBitSetters::diversity_param(const RankStrategy* msg) {
  return *msg->diversity_param_;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RankStrategy::kScoringParamFieldNumber;
const int RankStrategy::kMixParamFieldNumber;
const int RankStrategy::kDiversityParamFieldNumber;
const int RankStrategy::kNameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RankStrategy::RankStrategy()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.rank.RankStrategy)
}
RankStrategy::RankStrategy(const RankStrategy& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      scoring_param_(from.scoring_param_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.has_mix_param()) {
    mix_param_ = new ::abc::recommend_plt::rank::MixParam(*from.mix_param_);
  } else {
    mix_param_ = nullptr;
  }
  if (from.has_diversity_param()) {
    diversity_param_ = new ::abc::recommend_plt::rank::DiversityParam(*from.diversity_param_);
  } else {
    diversity_param_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.rank.RankStrategy)
}

void RankStrategy::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RankStrategy_proto_2frecommend_5frank_5fparam_2eproto.base);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&mix_param_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&diversity_param_) -
      reinterpret_cast<char*>(&mix_param_)) + sizeof(diversity_param_));
}

RankStrategy::~RankStrategy() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.rank.RankStrategy)
  SharedDtor();
}

void RankStrategy::SharedDtor() {
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete mix_param_;
  if (this != internal_default_instance()) delete diversity_param_;
}

void RankStrategy::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RankStrategy& RankStrategy::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RankStrategy_proto_2frecommend_5frank_5fparam_2eproto.base);
  return *internal_default_instance();
}


void RankStrategy::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.rank.RankStrategy)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  scoring_param_.Clear();
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == nullptr && mix_param_ != nullptr) {
    delete mix_param_;
  }
  mix_param_ = nullptr;
  if (GetArenaNoVirtual() == nullptr && diversity_param_ != nullptr) {
    delete diversity_param_;
  }
  diversity_param_ = nullptr;
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* RankStrategy::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .abc.recommend_plt.rank.ScoringParam scoring_param = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(add_scoring_param(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 10);
        } else goto handle_unusual;
        continue;
      // .abc.recommend_plt.rank.MixParam mix_param = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(mutable_mix_param(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .abc.recommend_plt.rank.DiversityParam diversity_param = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(mutable_diversity_param(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string name = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_name(), ptr, ctx, "abc.recommend_plt.rank.RankStrategy.name");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool RankStrategy::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.rank.RankStrategy)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .abc.recommend_plt.rank.ScoringParam scoring_param = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
                input, add_scoring_param()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .abc.recommend_plt.rank.MixParam mix_param = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_mix_param()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .abc.recommend_plt.rank.DiversityParam diversity_param = 3;
      case 3: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (26 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_diversity_param()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string name = 4;
      case 4: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (34 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.RankStrategy.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.rank.RankStrategy)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.rank.RankStrategy)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void RankStrategy::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.rank.RankStrategy)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.rank.ScoringParam scoring_param = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->scoring_param_size()); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->scoring_param(static_cast<int>(i)),
      output);
  }

  // .abc.recommend_plt.rank.MixParam mix_param = 2;
  if (this->has_mix_param()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, HasBitSetters::mix_param(this), output);
  }

  // .abc.recommend_plt.rank.DiversityParam diversity_param = 3;
  if (this->has_diversity_param()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, HasBitSetters::diversity_param(this), output);
  }

  // string name = 4;
  if (this->name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.RankStrategy.name");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->name(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.rank.RankStrategy)
}

::PROTOBUF_NAMESPACE_ID::uint8* RankStrategy::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.rank.RankStrategy)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.rank.ScoringParam scoring_param = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->scoring_param_size()); i < n; i++) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->scoring_param(static_cast<int>(i)), target);
  }

  // .abc.recommend_plt.rank.MixParam mix_param = 2;
  if (this->has_mix_param()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, HasBitSetters::mix_param(this), target);
  }

  // .abc.recommend_plt.rank.DiversityParam diversity_param = 3;
  if (this->has_diversity_param()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, HasBitSetters::diversity_param(this), target);
  }

  // string name = 4;
  if (this->name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.rank.RankStrategy.name");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        4, this->name(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.rank.RankStrategy)
  return target;
}

size_t RankStrategy::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.rank.RankStrategy)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.rank.ScoringParam scoring_param = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->scoring_param_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          this->scoring_param(static_cast<int>(i)));
    }
  }

  // string name = 4;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->name());
  }

  // .abc.recommend_plt.rank.MixParam mix_param = 2;
  if (this->has_mix_param()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *mix_param_);
  }

  // .abc.recommend_plt.rank.DiversityParam diversity_param = 3;
  if (this->has_diversity_param()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *diversity_param_);
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RankStrategy::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.rank.RankStrategy)
  GOOGLE_DCHECK_NE(&from, this);
  const RankStrategy* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RankStrategy>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.rank.RankStrategy)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.rank.RankStrategy)
    MergeFrom(*source);
  }
}

void RankStrategy::MergeFrom(const RankStrategy& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.rank.RankStrategy)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  scoring_param_.MergeFrom(from.scoring_param_);
  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.has_mix_param()) {
    mutable_mix_param()->::abc::recommend_plt::rank::MixParam::MergeFrom(from.mix_param());
  }
  if (from.has_diversity_param()) {
    mutable_diversity_param()->::abc::recommend_plt::rank::DiversityParam::MergeFrom(from.diversity_param());
  }
}

void RankStrategy::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.rank.RankStrategy)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RankStrategy::CopyFrom(const RankStrategy& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.rank.RankStrategy)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RankStrategy::IsInitialized() const {
  return true;
}

void RankStrategy::Swap(RankStrategy* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RankStrategy::InternalSwap(RankStrategy* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  CastToBase(&scoring_param_)->InternalSwap(CastToBase(&other->scoring_param_));
  name_.Swap(&other->name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(mix_param_, other->mix_param_);
  swap(diversity_param_, other->diversity_param_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RankStrategy::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

RankStrategyConfig_RankStrategyConfEntry_DoNotUse::RankStrategyConfig_RankStrategyConfEntry_DoNotUse() {}
RankStrategyConfig_RankStrategyConfEntry_DoNotUse::RankStrategyConfig_RankStrategyConfEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void RankStrategyConfig_RankStrategyConfEntry_DoNotUse::MergeFrom(const RankStrategyConfig_RankStrategyConfEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata RankStrategyConfig_RankStrategyConfEntry_DoNotUse::GetMetadata() const {
  return GetMetadataStatic();
}
void RankStrategyConfig_RankStrategyConfEntry_DoNotUse::MergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::Message& other) {
  ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom(other);
}


// ===================================================================

void RankStrategyConfig::InitAsDefaultInstance() {
}
class RankStrategyConfig::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RankStrategyConfig::kRankStrategyConfFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RankStrategyConfig::RankStrategyConfig()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.rank.RankStrategyConfig)
}
RankStrategyConfig::RankStrategyConfig(const RankStrategyConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  rank_strategy_conf_.MergeFrom(from.rank_strategy_conf_);
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.rank.RankStrategyConfig)
}

void RankStrategyConfig::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RankStrategyConfig_proto_2frecommend_5frank_5fparam_2eproto.base);
}

RankStrategyConfig::~RankStrategyConfig() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.rank.RankStrategyConfig)
  SharedDtor();
}

void RankStrategyConfig::SharedDtor() {
}

void RankStrategyConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RankStrategyConfig& RankStrategyConfig::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RankStrategyConfig_proto_2frecommend_5frank_5fparam_2eproto.base);
  return *internal_default_instance();
}


void RankStrategyConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.rank.RankStrategyConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  rank_strategy_conf_.Clear();
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* RankStrategyConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // map<string, .abc.recommend_plt.rank.RankStrategy> rank_strategy_conf = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&rank_strategy_conf_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 10);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool RankStrategyConfig::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.rank.RankStrategyConfig)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<string, .abc.recommend_plt.rank.RankStrategy> rank_strategy_conf = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          RankStrategyConfig_RankStrategyConfEntry_DoNotUse::Parser< ::PROTOBUF_NAMESPACE_ID::internal::MapField<
              RankStrategyConfig_RankStrategyConfEntry_DoNotUse,
              std::string, ::abc::recommend_plt::rank::RankStrategy,
              ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
              ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::rank::RankStrategy > > parser(&rank_strategy_conf_);
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.rank.RankStrategyConfig.RankStrategyConfEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.rank.RankStrategyConfig)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.rank.RankStrategyConfig)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void RankStrategyConfig::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.rank.RankStrategyConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .abc.recommend_plt.rank.RankStrategy> rank_strategy_conf = 1;
  if (!this->rank_strategy_conf().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::rank::RankStrategy >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.rank.RankStrategyConfig.RankStrategyConfEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->rank_strategy_conf().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->rank_strategy_conf().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::rank::RankStrategy >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::rank::RankStrategy >::const_iterator
          it = this->rank_strategy_conf().begin();
          it != this->rank_strategy_conf().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        RankStrategyConfig_RankStrategyConfEntry_DoNotUse::MapEntryWrapper entry(nullptr, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second);
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(1, entry, output);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::rank::RankStrategy >::const_iterator
          it = this->rank_strategy_conf().begin();
          it != this->rank_strategy_conf().end(); ++it) {
        RankStrategyConfig_RankStrategyConfEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(1, entry, output);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.rank.RankStrategyConfig)
}

::PROTOBUF_NAMESPACE_ID::uint8* RankStrategyConfig::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.rank.RankStrategyConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .abc.recommend_plt.rank.RankStrategy> rank_strategy_conf = 1;
  if (!this->rank_strategy_conf().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::rank::RankStrategy >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.rank.RankStrategyConfig.RankStrategyConfEntry.key");
      }
    };

    if (false &&
        this->rank_strategy_conf().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->rank_strategy_conf().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::rank::RankStrategy >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::rank::RankStrategy >::const_iterator
          it = this->rank_strategy_conf().begin();
          it != this->rank_strategy_conf().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        RankStrategyConfig_RankStrategyConfEntry_DoNotUse::MapEntryWrapper entry(nullptr, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second);
        target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::InternalWriteMessageNoVirtualToArray(1, entry, target);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::rank::RankStrategy >::const_iterator
          it = this->rank_strategy_conf().begin();
          it != this->rank_strategy_conf().end(); ++it) {
        RankStrategyConfig_RankStrategyConfEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
        target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::InternalWriteMessageNoVirtualToArray(1, entry, target);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.rank.RankStrategyConfig)
  return target;
}

size_t RankStrategyConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.rank.RankStrategyConfig)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, .abc.recommend_plt.rank.RankStrategy> rank_strategy_conf = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->rank_strategy_conf_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::rank::RankStrategy >::const_iterator
      it = this->rank_strategy_conf().begin();
      it != this->rank_strategy_conf().end(); ++it) {
    RankStrategyConfig_RankStrategyConfEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        MessageSizeNoVirtual(entry);
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RankStrategyConfig::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.rank.RankStrategyConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const RankStrategyConfig* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RankStrategyConfig>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.rank.RankStrategyConfig)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.rank.RankStrategyConfig)
    MergeFrom(*source);
  }
}

void RankStrategyConfig::MergeFrom(const RankStrategyConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.rank.RankStrategyConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  rank_strategy_conf_.MergeFrom(from.rank_strategy_conf_);
}

void RankStrategyConfig::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.rank.RankStrategyConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RankStrategyConfig::CopyFrom(const RankStrategyConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.rank.RankStrategyConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RankStrategyConfig::IsInitialized() const {
  return true;
}

void RankStrategyConfig::Swap(RankStrategyConfig* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RankStrategyConfig::InternalSwap(RankStrategyConfig* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  rank_strategy_conf_.Swap(&other->rank_strategy_conf_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RankStrategyConfig::GetMetadata() const {
  return GetMetadataStatic();
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace rank
}  // namespace recommend_plt
}  // namespace abc
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::rank::ScoreName* Arena::CreateMaybeMessage< ::abc::recommend_plt::rank::ScoreName >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::rank::ScoreName >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::rank::TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse* Arena::CreateMaybeMessage< ::abc::recommend_plt::rank::TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::rank::TwinTowerScoringParam_ModelSiteUidMapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::rank::TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse* Arena::CreateMaybeMessage< ::abc::recommend_plt::rank::TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::rank::TwinTowerScoringParam_FeaSiteUidMapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::rank::TwinTowerScoringParam* Arena::CreateMaybeMessage< ::abc::recommend_plt::rank::TwinTowerScoringParam >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::rank::TwinTowerScoringParam >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::rank::HotScoringParam* Arena::CreateMaybeMessage< ::abc::recommend_plt::rank::HotScoringParam >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::rank::HotScoringParam >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::rank::TFScoringParam_ModelSiteUidMapEntry_DoNotUse* Arena::CreateMaybeMessage< ::abc::recommend_plt::rank::TFScoringParam_ModelSiteUidMapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::rank::TFScoringParam_ModelSiteUidMapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::rank::TFScoringParam_FeaSiteUidMapEntry_DoNotUse* Arena::CreateMaybeMessage< ::abc::recommend_plt::rank::TFScoringParam_FeaSiteUidMapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::rank::TFScoringParam_FeaSiteUidMapEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::rank::TFScoringParam* Arena::CreateMaybeMessage< ::abc::recommend_plt::rank::TFScoringParam >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::rank::TFScoringParam >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::rank::DslScoringParam* Arena::CreateMaybeMessage< ::abc::recommend_plt::rank::DslScoringParam >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::rank::DslScoringParam >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::rank::ScoringParam* Arena::CreateMaybeMessage< ::abc::recommend_plt::rank::ScoringParam >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::rank::ScoringParam >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::rank::CrossMixParam_Factor* Arena::CreateMaybeMessage< ::abc::recommend_plt::rank::CrossMixParam_Factor >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::rank::CrossMixParam_Factor >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::rank::CrossMixParam* Arena::CreateMaybeMessage< ::abc::recommend_plt::rank::CrossMixParam >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::rank::CrossMixParam >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::rank::DslMixParam* Arena::CreateMaybeMessage< ::abc::recommend_plt::rank::DslMixParam >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::rank::DslMixParam >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::rank::MixParam* Arena::CreateMaybeMessage< ::abc::recommend_plt::rank::MixParam >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::rank::MixParam >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::rank::ItemFeatureDiversityParam_Factor* Arena::CreateMaybeMessage< ::abc::recommend_plt::rank::ItemFeatureDiversityParam_Factor >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::rank::ItemFeatureDiversityParam_Factor >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::rank::ItemFeatureDiversityParam* Arena::CreateMaybeMessage< ::abc::recommend_plt::rank::ItemFeatureDiversityParam >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::rank::ItemFeatureDiversityParam >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::rank::DiversityParam* Arena::CreateMaybeMessage< ::abc::recommend_plt::rank::DiversityParam >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::rank::DiversityParam >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::rank::RankStrategy* Arena::CreateMaybeMessage< ::abc::recommend_plt::rank::RankStrategy >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::rank::RankStrategy >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::rank::RankStrategyConfig_RankStrategyConfEntry_DoNotUse* Arena::CreateMaybeMessage< ::abc::recommend_plt::rank::RankStrategyConfig_RankStrategyConfEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::rank::RankStrategyConfig_RankStrategyConfEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::rank::RankStrategyConfig* Arena::CreateMaybeMessage< ::abc::recommend_plt::rank::RankStrategyConfig >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::rank::RankStrategyConfig >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
