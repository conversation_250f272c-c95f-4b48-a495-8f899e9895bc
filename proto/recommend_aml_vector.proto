syntax = "proto3";

package abc.aml_vector.recall;
option go_package = "../recall";
import "proto/recommend_mmp.proto";
option cc_enable_arenas = true;
 
//数据类型 the same as milvus data type
//https://github.sheincorp.cn/milvus-io/milvus-proto/blob/master/proto/schema.proto
//milvus schema.proto中的数据类型
enum DataType {
  None = 0;
  Bool = 1;
  Int8 = 2;
  Int16 = 3;
  Int32 = 4;
  Int64 = 5;
  Float = 10;
  Double = 11;
  String = 20;
  VarChar = 21; // variable-length strings with a specified maximum length
  Array = 22;
  JSON = 23;
  BinaryVector = 100;
  FloatVector = 101;
  Float16Vector = 102;
  BFloat16Vector = 103;
  SparseFloatVector = 104;
}
 
//数组类型
message BoolArray {
  repeated bool arr = 1;
}
message IntArray {
  repeated int32 arr = 1;
}
message LongArray {
  repeated int64 arr = 1;
}
message FloatArray {
  repeated float arr = 1;
}
message DoubleArray {
  repeated double arr = 1;
}
message BytesArray {
  repeated bytes arr = 1;
}
message StringArray {
  repeated string arr = 1;
}
message JSONArray {
  repeated bytes arr = 1;
}
 
//数组的数组
message ArrayArray {
  DataType type = 1;
  repeated AnyArray arr = 2;
}
 
//map类型
message AnyMap {
  map<string, Any> map = 1;
}
 
//任意数组类型
message AnyArray {
  DataType type = 1;
  oneof kind {
    BoolArray bool_list = 2;
    IntArray int_list = 3;
    LongArray long_list = 4;
    FloatArray float_list = 5;
    DoubleArray double_list = 6;
    StringArray string_list = 7;
    BytesArray bytes_list = 8;
    JSONArray json_list = 9;
    ArrayArray array_list = 10;
  }
}
 
//任意类型
message Any {
  DataType type = 1;
  oneof kind {
    bool bool_data = 2;
    int32 int_data = 3;
    int64 long_data = 4;
    float float_data = 5;
    double double_data = 6;
    string string_data = 7;
    bytes bytes_data = 8;
    AnyArray array_data = 9;
    AnyMap map_data = 10;
  }
}

message SearchParam {
  int32 top_k = 1;// top k 召回个数   //hnsw ef
  float score = 2;// 分数阈值 0.0-1.0  score会转为distance IP,COSINE 返回大于distance的结果  L2 返回小于distance的结果
  int32 nprobe = 3; //nprobe        //ivf_flat nprobe
  int32 offset = 4; //offset
  //other param 5-10
 
  repeated string output_fields = 11;  //milvus返回的字段  ["id", "new_cate_1_id", "new_cate_2_id"]
  int32 match_type = 12; //匹配类型 0:and  1:or
  map<string, Any> scalar = 13; //标量过滤条件 map<string,object> //key:new_cate_id_id value:[1,2,3]  ==> new_cate_id_id in [1,2,3]
  string expr = 14; //表达式  https://en.wikipedia.org/wiki/Extended_Backus%E2%80%93Naur_form
}
 

message AMLRecallRequest {
  string appid = 1; //appid string
  abc.recommend_plt.mmp.TFModelRequest req = 2; //tfmodel req
  SearchParam search_param = 3;  //参数
}
 
message AMLRecallResponse{
  int32 ret = 1; //0:success
  string msg = 2; //msg
  repeated Result results = 3;
}
 
message Match {
  string id = 1;  //vector id
  float score = 2; //score
  map<string, Any> scalar = 3; //标量字段
}
 
//匹配结果列表
message Result {
  string name = 1; //name
  repeated Match match_list = 2; //匹配列表
}
 
service AMLVectorService {
  rpc Recall(AMLRecallRequest) returns (AMLRecallResponse) {}
}