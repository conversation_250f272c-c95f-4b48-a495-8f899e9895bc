// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: proto/recommend_srch_recall.proto
#ifndef GRPC_proto_2frecommend_5fsrch_5frecall_2eproto__INCLUDED
#define GRPC_proto_2frecommend_5fsrch_5frecall_2eproto__INCLUDED

#include "proto/recommend_srch_recall.pb.h"

#include <functional>
#include <grpc/impl/codegen/port_platform.h>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace abc {
namespace recommend_plt {
namespace match {

//
// rpc 服务
//
class SrchRecallService final {
 public:
  static constexpr char const* service_full_name() {
    return "abc.recommend_plt.match.SrchRecallService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status DoSrchRecall(::grpc::ClientContext* context, const ::abc::recommend_plt::match::SrchRecallRequest& request, ::abc::recommend_plt::match::SrchRecallResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::abc::recommend_plt::match::SrchRecallResponse>> AsyncDoSrchRecall(::grpc::ClientContext* context, const ::abc::recommend_plt::match::SrchRecallRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::abc::recommend_plt::match::SrchRecallResponse>>(AsyncDoSrchRecallRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::abc::recommend_plt::match::SrchRecallResponse>> PrepareAsyncDoSrchRecall(::grpc::ClientContext* context, const ::abc::recommend_plt::match::SrchRecallRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::abc::recommend_plt::match::SrchRecallResponse>>(PrepareAsyncDoSrchRecallRaw(context, request, cq));
    }
    class experimental_async_interface {
     public:
      virtual ~experimental_async_interface() {}
      virtual void DoSrchRecall(::grpc::ClientContext* context, const ::abc::recommend_plt::match::SrchRecallRequest* request, ::abc::recommend_plt::match::SrchRecallResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void DoSrchRecall(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::abc::recommend_plt::match::SrchRecallResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void DoSrchRecall(::grpc::ClientContext* context, const ::abc::recommend_plt::match::SrchRecallRequest* request, ::abc::recommend_plt::match::SrchRecallResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void DoSrchRecall(::grpc::ClientContext* context, const ::abc::recommend_plt::match::SrchRecallRequest* request, ::abc::recommend_plt::match::SrchRecallResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void DoSrchRecall(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::abc::recommend_plt::match::SrchRecallResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void DoSrchRecall(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::abc::recommend_plt::match::SrchRecallResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
    };
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    typedef class experimental_async_interface async_interface;
    #endif
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    async_interface* async() { return experimental_async(); }
    #endif
    virtual class experimental_async_interface* experimental_async() { return nullptr; }
  private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::abc::recommend_plt::match::SrchRecallResponse>* AsyncDoSrchRecallRaw(::grpc::ClientContext* context, const ::abc::recommend_plt::match::SrchRecallRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::abc::recommend_plt::match::SrchRecallResponse>* PrepareAsyncDoSrchRecallRaw(::grpc::ClientContext* context, const ::abc::recommend_plt::match::SrchRecallRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel);
    ::grpc::Status DoSrchRecall(::grpc::ClientContext* context, const ::abc::recommend_plt::match::SrchRecallRequest& request, ::abc::recommend_plt::match::SrchRecallResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::abc::recommend_plt::match::SrchRecallResponse>> AsyncDoSrchRecall(::grpc::ClientContext* context, const ::abc::recommend_plt::match::SrchRecallRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::abc::recommend_plt::match::SrchRecallResponse>>(AsyncDoSrchRecallRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::abc::recommend_plt::match::SrchRecallResponse>> PrepareAsyncDoSrchRecall(::grpc::ClientContext* context, const ::abc::recommend_plt::match::SrchRecallRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::abc::recommend_plt::match::SrchRecallResponse>>(PrepareAsyncDoSrchRecallRaw(context, request, cq));
    }
    class experimental_async final :
      public StubInterface::experimental_async_interface {
     public:
      void DoSrchRecall(::grpc::ClientContext* context, const ::abc::recommend_plt::match::SrchRecallRequest* request, ::abc::recommend_plt::match::SrchRecallResponse* response, std::function<void(::grpc::Status)>) override;
      void DoSrchRecall(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::abc::recommend_plt::match::SrchRecallResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void DoSrchRecall(::grpc::ClientContext* context, const ::abc::recommend_plt::match::SrchRecallRequest* request, ::abc::recommend_plt::match::SrchRecallResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void DoSrchRecall(::grpc::ClientContext* context, const ::abc::recommend_plt::match::SrchRecallRequest* request, ::abc::recommend_plt::match::SrchRecallResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void DoSrchRecall(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::abc::recommend_plt::match::SrchRecallResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void DoSrchRecall(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::abc::recommend_plt::match::SrchRecallResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
     private:
      friend class Stub;
      explicit experimental_async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class experimental_async_interface* experimental_async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class experimental_async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::abc::recommend_plt::match::SrchRecallResponse>* AsyncDoSrchRecallRaw(::grpc::ClientContext* context, const ::abc::recommend_plt::match::SrchRecallRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::abc::recommend_plt::match::SrchRecallResponse>* PrepareAsyncDoSrchRecallRaw(::grpc::ClientContext* context, const ::abc::recommend_plt::match::SrchRecallRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_DoSrchRecall_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status DoSrchRecall(::grpc::ServerContext* context, const ::abc::recommend_plt::match::SrchRecallRequest* request, ::abc::recommend_plt::match::SrchRecallResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_DoSrchRecall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_DoSrchRecall() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_DoSrchRecall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DoSrchRecall(::grpc::ServerContext* /*context*/, const ::abc::recommend_plt::match::SrchRecallRequest* /*request*/, ::abc::recommend_plt::match::SrchRecallResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDoSrchRecall(::grpc::ServerContext* context, ::abc::recommend_plt::match::SrchRecallRequest* request, ::grpc::ServerAsyncResponseWriter< ::abc::recommend_plt::match::SrchRecallResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_DoSrchRecall<Service > AsyncService;
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_DoSrchRecall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_DoSrchRecall() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(0,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::abc::recommend_plt::match::SrchRecallRequest, ::abc::recommend_plt::match::SrchRecallResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::abc::recommend_plt::match::SrchRecallRequest* request, ::abc::recommend_plt::match::SrchRecallResponse* response) { return this->DoSrchRecall(context, request, response); }));}
    void SetMessageAllocatorFor_DoSrchRecall(
        ::grpc::experimental::MessageAllocator< ::abc::recommend_plt::match::SrchRecallRequest, ::abc::recommend_plt::match::SrchRecallResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(0);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::abc::recommend_plt::match::SrchRecallRequest, ::abc::recommend_plt::match::SrchRecallResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_DoSrchRecall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DoSrchRecall(::grpc::ServerContext* /*context*/, const ::abc::recommend_plt::match::SrchRecallRequest* /*request*/, ::abc::recommend_plt::match::SrchRecallResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* DoSrchRecall(
      ::grpc::CallbackServerContext* /*context*/, const ::abc::recommend_plt::match::SrchRecallRequest* /*request*/, ::abc::recommend_plt::match::SrchRecallResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* DoSrchRecall(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::abc::recommend_plt::match::SrchRecallRequest* /*request*/, ::abc::recommend_plt::match::SrchRecallResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
  typedef ExperimentalWithCallbackMethod_DoSrchRecall<Service > CallbackService;
  #endif

  typedef ExperimentalWithCallbackMethod_DoSrchRecall<Service > ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_DoSrchRecall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_DoSrchRecall() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_DoSrchRecall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DoSrchRecall(::grpc::ServerContext* /*context*/, const ::abc::recommend_plt::match::SrchRecallRequest* /*request*/, ::abc::recommend_plt::match::SrchRecallResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_DoSrchRecall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_DoSrchRecall() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_DoSrchRecall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DoSrchRecall(::grpc::ServerContext* /*context*/, const ::abc::recommend_plt::match::SrchRecallRequest* /*request*/, ::abc::recommend_plt::match::SrchRecallResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDoSrchRecall(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_DoSrchRecall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_DoSrchRecall() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(0,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->DoSrchRecall(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_DoSrchRecall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DoSrchRecall(::grpc::ServerContext* /*context*/, const ::abc::recommend_plt::match::SrchRecallRequest* /*request*/, ::abc::recommend_plt::match::SrchRecallResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* DoSrchRecall(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* DoSrchRecall(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_DoSrchRecall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_DoSrchRecall() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::abc::recommend_plt::match::SrchRecallRequest, ::abc::recommend_plt::match::SrchRecallResponse>(
            [this](::grpc_impl::ServerContext* context,
                   ::grpc_impl::ServerUnaryStreamer<
                     ::abc::recommend_plt::match::SrchRecallRequest, ::abc::recommend_plt::match::SrchRecallResponse>* streamer) {
                       return this->StreamedDoSrchRecall(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_DoSrchRecall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status DoSrchRecall(::grpc::ServerContext* /*context*/, const ::abc::recommend_plt::match::SrchRecallRequest* /*request*/, ::abc::recommend_plt::match::SrchRecallResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedDoSrchRecall(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::abc::recommend_plt::match::SrchRecallRequest,::abc::recommend_plt::match::SrchRecallResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_DoSrchRecall<Service > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_DoSrchRecall<Service > StreamedService;
};

}  // namespace match
}  // namespace recommend_plt
}  // namespace abc


#endif  // GRPC_proto_2frecommend_5fsrch_5frecall_2eproto__INCLUDED
