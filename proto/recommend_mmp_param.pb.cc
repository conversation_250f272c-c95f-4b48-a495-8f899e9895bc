// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/recommend_mmp_param.proto

#include "proto/recommend_mmp_param.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fmmp_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_FuseParam_proto_2frecommend_5fmmp_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fmmp_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_FuseParam_OutputParam_proto_2frecommend_5fmmp_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fmmp_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_KVModelParam_proto_2frecommend_5fmmp_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fmmp_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_RankStrategy_proto_2frecommend_5fmmp_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fmmp_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_RankStrategyConfig_ConfEntry_DoNotUse_proto_2frecommend_5fmmp_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fmmp_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_TFModelParam_proto_2frecommend_5fmmp_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fmmp_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_TFModelParam_ObjectParam_proto_2frecommend_5fmmp_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fmmp_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_TFModelParam_ObjectParamsEntry_DoNotUse_proto_2frecommend_5fmmp_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fmmp_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_TensorParam_proto_2frecommend_5fmmp_5fparam_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fmmp_5fparam_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_TensorParamList_proto_2frecommend_5fmmp_5fparam_2eproto;
namespace abc {
namespace recommend_plt {
namespace mmp {
class TensorParamDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TensorParam> _instance;
  ::PROTOBUF_NAMESPACE_ID::int64 int_val_;
  float float_val_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr string_val_;
} _TensorParam_default_instance_;
class TensorParamListDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TensorParamList> _instance;
} _TensorParamList_default_instance_;
class TFModelParam_ObjectParamDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TFModelParam_ObjectParam> _instance;
} _TFModelParam_ObjectParam_default_instance_;
class TFModelParam_ObjectParamsEntry_DoNotUseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TFModelParam_ObjectParamsEntry_DoNotUse> _instance;
} _TFModelParam_ObjectParamsEntry_DoNotUse_default_instance_;
class TFModelParamDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TFModelParam> _instance;
} _TFModelParam_default_instance_;
class TFModelParamListDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TFModelParamList> _instance;
} _TFModelParamList_default_instance_;
class FuseParam_OutputParamDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<FuseParam_OutputParam> _instance;
} _FuseParam_OutputParam_default_instance_;
class FuseParamDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<FuseParam> _instance;
} _FuseParam_default_instance_;
class RankStrategyDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RankStrategy> _instance;
} _RankStrategy_default_instance_;
class RankStrategyConfig_ConfEntry_DoNotUseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RankStrategyConfig_ConfEntry_DoNotUse> _instance;
} _RankStrategyConfig_ConfEntry_DoNotUse_default_instance_;
class RankStrategyConfigDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RankStrategyConfig> _instance;
} _RankStrategyConfig_default_instance_;
class KVModelParamDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<KVModelParam> _instance;
} _KVModelParam_default_instance_;
class KVModelParamListDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<KVModelParamList> _instance;
} _KVModelParamList_default_instance_;
}  // namespace mmp
}  // namespace recommend_plt
}  // namespace abc
static void InitDefaultsscc_info_FuseParam_proto_2frecommend_5fmmp_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::mmp::_FuseParam_default_instance_;
    new (ptr) ::abc::recommend_plt::mmp::FuseParam();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::mmp::FuseParam::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_FuseParam_proto_2frecommend_5fmmp_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsscc_info_FuseParam_proto_2frecommend_5fmmp_5fparam_2eproto}, {
      &scc_info_FuseParam_OutputParam_proto_2frecommend_5fmmp_5fparam_2eproto.base,}};

static void InitDefaultsscc_info_FuseParam_OutputParam_proto_2frecommend_5fmmp_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::mmp::_FuseParam_OutputParam_default_instance_;
    new (ptr) ::abc::recommend_plt::mmp::FuseParam_OutputParam();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::mmp::FuseParam_OutputParam::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_FuseParam_OutputParam_proto_2frecommend_5fmmp_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsscc_info_FuseParam_OutputParam_proto_2frecommend_5fmmp_5fparam_2eproto}, {}};

static void InitDefaultsscc_info_KVModelParam_proto_2frecommend_5fmmp_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::mmp::_KVModelParam_default_instance_;
    new (ptr) ::abc::recommend_plt::mmp::KVModelParam();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::mmp::KVModelParam::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_KVModelParam_proto_2frecommend_5fmmp_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsscc_info_KVModelParam_proto_2frecommend_5fmmp_5fparam_2eproto}, {}};

static void InitDefaultsscc_info_KVModelParamList_proto_2frecommend_5fmmp_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::mmp::_KVModelParamList_default_instance_;
    new (ptr) ::abc::recommend_plt::mmp::KVModelParamList();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::mmp::KVModelParamList::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_KVModelParamList_proto_2frecommend_5fmmp_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsscc_info_KVModelParamList_proto_2frecommend_5fmmp_5fparam_2eproto}, {
      &scc_info_KVModelParam_proto_2frecommend_5fmmp_5fparam_2eproto.base,}};

static void InitDefaultsscc_info_RankStrategy_proto_2frecommend_5fmmp_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::mmp::_RankStrategy_default_instance_;
    new (ptr) ::abc::recommend_plt::mmp::RankStrategy();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::mmp::RankStrategy::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_RankStrategy_proto_2frecommend_5fmmp_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsscc_info_RankStrategy_proto_2frecommend_5fmmp_5fparam_2eproto}, {
      &scc_info_FuseParam_proto_2frecommend_5fmmp_5fparam_2eproto.base,}};

static void InitDefaultsscc_info_RankStrategyConfig_proto_2frecommend_5fmmp_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::mmp::_RankStrategyConfig_default_instance_;
    new (ptr) ::abc::recommend_plt::mmp::RankStrategyConfig();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::mmp::RankStrategyConfig::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_RankStrategyConfig_proto_2frecommend_5fmmp_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsscc_info_RankStrategyConfig_proto_2frecommend_5fmmp_5fparam_2eproto}, {
      &scc_info_RankStrategyConfig_ConfEntry_DoNotUse_proto_2frecommend_5fmmp_5fparam_2eproto.base,}};

static void InitDefaultsscc_info_RankStrategyConfig_ConfEntry_DoNotUse_proto_2frecommend_5fmmp_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::mmp::_RankStrategyConfig_ConfEntry_DoNotUse_default_instance_;
    new (ptr) ::abc::recommend_plt::mmp::RankStrategyConfig_ConfEntry_DoNotUse();
  }
  ::abc::recommend_plt::mmp::RankStrategyConfig_ConfEntry_DoNotUse::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_RankStrategyConfig_ConfEntry_DoNotUse_proto_2frecommend_5fmmp_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsscc_info_RankStrategyConfig_ConfEntry_DoNotUse_proto_2frecommend_5fmmp_5fparam_2eproto}, {
      &scc_info_RankStrategy_proto_2frecommend_5fmmp_5fparam_2eproto.base,}};

static void InitDefaultsscc_info_TFModelParam_proto_2frecommend_5fmmp_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::mmp::_TFModelParam_default_instance_;
    new (ptr) ::abc::recommend_plt::mmp::TFModelParam();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::mmp::TFModelParam::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_TFModelParam_proto_2frecommend_5fmmp_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsscc_info_TFModelParam_proto_2frecommend_5fmmp_5fparam_2eproto}, {
      &scc_info_TensorParamList_proto_2frecommend_5fmmp_5fparam_2eproto.base,
      &scc_info_TFModelParam_ObjectParamsEntry_DoNotUse_proto_2frecommend_5fmmp_5fparam_2eproto.base,}};

static void InitDefaultsscc_info_TFModelParam_ObjectParam_proto_2frecommend_5fmmp_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::mmp::_TFModelParam_ObjectParam_default_instance_;
    new (ptr) ::abc::recommend_plt::mmp::TFModelParam_ObjectParam();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::mmp::TFModelParam_ObjectParam::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_TFModelParam_ObjectParam_proto_2frecommend_5fmmp_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsscc_info_TFModelParam_ObjectParam_proto_2frecommend_5fmmp_5fparam_2eproto}, {}};

static void InitDefaultsscc_info_TFModelParam_ObjectParamsEntry_DoNotUse_proto_2frecommend_5fmmp_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::mmp::_TFModelParam_ObjectParamsEntry_DoNotUse_default_instance_;
    new (ptr) ::abc::recommend_plt::mmp::TFModelParam_ObjectParamsEntry_DoNotUse();
  }
  ::abc::recommend_plt::mmp::TFModelParam_ObjectParamsEntry_DoNotUse::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_TFModelParam_ObjectParamsEntry_DoNotUse_proto_2frecommend_5fmmp_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsscc_info_TFModelParam_ObjectParamsEntry_DoNotUse_proto_2frecommend_5fmmp_5fparam_2eproto}, {
      &scc_info_TFModelParam_ObjectParam_proto_2frecommend_5fmmp_5fparam_2eproto.base,}};

static void InitDefaultsscc_info_TFModelParamList_proto_2frecommend_5fmmp_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::mmp::_TFModelParamList_default_instance_;
    new (ptr) ::abc::recommend_plt::mmp::TFModelParamList();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::mmp::TFModelParamList::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_TFModelParamList_proto_2frecommend_5fmmp_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsscc_info_TFModelParamList_proto_2frecommend_5fmmp_5fparam_2eproto}, {
      &scc_info_TFModelParam_proto_2frecommend_5fmmp_5fparam_2eproto.base,}};

static void InitDefaultsscc_info_TensorParam_proto_2frecommend_5fmmp_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::mmp::_TensorParam_default_instance_;
    new (ptr) ::abc::recommend_plt::mmp::TensorParam();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::mmp::TensorParam::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_TensorParam_proto_2frecommend_5fmmp_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsscc_info_TensorParam_proto_2frecommend_5fmmp_5fparam_2eproto}, {}};

static void InitDefaultsscc_info_TensorParamList_proto_2frecommend_5fmmp_5fparam_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::mmp::_TensorParamList_default_instance_;
    new (ptr) ::abc::recommend_plt::mmp::TensorParamList();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::mmp::TensorParamList::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_TensorParamList_proto_2frecommend_5fmmp_5fparam_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsscc_info_TensorParamList_proto_2frecommend_5fmmp_5fparam_2eproto}, {
      &scc_info_TensorParam_proto_2frecommend_5fmmp_5fparam_2eproto.base,}};

static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_proto_2frecommend_5fmmp_5fparam_2eproto[13];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_proto_2frecommend_5fmmp_5fparam_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_proto_2frecommend_5fmmp_5fparam_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_proto_2frecommend_5fmmp_5fparam_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TensorParam, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TensorParam, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TensorParam, feature_name_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TensorParam, feature_type_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TensorParam, input_name_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TensorParam, dtype_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TensorParam, dsize_),
  offsetof(::abc::recommend_plt::mmp::TensorParamDefaultTypeInternal, int_val_),
  offsetof(::abc::recommend_plt::mmp::TensorParamDefaultTypeInternal, float_val_),
  offsetof(::abc::recommend_plt::mmp::TensorParamDefaultTypeInternal, string_val_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TensorParam, feature_group_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TensorParam, default_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TensorParamList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TensorParamList, tensor_param_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TFModelParam_ObjectParam, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TFModelParam_ObjectParam, weight_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TFModelParam_ObjectParam, bias_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TFModelParam_ObjectParam, exponent_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TFModelParam_ObjectParamsEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TFModelParam_ObjectParamsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TFModelParam_ObjectParamsEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TFModelParam_ObjectParamsEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TFModelParam, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TFModelParam, model_id_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TFModelParam, feature_group_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TFModelParam, tensor_param_list_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TFModelParam, object_params_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TFModelParam, score_coef_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TFModelParamList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::TFModelParamList, tf_model_param_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::FuseParam_OutputParam, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::FuseParam_OutputParam, name_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::FuseParam_OutputParam, weight_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::FuseParam_OutputParam, bias_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::FuseParam_OutputParam, exponent_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::FuseParam, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::FuseParam, model_id_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::FuseParam, model_site_uid_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::FuseParam, fea_site_uid_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::FuseParam, output_params_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::FuseParam, oper_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::RankStrategy, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::RankStrategy, fuse_param_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::RankStrategy, oper_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::RankStrategy, name_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::RankStrategyConfig_ConfEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::RankStrategyConfig_ConfEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::RankStrategyConfig_ConfEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::RankStrategyConfig_ConfEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::RankStrategyConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::RankStrategyConfig, conf_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::KVModelParam, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::KVModelParam, model_id_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::KVModelParam, model_name_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::KVModelParam, model_type_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::KVModelParam, key_dims_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::KVModelParam, cache_flag_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::KVModelParam, cache_ttl_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::KVModelParam, module_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::KVModelParam, pvc_file_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::KVModelParamList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::mmp::KVModelParamList, kv_model_param_list_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::abc::recommend_plt::mmp::TensorParam)},
  { 15, -1, sizeof(::abc::recommend_plt::mmp::TensorParamList)},
  { 21, -1, sizeof(::abc::recommend_plt::mmp::TFModelParam_ObjectParam)},
  { 29, 36, sizeof(::abc::recommend_plt::mmp::TFModelParam_ObjectParamsEntry_DoNotUse)},
  { 38, -1, sizeof(::abc::recommend_plt::mmp::TFModelParam)},
  { 48, -1, sizeof(::abc::recommend_plt::mmp::TFModelParamList)},
  { 54, -1, sizeof(::abc::recommend_plt::mmp::FuseParam_OutputParam)},
  { 63, -1, sizeof(::abc::recommend_plt::mmp::FuseParam)},
  { 73, -1, sizeof(::abc::recommend_plt::mmp::RankStrategy)},
  { 81, 88, sizeof(::abc::recommend_plt::mmp::RankStrategyConfig_ConfEntry_DoNotUse)},
  { 90, -1, sizeof(::abc::recommend_plt::mmp::RankStrategyConfig)},
  { 96, -1, sizeof(::abc::recommend_plt::mmp::KVModelParam)},
  { 109, -1, sizeof(::abc::recommend_plt::mmp::KVModelParamList)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::mmp::_TensorParam_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::mmp::_TensorParamList_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::mmp::_TFModelParam_ObjectParam_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::mmp::_TFModelParam_ObjectParamsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::mmp::_TFModelParam_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::mmp::_TFModelParamList_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::mmp::_FuseParam_OutputParam_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::mmp::_FuseParam_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::mmp::_RankStrategy_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::mmp::_RankStrategyConfig_ConfEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::mmp::_RankStrategyConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::mmp::_KVModelParam_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::mmp::_KVModelParamList_default_instance_),
};

const char descriptor_table_protodef_proto_2frecommend_5fmmp_5fparam_2eproto[] =
  "\n\037proto/recommend_mmp_param.proto\022\025abc.r"
  "ecommend_plt.mmp\032\031proto/tf_apis/types.pr"
  "oto\032\037proto/recommend_fmp_param.proto\"\201\002\n"
  "\013TensorParam\022\024\n\014feature_name\030\001 \001(\t\0224\n\014fe"
  "ature_type\030\002 \001(\0162\036.abc.recommend_plt.fmp"
  ".FeaType\022\022\n\ninput_name\030\003 \001(\t\022#\n\005dtype\030\004 "
  "\001(\0162\024.tensorflow.DataType\022\r\n\005dsize\030\005 \001(\005"
  "\022\021\n\007int_val\030\006 \001(\003H\000\022\023\n\tfloat_val\030\007 \001(\002H\000"
  "\022\024\n\nstring_val\030\010 \001(\tH\000\022\025\n\rfeature_group\030"
  "\t \001(\tB\t\n\007default\"K\n\017TensorParamList\0228\n\014t"
  "ensor_param\030\001 \003(\0132\".abc.recommend_plt.mm"
  "p.TensorParam\"\201\003\n\014TFModelParam\022\020\n\010model_"
  "id\030\001 \001(\t\022\025\n\rfeature_group\030\002 \003(\t\022A\n\021tenso"
  "r_param_list\030\003 \001(\0132&.abc.recommend_plt.m"
  "mp.TensorParamList\022L\n\robject_params\030\004 \003("
  "\01325.abc.recommend_plt.mmp.TFModelParam.O"
  "bjectParamsEntry\022\022\n\nscore_coef\030\005 \001(\005\032=\n\013"
  "ObjectParam\022\016\n\006weight\030\001 \001(\002\022\014\n\004bias\030\002 \001("
  "\002\022\020\n\010exponent\030\003 \001(\002\032d\n\021ObjectParamsEntry"
  "\022\013\n\003key\030\001 \001(\t\022>\n\005value\030\002 \001(\0132/.abc.recom"
  "mend_plt.mmp.TFModelParam.ObjectParam:\0028"
  "\001\"O\n\020TFModelParamList\022;\n\016tf_model_param\030"
  "\001 \003(\0132#.abc.recommend_plt.mmp.TFModelPar"
  "am\"\217\002\n\tFuseParam\022\020\n\010model_id\030\001 \001(\t\022\026\n\016mo"
  "del_site_uid\030\002 \001(\t\022\024\n\014fea_site_uid\030\003 \001(\t"
  "\022C\n\routput_params\030\004 \003(\0132,.abc.recommend_"
  "plt.mmp.FuseParam.OutputParam\0220\n\004oper\030\005 "
  "\001(\0162\".abc.recommend_plt.mmp.LogicalOper\032"
  "K\n\013OutputParam\022\014\n\004name\030\001 \001(\t\022\016\n\006weight\030\002"
  " \001(\002\022\014\n\004bias\030\003 \001(\002\022\020\n\010exponent\030\004 \001(\002\"\204\001\n"
  "\014RankStrategy\0224\n\nfuse_param\030\001 \003(\0132 .abc."
  "recommend_plt.mmp.FuseParam\0220\n\004oper\030\002 \001("
  "\0162\".abc.recommend_plt.mmp.LogicalOper\022\014\n"
  "\004name\030\003 \001(\t\"\251\001\n\022RankStrategyConfig\022A\n\004co"
  "nf\030\001 \003(\01323.abc.recommend_plt.mmp.RankStr"
  "ategyConfig.ConfEntry\032P\n\tConfEntry\022\013\n\003ke"
  "y\030\001 \001(\t\0222\n\005value\030\002 \001(\0132#.abc.recommend_p"
  "lt.mmp.RankStrategy:\0028\001\"\243\001\n\014KVModelParam"
  "\022\020\n\010model_id\030\001 \001(\t\022\022\n\nmodel_name\030\002 \001(\t\022\022"
  "\n\nmodel_type\030\003 \001(\005\022\020\n\010key_dims\030\004 \003(\t\022\022\n\n"
  "cache_flag\030\005 \001(\010\022\021\n\tcache_ttl\030\006 \001(\005\022\016\n\006m"
  "odule\030\007 \003(\t\022\020\n\010pvc_file\030\010 \003(\t\"T\n\020KVModel"
  "ParamList\022@\n\023kv_model_param_list\030\001 \003(\0132#"
  ".abc.recommend_plt.mmp.KVModelParam*V\n\013L"
  "ogicalOper\022\r\n\tOPER_NONE\020\000\022\r\n\tOPER_PLUS\020\001"
  "\022\014\n\010OPER_SUB\020\002\022\r\n\tOPER_MULT\020\003\022\014\n\010OPER_DI"
  "V\020\004b\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_proto_2frecommend_5fmmp_5fparam_2eproto_deps[2] = {
  &::descriptor_table_proto_2frecommend_5ffmp_5fparam_2eproto,
  &::descriptor_table_proto_2ftf_5fapis_2ftypes_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_proto_2frecommend_5fmmp_5fparam_2eproto_sccs[13] = {
  &scc_info_FuseParam_proto_2frecommend_5fmmp_5fparam_2eproto.base,
  &scc_info_FuseParam_OutputParam_proto_2frecommend_5fmmp_5fparam_2eproto.base,
  &scc_info_KVModelParam_proto_2frecommend_5fmmp_5fparam_2eproto.base,
  &scc_info_KVModelParamList_proto_2frecommend_5fmmp_5fparam_2eproto.base,
  &scc_info_RankStrategy_proto_2frecommend_5fmmp_5fparam_2eproto.base,
  &scc_info_RankStrategyConfig_proto_2frecommend_5fmmp_5fparam_2eproto.base,
  &scc_info_RankStrategyConfig_ConfEntry_DoNotUse_proto_2frecommend_5fmmp_5fparam_2eproto.base,
  &scc_info_TFModelParam_proto_2frecommend_5fmmp_5fparam_2eproto.base,
  &scc_info_TFModelParam_ObjectParam_proto_2frecommend_5fmmp_5fparam_2eproto.base,
  &scc_info_TFModelParam_ObjectParamsEntry_DoNotUse_proto_2frecommend_5fmmp_5fparam_2eproto.base,
  &scc_info_TFModelParamList_proto_2frecommend_5fmmp_5fparam_2eproto.base,
  &scc_info_TensorParam_proto_2frecommend_5fmmp_5fparam_2eproto.base,
  &scc_info_TensorParamList_proto_2frecommend_5fmmp_5fparam_2eproto.base,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_proto_2frecommend_5fmmp_5fparam_2eproto_once;
static bool descriptor_table_proto_2frecommend_5fmmp_5fparam_2eproto_initialized = false;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2frecommend_5fmmp_5fparam_2eproto = {
  &descriptor_table_proto_2frecommend_5fmmp_5fparam_2eproto_initialized, descriptor_table_protodef_proto_2frecommend_5fmmp_5fparam_2eproto, "proto/recommend_mmp_param.proto", 1851,
  &descriptor_table_proto_2frecommend_5fmmp_5fparam_2eproto_once, descriptor_table_proto_2frecommend_5fmmp_5fparam_2eproto_sccs, descriptor_table_proto_2frecommend_5fmmp_5fparam_2eproto_deps, 13, 2,
  schemas, file_default_instances, TableStruct_proto_2frecommend_5fmmp_5fparam_2eproto::offsets,
  file_level_metadata_proto_2frecommend_5fmmp_5fparam_2eproto, 13, file_level_enum_descriptors_proto_2frecommend_5fmmp_5fparam_2eproto, file_level_service_descriptors_proto_2frecommend_5fmmp_5fparam_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_proto_2frecommend_5fmmp_5fparam_2eproto = (  ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_proto_2frecommend_5fmmp_5fparam_2eproto), true);
namespace abc {
namespace recommend_plt {
namespace mmp {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* LogicalOper_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_proto_2frecommend_5fmmp_5fparam_2eproto);
  return file_level_enum_descriptors_proto_2frecommend_5fmmp_5fparam_2eproto[0];
}
bool LogicalOper_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
      return true;
    default:
      return false;
  }
}


// ===================================================================

void TensorParam::InitAsDefaultInstance() {
  ::abc::recommend_plt::mmp::_TensorParam_default_instance_.int_val_ = PROTOBUF_LONGLONG(0);
  ::abc::recommend_plt::mmp::_TensorParam_default_instance_.float_val_ = 0;
  ::abc::recommend_plt::mmp::_TensorParam_default_instance_.string_val_.UnsafeSetDefault(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
class TensorParam::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TensorParam::kFeatureNameFieldNumber;
const int TensorParam::kFeatureTypeFieldNumber;
const int TensorParam::kInputNameFieldNumber;
const int TensorParam::kDtypeFieldNumber;
const int TensorParam::kDsizeFieldNumber;
const int TensorParam::kIntValFieldNumber;
const int TensorParam::kFloatValFieldNumber;
const int TensorParam::kStringValFieldNumber;
const int TensorParam::kFeatureGroupFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TensorParam::TensorParam()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.mmp.TensorParam)
}
TensorParam::TensorParam(const TensorParam& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  feature_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.feature_name().size() > 0) {
    feature_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.feature_name_);
  }
  input_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.input_name().size() > 0) {
    input_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.input_name_);
  }
  feature_group_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.feature_group().size() > 0) {
    feature_group_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.feature_group_);
  }
  ::memcpy(&feature_type_, &from.feature_type_,
    static_cast<size_t>(reinterpret_cast<char*>(&dsize_) -
    reinterpret_cast<char*>(&feature_type_)) + sizeof(dsize_));
  clear_has_default();
  switch (from.default_case()) {
    case kIntVal: {
      set_int_val(from.int_val());
      break;
    }
    case kFloatVal: {
      set_float_val(from.float_val());
      break;
    }
    case kStringVal: {
      set_string_val(from.string_val());
      break;
    }
    case DEFAULT_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.mmp.TensorParam)
}

void TensorParam::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TensorParam_proto_2frecommend_5fmmp_5fparam_2eproto.base);
  feature_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  input_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  feature_group_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&feature_type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&dsize_) -
      reinterpret_cast<char*>(&feature_type_)) + sizeof(dsize_));
  clear_has_default();
}

TensorParam::~TensorParam() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.mmp.TensorParam)
  SharedDtor();
}

void TensorParam::SharedDtor() {
  feature_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  input_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  feature_group_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (has_default()) {
    clear_default();
  }
}

void TensorParam::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TensorParam& TensorParam::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TensorParam_proto_2frecommend_5fmmp_5fparam_2eproto.base);
  return *internal_default_instance();
}


void TensorParam::clear_default() {
// @@protoc_insertion_point(one_of_clear_start:abc.recommend_plt.mmp.TensorParam)
  switch (default_case()) {
    case kIntVal: {
      // No need to clear
      break;
    }
    case kFloatVal: {
      // No need to clear
      break;
    }
    case kStringVal: {
      default_.string_val_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
      break;
    }
    case DEFAULT_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = DEFAULT_NOT_SET;
}


void TensorParam::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.mmp.TensorParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  feature_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  input_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  feature_group_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&feature_type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&dsize_) -
      reinterpret_cast<char*>(&feature_type_)) + sizeof(dsize_));
  clear_default();
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* TensorParam::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string feature_name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_feature_name(), ptr, ctx, "abc.recommend_plt.mmp.TensorParam.feature_name");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .abc.recommend_plt.fmp.FeaType feature_type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
          set_feature_type(static_cast<::abc::recommend_plt::fmp::FeaType>(val));
        } else goto handle_unusual;
        continue;
      // string input_name = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_input_name(), ptr, ctx, "abc.recommend_plt.mmp.TensorParam.input_name");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .tensorflow.DataType dtype = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
          set_dtype(static_cast<::tensorflow::DataType>(val));
        } else goto handle_unusual;
        continue;
      // int32 dsize = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          dsize_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int64 int_val = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          set_int_val(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float float_val = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 61)) {
          set_float_val(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr));
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // string string_val = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_string_val(), ptr, ctx, "abc.recommend_plt.mmp.TensorParam.string_val");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string feature_group = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_feature_group(), ptr, ctx, "abc.recommend_plt.mmp.TensorParam.feature_group");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool TensorParam::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.mmp.TensorParam)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string feature_name = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_feature_name()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->feature_name().data(), static_cast<int>(this->feature_name().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.mmp.TensorParam.feature_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .abc.recommend_plt.fmp.FeaType feature_type = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (16 & 0xFF)) {
          int value = 0;
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   int, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_feature_type(static_cast< ::abc::recommend_plt::fmp::FeaType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string input_name = 3;
      case 3: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (26 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_input_name()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->input_name().data(), static_cast<int>(this->input_name().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.mmp.TensorParam.input_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.DataType dtype = 4;
      case 4: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (32 & 0xFF)) {
          int value = 0;
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   int, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_dtype(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 dsize = 5;
      case 5: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (40 & 0xFF)) {

          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32>(
                 input, &dsize_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 int_val = 6;
      case 6: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (48 & 0xFF)) {
          clear_default();
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   ::PROTOBUF_NAMESPACE_ID::int64, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64>(
                 input, &default_.int_val_)));
          set_has_int_val();
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float float_val = 7;
      case 7: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (61 & 0xFF)) {
          clear_default();
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   float, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &default_.float_val_)));
          set_has_float_val();
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string string_val = 8;
      case 8: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (66 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_string_val()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->string_val().data(), static_cast<int>(this->string_val().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.mmp.TensorParam.string_val"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string feature_group = 9;
      case 9: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (74 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_feature_group()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->feature_group().data(), static_cast<int>(this->feature_group().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.mmp.TensorParam.feature_group"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.mmp.TensorParam)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.mmp.TensorParam)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void TensorParam::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.mmp.TensorParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string feature_name = 1;
  if (this->feature_name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->feature_name().data(), static_cast<int>(this->feature_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.TensorParam.feature_name");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->feature_name(), output);
  }

  // .abc.recommend_plt.fmp.FeaType feature_type = 2;
  if (this->feature_type() != 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnum(
      2, this->feature_type(), output);
  }

  // string input_name = 3;
  if (this->input_name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->input_name().data(), static_cast<int>(this->input_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.TensorParam.input_name");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->input_name(), output);
  }

  // .tensorflow.DataType dtype = 4;
  if (this->dtype() != 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnum(
      4, this->dtype(), output);
  }

  // int32 dsize = 5;
  if (this->dsize() != 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32(5, this->dsize(), output);
  }

  // int64 int_val = 6;
  if (has_int_val()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64(6, this->int_val(), output);
  }

  // float float_val = 7;
  if (has_float_val()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloat(7, this->float_val(), output);
  }

  // string string_val = 8;
  if (has_string_val()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->string_val().data(), static_cast<int>(this->string_val().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.TensorParam.string_val");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      8, this->string_val(), output);
  }

  // string feature_group = 9;
  if (this->feature_group().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->feature_group().data(), static_cast<int>(this->feature_group().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.TensorParam.feature_group");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      9, this->feature_group(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.mmp.TensorParam)
}

::PROTOBUF_NAMESPACE_ID::uint8* TensorParam::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.mmp.TensorParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string feature_name = 1;
  if (this->feature_name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->feature_name().data(), static_cast<int>(this->feature_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.TensorParam.feature_name");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        1, this->feature_name(), target);
  }

  // .abc.recommend_plt.fmp.FeaType feature_type = 2;
  if (this->feature_type() != 0) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->feature_type(), target);
  }

  // string input_name = 3;
  if (this->input_name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->input_name().data(), static_cast<int>(this->input_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.TensorParam.input_name");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        3, this->input_name(), target);
  }

  // .tensorflow.DataType dtype = 4;
  if (this->dtype() != 0) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      4, this->dtype(), target);
  }

  // int32 dsize = 5;
  if (this->dsize() != 0) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(5, this->dsize(), target);
  }

  // int64 int_val = 6;
  if (has_int_val()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(6, this->int_val(), target);
  }

  // float float_val = 7;
  if (has_float_val()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(7, this->float_val(), target);
  }

  // string string_val = 8;
  if (has_string_val()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->string_val().data(), static_cast<int>(this->string_val().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.TensorParam.string_val");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        8, this->string_val(), target);
  }

  // string feature_group = 9;
  if (this->feature_group().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->feature_group().data(), static_cast<int>(this->feature_group().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.TensorParam.feature_group");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        9, this->feature_group(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.mmp.TensorParam)
  return target;
}

size_t TensorParam::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.mmp.TensorParam)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string feature_name = 1;
  if (this->feature_name().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->feature_name());
  }

  // string input_name = 3;
  if (this->input_name().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->input_name());
  }

  // string feature_group = 9;
  if (this->feature_group().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->feature_group());
  }

  // .abc.recommend_plt.fmp.FeaType feature_type = 2;
  if (this->feature_type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->feature_type());
  }

  // .tensorflow.DataType dtype = 4;
  if (this->dtype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->dtype());
  }

  // int32 dsize = 5;
  if (this->dsize() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->dsize());
  }

  switch (default_case()) {
    // int64 int_val = 6;
    case kIntVal: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
          this->int_val());
      break;
    }
    // float float_val = 7;
    case kFloatVal: {
      total_size += 1 + 4;
      break;
    }
    // string string_val = 8;
    case kStringVal: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->string_val());
      break;
    }
    case DEFAULT_NOT_SET: {
      break;
    }
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TensorParam::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.mmp.TensorParam)
  GOOGLE_DCHECK_NE(&from, this);
  const TensorParam* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TensorParam>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.mmp.TensorParam)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.mmp.TensorParam)
    MergeFrom(*source);
  }
}

void TensorParam::MergeFrom(const TensorParam& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.mmp.TensorParam)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.feature_name().size() > 0) {

    feature_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.feature_name_);
  }
  if (from.input_name().size() > 0) {

    input_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.input_name_);
  }
  if (from.feature_group().size() > 0) {

    feature_group_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.feature_group_);
  }
  if (from.feature_type() != 0) {
    set_feature_type(from.feature_type());
  }
  if (from.dtype() != 0) {
    set_dtype(from.dtype());
  }
  if (from.dsize() != 0) {
    set_dsize(from.dsize());
  }
  switch (from.default_case()) {
    case kIntVal: {
      set_int_val(from.int_val());
      break;
    }
    case kFloatVal: {
      set_float_val(from.float_val());
      break;
    }
    case kStringVal: {
      set_string_val(from.string_val());
      break;
    }
    case DEFAULT_NOT_SET: {
      break;
    }
  }
}

void TensorParam::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.mmp.TensorParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TensorParam::CopyFrom(const TensorParam& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.mmp.TensorParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TensorParam::IsInitialized() const {
  return true;
}

void TensorParam::Swap(TensorParam* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TensorParam::InternalSwap(TensorParam* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  feature_name_.Swap(&other->feature_name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  input_name_.Swap(&other->input_name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  feature_group_.Swap(&other->feature_group_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(feature_type_, other->feature_type_);
  swap(dtype_, other->dtype_);
  swap(dsize_, other->dsize_);
  swap(default_, other->default_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata TensorParam::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void TensorParamList::InitAsDefaultInstance() {
}
class TensorParamList::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TensorParamList::kTensorParamFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TensorParamList::TensorParamList()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.mmp.TensorParamList)
}
TensorParamList::TensorParamList(const TensorParamList& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      tensor_param_(from.tensor_param_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.mmp.TensorParamList)
}

void TensorParamList::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TensorParamList_proto_2frecommend_5fmmp_5fparam_2eproto.base);
}

TensorParamList::~TensorParamList() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.mmp.TensorParamList)
  SharedDtor();
}

void TensorParamList::SharedDtor() {
}

void TensorParamList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TensorParamList& TensorParamList::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TensorParamList_proto_2frecommend_5fmmp_5fparam_2eproto.base);
  return *internal_default_instance();
}


void TensorParamList::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.mmp.TensorParamList)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  tensor_param_.Clear();
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* TensorParamList::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .abc.recommend_plt.mmp.TensorParam tensor_param = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(add_tensor_param(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 10);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool TensorParamList::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.mmp.TensorParamList)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .abc.recommend_plt.mmp.TensorParam tensor_param = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
                input, add_tensor_param()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.mmp.TensorParamList)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.mmp.TensorParamList)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void TensorParamList::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.mmp.TensorParamList)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.mmp.TensorParam tensor_param = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->tensor_param_size()); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->tensor_param(static_cast<int>(i)),
      output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.mmp.TensorParamList)
}

::PROTOBUF_NAMESPACE_ID::uint8* TensorParamList::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.mmp.TensorParamList)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.mmp.TensorParam tensor_param = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->tensor_param_size()); i < n; i++) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->tensor_param(static_cast<int>(i)), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.mmp.TensorParamList)
  return target;
}

size_t TensorParamList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.mmp.TensorParamList)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.mmp.TensorParam tensor_param = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->tensor_param_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          this->tensor_param(static_cast<int>(i)));
    }
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TensorParamList::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.mmp.TensorParamList)
  GOOGLE_DCHECK_NE(&from, this);
  const TensorParamList* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TensorParamList>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.mmp.TensorParamList)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.mmp.TensorParamList)
    MergeFrom(*source);
  }
}

void TensorParamList::MergeFrom(const TensorParamList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.mmp.TensorParamList)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  tensor_param_.MergeFrom(from.tensor_param_);
}

void TensorParamList::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.mmp.TensorParamList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TensorParamList::CopyFrom(const TensorParamList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.mmp.TensorParamList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TensorParamList::IsInitialized() const {
  return true;
}

void TensorParamList::Swap(TensorParamList* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TensorParamList::InternalSwap(TensorParamList* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  CastToBase(&tensor_param_)->InternalSwap(CastToBase(&other->tensor_param_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TensorParamList::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void TFModelParam_ObjectParam::InitAsDefaultInstance() {
}
class TFModelParam_ObjectParam::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TFModelParam_ObjectParam::kWeightFieldNumber;
const int TFModelParam_ObjectParam::kBiasFieldNumber;
const int TFModelParam_ObjectParam::kExponentFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TFModelParam_ObjectParam::TFModelParam_ObjectParam()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.mmp.TFModelParam.ObjectParam)
}
TFModelParam_ObjectParam::TFModelParam_ObjectParam(const TFModelParam_ObjectParam& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&weight_, &from.weight_,
    static_cast<size_t>(reinterpret_cast<char*>(&exponent_) -
    reinterpret_cast<char*>(&weight_)) + sizeof(exponent_));
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.mmp.TFModelParam.ObjectParam)
}

void TFModelParam_ObjectParam::SharedCtor() {
  ::memset(&weight_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&exponent_) -
      reinterpret_cast<char*>(&weight_)) + sizeof(exponent_));
}

TFModelParam_ObjectParam::~TFModelParam_ObjectParam() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.mmp.TFModelParam.ObjectParam)
  SharedDtor();
}

void TFModelParam_ObjectParam::SharedDtor() {
}

void TFModelParam_ObjectParam::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TFModelParam_ObjectParam& TFModelParam_ObjectParam::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TFModelParam_ObjectParam_proto_2frecommend_5fmmp_5fparam_2eproto.base);
  return *internal_default_instance();
}


void TFModelParam_ObjectParam::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.mmp.TFModelParam.ObjectParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&weight_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&exponent_) -
      reinterpret_cast<char*>(&weight_)) + sizeof(exponent_));
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* TFModelParam_ObjectParam::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // float weight = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          weight_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float bias = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 21)) {
          bias_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float exponent = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          exponent_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool TFModelParam_ObjectParam::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.mmp.TFModelParam.ObjectParam)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // float weight = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (13 & 0xFF)) {

          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   float, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &weight_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float bias = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (21 & 0xFF)) {

          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   float, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &bias_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float exponent = 3;
      case 3: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (29 & 0xFF)) {

          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   float, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &exponent_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.mmp.TFModelParam.ObjectParam)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.mmp.TFModelParam.ObjectParam)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void TFModelParam_ObjectParam::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.mmp.TFModelParam.ObjectParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float weight = 1;
  if (!(this->weight() <= 0 && this->weight() >= 0)) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloat(1, this->weight(), output);
  }

  // float bias = 2;
  if (!(this->bias() <= 0 && this->bias() >= 0)) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloat(2, this->bias(), output);
  }

  // float exponent = 3;
  if (!(this->exponent() <= 0 && this->exponent() >= 0)) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloat(3, this->exponent(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.mmp.TFModelParam.ObjectParam)
}

::PROTOBUF_NAMESPACE_ID::uint8* TFModelParam_ObjectParam::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.mmp.TFModelParam.ObjectParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float weight = 1;
  if (!(this->weight() <= 0 && this->weight() >= 0)) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->weight(), target);
  }

  // float bias = 2;
  if (!(this->bias() <= 0 && this->bias() >= 0)) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->bias(), target);
  }

  // float exponent = 3;
  if (!(this->exponent() <= 0 && this->exponent() >= 0)) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->exponent(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.mmp.TFModelParam.ObjectParam)
  return target;
}

size_t TFModelParam_ObjectParam::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.mmp.TFModelParam.ObjectParam)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float weight = 1;
  if (!(this->weight() <= 0 && this->weight() >= 0)) {
    total_size += 1 + 4;
  }

  // float bias = 2;
  if (!(this->bias() <= 0 && this->bias() >= 0)) {
    total_size += 1 + 4;
  }

  // float exponent = 3;
  if (!(this->exponent() <= 0 && this->exponent() >= 0)) {
    total_size += 1 + 4;
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TFModelParam_ObjectParam::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.mmp.TFModelParam.ObjectParam)
  GOOGLE_DCHECK_NE(&from, this);
  const TFModelParam_ObjectParam* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TFModelParam_ObjectParam>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.mmp.TFModelParam.ObjectParam)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.mmp.TFModelParam.ObjectParam)
    MergeFrom(*source);
  }
}

void TFModelParam_ObjectParam::MergeFrom(const TFModelParam_ObjectParam& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.mmp.TFModelParam.ObjectParam)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from.weight() <= 0 && from.weight() >= 0)) {
    set_weight(from.weight());
  }
  if (!(from.bias() <= 0 && from.bias() >= 0)) {
    set_bias(from.bias());
  }
  if (!(from.exponent() <= 0 && from.exponent() >= 0)) {
    set_exponent(from.exponent());
  }
}

void TFModelParam_ObjectParam::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.mmp.TFModelParam.ObjectParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TFModelParam_ObjectParam::CopyFrom(const TFModelParam_ObjectParam& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.mmp.TFModelParam.ObjectParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TFModelParam_ObjectParam::IsInitialized() const {
  return true;
}

void TFModelParam_ObjectParam::Swap(TFModelParam_ObjectParam* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TFModelParam_ObjectParam::InternalSwap(TFModelParam_ObjectParam* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(weight_, other->weight_);
  swap(bias_, other->bias_);
  swap(exponent_, other->exponent_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TFModelParam_ObjectParam::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

TFModelParam_ObjectParamsEntry_DoNotUse::TFModelParam_ObjectParamsEntry_DoNotUse() {}
TFModelParam_ObjectParamsEntry_DoNotUse::TFModelParam_ObjectParamsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TFModelParam_ObjectParamsEntry_DoNotUse::MergeFrom(const TFModelParam_ObjectParamsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TFModelParam_ObjectParamsEntry_DoNotUse::GetMetadata() const {
  return GetMetadataStatic();
}
void TFModelParam_ObjectParamsEntry_DoNotUse::MergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::Message& other) {
  ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom(other);
}


// ===================================================================

void TFModelParam::InitAsDefaultInstance() {
  ::abc::recommend_plt::mmp::_TFModelParam_default_instance_._instance.get_mutable()->tensor_param_list_ = const_cast< ::abc::recommend_plt::mmp::TensorParamList*>(
      ::abc::recommend_plt::mmp::TensorParamList::internal_default_instance());
}
class TFModelParam::HasBitSetters {
 public:
  static const ::abc::recommend_plt::mmp::TensorParamList& tensor_param_list(const TFModelParam* msg);
};

const ::abc::recommend_plt::mmp::TensorParamList&
TFModelParam::HasBitSetters::tensor_param_list(const TFModelParam* msg) {
  return *msg->tensor_param_list_;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TFModelParam::kModelIdFieldNumber;
const int TFModelParam::kFeatureGroupFieldNumber;
const int TFModelParam::kTensorParamListFieldNumber;
const int TFModelParam::kObjectParamsFieldNumber;
const int TFModelParam::kScoreCoefFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TFModelParam::TFModelParam()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.mmp.TFModelParam)
}
TFModelParam::TFModelParam(const TFModelParam& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      feature_group_(from.feature_group_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  object_params_.MergeFrom(from.object_params_);
  model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.model_id().size() > 0) {
    model_id_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.model_id_);
  }
  if (from.has_tensor_param_list()) {
    tensor_param_list_ = new ::abc::recommend_plt::mmp::TensorParamList(*from.tensor_param_list_);
  } else {
    tensor_param_list_ = nullptr;
  }
  score_coef_ = from.score_coef_;
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.mmp.TFModelParam)
}

void TFModelParam::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TFModelParam_proto_2frecommend_5fmmp_5fparam_2eproto.base);
  model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&tensor_param_list_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&score_coef_) -
      reinterpret_cast<char*>(&tensor_param_list_)) + sizeof(score_coef_));
}

TFModelParam::~TFModelParam() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.mmp.TFModelParam)
  SharedDtor();
}

void TFModelParam::SharedDtor() {
  model_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete tensor_param_list_;
}

void TFModelParam::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TFModelParam& TFModelParam::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TFModelParam_proto_2frecommend_5fmmp_5fparam_2eproto.base);
  return *internal_default_instance();
}


void TFModelParam::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.mmp.TFModelParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  feature_group_.Clear();
  object_params_.Clear();
  model_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == nullptr && tensor_param_list_ != nullptr) {
    delete tensor_param_list_;
  }
  tensor_param_list_ = nullptr;
  score_coef_ = 0;
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* TFModelParam::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string model_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_model_id(), ptr, ctx, "abc.recommend_plt.mmp.TFModelParam.model_id");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated string feature_group = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(add_feature_group(), ptr, ctx, "abc.recommend_plt.mmp.TFModelParam.feature_group");
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 18);
        } else goto handle_unusual;
        continue;
      // .abc.recommend_plt.mmp.TensorParamList tensor_param_list = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(mutable_tensor_param_list(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // map<string, .abc.recommend_plt.mmp.TFModelParam.ObjectParam> object_params = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&object_params_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 34);
        } else goto handle_unusual;
        continue;
      // int32 score_coef = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          score_coef_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool TFModelParam::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.mmp.TFModelParam)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string model_id = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_model_id()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->model_id().data(), static_cast<int>(this->model_id().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.mmp.TFModelParam.model_id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string feature_group = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->add_feature_group()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->feature_group(this->feature_group_size() - 1).data(),
            static_cast<int>(this->feature_group(this->feature_group_size() - 1).length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.mmp.TFModelParam.feature_group"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .abc.recommend_plt.mmp.TensorParamList tensor_param_list = 3;
      case 3: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (26 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_tensor_param_list()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, .abc.recommend_plt.mmp.TFModelParam.ObjectParam> object_params = 4;
      case 4: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (34 & 0xFF)) {
          TFModelParam_ObjectParamsEntry_DoNotUse::Parser< ::PROTOBUF_NAMESPACE_ID::internal::MapField<
              TFModelParam_ObjectParamsEntry_DoNotUse,
              std::string, ::abc::recommend_plt::mmp::TFModelParam_ObjectParam,
              ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
              ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::mmp::TFModelParam_ObjectParam > > parser(&object_params_);
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.mmp.TFModelParam.ObjectParamsEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 score_coef = 5;
      case 5: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (40 & 0xFF)) {

          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32>(
                 input, &score_coef_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.mmp.TFModelParam)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.mmp.TFModelParam)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void TFModelParam::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.mmp.TFModelParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string model_id = 1;
  if (this->model_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->model_id().data(), static_cast<int>(this->model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.TFModelParam.model_id");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->model_id(), output);
  }

  // repeated string feature_group = 2;
  for (int i = 0, n = this->feature_group_size(); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->feature_group(i).data(), static_cast<int>(this->feature_group(i).length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.TFModelParam.feature_group");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteString(
      2, this->feature_group(i), output);
  }

  // .abc.recommend_plt.mmp.TensorParamList tensor_param_list = 3;
  if (this->has_tensor_param_list()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, HasBitSetters::tensor_param_list(this), output);
  }

  // map<string, .abc.recommend_plt.mmp.TFModelParam.ObjectParam> object_params = 4;
  if (!this->object_params().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::mmp::TFModelParam_ObjectParam >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.mmp.TFModelParam.ObjectParamsEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->object_params().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->object_params().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::mmp::TFModelParam_ObjectParam >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::mmp::TFModelParam_ObjectParam >::const_iterator
          it = this->object_params().begin();
          it != this->object_params().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        TFModelParam_ObjectParamsEntry_DoNotUse::MapEntryWrapper entry(nullptr, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second);
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(4, entry, output);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::mmp::TFModelParam_ObjectParam >::const_iterator
          it = this->object_params().begin();
          it != this->object_params().end(); ++it) {
        TFModelParam_ObjectParamsEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(4, entry, output);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // int32 score_coef = 5;
  if (this->score_coef() != 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32(5, this->score_coef(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.mmp.TFModelParam)
}

::PROTOBUF_NAMESPACE_ID::uint8* TFModelParam::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.mmp.TFModelParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string model_id = 1;
  if (this->model_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->model_id().data(), static_cast<int>(this->model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.TFModelParam.model_id");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        1, this->model_id(), target);
  }

  // repeated string feature_group = 2;
  for (int i = 0, n = this->feature_group_size(); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->feature_group(i).data(), static_cast<int>(this->feature_group(i).length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.TFModelParam.feature_group");
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      WriteStringToArray(2, this->feature_group(i), target);
  }

  // .abc.recommend_plt.mmp.TensorParamList tensor_param_list = 3;
  if (this->has_tensor_param_list()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, HasBitSetters::tensor_param_list(this), target);
  }

  // map<string, .abc.recommend_plt.mmp.TFModelParam.ObjectParam> object_params = 4;
  if (!this->object_params().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::mmp::TFModelParam_ObjectParam >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.mmp.TFModelParam.ObjectParamsEntry.key");
      }
    };

    if (false &&
        this->object_params().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->object_params().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::mmp::TFModelParam_ObjectParam >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::mmp::TFModelParam_ObjectParam >::const_iterator
          it = this->object_params().begin();
          it != this->object_params().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        TFModelParam_ObjectParamsEntry_DoNotUse::MapEntryWrapper entry(nullptr, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second);
        target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::InternalWriteMessageNoVirtualToArray(4, entry, target);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::mmp::TFModelParam_ObjectParam >::const_iterator
          it = this->object_params().begin();
          it != this->object_params().end(); ++it) {
        TFModelParam_ObjectParamsEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
        target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::InternalWriteMessageNoVirtualToArray(4, entry, target);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // int32 score_coef = 5;
  if (this->score_coef() != 0) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(5, this->score_coef(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.mmp.TFModelParam)
  return target;
}

size_t TFModelParam::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.mmp.TFModelParam)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string feature_group = 2;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->feature_group_size());
  for (int i = 0, n = this->feature_group_size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      this->feature_group(i));
  }

  // map<string, .abc.recommend_plt.mmp.TFModelParam.ObjectParam> object_params = 4;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->object_params_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::mmp::TFModelParam_ObjectParam >::const_iterator
      it = this->object_params().begin();
      it != this->object_params().end(); ++it) {
    TFModelParam_ObjectParamsEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        MessageSizeNoVirtual(entry);
  }

  // string model_id = 1;
  if (this->model_id().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->model_id());
  }

  // .abc.recommend_plt.mmp.TensorParamList tensor_param_list = 3;
  if (this->has_tensor_param_list()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *tensor_param_list_);
  }

  // int32 score_coef = 5;
  if (this->score_coef() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->score_coef());
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TFModelParam::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.mmp.TFModelParam)
  GOOGLE_DCHECK_NE(&from, this);
  const TFModelParam* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TFModelParam>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.mmp.TFModelParam)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.mmp.TFModelParam)
    MergeFrom(*source);
  }
}

void TFModelParam::MergeFrom(const TFModelParam& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.mmp.TFModelParam)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  feature_group_.MergeFrom(from.feature_group_);
  object_params_.MergeFrom(from.object_params_);
  if (from.model_id().size() > 0) {

    model_id_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.model_id_);
  }
  if (from.has_tensor_param_list()) {
    mutable_tensor_param_list()->::abc::recommend_plt::mmp::TensorParamList::MergeFrom(from.tensor_param_list());
  }
  if (from.score_coef() != 0) {
    set_score_coef(from.score_coef());
  }
}

void TFModelParam::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.mmp.TFModelParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TFModelParam::CopyFrom(const TFModelParam& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.mmp.TFModelParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TFModelParam::IsInitialized() const {
  return true;
}

void TFModelParam::Swap(TFModelParam* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TFModelParam::InternalSwap(TFModelParam* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  feature_group_.InternalSwap(CastToBase(&other->feature_group_));
  object_params_.Swap(&other->object_params_);
  model_id_.Swap(&other->model_id_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(tensor_param_list_, other->tensor_param_list_);
  swap(score_coef_, other->score_coef_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TFModelParam::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void TFModelParamList::InitAsDefaultInstance() {
}
class TFModelParamList::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TFModelParamList::kTfModelParamFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TFModelParamList::TFModelParamList()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.mmp.TFModelParamList)
}
TFModelParamList::TFModelParamList(const TFModelParamList& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      tf_model_param_(from.tf_model_param_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.mmp.TFModelParamList)
}

void TFModelParamList::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TFModelParamList_proto_2frecommend_5fmmp_5fparam_2eproto.base);
}

TFModelParamList::~TFModelParamList() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.mmp.TFModelParamList)
  SharedDtor();
}

void TFModelParamList::SharedDtor() {
}

void TFModelParamList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TFModelParamList& TFModelParamList::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TFModelParamList_proto_2frecommend_5fmmp_5fparam_2eproto.base);
  return *internal_default_instance();
}


void TFModelParamList::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.mmp.TFModelParamList)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  tf_model_param_.Clear();
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* TFModelParamList::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .abc.recommend_plt.mmp.TFModelParam tf_model_param = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(add_tf_model_param(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 10);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool TFModelParamList::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.mmp.TFModelParamList)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .abc.recommend_plt.mmp.TFModelParam tf_model_param = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
                input, add_tf_model_param()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.mmp.TFModelParamList)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.mmp.TFModelParamList)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void TFModelParamList::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.mmp.TFModelParamList)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.mmp.TFModelParam tf_model_param = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->tf_model_param_size()); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->tf_model_param(static_cast<int>(i)),
      output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.mmp.TFModelParamList)
}

::PROTOBUF_NAMESPACE_ID::uint8* TFModelParamList::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.mmp.TFModelParamList)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.mmp.TFModelParam tf_model_param = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->tf_model_param_size()); i < n; i++) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->tf_model_param(static_cast<int>(i)), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.mmp.TFModelParamList)
  return target;
}

size_t TFModelParamList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.mmp.TFModelParamList)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.mmp.TFModelParam tf_model_param = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->tf_model_param_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          this->tf_model_param(static_cast<int>(i)));
    }
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TFModelParamList::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.mmp.TFModelParamList)
  GOOGLE_DCHECK_NE(&from, this);
  const TFModelParamList* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TFModelParamList>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.mmp.TFModelParamList)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.mmp.TFModelParamList)
    MergeFrom(*source);
  }
}

void TFModelParamList::MergeFrom(const TFModelParamList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.mmp.TFModelParamList)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  tf_model_param_.MergeFrom(from.tf_model_param_);
}

void TFModelParamList::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.mmp.TFModelParamList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TFModelParamList::CopyFrom(const TFModelParamList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.mmp.TFModelParamList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TFModelParamList::IsInitialized() const {
  return true;
}

void TFModelParamList::Swap(TFModelParamList* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TFModelParamList::InternalSwap(TFModelParamList* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  CastToBase(&tf_model_param_)->InternalSwap(CastToBase(&other->tf_model_param_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TFModelParamList::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void FuseParam_OutputParam::InitAsDefaultInstance() {
}
class FuseParam_OutputParam::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FuseParam_OutputParam::kNameFieldNumber;
const int FuseParam_OutputParam::kWeightFieldNumber;
const int FuseParam_OutputParam::kBiasFieldNumber;
const int FuseParam_OutputParam::kExponentFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FuseParam_OutputParam::FuseParam_OutputParam()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.mmp.FuseParam.OutputParam)
}
FuseParam_OutputParam::FuseParam_OutputParam(const FuseParam_OutputParam& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  ::memcpy(&weight_, &from.weight_,
    static_cast<size_t>(reinterpret_cast<char*>(&exponent_) -
    reinterpret_cast<char*>(&weight_)) + sizeof(exponent_));
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.mmp.FuseParam.OutputParam)
}

void FuseParam_OutputParam::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_FuseParam_OutputParam_proto_2frecommend_5fmmp_5fparam_2eproto.base);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&weight_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&exponent_) -
      reinterpret_cast<char*>(&weight_)) + sizeof(exponent_));
}

FuseParam_OutputParam::~FuseParam_OutputParam() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.mmp.FuseParam.OutputParam)
  SharedDtor();
}

void FuseParam_OutputParam::SharedDtor() {
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void FuseParam_OutputParam::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const FuseParam_OutputParam& FuseParam_OutputParam::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_FuseParam_OutputParam_proto_2frecommend_5fmmp_5fparam_2eproto.base);
  return *internal_default_instance();
}


void FuseParam_OutputParam::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.mmp.FuseParam.OutputParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&weight_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&exponent_) -
      reinterpret_cast<char*>(&weight_)) + sizeof(exponent_));
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* FuseParam_OutputParam::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_name(), ptr, ctx, "abc.recommend_plt.mmp.FuseParam.OutputParam.name");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float weight = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 21)) {
          weight_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float bias = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          bias_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float exponent = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 37)) {
          exponent_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool FuseParam_OutputParam::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.mmp.FuseParam.OutputParam)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.mmp.FuseParam.OutputParam.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float weight = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (21 & 0xFF)) {

          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   float, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &weight_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float bias = 3;
      case 3: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (29 & 0xFF)) {

          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   float, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &bias_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float exponent = 4;
      case 4: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (37 & 0xFF)) {

          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   float, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &exponent_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.mmp.FuseParam.OutputParam)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.mmp.FuseParam.OutputParam)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void FuseParam_OutputParam::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.mmp.FuseParam.OutputParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.FuseParam.OutputParam.name");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // float weight = 2;
  if (!(this->weight() <= 0 && this->weight() >= 0)) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloat(2, this->weight(), output);
  }

  // float bias = 3;
  if (!(this->bias() <= 0 && this->bias() >= 0)) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloat(3, this->bias(), output);
  }

  // float exponent = 4;
  if (!(this->exponent() <= 0 && this->exponent() >= 0)) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloat(4, this->exponent(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.mmp.FuseParam.OutputParam)
}

::PROTOBUF_NAMESPACE_ID::uint8* FuseParam_OutputParam::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.mmp.FuseParam.OutputParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.FuseParam.OutputParam.name");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // float weight = 2;
  if (!(this->weight() <= 0 && this->weight() >= 0)) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->weight(), target);
  }

  // float bias = 3;
  if (!(this->bias() <= 0 && this->bias() >= 0)) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->bias(), target);
  }

  // float exponent = 4;
  if (!(this->exponent() <= 0 && this->exponent() >= 0)) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->exponent(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.mmp.FuseParam.OutputParam)
  return target;
}

size_t FuseParam_OutputParam::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.mmp.FuseParam.OutputParam)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->name());
  }

  // float weight = 2;
  if (!(this->weight() <= 0 && this->weight() >= 0)) {
    total_size += 1 + 4;
  }

  // float bias = 3;
  if (!(this->bias() <= 0 && this->bias() >= 0)) {
    total_size += 1 + 4;
  }

  // float exponent = 4;
  if (!(this->exponent() <= 0 && this->exponent() >= 0)) {
    total_size += 1 + 4;
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void FuseParam_OutputParam::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.mmp.FuseParam.OutputParam)
  GOOGLE_DCHECK_NE(&from, this);
  const FuseParam_OutputParam* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<FuseParam_OutputParam>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.mmp.FuseParam.OutputParam)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.mmp.FuseParam.OutputParam)
    MergeFrom(*source);
  }
}

void FuseParam_OutputParam::MergeFrom(const FuseParam_OutputParam& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.mmp.FuseParam.OutputParam)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (!(from.weight() <= 0 && from.weight() >= 0)) {
    set_weight(from.weight());
  }
  if (!(from.bias() <= 0 && from.bias() >= 0)) {
    set_bias(from.bias());
  }
  if (!(from.exponent() <= 0 && from.exponent() >= 0)) {
    set_exponent(from.exponent());
  }
}

void FuseParam_OutputParam::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.mmp.FuseParam.OutputParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FuseParam_OutputParam::CopyFrom(const FuseParam_OutputParam& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.mmp.FuseParam.OutputParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FuseParam_OutputParam::IsInitialized() const {
  return true;
}

void FuseParam_OutputParam::Swap(FuseParam_OutputParam* other) {
  if (other == this) return;
  InternalSwap(other);
}
void FuseParam_OutputParam::InternalSwap(FuseParam_OutputParam* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  name_.Swap(&other->name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(weight_, other->weight_);
  swap(bias_, other->bias_);
  swap(exponent_, other->exponent_);
}

::PROTOBUF_NAMESPACE_ID::Metadata FuseParam_OutputParam::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void FuseParam::InitAsDefaultInstance() {
}
class FuseParam::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FuseParam::kModelIdFieldNumber;
const int FuseParam::kModelSiteUidFieldNumber;
const int FuseParam::kFeaSiteUidFieldNumber;
const int FuseParam::kOutputParamsFieldNumber;
const int FuseParam::kOperFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FuseParam::FuseParam()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.mmp.FuseParam)
}
FuseParam::FuseParam(const FuseParam& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      output_params_(from.output_params_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.model_id().size() > 0) {
    model_id_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.model_id_);
  }
  model_site_uid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.model_site_uid().size() > 0) {
    model_site_uid_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.model_site_uid_);
  }
  fea_site_uid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.fea_site_uid().size() > 0) {
    fea_site_uid_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.fea_site_uid_);
  }
  oper_ = from.oper_;
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.mmp.FuseParam)
}

void FuseParam::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_FuseParam_proto_2frecommend_5fmmp_5fparam_2eproto.base);
  model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_site_uid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  fea_site_uid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  oper_ = 0;
}

FuseParam::~FuseParam() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.mmp.FuseParam)
  SharedDtor();
}

void FuseParam::SharedDtor() {
  model_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_site_uid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  fea_site_uid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void FuseParam::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const FuseParam& FuseParam::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_FuseParam_proto_2frecommend_5fmmp_5fparam_2eproto.base);
  return *internal_default_instance();
}


void FuseParam::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.mmp.FuseParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  output_params_.Clear();
  model_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_site_uid_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  fea_site_uid_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  oper_ = 0;
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* FuseParam::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string model_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_model_id(), ptr, ctx, "abc.recommend_plt.mmp.FuseParam.model_id");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string model_site_uid = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_model_site_uid(), ptr, ctx, "abc.recommend_plt.mmp.FuseParam.model_site_uid");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string fea_site_uid = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_fea_site_uid(), ptr, ctx, "abc.recommend_plt.mmp.FuseParam.fea_site_uid");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .abc.recommend_plt.mmp.FuseParam.OutputParam output_params = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(add_output_params(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 34);
        } else goto handle_unusual;
        continue;
      // .abc.recommend_plt.mmp.LogicalOper oper = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
          set_oper(static_cast<::abc::recommend_plt::mmp::LogicalOper>(val));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool FuseParam::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.mmp.FuseParam)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string model_id = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_model_id()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->model_id().data(), static_cast<int>(this->model_id().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.mmp.FuseParam.model_id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string model_site_uid = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_model_site_uid()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->model_site_uid().data(), static_cast<int>(this->model_site_uid().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.mmp.FuseParam.model_site_uid"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string fea_site_uid = 3;
      case 3: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (26 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_fea_site_uid()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->fea_site_uid().data(), static_cast<int>(this->fea_site_uid().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.mmp.FuseParam.fea_site_uid"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .abc.recommend_plt.mmp.FuseParam.OutputParam output_params = 4;
      case 4: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (34 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
                input, add_output_params()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .abc.recommend_plt.mmp.LogicalOper oper = 5;
      case 5: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (40 & 0xFF)) {
          int value = 0;
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   int, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_oper(static_cast< ::abc::recommend_plt::mmp::LogicalOper >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.mmp.FuseParam)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.mmp.FuseParam)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void FuseParam::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.mmp.FuseParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string model_id = 1;
  if (this->model_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->model_id().data(), static_cast<int>(this->model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.FuseParam.model_id");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->model_id(), output);
  }

  // string model_site_uid = 2;
  if (this->model_site_uid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->model_site_uid().data(), static_cast<int>(this->model_site_uid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.FuseParam.model_site_uid");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->model_site_uid(), output);
  }

  // string fea_site_uid = 3;
  if (this->fea_site_uid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->fea_site_uid().data(), static_cast<int>(this->fea_site_uid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.FuseParam.fea_site_uid");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->fea_site_uid(), output);
  }

  // repeated .abc.recommend_plt.mmp.FuseParam.OutputParam output_params = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->output_params_size()); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      4,
      this->output_params(static_cast<int>(i)),
      output);
  }

  // .abc.recommend_plt.mmp.LogicalOper oper = 5;
  if (this->oper() != 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnum(
      5, this->oper(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.mmp.FuseParam)
}

::PROTOBUF_NAMESPACE_ID::uint8* FuseParam::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.mmp.FuseParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string model_id = 1;
  if (this->model_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->model_id().data(), static_cast<int>(this->model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.FuseParam.model_id");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        1, this->model_id(), target);
  }

  // string model_site_uid = 2;
  if (this->model_site_uid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->model_site_uid().data(), static_cast<int>(this->model_site_uid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.FuseParam.model_site_uid");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        2, this->model_site_uid(), target);
  }

  // string fea_site_uid = 3;
  if (this->fea_site_uid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->fea_site_uid().data(), static_cast<int>(this->fea_site_uid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.FuseParam.fea_site_uid");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        3, this->fea_site_uid(), target);
  }

  // repeated .abc.recommend_plt.mmp.FuseParam.OutputParam output_params = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->output_params_size()); i < n; i++) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->output_params(static_cast<int>(i)), target);
  }

  // .abc.recommend_plt.mmp.LogicalOper oper = 5;
  if (this->oper() != 0) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      5, this->oper(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.mmp.FuseParam)
  return target;
}

size_t FuseParam::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.mmp.FuseParam)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.mmp.FuseParam.OutputParam output_params = 4;
  {
    unsigned int count = static_cast<unsigned int>(this->output_params_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          this->output_params(static_cast<int>(i)));
    }
  }

  // string model_id = 1;
  if (this->model_id().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->model_id());
  }

  // string model_site_uid = 2;
  if (this->model_site_uid().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->model_site_uid());
  }

  // string fea_site_uid = 3;
  if (this->fea_site_uid().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->fea_site_uid());
  }

  // .abc.recommend_plt.mmp.LogicalOper oper = 5;
  if (this->oper() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->oper());
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void FuseParam::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.mmp.FuseParam)
  GOOGLE_DCHECK_NE(&from, this);
  const FuseParam* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<FuseParam>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.mmp.FuseParam)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.mmp.FuseParam)
    MergeFrom(*source);
  }
}

void FuseParam::MergeFrom(const FuseParam& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.mmp.FuseParam)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  output_params_.MergeFrom(from.output_params_);
  if (from.model_id().size() > 0) {

    model_id_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.model_id_);
  }
  if (from.model_site_uid().size() > 0) {

    model_site_uid_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.model_site_uid_);
  }
  if (from.fea_site_uid().size() > 0) {

    fea_site_uid_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.fea_site_uid_);
  }
  if (from.oper() != 0) {
    set_oper(from.oper());
  }
}

void FuseParam::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.mmp.FuseParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FuseParam::CopyFrom(const FuseParam& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.mmp.FuseParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FuseParam::IsInitialized() const {
  return true;
}

void FuseParam::Swap(FuseParam* other) {
  if (other == this) return;
  InternalSwap(other);
}
void FuseParam::InternalSwap(FuseParam* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  CastToBase(&output_params_)->InternalSwap(CastToBase(&other->output_params_));
  model_id_.Swap(&other->model_id_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  model_site_uid_.Swap(&other->model_site_uid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  fea_site_uid_.Swap(&other->fea_site_uid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(oper_, other->oper_);
}

::PROTOBUF_NAMESPACE_ID::Metadata FuseParam::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void RankStrategy::InitAsDefaultInstance() {
}
class RankStrategy::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RankStrategy::kFuseParamFieldNumber;
const int RankStrategy::kOperFieldNumber;
const int RankStrategy::kNameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RankStrategy::RankStrategy()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.mmp.RankStrategy)
}
RankStrategy::RankStrategy(const RankStrategy& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      fuse_param_(from.fuse_param_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  oper_ = from.oper_;
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.mmp.RankStrategy)
}

void RankStrategy::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RankStrategy_proto_2frecommend_5fmmp_5fparam_2eproto.base);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  oper_ = 0;
}

RankStrategy::~RankStrategy() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.mmp.RankStrategy)
  SharedDtor();
}

void RankStrategy::SharedDtor() {
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void RankStrategy::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RankStrategy& RankStrategy::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RankStrategy_proto_2frecommend_5fmmp_5fparam_2eproto.base);
  return *internal_default_instance();
}


void RankStrategy::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.mmp.RankStrategy)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  fuse_param_.Clear();
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  oper_ = 0;
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* RankStrategy::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .abc.recommend_plt.mmp.FuseParam fuse_param = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(add_fuse_param(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 10);
        } else goto handle_unusual;
        continue;
      // .abc.recommend_plt.mmp.LogicalOper oper = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
          set_oper(static_cast<::abc::recommend_plt::mmp::LogicalOper>(val));
        } else goto handle_unusual;
        continue;
      // string name = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_name(), ptr, ctx, "abc.recommend_plt.mmp.RankStrategy.name");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool RankStrategy::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.mmp.RankStrategy)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .abc.recommend_plt.mmp.FuseParam fuse_param = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
                input, add_fuse_param()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .abc.recommend_plt.mmp.LogicalOper oper = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (16 & 0xFF)) {
          int value = 0;
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   int, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_oper(static_cast< ::abc::recommend_plt::mmp::LogicalOper >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string name = 3;
      case 3: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (26 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.mmp.RankStrategy.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.mmp.RankStrategy)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.mmp.RankStrategy)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void RankStrategy::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.mmp.RankStrategy)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.mmp.FuseParam fuse_param = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->fuse_param_size()); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->fuse_param(static_cast<int>(i)),
      output);
  }

  // .abc.recommend_plt.mmp.LogicalOper oper = 2;
  if (this->oper() != 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnum(
      2, this->oper(), output);
  }

  // string name = 3;
  if (this->name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.RankStrategy.name");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->name(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.mmp.RankStrategy)
}

::PROTOBUF_NAMESPACE_ID::uint8* RankStrategy::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.mmp.RankStrategy)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.mmp.FuseParam fuse_param = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->fuse_param_size()); i < n; i++) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->fuse_param(static_cast<int>(i)), target);
  }

  // .abc.recommend_plt.mmp.LogicalOper oper = 2;
  if (this->oper() != 0) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->oper(), target);
  }

  // string name = 3;
  if (this->name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.RankStrategy.name");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        3, this->name(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.mmp.RankStrategy)
  return target;
}

size_t RankStrategy::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.mmp.RankStrategy)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.mmp.FuseParam fuse_param = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->fuse_param_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          this->fuse_param(static_cast<int>(i)));
    }
  }

  // string name = 3;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->name());
  }

  // .abc.recommend_plt.mmp.LogicalOper oper = 2;
  if (this->oper() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->oper());
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RankStrategy::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.mmp.RankStrategy)
  GOOGLE_DCHECK_NE(&from, this);
  const RankStrategy* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RankStrategy>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.mmp.RankStrategy)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.mmp.RankStrategy)
    MergeFrom(*source);
  }
}

void RankStrategy::MergeFrom(const RankStrategy& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.mmp.RankStrategy)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  fuse_param_.MergeFrom(from.fuse_param_);
  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.oper() != 0) {
    set_oper(from.oper());
  }
}

void RankStrategy::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.mmp.RankStrategy)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RankStrategy::CopyFrom(const RankStrategy& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.mmp.RankStrategy)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RankStrategy::IsInitialized() const {
  return true;
}

void RankStrategy::Swap(RankStrategy* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RankStrategy::InternalSwap(RankStrategy* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  CastToBase(&fuse_param_)->InternalSwap(CastToBase(&other->fuse_param_));
  name_.Swap(&other->name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(oper_, other->oper_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RankStrategy::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

RankStrategyConfig_ConfEntry_DoNotUse::RankStrategyConfig_ConfEntry_DoNotUse() {}
RankStrategyConfig_ConfEntry_DoNotUse::RankStrategyConfig_ConfEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void RankStrategyConfig_ConfEntry_DoNotUse::MergeFrom(const RankStrategyConfig_ConfEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata RankStrategyConfig_ConfEntry_DoNotUse::GetMetadata() const {
  return GetMetadataStatic();
}
void RankStrategyConfig_ConfEntry_DoNotUse::MergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::Message& other) {
  ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom(other);
}


// ===================================================================

void RankStrategyConfig::InitAsDefaultInstance() {
}
class RankStrategyConfig::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RankStrategyConfig::kConfFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RankStrategyConfig::RankStrategyConfig()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.mmp.RankStrategyConfig)
}
RankStrategyConfig::RankStrategyConfig(const RankStrategyConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  conf_.MergeFrom(from.conf_);
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.mmp.RankStrategyConfig)
}

void RankStrategyConfig::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RankStrategyConfig_proto_2frecommend_5fmmp_5fparam_2eproto.base);
}

RankStrategyConfig::~RankStrategyConfig() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.mmp.RankStrategyConfig)
  SharedDtor();
}

void RankStrategyConfig::SharedDtor() {
}

void RankStrategyConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RankStrategyConfig& RankStrategyConfig::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RankStrategyConfig_proto_2frecommend_5fmmp_5fparam_2eproto.base);
  return *internal_default_instance();
}


void RankStrategyConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.mmp.RankStrategyConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  conf_.Clear();
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* RankStrategyConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // map<string, .abc.recommend_plt.mmp.RankStrategy> conf = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&conf_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 10);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool RankStrategyConfig::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.mmp.RankStrategyConfig)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<string, .abc.recommend_plt.mmp.RankStrategy> conf = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          RankStrategyConfig_ConfEntry_DoNotUse::Parser< ::PROTOBUF_NAMESPACE_ID::internal::MapField<
              RankStrategyConfig_ConfEntry_DoNotUse,
              std::string, ::abc::recommend_plt::mmp::RankStrategy,
              ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
              ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::mmp::RankStrategy > > parser(&conf_);
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.mmp.RankStrategyConfig.ConfEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.mmp.RankStrategyConfig)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.mmp.RankStrategyConfig)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void RankStrategyConfig::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.mmp.RankStrategyConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .abc.recommend_plt.mmp.RankStrategy> conf = 1;
  if (!this->conf().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::mmp::RankStrategy >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.mmp.RankStrategyConfig.ConfEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->conf().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->conf().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::mmp::RankStrategy >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::mmp::RankStrategy >::const_iterator
          it = this->conf().begin();
          it != this->conf().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        RankStrategyConfig_ConfEntry_DoNotUse::MapEntryWrapper entry(nullptr, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second);
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(1, entry, output);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::mmp::RankStrategy >::const_iterator
          it = this->conf().begin();
          it != this->conf().end(); ++it) {
        RankStrategyConfig_ConfEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(1, entry, output);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.mmp.RankStrategyConfig)
}

::PROTOBUF_NAMESPACE_ID::uint8* RankStrategyConfig::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.mmp.RankStrategyConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .abc.recommend_plt.mmp.RankStrategy> conf = 1;
  if (!this->conf().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::mmp::RankStrategy >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "abc.recommend_plt.mmp.RankStrategyConfig.ConfEntry.key");
      }
    };

    if (false &&
        this->conf().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->conf().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::mmp::RankStrategy >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::mmp::RankStrategy >::const_iterator
          it = this->conf().begin();
          it != this->conf().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        RankStrategyConfig_ConfEntry_DoNotUse::MapEntryWrapper entry(nullptr, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second);
        target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::InternalWriteMessageNoVirtualToArray(1, entry, target);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::mmp::RankStrategy >::const_iterator
          it = this->conf().begin();
          it != this->conf().end(); ++it) {
        RankStrategyConfig_ConfEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
        target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::InternalWriteMessageNoVirtualToArray(1, entry, target);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.mmp.RankStrategyConfig)
  return target;
}

size_t RankStrategyConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.mmp.RankStrategyConfig)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, .abc.recommend_plt.mmp.RankStrategy> conf = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->conf_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::mmp::RankStrategy >::const_iterator
      it = this->conf().begin();
      it != this->conf().end(); ++it) {
    RankStrategyConfig_ConfEntry_DoNotUse::MapEntryWrapper entry(nullptr, it->first, it->second);
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        MessageSizeNoVirtual(entry);
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RankStrategyConfig::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.mmp.RankStrategyConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const RankStrategyConfig* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RankStrategyConfig>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.mmp.RankStrategyConfig)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.mmp.RankStrategyConfig)
    MergeFrom(*source);
  }
}

void RankStrategyConfig::MergeFrom(const RankStrategyConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.mmp.RankStrategyConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  conf_.MergeFrom(from.conf_);
}

void RankStrategyConfig::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.mmp.RankStrategyConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RankStrategyConfig::CopyFrom(const RankStrategyConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.mmp.RankStrategyConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RankStrategyConfig::IsInitialized() const {
  return true;
}

void RankStrategyConfig::Swap(RankStrategyConfig* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RankStrategyConfig::InternalSwap(RankStrategyConfig* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  conf_.Swap(&other->conf_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RankStrategyConfig::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void KVModelParam::InitAsDefaultInstance() {
}
class KVModelParam::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int KVModelParam::kModelIdFieldNumber;
const int KVModelParam::kModelNameFieldNumber;
const int KVModelParam::kModelTypeFieldNumber;
const int KVModelParam::kKeyDimsFieldNumber;
const int KVModelParam::kCacheFlagFieldNumber;
const int KVModelParam::kCacheTtlFieldNumber;
const int KVModelParam::kModuleFieldNumber;
const int KVModelParam::kPvcFileFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

KVModelParam::KVModelParam()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.mmp.KVModelParam)
}
KVModelParam::KVModelParam(const KVModelParam& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      key_dims_(from.key_dims_),
      module_(from.module_),
      pvc_file_(from.pvc_file_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.model_id().size() > 0) {
    model_id_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.model_id_);
  }
  model_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.model_name().size() > 0) {
    model_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.model_name_);
  }
  ::memcpy(&model_type_, &from.model_type_,
    static_cast<size_t>(reinterpret_cast<char*>(&cache_ttl_) -
    reinterpret_cast<char*>(&model_type_)) + sizeof(cache_ttl_));
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.mmp.KVModelParam)
}

void KVModelParam::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_KVModelParam_proto_2frecommend_5fmmp_5fparam_2eproto.base);
  model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&model_type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&cache_ttl_) -
      reinterpret_cast<char*>(&model_type_)) + sizeof(cache_ttl_));
}

KVModelParam::~KVModelParam() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.mmp.KVModelParam)
  SharedDtor();
}

void KVModelParam::SharedDtor() {
  model_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void KVModelParam::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const KVModelParam& KVModelParam::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_KVModelParam_proto_2frecommend_5fmmp_5fparam_2eproto.base);
  return *internal_default_instance();
}


void KVModelParam::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.mmp.KVModelParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  key_dims_.Clear();
  module_.Clear();
  pvc_file_.Clear();
  model_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&model_type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&cache_ttl_) -
      reinterpret_cast<char*>(&model_type_)) + sizeof(cache_ttl_));
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* KVModelParam::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string model_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_model_id(), ptr, ctx, "abc.recommend_plt.mmp.KVModelParam.model_id");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string model_name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_model_name(), ptr, ctx, "abc.recommend_plt.mmp.KVModelParam.model_name");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 model_type = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          model_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated string key_dims = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(add_key_dims(), ptr, ctx, "abc.recommend_plt.mmp.KVModelParam.key_dims");
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 34);
        } else goto handle_unusual;
        continue;
      // bool cache_flag = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          cache_flag_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 cache_ttl = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          cache_ttl_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated string module = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(add_module(), ptr, ctx, "abc.recommend_plt.mmp.KVModelParam.module");
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 58);
        } else goto handle_unusual;
        continue;
      // repeated string pvc_file = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(add_pvc_file(), ptr, ctx, "abc.recommend_plt.mmp.KVModelParam.pvc_file");
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 66);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool KVModelParam::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.mmp.KVModelParam)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string model_id = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_model_id()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->model_id().data(), static_cast<int>(this->model_id().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.mmp.KVModelParam.model_id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string model_name = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_model_name()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->model_name().data(), static_cast<int>(this->model_name().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.mmp.KVModelParam.model_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 model_type = 3;
      case 3: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (24 & 0xFF)) {

          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32>(
                 input, &model_type_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string key_dims = 4;
      case 4: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (34 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->add_key_dims()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->key_dims(this->key_dims_size() - 1).data(),
            static_cast<int>(this->key_dims(this->key_dims_size() - 1).length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.mmp.KVModelParam.key_dims"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool cache_flag = 5;
      case 5: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (40 & 0xFF)) {

          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   bool, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BOOL>(
                 input, &cache_flag_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 cache_ttl = 6;
      case 6: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (48 & 0xFF)) {

          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32>(
                 input, &cache_ttl_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string module = 7;
      case 7: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (58 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->add_module()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->module(this->module_size() - 1).data(),
            static_cast<int>(this->module(this->module_size() - 1).length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.mmp.KVModelParam.module"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string pvc_file = 8;
      case 8: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (66 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->add_pvc_file()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->pvc_file(this->pvc_file_size() - 1).data(),
            static_cast<int>(this->pvc_file(this->pvc_file_size() - 1).length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.mmp.KVModelParam.pvc_file"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.mmp.KVModelParam)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.mmp.KVModelParam)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void KVModelParam::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.mmp.KVModelParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string model_id = 1;
  if (this->model_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->model_id().data(), static_cast<int>(this->model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.KVModelParam.model_id");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->model_id(), output);
  }

  // string model_name = 2;
  if (this->model_name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->model_name().data(), static_cast<int>(this->model_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.KVModelParam.model_name");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->model_name(), output);
  }

  // int32 model_type = 3;
  if (this->model_type() != 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32(3, this->model_type(), output);
  }

  // repeated string key_dims = 4;
  for (int i = 0, n = this->key_dims_size(); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->key_dims(i).data(), static_cast<int>(this->key_dims(i).length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.KVModelParam.key_dims");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteString(
      4, this->key_dims(i), output);
  }

  // bool cache_flag = 5;
  if (this->cache_flag() != 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBool(5, this->cache_flag(), output);
  }

  // int32 cache_ttl = 6;
  if (this->cache_ttl() != 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32(6, this->cache_ttl(), output);
  }

  // repeated string module = 7;
  for (int i = 0, n = this->module_size(); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->module(i).data(), static_cast<int>(this->module(i).length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.KVModelParam.module");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteString(
      7, this->module(i), output);
  }

  // repeated string pvc_file = 8;
  for (int i = 0, n = this->pvc_file_size(); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->pvc_file(i).data(), static_cast<int>(this->pvc_file(i).length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.KVModelParam.pvc_file");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteString(
      8, this->pvc_file(i), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.mmp.KVModelParam)
}

::PROTOBUF_NAMESPACE_ID::uint8* KVModelParam::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.mmp.KVModelParam)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string model_id = 1;
  if (this->model_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->model_id().data(), static_cast<int>(this->model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.KVModelParam.model_id");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        1, this->model_id(), target);
  }

  // string model_name = 2;
  if (this->model_name().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->model_name().data(), static_cast<int>(this->model_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.KVModelParam.model_name");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        2, this->model_name(), target);
  }

  // int32 model_type = 3;
  if (this->model_type() != 0) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->model_type(), target);
  }

  // repeated string key_dims = 4;
  for (int i = 0, n = this->key_dims_size(); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->key_dims(i).data(), static_cast<int>(this->key_dims(i).length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.KVModelParam.key_dims");
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      WriteStringToArray(4, this->key_dims(i), target);
  }

  // bool cache_flag = 5;
  if (this->cache_flag() != 0) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(5, this->cache_flag(), target);
  }

  // int32 cache_ttl = 6;
  if (this->cache_ttl() != 0) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(6, this->cache_ttl(), target);
  }

  // repeated string module = 7;
  for (int i = 0, n = this->module_size(); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->module(i).data(), static_cast<int>(this->module(i).length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.KVModelParam.module");
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      WriteStringToArray(7, this->module(i), target);
  }

  // repeated string pvc_file = 8;
  for (int i = 0, n = this->pvc_file_size(); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->pvc_file(i).data(), static_cast<int>(this->pvc_file(i).length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.mmp.KVModelParam.pvc_file");
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      WriteStringToArray(8, this->pvc_file(i), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.mmp.KVModelParam)
  return target;
}

size_t KVModelParam::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.mmp.KVModelParam)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string key_dims = 4;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->key_dims_size());
  for (int i = 0, n = this->key_dims_size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      this->key_dims(i));
  }

  // repeated string module = 7;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->module_size());
  for (int i = 0, n = this->module_size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      this->module(i));
  }

  // repeated string pvc_file = 8;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->pvc_file_size());
  for (int i = 0, n = this->pvc_file_size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      this->pvc_file(i));
  }

  // string model_id = 1;
  if (this->model_id().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->model_id());
  }

  // string model_name = 2;
  if (this->model_name().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->model_name());
  }

  // int32 model_type = 3;
  if (this->model_type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->model_type());
  }

  // bool cache_flag = 5;
  if (this->cache_flag() != 0) {
    total_size += 1 + 1;
  }

  // int32 cache_ttl = 6;
  if (this->cache_ttl() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->cache_ttl());
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void KVModelParam::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.mmp.KVModelParam)
  GOOGLE_DCHECK_NE(&from, this);
  const KVModelParam* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<KVModelParam>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.mmp.KVModelParam)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.mmp.KVModelParam)
    MergeFrom(*source);
  }
}

void KVModelParam::MergeFrom(const KVModelParam& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.mmp.KVModelParam)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  key_dims_.MergeFrom(from.key_dims_);
  module_.MergeFrom(from.module_);
  pvc_file_.MergeFrom(from.pvc_file_);
  if (from.model_id().size() > 0) {

    model_id_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.model_id_);
  }
  if (from.model_name().size() > 0) {

    model_name_.AssignWithDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.model_name_);
  }
  if (from.model_type() != 0) {
    set_model_type(from.model_type());
  }
  if (from.cache_flag() != 0) {
    set_cache_flag(from.cache_flag());
  }
  if (from.cache_ttl() != 0) {
    set_cache_ttl(from.cache_ttl());
  }
}

void KVModelParam::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.mmp.KVModelParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void KVModelParam::CopyFrom(const KVModelParam& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.mmp.KVModelParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool KVModelParam::IsInitialized() const {
  return true;
}

void KVModelParam::Swap(KVModelParam* other) {
  if (other == this) return;
  InternalSwap(other);
}
void KVModelParam::InternalSwap(KVModelParam* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  key_dims_.InternalSwap(CastToBase(&other->key_dims_));
  module_.InternalSwap(CastToBase(&other->module_));
  pvc_file_.InternalSwap(CastToBase(&other->pvc_file_));
  model_id_.Swap(&other->model_id_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  model_name_.Swap(&other->model_name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(model_type_, other->model_type_);
  swap(cache_flag_, other->cache_flag_);
  swap(cache_ttl_, other->cache_ttl_);
}

::PROTOBUF_NAMESPACE_ID::Metadata KVModelParam::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void KVModelParamList::InitAsDefaultInstance() {
}
class KVModelParamList::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int KVModelParamList::kKvModelParamListFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

KVModelParamList::KVModelParamList()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.mmp.KVModelParamList)
}
KVModelParamList::KVModelParamList(const KVModelParamList& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      kv_model_param_list_(from.kv_model_param_list_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.mmp.KVModelParamList)
}

void KVModelParamList::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_KVModelParamList_proto_2frecommend_5fmmp_5fparam_2eproto.base);
}

KVModelParamList::~KVModelParamList() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.mmp.KVModelParamList)
  SharedDtor();
}

void KVModelParamList::SharedDtor() {
}

void KVModelParamList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const KVModelParamList& KVModelParamList::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_KVModelParamList_proto_2frecommend_5fmmp_5fparam_2eproto.base);
  return *internal_default_instance();
}


void KVModelParamList::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.mmp.KVModelParamList)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  kv_model_param_list_.Clear();
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* KVModelParamList::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .abc.recommend_plt.mmp.KVModelParam kv_model_param_list = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(add_kv_model_param_list(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 10);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool KVModelParamList::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.mmp.KVModelParamList)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .abc.recommend_plt.mmp.KVModelParam kv_model_param_list = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
                input, add_kv_model_param_list()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.mmp.KVModelParamList)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.mmp.KVModelParamList)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void KVModelParamList::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.mmp.KVModelParamList)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.mmp.KVModelParam kv_model_param_list = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->kv_model_param_list_size()); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->kv_model_param_list(static_cast<int>(i)),
      output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.mmp.KVModelParamList)
}

::PROTOBUF_NAMESPACE_ID::uint8* KVModelParamList::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.mmp.KVModelParamList)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.mmp.KVModelParam kv_model_param_list = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->kv_model_param_list_size()); i < n; i++) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->kv_model_param_list(static_cast<int>(i)), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.mmp.KVModelParamList)
  return target;
}

size_t KVModelParamList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.mmp.KVModelParamList)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.mmp.KVModelParam kv_model_param_list = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->kv_model_param_list_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          this->kv_model_param_list(static_cast<int>(i)));
    }
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void KVModelParamList::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.mmp.KVModelParamList)
  GOOGLE_DCHECK_NE(&from, this);
  const KVModelParamList* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<KVModelParamList>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.mmp.KVModelParamList)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.mmp.KVModelParamList)
    MergeFrom(*source);
  }
}

void KVModelParamList::MergeFrom(const KVModelParamList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.mmp.KVModelParamList)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  kv_model_param_list_.MergeFrom(from.kv_model_param_list_);
}

void KVModelParamList::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.mmp.KVModelParamList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void KVModelParamList::CopyFrom(const KVModelParamList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.mmp.KVModelParamList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool KVModelParamList::IsInitialized() const {
  return true;
}

void KVModelParamList::Swap(KVModelParamList* other) {
  if (other == this) return;
  InternalSwap(other);
}
void KVModelParamList::InternalSwap(KVModelParamList* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  CastToBase(&kv_model_param_list_)->InternalSwap(CastToBase(&other->kv_model_param_list_));
}

::PROTOBUF_NAMESPACE_ID::Metadata KVModelParamList::GetMetadata() const {
  return GetMetadataStatic();
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace mmp
}  // namespace recommend_plt
}  // namespace abc
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::mmp::TensorParam* Arena::CreateMaybeMessage< ::abc::recommend_plt::mmp::TensorParam >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::mmp::TensorParam >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::mmp::TensorParamList* Arena::CreateMaybeMessage< ::abc::recommend_plt::mmp::TensorParamList >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::mmp::TensorParamList >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::mmp::TFModelParam_ObjectParam* Arena::CreateMaybeMessage< ::abc::recommend_plt::mmp::TFModelParam_ObjectParam >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::mmp::TFModelParam_ObjectParam >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::mmp::TFModelParam_ObjectParamsEntry_DoNotUse* Arena::CreateMaybeMessage< ::abc::recommend_plt::mmp::TFModelParam_ObjectParamsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::mmp::TFModelParam_ObjectParamsEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::mmp::TFModelParam* Arena::CreateMaybeMessage< ::abc::recommend_plt::mmp::TFModelParam >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::mmp::TFModelParam >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::mmp::TFModelParamList* Arena::CreateMaybeMessage< ::abc::recommend_plt::mmp::TFModelParamList >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::mmp::TFModelParamList >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::mmp::FuseParam_OutputParam* Arena::CreateMaybeMessage< ::abc::recommend_plt::mmp::FuseParam_OutputParam >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::mmp::FuseParam_OutputParam >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::mmp::FuseParam* Arena::CreateMaybeMessage< ::abc::recommend_plt::mmp::FuseParam >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::mmp::FuseParam >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::mmp::RankStrategy* Arena::CreateMaybeMessage< ::abc::recommend_plt::mmp::RankStrategy >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::mmp::RankStrategy >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::mmp::RankStrategyConfig_ConfEntry_DoNotUse* Arena::CreateMaybeMessage< ::abc::recommend_plt::mmp::RankStrategyConfig_ConfEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::mmp::RankStrategyConfig_ConfEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::mmp::RankStrategyConfig* Arena::CreateMaybeMessage< ::abc::recommend_plt::mmp::RankStrategyConfig >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::mmp::RankStrategyConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::mmp::KVModelParam* Arena::CreateMaybeMessage< ::abc::recommend_plt::mmp::KVModelParam >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::mmp::KVModelParam >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::mmp::KVModelParamList* Arena::CreateMaybeMessage< ::abc::recommend_plt::mmp::KVModelParamList >(Arena* arena) {
  return Arena::CreateInternal< ::abc::recommend_plt::mmp::KVModelParamList >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
