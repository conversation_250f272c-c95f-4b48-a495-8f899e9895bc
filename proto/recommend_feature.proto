syntax = "proto3";
package abc.recommend_plt.fmp;
option cc_enable_arenas = true;

//特征值类型
enum FeatureValueType {
    BOOL = 0;
    FLOAT32 = 1;
    INT64 = 3;
    STRING = 7;
    LIST_FLOAT32 = 11;
    LIST_INT64 = 13;
    LIST_STRING = 17;
    LIST_FEATURE = 18;
    LIST_BOOL = 19;
    MAP_INT64_INT64 = 20;
    LLIST_FLOAT32 = 21;
    LLIST_INT64 = 22;
    LLIST_STRING = 23;
    LLIST_BOOL = 24;
    LIST_FLOAT32_PB = 25;
    LIST_INT64_PB = 26;
    LIST_STRING_PB = 27;
    MAP_INT64_FLOAT32 = 28;
    RAW_FEATURE = 29;
    MAP_INT64_LIST_INT64 = 30;
    MAP_INT64_LIST_FLOAT32 = 31;
    MAP_INT64_STRING = 34;

    MAP_INT64_LIST_STRING = 37;
    MAP_INT64_LLIST_INT64 = 38;
    MAP_INT64_LLIST_FLOAT32 = 39;
    MAP_INT64_LLIST_STRING = 40;
    MAP_STRING_INT64 = 41;
    MAP_STRING_FLOAT32 = 42;
    MAP_STRING_STRING = 43;
    MAP_STRING_LIST_INT64 = 44;
    MAP_STRING_LIST_FLOAT32 = 45;
    MAP_STRING_LIST_STRING = 46;
    MAP_STRING_LLIST_INT64 = 47;
    MAP_STRING_LLIST_FLOAT32 = 48;
    MAP_STRING_LLIST_STRING = 49;
}

message BytesList {
    repeated bytes value = 1;
}
message FloatList {
    repeated float value = 1 [packed = true];
}
message Int64List {
    repeated int64 value = 1 [packed = true];
}
message Int64ListMap {
    map<int64, Int64List> value = 1;
}

message Feature { // 用于上下文特征、用户特征
    oneof kind {
      BytesList bytes_list = 1;
      FloatList float_list = 2;
      Int64List int64_list = 3;
      Int64ListMap int64_list_map = 4;  // (新增字段) 存储DMP过滤出的结果
    }
};

// 用于商品特征
message FeatureList {
    repeated int64 value = 1; // 特征服务仅支持int
};

message FeatureMeta {
    string feature_name = 1;
    FeatureValueType feature_value_type = 2;
    int64 feature_size = 3; // 特征长度
    int64 feature_default_value = 4; // 特征默认值
}

message FeatureMetaList {
    repeated FeatureMeta feature_meta = 1;
}


// 商品特征组数据
message FeatureGroupData {
    string feature_group_name = 1;
    FeatureMetaList feature_meta_list = 2;
    repeated FeatureList feature_list = 3;
    string version = 4;  // 版本
}

// 特征转化数据类型
message FeatureData {
    uint64 fea_key = 1;
    enum FeatureConvertValueType {
        FEATURE_LIST_TYPE = 0;      // 普通特征类型
        FEATURE_STRING_TYPE = 1;    // 字符串类型
    }
    FeatureConvertValueType fea_val_type = 2; // 特征转化类型
    FeatureList fea_val_list = 3; // 特征数据
    string fea_val_str = 4; // 字符串类型特征数据
}

message FeatureDataConvert {
    repeated FeatureData feature_data = 1;
}

message AmlLandingExtraData {
    int32 item_source = 1;     // 链路类型
    int32 item_type = 2;
    string item_country = 3;   // 国家
    string item_brand_id = 4;  // 物品的品牌
    string item_site_id = 5;   // 物品的站点
    int64 req_time = 8;

    // Not used now, <特征组名, 特征组版本>
    map<string, uint64> feature_group_info = 10;
    // Not used now, 精排的item_id list
    repeated int64 item_ids = 11;
    // Not used now
    map<string, abc.recommend_plt.fmp.Feature> item_features = 12;
}
