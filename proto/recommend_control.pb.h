// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/recommend_control.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_proto_2frecommend_5fcontrol_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_proto_2frecommend_5fcontrol_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3008000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3008000 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "proto/recommend_api.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fcontrol_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_proto_2frecommend_5fcontrol_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[2]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2frecommend_5fcontrol_2eproto;
namespace abc {
namespace recommend_plt {
namespace control {
class ControlRequest;
class ControlRequestDefaultTypeInternal;
extern ControlRequestDefaultTypeInternal _ControlRequest_default_instance_;
class ControlResponse;
class ControlResponseDefaultTypeInternal;
extern ControlResponseDefaultTypeInternal _ControlResponse_default_instance_;
}  // namespace control
}  // namespace recommend_plt
}  // namespace abc
PROTOBUF_NAMESPACE_OPEN
template<> ::abc::recommend_plt::control::ControlRequest* Arena::CreateMaybeMessage<::abc::recommend_plt::control::ControlRequest>(Arena*);
template<> ::abc::recommend_plt::control::ControlResponse* Arena::CreateMaybeMessage<::abc::recommend_plt::control::ControlResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace abc {
namespace recommend_plt {
namespace control {

// ===================================================================

class ControlRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.control.ControlRequest) */ {
 public:
  ControlRequest();
  virtual ~ControlRequest();

  ControlRequest(const ControlRequest& from);
  ControlRequest(ControlRequest&& from) noexcept
    : ControlRequest() {
    *this = ::std::move(from);
  }

  inline ControlRequest& operator=(const ControlRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ControlRequest& operator=(ControlRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ControlRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ControlRequest* internal_default_instance() {
    return reinterpret_cast<const ControlRequest*>(
               &_ControlRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(ControlRequest* other);
  friend void swap(ControlRequest& a, ControlRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ControlRequest* New() const final {
    return CreateMaybeMessage<ControlRequest>(nullptr);
  }

  ControlRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ControlRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ControlRequest& from);
  void MergeFrom(const ControlRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ControlRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.control.ControlRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fcontrol_2eproto);
    return ::descriptor_table_proto_2frecommend_5fcontrol_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string rec_trace_id = 1;
  void clear_rec_trace_id();
  static const int kRecTraceIdFieldNumber = 1;
  const std::string& rec_trace_id() const;
  void set_rec_trace_id(const std::string& value);
  void set_rec_trace_id(std::string&& value);
  void set_rec_trace_id(const char* value);
  void set_rec_trace_id(const char* value, size_t size);
  std::string* mutable_rec_trace_id();
  std::string* release_rec_trace_id();
  void set_allocated_rec_trace_id(std::string* rec_trace_id);

  // .abc.recommend_plt.api.RecommendRequest recommend_request = 2;
  bool has_recommend_request() const;
  void clear_recommend_request();
  static const int kRecommendRequestFieldNumber = 2;
  const ::abc::recommend_plt::api::RecommendRequest& recommend_request() const;
  ::abc::recommend_plt::api::RecommendRequest* release_recommend_request();
  ::abc::recommend_plt::api::RecommendRequest* mutable_recommend_request();
  void set_allocated_recommend_request(::abc::recommend_plt::api::RecommendRequest* recommend_request);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.control.ControlRequest)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr rec_trace_id_;
  ::abc::recommend_plt::api::RecommendRequest* recommend_request_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class ControlResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:abc.recommend_plt.control.ControlResponse) */ {
 public:
  ControlResponse();
  virtual ~ControlResponse();

  ControlResponse(const ControlResponse& from);
  ControlResponse(ControlResponse&& from) noexcept
    : ControlResponse() {
    *this = ::std::move(from);
  }

  inline ControlResponse& operator=(const ControlResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ControlResponse& operator=(ControlResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ControlResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ControlResponse* internal_default_instance() {
    return reinterpret_cast<const ControlResponse*>(
               &_ControlResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void Swap(ControlResponse* other);
  friend void swap(ControlResponse& a, ControlResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ControlResponse* New() const final {
    return CreateMaybeMessage<ControlResponse>(nullptr);
  }

  ControlResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ControlResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ControlResponse& from);
  void MergeFrom(const ControlResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ControlResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "abc.recommend_plt.control.ControlResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2frecommend_5fcontrol_2eproto);
    return ::descriptor_table_proto_2frecommend_5fcontrol_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .abc.recommend_plt.api.RecommendResponse recommend_response = 1;
  bool has_recommend_response() const;
  void clear_recommend_response();
  static const int kRecommendResponseFieldNumber = 1;
  const ::abc::recommend_plt::api::RecommendResponse& recommend_response() const;
  ::abc::recommend_plt::api::RecommendResponse* release_recommend_response();
  ::abc::recommend_plt::api::RecommendResponse* mutable_recommend_response();
  void set_allocated_recommend_response(::abc::recommend_plt::api::RecommendResponse* recommend_response);

  // @@protoc_insertion_point(class_scope:abc.recommend_plt.control.ControlResponse)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::abc::recommend_plt::api::RecommendResponse* recommend_response_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frecommend_5fcontrol_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ControlRequest

// string rec_trace_id = 1;
inline void ControlRequest::clear_rec_trace_id() {
  rec_trace_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ControlRequest::rec_trace_id() const {
  // @@protoc_insertion_point(field_get:abc.recommend_plt.control.ControlRequest.rec_trace_id)
  return rec_trace_id_.GetNoArena();
}
inline void ControlRequest::set_rec_trace_id(const std::string& value) {
  
  rec_trace_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:abc.recommend_plt.control.ControlRequest.rec_trace_id)
}
inline void ControlRequest::set_rec_trace_id(std::string&& value) {
  
  rec_trace_id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:abc.recommend_plt.control.ControlRequest.rec_trace_id)
}
inline void ControlRequest::set_rec_trace_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  rec_trace_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:abc.recommend_plt.control.ControlRequest.rec_trace_id)
}
inline void ControlRequest::set_rec_trace_id(const char* value, size_t size) {
  
  rec_trace_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:abc.recommend_plt.control.ControlRequest.rec_trace_id)
}
inline std::string* ControlRequest::mutable_rec_trace_id() {
  
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.control.ControlRequest.rec_trace_id)
  return rec_trace_id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ControlRequest::release_rec_trace_id() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.control.ControlRequest.rec_trace_id)
  
  return rec_trace_id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ControlRequest::set_allocated_rec_trace_id(std::string* rec_trace_id) {
  if (rec_trace_id != nullptr) {
    
  } else {
    
  }
  rec_trace_id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), rec_trace_id);
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.control.ControlRequest.rec_trace_id)
}

// .abc.recommend_plt.api.RecommendRequest recommend_request = 2;
inline bool ControlRequest::has_recommend_request() const {
  return this != internal_default_instance() && recommend_request_ != nullptr;
}
inline const ::abc::recommend_plt::api::RecommendRequest& ControlRequest::recommend_request() const {
  const ::abc::recommend_plt::api::RecommendRequest* p = recommend_request_;
  // @@protoc_insertion_point(field_get:abc.recommend_plt.control.ControlRequest.recommend_request)
  return p != nullptr ? *p : *reinterpret_cast<const ::abc::recommend_plt::api::RecommendRequest*>(
      &::abc::recommend_plt::api::_RecommendRequest_default_instance_);
}
inline ::abc::recommend_plt::api::RecommendRequest* ControlRequest::release_recommend_request() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.control.ControlRequest.recommend_request)
  
  ::abc::recommend_plt::api::RecommendRequest* temp = recommend_request_;
  recommend_request_ = nullptr;
  return temp;
}
inline ::abc::recommend_plt::api::RecommendRequest* ControlRequest::mutable_recommend_request() {
  
  if (recommend_request_ == nullptr) {
    auto* p = CreateMaybeMessage<::abc::recommend_plt::api::RecommendRequest>(GetArenaNoVirtual());
    recommend_request_ = p;
  }
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.control.ControlRequest.recommend_request)
  return recommend_request_;
}
inline void ControlRequest::set_allocated_recommend_request(::abc::recommend_plt::api::RecommendRequest* recommend_request) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(recommend_request_);
  }
  if (recommend_request) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      recommend_request = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, recommend_request, submessage_arena);
    }
    
  } else {
    
  }
  recommend_request_ = recommend_request;
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.control.ControlRequest.recommend_request)
}

// -------------------------------------------------------------------

// ControlResponse

// .abc.recommend_plt.api.RecommendResponse recommend_response = 1;
inline bool ControlResponse::has_recommend_response() const {
  return this != internal_default_instance() && recommend_response_ != nullptr;
}
inline const ::abc::recommend_plt::api::RecommendResponse& ControlResponse::recommend_response() const {
  const ::abc::recommend_plt::api::RecommendResponse* p = recommend_response_;
  // @@protoc_insertion_point(field_get:abc.recommend_plt.control.ControlResponse.recommend_response)
  return p != nullptr ? *p : *reinterpret_cast<const ::abc::recommend_plt::api::RecommendResponse*>(
      &::abc::recommend_plt::api::_RecommendResponse_default_instance_);
}
inline ::abc::recommend_plt::api::RecommendResponse* ControlResponse::release_recommend_response() {
  // @@protoc_insertion_point(field_release:abc.recommend_plt.control.ControlResponse.recommend_response)
  
  ::abc::recommend_plt::api::RecommendResponse* temp = recommend_response_;
  recommend_response_ = nullptr;
  return temp;
}
inline ::abc::recommend_plt::api::RecommendResponse* ControlResponse::mutable_recommend_response() {
  
  if (recommend_response_ == nullptr) {
    auto* p = CreateMaybeMessage<::abc::recommend_plt::api::RecommendResponse>(GetArenaNoVirtual());
    recommend_response_ = p;
  }
  // @@protoc_insertion_point(field_mutable:abc.recommend_plt.control.ControlResponse.recommend_response)
  return recommend_response_;
}
inline void ControlResponse::set_allocated_recommend_response(::abc::recommend_plt::api::RecommendResponse* recommend_response) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(recommend_response_);
  }
  if (recommend_response) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      recommend_response = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, recommend_response, submessage_arena);
    }
    
  } else {
    
  }
  recommend_response_ = recommend_response;
  // @@protoc_insertion_point(field_set_allocated:abc.recommend_plt.control.ControlResponse.recommend_response)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace control
}  // namespace recommend_plt
}  // namespace abc

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_proto_2frecommend_5fcontrol_2eproto
