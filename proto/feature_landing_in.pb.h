// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/feature_landing_in.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_proto_2ffeature_5flanding_5fin_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_proto_2ffeature_5flanding_5fin_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3008000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3008000 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "proto/recommend_feature.pb.h"
#include "proto/recommend_mmp.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_proto_2ffeature_5flanding_5fin_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_proto_2ffeature_5flanding_5fin_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[13]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2ffeature_5flanding_5fin_2eproto;
namespace aml {
namespace public_idl {
namespace feature_landing {
class AmlFeatureSample;
class AmlFeatureSampleDefaultTypeInternal;
extern AmlFeatureSampleDefaultTypeInternal _AmlFeatureSample_default_instance_;
class BaseInfo;
class BaseInfoDefaultTypeInternal;
extern BaseInfoDefaultTypeInternal _BaseInfo_default_instance_;
class FeatureSampleV1;
class FeatureSampleV1DefaultTypeInternal;
extern FeatureSampleV1DefaultTypeInternal _FeatureSampleV1_default_instance_;
class FeatureSampleV1_ContextFeaturesEntry_DoNotUse;
class FeatureSampleV1_ContextFeaturesEntry_DoNotUseDefaultTypeInternal;
extern FeatureSampleV1_ContextFeaturesEntry_DoNotUseDefaultTypeInternal _FeatureSampleV1_ContextFeaturesEntry_DoNotUse_default_instance_;
class FeatureSampleV1_ItemFeaturesEntry_DoNotUse;
class FeatureSampleV1_ItemFeaturesEntry_DoNotUseDefaultTypeInternal;
extern FeatureSampleV1_ItemFeaturesEntry_DoNotUseDefaultTypeInternal _FeatureSampleV1_ItemFeaturesEntry_DoNotUse_default_instance_;
class FeatureSampleV2;
class FeatureSampleV2DefaultTypeInternal;
extern FeatureSampleV2DefaultTypeInternal _FeatureSampleV2_default_instance_;
class FeatureSampleV2_ContextFeaturesEntry_DoNotUse;
class FeatureSampleV2_ContextFeaturesEntry_DoNotUseDefaultTypeInternal;
extern FeatureSampleV2_ContextFeaturesEntry_DoNotUseDefaultTypeInternal _FeatureSampleV2_ContextFeaturesEntry_DoNotUse_default_instance_;
class FeatureSampleV2_ItemFeaturesEntry_DoNotUse;
class FeatureSampleV2_ItemFeaturesEntry_DoNotUseDefaultTypeInternal;
extern FeatureSampleV2_ItemFeaturesEntry_DoNotUseDefaultTypeInternal _FeatureSampleV2_ItemFeaturesEntry_DoNotUse_default_instance_;
class ItemInfo;
class ItemInfoDefaultTypeInternal;
extern ItemInfoDefaultTypeInternal _ItemInfo_default_instance_;
class ItemSourceLogInfo;
class ItemSourceLogInfoDefaultTypeInternal;
extern ItemSourceLogInfoDefaultTypeInternal _ItemSourceLogInfo_default_instance_;
class ItemSourceLogInfo_ContextFeaturesEntry_DoNotUse;
class ItemSourceLogInfo_ContextFeaturesEntry_DoNotUseDefaultTypeInternal;
extern ItemSourceLogInfo_ContextFeaturesEntry_DoNotUseDefaultTypeInternal _ItemSourceLogInfo_ContextFeaturesEntry_DoNotUse_default_instance_;
class ItemSourceLogInfo_ItemFeaturesEntry_DoNotUse;
class ItemSourceLogInfo_ItemFeaturesEntry_DoNotUseDefaultTypeInternal;
extern ItemSourceLogInfo_ItemFeaturesEntry_DoNotUseDefaultTypeInternal _ItemSourceLogInfo_ItemFeaturesEntry_DoNotUse_default_instance_;
class PreRankItemInfo;
class PreRankItemInfoDefaultTypeInternal;
extern PreRankItemInfoDefaultTypeInternal _PreRankItemInfo_default_instance_;
}  // namespace feature_landing
}  // namespace public_idl
}  // namespace aml
PROTOBUF_NAMESPACE_OPEN
template<> ::aml::public_idl::feature_landing::AmlFeatureSample* Arena::CreateMaybeMessage<::aml::public_idl::feature_landing::AmlFeatureSample>(Arena*);
template<> ::aml::public_idl::feature_landing::BaseInfo* Arena::CreateMaybeMessage<::aml::public_idl::feature_landing::BaseInfo>(Arena*);
template<> ::aml::public_idl::feature_landing::FeatureSampleV1* Arena::CreateMaybeMessage<::aml::public_idl::feature_landing::FeatureSampleV1>(Arena*);
template<> ::aml::public_idl::feature_landing::FeatureSampleV1_ContextFeaturesEntry_DoNotUse* Arena::CreateMaybeMessage<::aml::public_idl::feature_landing::FeatureSampleV1_ContextFeaturesEntry_DoNotUse>(Arena*);
template<> ::aml::public_idl::feature_landing::FeatureSampleV1_ItemFeaturesEntry_DoNotUse* Arena::CreateMaybeMessage<::aml::public_idl::feature_landing::FeatureSampleV1_ItemFeaturesEntry_DoNotUse>(Arena*);
template<> ::aml::public_idl::feature_landing::FeatureSampleV2* Arena::CreateMaybeMessage<::aml::public_idl::feature_landing::FeatureSampleV2>(Arena*);
template<> ::aml::public_idl::feature_landing::FeatureSampleV2_ContextFeaturesEntry_DoNotUse* Arena::CreateMaybeMessage<::aml::public_idl::feature_landing::FeatureSampleV2_ContextFeaturesEntry_DoNotUse>(Arena*);
template<> ::aml::public_idl::feature_landing::FeatureSampleV2_ItemFeaturesEntry_DoNotUse* Arena::CreateMaybeMessage<::aml::public_idl::feature_landing::FeatureSampleV2_ItemFeaturesEntry_DoNotUse>(Arena*);
template<> ::aml::public_idl::feature_landing::ItemInfo* Arena::CreateMaybeMessage<::aml::public_idl::feature_landing::ItemInfo>(Arena*);
template<> ::aml::public_idl::feature_landing::ItemSourceLogInfo* Arena::CreateMaybeMessage<::aml::public_idl::feature_landing::ItemSourceLogInfo>(Arena*);
template<> ::aml::public_idl::feature_landing::ItemSourceLogInfo_ContextFeaturesEntry_DoNotUse* Arena::CreateMaybeMessage<::aml::public_idl::feature_landing::ItemSourceLogInfo_ContextFeaturesEntry_DoNotUse>(Arena*);
template<> ::aml::public_idl::feature_landing::ItemSourceLogInfo_ItemFeaturesEntry_DoNotUse* Arena::CreateMaybeMessage<::aml::public_idl::feature_landing::ItemSourceLogInfo_ItemFeaturesEntry_DoNotUse>(Arena*);
template<> ::aml::public_idl::feature_landing::PreRankItemInfo* Arena::CreateMaybeMessage<::aml::public_idl::feature_landing::PreRankItemInfo>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace aml {
namespace public_idl {
namespace feature_landing {

enum FeatureSampleProtol : int {
  FeatureSampleProto_V1 = 0,
  FeatureSampleProto_V2 = 1,
  FeatureSampleProtol_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  FeatureSampleProtol_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool FeatureSampleProtol_IsValid(int value);
constexpr FeatureSampleProtol FeatureSampleProtol_MIN = FeatureSampleProto_V1;
constexpr FeatureSampleProtol FeatureSampleProtol_MAX = FeatureSampleProto_V2;
constexpr int FeatureSampleProtol_ARRAYSIZE = FeatureSampleProtol_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* FeatureSampleProtol_descriptor();
template<typename T>
inline const std::string& FeatureSampleProtol_Name(T enum_t_value) {
  static_assert(::std::is_same<T, FeatureSampleProtol>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function FeatureSampleProtol_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    FeatureSampleProtol_descriptor(), enum_t_value);
}
inline bool FeatureSampleProtol_Parse(
    const std::string& name, FeatureSampleProtol* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<FeatureSampleProtol>(
    FeatureSampleProtol_descriptor(), name, value);
}
// ===================================================================

class ItemInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:aml.public_idl.feature_landing.ItemInfo) */ {
 public:
  ItemInfo();
  virtual ~ItemInfo();

  ItemInfo(const ItemInfo& from);
  ItemInfo(ItemInfo&& from) noexcept
    : ItemInfo() {
    *this = ::std::move(from);
  }

  inline ItemInfo& operator=(const ItemInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline ItemInfo& operator=(ItemInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ItemInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ItemInfo* internal_default_instance() {
    return reinterpret_cast<const ItemInfo*>(
               &_ItemInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(ItemInfo* other);
  void Swap(ItemInfo* other);
  friend void swap(ItemInfo& a, ItemInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ItemInfo* New() const final {
    return CreateMaybeMessage<ItemInfo>(nullptr);
  }

  ItemInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ItemInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ItemInfo& from);
  void MergeFrom(const ItemInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ItemInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "aml.public_idl.feature_landing.ItemInfo";
  }
  protected:
  explicit ItemInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto);
    return ::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .abc.recommend_plt.mmp.ItemModelScore item_rank_score = 2;
  bool has_item_rank_score() const;
  void clear_item_rank_score();
  static const int kItemRankScoreFieldNumber = 2;
  const ::abc::recommend_plt::mmp::ItemModelScore& item_rank_score() const;
  ::abc::recommend_plt::mmp::ItemModelScore* release_item_rank_score();
  ::abc::recommend_plt::mmp::ItemModelScore* mutable_item_rank_score();
  void set_allocated_item_rank_score(::abc::recommend_plt::mmp::ItemModelScore* item_rank_score);
  void unsafe_arena_set_allocated_item_rank_score(
      ::abc::recommend_plt::mmp::ItemModelScore* item_rank_score);
  ::abc::recommend_plt::mmp::ItemModelScore* unsafe_arena_release_item_rank_score();

  // int64 item_id = 1;
  void clear_item_id();
  static const int kItemIdFieldNumber = 1;
  ::PROTOBUF_NAMESPACE_ID::int64 item_id() const;
  void set_item_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:aml.public_idl.feature_landing.ItemInfo)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::abc::recommend_plt::mmp::ItemModelScore* item_rank_score_;
  ::PROTOBUF_NAMESPACE_ID::int64 item_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2ffeature_5flanding_5fin_2eproto;
};
// -------------------------------------------------------------------

class PreRankItemInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:aml.public_idl.feature_landing.PreRankItemInfo) */ {
 public:
  PreRankItemInfo();
  virtual ~PreRankItemInfo();

  PreRankItemInfo(const PreRankItemInfo& from);
  PreRankItemInfo(PreRankItemInfo&& from) noexcept
    : PreRankItemInfo() {
    *this = ::std::move(from);
  }

  inline PreRankItemInfo& operator=(const PreRankItemInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline PreRankItemInfo& operator=(PreRankItemInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const PreRankItemInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const PreRankItemInfo* internal_default_instance() {
    return reinterpret_cast<const PreRankItemInfo*>(
               &_PreRankItemInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(PreRankItemInfo* other);
  void Swap(PreRankItemInfo* other);
  friend void swap(PreRankItemInfo& a, PreRankItemInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline PreRankItemInfo* New() const final {
    return CreateMaybeMessage<PreRankItemInfo>(nullptr);
  }

  PreRankItemInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<PreRankItemInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const PreRankItemInfo& from);
  void MergeFrom(const PreRankItemInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PreRankItemInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "aml.public_idl.feature_landing.PreRankItemInfo";
  }
  protected:
  explicit PreRankItemInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto);
    return ::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .abc.recommend_plt.mmp.ItemModelScore item_prerank_score = 2;
  bool has_item_prerank_score() const;
  void clear_item_prerank_score();
  static const int kItemPrerankScoreFieldNumber = 2;
  const ::abc::recommend_plt::mmp::ItemModelScore& item_prerank_score() const;
  ::abc::recommend_plt::mmp::ItemModelScore* release_item_prerank_score();
  ::abc::recommend_plt::mmp::ItemModelScore* mutable_item_prerank_score();
  void set_allocated_item_prerank_score(::abc::recommend_plt::mmp::ItemModelScore* item_prerank_score);
  void unsafe_arena_set_allocated_item_prerank_score(
      ::abc::recommend_plt::mmp::ItemModelScore* item_prerank_score);
  ::abc::recommend_plt::mmp::ItemModelScore* unsafe_arena_release_item_prerank_score();

  // int64 item_id = 1;
  void clear_item_id();
  static const int kItemIdFieldNumber = 1;
  ::PROTOBUF_NAMESPACE_ID::int64 item_id() const;
  void set_item_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:aml.public_idl.feature_landing.PreRankItemInfo)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::abc::recommend_plt::mmp::ItemModelScore* item_prerank_score_;
  ::PROTOBUF_NAMESPACE_ID::int64 item_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2ffeature_5flanding_5fin_2eproto;
};
// -------------------------------------------------------------------

class BaseInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:aml.public_idl.feature_landing.BaseInfo) */ {
 public:
  BaseInfo();
  virtual ~BaseInfo();

  BaseInfo(const BaseInfo& from);
  BaseInfo(BaseInfo&& from) noexcept
    : BaseInfo() {
    *this = ::std::move(from);
  }

  inline BaseInfo& operator=(const BaseInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline BaseInfo& operator=(BaseInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const BaseInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const BaseInfo* internal_default_instance() {
    return reinterpret_cast<const BaseInfo*>(
               &_BaseInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(BaseInfo* other);
  void Swap(BaseInfo* other);
  friend void swap(BaseInfo& a, BaseInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline BaseInfo* New() const final {
    return CreateMaybeMessage<BaseInfo>(nullptr);
  }

  BaseInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<BaseInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const BaseInfo& from);
  void MergeFrom(const BaseInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BaseInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "aml.public_idl.feature_landing.BaseInfo";
  }
  protected:
  explicit BaseInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto);
    return ::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int64 user_tag_ids = 9;
  int user_tag_ids_size() const;
  void clear_user_tag_ids();
  static const int kUserTagIdsFieldNumber = 9;
  ::PROTOBUF_NAMESPACE_ID::int64 user_tag_ids(int index) const;
  void set_user_tag_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_user_tag_ids(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      user_tag_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_user_tag_ids();

  // string mid = 1;
  void clear_mid();
  static const int kMidFieldNumber = 1;
  const std::string& mid() const;
  void set_mid(const std::string& value);
  void set_mid(std::string&& value);
  void set_mid(const char* value);
  void set_mid(const char* value, size_t size);
  std::string* mutable_mid();
  std::string* release_mid();
  void set_allocated_mid(std::string* mid);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_mid();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_mid(
      std::string* mid);

  // string ugid = 2;
  void clear_ugid();
  static const int kUgidFieldNumber = 2;
  const std::string& ugid() const;
  void set_ugid(const std::string& value);
  void set_ugid(std::string&& value);
  void set_ugid(const char* value);
  void set_ugid(const char* value, size_t size);
  std::string* mutable_ugid();
  std::string* release_ugid();
  void set_allocated_ugid(std::string* ugid);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_ugid();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_ugid(
      std::string* ugid);

  // string cookie_id = 3;
  void clear_cookie_id();
  static const int kCookieIdFieldNumber = 3;
  const std::string& cookie_id() const;
  void set_cookie_id(const std::string& value);
  void set_cookie_id(std::string&& value);
  void set_cookie_id(const char* value);
  void set_cookie_id(const char* value, size_t size);
  std::string* mutable_cookie_id();
  std::string* release_cookie_id();
  void set_allocated_cookie_id(std::string* cookie_id);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_cookie_id();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_cookie_id(
      std::string* cookie_id);

  // string device_id = 4;
  void clear_device_id();
  static const int kDeviceIdFieldNumber = 4;
  const std::string& device_id() const;
  void set_device_id(const std::string& value);
  void set_device_id(std::string&& value);
  void set_device_id(const char* value);
  void set_device_id(const char* value, size_t size);
  std::string* mutable_device_id();
  std::string* release_device_id();
  void set_allocated_device_id(std::string* device_id);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_device_id();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_device_id(
      std::string* device_id);

  // string country = 5;
  void clear_country();
  static const int kCountryFieldNumber = 5;
  const std::string& country() const;
  void set_country(const std::string& value);
  void set_country(std::string&& value);
  void set_country(const char* value);
  void set_country(const char* value, size_t size);
  std::string* mutable_country();
  std::string* release_country();
  void set_allocated_country(std::string* country);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_country();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_country(
      std::string* country);

  // string item_brand_id = 6;
  void clear_item_brand_id();
  static const int kItemBrandIdFieldNumber = 6;
  const std::string& item_brand_id() const;
  void set_item_brand_id(const std::string& value);
  void set_item_brand_id(std::string&& value);
  void set_item_brand_id(const char* value);
  void set_item_brand_id(const char* value, size_t size);
  std::string* mutable_item_brand_id();
  std::string* release_item_brand_id();
  void set_allocated_item_brand_id(std::string* item_brand_id);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_item_brand_id();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_item_brand_id(
      std::string* item_brand_id);

  // string item_site_id = 7;
  void clear_item_site_id();
  static const int kItemSiteIdFieldNumber = 7;
  const std::string& item_site_id() const;
  void set_item_site_id(const std::string& value);
  void set_item_site_id(std::string&& value);
  void set_item_site_id(const char* value);
  void set_item_site_id(const char* value, size_t size);
  std::string* mutable_item_site_id();
  std::string* release_item_site_id();
  void set_allocated_item_site_id(std::string* item_site_id);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_item_site_id();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_item_site_id(
      std::string* item_site_id);

  // string user_site_id = 11;
  void clear_user_site_id();
  static const int kUserSiteIdFieldNumber = 11;
  const std::string& user_site_id() const;
  void set_user_site_id(const std::string& value);
  void set_user_site_id(std::string&& value);
  void set_user_site_id(const char* value);
  void set_user_site_id(const char* value, size_t size);
  std::string* mutable_user_site_id();
  std::string* release_user_site_id();
  void set_allocated_user_site_id(std::string* user_site_id);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_user_site_id();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_user_site_id(
      std::string* user_site_id);

  // int64 scene_id = 8;
  void clear_scene_id();
  static const int kSceneIdFieldNumber = 8;
  ::PROTOBUF_NAMESPACE_ID::int64 scene_id() const;
  void set_scene_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int32 item_type = 10;
  void clear_item_type();
  static const int kItemTypeFieldNumber = 10;
  ::PROTOBUF_NAMESPACE_ID::int32 item_type() const;
  void set_item_type(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:aml.public_idl.feature_landing.BaseInfo)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > user_tag_ids_;
  mutable std::atomic<int> _user_tag_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr mid_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr ugid_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cookie_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr country_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr item_brand_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr item_site_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr user_site_id_;
  ::PROTOBUF_NAMESPACE_ID::int64 scene_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 item_type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2ffeature_5flanding_5fin_2eproto;
};
// -------------------------------------------------------------------

class ItemSourceLogInfo_ContextFeaturesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ItemSourceLogInfo_ContextFeaturesEntry_DoNotUse, 
    std::string, ::abc::recommend_plt::fmp::Feature,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ItemSourceLogInfo_ContextFeaturesEntry_DoNotUse, 
    std::string, ::abc::recommend_plt::fmp::Feature,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  ItemSourceLogInfo_ContextFeaturesEntry_DoNotUse();
  ItemSourceLogInfo_ContextFeaturesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ItemSourceLogInfo_ContextFeaturesEntry_DoNotUse& other);
  static const ItemSourceLogInfo_ContextFeaturesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ItemSourceLogInfo_ContextFeaturesEntry_DoNotUse*>(&_ItemSourceLogInfo_ContextFeaturesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "aml.public_idl.feature_landing.ItemSourceLogInfo.ContextFeaturesEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto);
    return ::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto.file_level_metadata[3];
  }

  public:
};

// -------------------------------------------------------------------

class ItemSourceLogInfo_ItemFeaturesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ItemSourceLogInfo_ItemFeaturesEntry_DoNotUse, 
    std::string, ::abc::recommend_plt::fmp::Feature,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ItemSourceLogInfo_ItemFeaturesEntry_DoNotUse, 
    std::string, ::abc::recommend_plt::fmp::Feature,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  ItemSourceLogInfo_ItemFeaturesEntry_DoNotUse();
  ItemSourceLogInfo_ItemFeaturesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ItemSourceLogInfo_ItemFeaturesEntry_DoNotUse& other);
  static const ItemSourceLogInfo_ItemFeaturesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ItemSourceLogInfo_ItemFeaturesEntry_DoNotUse*>(&_ItemSourceLogInfo_ItemFeaturesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "aml.public_idl.feature_landing.ItemSourceLogInfo.ItemFeaturesEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto);
    return ::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto.file_level_metadata[4];
  }

  public:
};

// -------------------------------------------------------------------

class ItemSourceLogInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:aml.public_idl.feature_landing.ItemSourceLogInfo) */ {
 public:
  ItemSourceLogInfo();
  virtual ~ItemSourceLogInfo();

  ItemSourceLogInfo(const ItemSourceLogInfo& from);
  ItemSourceLogInfo(ItemSourceLogInfo&& from) noexcept
    : ItemSourceLogInfo() {
    *this = ::std::move(from);
  }

  inline ItemSourceLogInfo& operator=(const ItemSourceLogInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline ItemSourceLogInfo& operator=(ItemSourceLogInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ItemSourceLogInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ItemSourceLogInfo* internal_default_instance() {
    return reinterpret_cast<const ItemSourceLogInfo*>(
               &_ItemSourceLogInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void UnsafeArenaSwap(ItemSourceLogInfo* other);
  void Swap(ItemSourceLogInfo* other);
  friend void swap(ItemSourceLogInfo& a, ItemSourceLogInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ItemSourceLogInfo* New() const final {
    return CreateMaybeMessage<ItemSourceLogInfo>(nullptr);
  }

  ItemSourceLogInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ItemSourceLogInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ItemSourceLogInfo& from);
  void MergeFrom(const ItemSourceLogInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ItemSourceLogInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "aml.public_idl.feature_landing.ItemSourceLogInfo";
  }
  protected:
  explicit ItemSourceLogInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto);
    return ::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // repeated int64 item_ids = 8;
  int item_ids_size() const;
  void clear_item_ids();
  static const int kItemIdsFieldNumber = 8;
  ::PROTOBUF_NAMESPACE_ID::int64 item_ids(int index) const;
  void set_item_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_item_ids(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      item_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_item_ids();

  // repeated .abc.recommend_plt.mmp.ModelNameInfo prerank_model_infos = 10;
  int prerank_model_infos_size() const;
  void clear_prerank_model_infos();
  static const int kPrerankModelInfosFieldNumber = 10;
  ::abc::recommend_plt::mmp::ModelNameInfo* mutable_prerank_model_infos(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::mmp::ModelNameInfo >*
      mutable_prerank_model_infos();
  const ::abc::recommend_plt::mmp::ModelNameInfo& prerank_model_infos(int index) const;
  ::abc::recommend_plt::mmp::ModelNameInfo* add_prerank_model_infos();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::mmp::ModelNameInfo >&
      prerank_model_infos() const;

  // repeated .aml.public_idl.feature_landing.PreRankItemInfo prerank_items_info = 11;
  int prerank_items_info_size() const;
  void clear_prerank_items_info();
  static const int kPrerankItemsInfoFieldNumber = 11;
  ::aml::public_idl::feature_landing::PreRankItemInfo* mutable_prerank_items_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::PreRankItemInfo >*
      mutable_prerank_items_info();
  const ::aml::public_idl::feature_landing::PreRankItemInfo& prerank_items_info(int index) const;
  ::aml::public_idl::feature_landing::PreRankItemInfo* add_prerank_items_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::PreRankItemInfo >&
      prerank_items_info() const;

  // repeated .abc.recommend_plt.mmp.ModelNameInfo rank_model_infos = 12;
  int rank_model_infos_size() const;
  void clear_rank_model_infos();
  static const int kRankModelInfosFieldNumber = 12;
  ::abc::recommend_plt::mmp::ModelNameInfo* mutable_rank_model_infos(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::mmp::ModelNameInfo >*
      mutable_rank_model_infos();
  const ::abc::recommend_plt::mmp::ModelNameInfo& rank_model_infos(int index) const;
  ::abc::recommend_plt::mmp::ModelNameInfo* add_rank_model_infos();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::mmp::ModelNameInfo >&
      rank_model_infos() const;

  // repeated .aml.public_idl.feature_landing.ItemInfo rank_items_info = 13;
  int rank_items_info_size() const;
  void clear_rank_items_info();
  static const int kRankItemsInfoFieldNumber = 13;
  ::aml::public_idl::feature_landing::ItemInfo* mutable_rank_items_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::ItemInfo >*
      mutable_rank_items_info();
  const ::aml::public_idl::feature_landing::ItemInfo& rank_items_info(int index) const;
  ::aml::public_idl::feature_landing::ItemInfo* add_rank_items_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::ItemInfo >&
      rank_items_info() const;

  // map<string, .abc.recommend_plt.fmp.Feature> context_features = 14;
  int context_features_size() const;
  void clear_context_features();
  static const int kContextFeaturesFieldNumber = 14;
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >&
      context_features() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >*
      mutable_context_features();

  // map<string, .abc.recommend_plt.fmp.Feature> item_features = 15;
  int item_features_size() const;
  void clear_item_features();
  static const int kItemFeaturesFieldNumber = 15;
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >&
      item_features() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >*
      mutable_item_features();

  // string item_site_id = 2;
  void clear_item_site_id();
  static const int kItemSiteIdFieldNumber = 2;
  const std::string& item_site_id() const;
  void set_item_site_id(const std::string& value);
  void set_item_site_id(std::string&& value);
  void set_item_site_id(const char* value);
  void set_item_site_id(const char* value, size_t size);
  std::string* mutable_item_site_id();
  std::string* release_item_site_id();
  void set_allocated_item_site_id(std::string* item_site_id);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_item_site_id();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_item_site_id(
      std::string* item_site_id);

  // string item_brand_id = 3;
  void clear_item_brand_id();
  static const int kItemBrandIdFieldNumber = 3;
  const std::string& item_brand_id() const;
  void set_item_brand_id(const std::string& value);
  void set_item_brand_id(std::string&& value);
  void set_item_brand_id(const char* value);
  void set_item_brand_id(const char* value, size_t size);
  std::string* mutable_item_brand_id();
  std::string* release_item_brand_id();
  void set_allocated_item_brand_id(std::string* item_brand_id);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_item_brand_id();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_item_brand_id(
      std::string* item_brand_id);

  // string item_country = 4;
  void clear_item_country();
  static const int kItemCountryFieldNumber = 4;
  const std::string& item_country() const;
  void set_item_country(const std::string& value);
  void set_item_country(std::string&& value);
  void set_item_country(const char* value);
  void set_item_country(const char* value, size_t size);
  std::string* mutable_item_country();
  std::string* release_item_country();
  void set_allocated_item_country(std::string* item_country);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_item_country();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_item_country(
      std::string* item_country);

  // string step_id = 5;
  void clear_step_id();
  static const int kStepIdFieldNumber = 5;
  const std::string& step_id() const;
  void set_step_id(const std::string& value);
  void set_step_id(std::string&& value);
  void set_step_id(const char* value);
  void set_step_id(const char* value, size_t size);
  std::string* mutable_step_id();
  std::string* release_step_id();
  void set_allocated_step_id(std::string* step_id);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_step_id();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_step_id(
      std::string* step_id);

  // .abc.recommend_plt.fmp.AmlLandingExtraData extra_data = 20;
  bool has_extra_data() const;
  void clear_extra_data();
  static const int kExtraDataFieldNumber = 20;
  const ::abc::recommend_plt::fmp::AmlLandingExtraData& extra_data() const;
  ::abc::recommend_plt::fmp::AmlLandingExtraData* release_extra_data();
  ::abc::recommend_plt::fmp::AmlLandingExtraData* mutable_extra_data();
  void set_allocated_extra_data(::abc::recommend_plt::fmp::AmlLandingExtraData* extra_data);
  void unsafe_arena_set_allocated_extra_data(
      ::abc::recommend_plt::fmp::AmlLandingExtraData* extra_data);
  ::abc::recommend_plt::fmp::AmlLandingExtraData* unsafe_arena_release_extra_data();

  // int32 item_source = 1;
  void clear_item_source();
  static const int kItemSourceFieldNumber = 1;
  ::PROTOBUF_NAMESPACE_ID::int32 item_source() const;
  void set_item_source(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:aml.public_idl.feature_landing.ItemSourceLogInfo)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > item_ids_;
  mutable std::atomic<int> _item_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::mmp::ModelNameInfo > prerank_model_infos_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::PreRankItemInfo > prerank_items_info_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::mmp::ModelNameInfo > rank_model_infos_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::ItemInfo > rank_items_info_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      ItemSourceLogInfo_ContextFeaturesEntry_DoNotUse,
      std::string, ::abc::recommend_plt::fmp::Feature,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > context_features_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      ItemSourceLogInfo_ItemFeaturesEntry_DoNotUse,
      std::string, ::abc::recommend_plt::fmp::Feature,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > item_features_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr item_site_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr item_brand_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr item_country_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr step_id_;
  ::abc::recommend_plt::fmp::AmlLandingExtraData* extra_data_;
  ::PROTOBUF_NAMESPACE_ID::int32 item_source_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2ffeature_5flanding_5fin_2eproto;
};
// -------------------------------------------------------------------

class FeatureSampleV1_ContextFeaturesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FeatureSampleV1_ContextFeaturesEntry_DoNotUse, 
    std::string, ::abc::recommend_plt::fmp::Feature,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FeatureSampleV1_ContextFeaturesEntry_DoNotUse, 
    std::string, ::abc::recommend_plt::fmp::Feature,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  FeatureSampleV1_ContextFeaturesEntry_DoNotUse();
  FeatureSampleV1_ContextFeaturesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const FeatureSampleV1_ContextFeaturesEntry_DoNotUse& other);
  static const FeatureSampleV1_ContextFeaturesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FeatureSampleV1_ContextFeaturesEntry_DoNotUse*>(&_FeatureSampleV1_ContextFeaturesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "aml.public_idl.feature_landing.FeatureSampleV1.ContextFeaturesEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto);
    return ::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto.file_level_metadata[6];
  }

  public:
};

// -------------------------------------------------------------------

class FeatureSampleV1_ItemFeaturesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FeatureSampleV1_ItemFeaturesEntry_DoNotUse, 
    std::string, ::abc::recommend_plt::fmp::Feature,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FeatureSampleV1_ItemFeaturesEntry_DoNotUse, 
    std::string, ::abc::recommend_plt::fmp::Feature,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  FeatureSampleV1_ItemFeaturesEntry_DoNotUse();
  FeatureSampleV1_ItemFeaturesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const FeatureSampleV1_ItemFeaturesEntry_DoNotUse& other);
  static const FeatureSampleV1_ItemFeaturesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FeatureSampleV1_ItemFeaturesEntry_DoNotUse*>(&_FeatureSampleV1_ItemFeaturesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "aml.public_idl.feature_landing.FeatureSampleV1.ItemFeaturesEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto);
    return ::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto.file_level_metadata[7];
  }

  public:
};

// -------------------------------------------------------------------

class FeatureSampleV1 :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:aml.public_idl.feature_landing.FeatureSampleV1) */ {
 public:
  FeatureSampleV1();
  virtual ~FeatureSampleV1();

  FeatureSampleV1(const FeatureSampleV1& from);
  FeatureSampleV1(FeatureSampleV1&& from) noexcept
    : FeatureSampleV1() {
    *this = ::std::move(from);
  }

  inline FeatureSampleV1& operator=(const FeatureSampleV1& from) {
    CopyFrom(from);
    return *this;
  }
  inline FeatureSampleV1& operator=(FeatureSampleV1&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const FeatureSampleV1& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FeatureSampleV1* internal_default_instance() {
    return reinterpret_cast<const FeatureSampleV1*>(
               &_FeatureSampleV1_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  void UnsafeArenaSwap(FeatureSampleV1* other);
  void Swap(FeatureSampleV1* other);
  friend void swap(FeatureSampleV1& a, FeatureSampleV1& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline FeatureSampleV1* New() const final {
    return CreateMaybeMessage<FeatureSampleV1>(nullptr);
  }

  FeatureSampleV1* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FeatureSampleV1>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const FeatureSampleV1& from);
  void MergeFrom(const FeatureSampleV1& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FeatureSampleV1* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "aml.public_idl.feature_landing.FeatureSampleV1";
  }
  protected:
  explicit FeatureSampleV1(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto);
    return ::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // repeated int64 item_ids = 2;
  int item_ids_size() const;
  void clear_item_ids();
  static const int kItemIdsFieldNumber = 2;
  ::PROTOBUF_NAMESPACE_ID::int64 item_ids(int index) const;
  void set_item_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_item_ids(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      item_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_item_ids();

  // repeated .aml.public_idl.feature_landing.ItemInfo items_info = 4;
  int items_info_size() const;
  void clear_items_info();
  static const int kItemsInfoFieldNumber = 4;
  ::aml::public_idl::feature_landing::ItemInfo* mutable_items_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::ItemInfo >*
      mutable_items_info();
  const ::aml::public_idl::feature_landing::ItemInfo& items_info(int index) const;
  ::aml::public_idl::feature_landing::ItemInfo* add_items_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::ItemInfo >&
      items_info() const;

  // repeated .abc.recommend_plt.mmp.ModelNameInfo model_infos = 5;
  int model_infos_size() const;
  void clear_model_infos();
  static const int kModelInfosFieldNumber = 5;
  ::abc::recommend_plt::mmp::ModelNameInfo* mutable_model_infos(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::mmp::ModelNameInfo >*
      mutable_model_infos();
  const ::abc::recommend_plt::mmp::ModelNameInfo& model_infos(int index) const;
  ::abc::recommend_plt::mmp::ModelNameInfo* add_model_infos();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::mmp::ModelNameInfo >&
      model_infos() const;

  // map<string, .abc.recommend_plt.fmp.Feature> context_features = 6;
  int context_features_size() const;
  void clear_context_features();
  static const int kContextFeaturesFieldNumber = 6;
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >&
      context_features() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >*
      mutable_context_features();

  // map<string, .abc.recommend_plt.fmp.Feature> item_features = 7;
  int item_features_size() const;
  void clear_item_features();
  static const int kItemFeaturesFieldNumber = 7;
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >&
      item_features() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >*
      mutable_item_features();

  // repeated .aml.public_idl.feature_landing.PreRankItemInfo prerank_items_info = 8;
  int prerank_items_info_size() const;
  void clear_prerank_items_info();
  static const int kPrerankItemsInfoFieldNumber = 8;
  ::aml::public_idl::feature_landing::PreRankItemInfo* mutable_prerank_items_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::PreRankItemInfo >*
      mutable_prerank_items_info();
  const ::aml::public_idl::feature_landing::PreRankItemInfo& prerank_items_info(int index) const;
  ::aml::public_idl::feature_landing::PreRankItemInfo* add_prerank_items_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::PreRankItemInfo >&
      prerank_items_info() const;

  // repeated .abc.recommend_plt.mmp.ModelNameInfo prerank_model_infos = 9;
  int prerank_model_infos_size() const;
  void clear_prerank_model_infos();
  static const int kPrerankModelInfosFieldNumber = 9;
  ::abc::recommend_plt::mmp::ModelNameInfo* mutable_prerank_model_infos(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::mmp::ModelNameInfo >*
      mutable_prerank_model_infos();
  const ::abc::recommend_plt::mmp::ModelNameInfo& prerank_model_infos(int index) const;
  ::abc::recommend_plt::mmp::ModelNameInfo* add_prerank_model_infos();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::mmp::ModelNameInfo >&
      prerank_model_infos() const;

  // string trace_id = 1;
  void clear_trace_id();
  static const int kTraceIdFieldNumber = 1;
  const std::string& trace_id() const;
  void set_trace_id(const std::string& value);
  void set_trace_id(std::string&& value);
  void set_trace_id(const char* value);
  void set_trace_id(const char* value, size_t size);
  std::string* mutable_trace_id();
  std::string* release_trace_id();
  void set_allocated_trace_id(std::string* trace_id);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_trace_id();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_trace_id(
      std::string* trace_id);

  // .aml.public_idl.feature_landing.BaseInfo base_info = 3;
  bool has_base_info() const;
  void clear_base_info();
  static const int kBaseInfoFieldNumber = 3;
  const ::aml::public_idl::feature_landing::BaseInfo& base_info() const;
  ::aml::public_idl::feature_landing::BaseInfo* release_base_info();
  ::aml::public_idl::feature_landing::BaseInfo* mutable_base_info();
  void set_allocated_base_info(::aml::public_idl::feature_landing::BaseInfo* base_info);
  void unsafe_arena_set_allocated_base_info(
      ::aml::public_idl::feature_landing::BaseInfo* base_info);
  ::aml::public_idl::feature_landing::BaseInfo* unsafe_arena_release_base_info();

  // @@protoc_insertion_point(class_scope:aml.public_idl.feature_landing.FeatureSampleV1)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > item_ids_;
  mutable std::atomic<int> _item_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::ItemInfo > items_info_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::mmp::ModelNameInfo > model_infos_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      FeatureSampleV1_ContextFeaturesEntry_DoNotUse,
      std::string, ::abc::recommend_plt::fmp::Feature,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > context_features_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      FeatureSampleV1_ItemFeaturesEntry_DoNotUse,
      std::string, ::abc::recommend_plt::fmp::Feature,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > item_features_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::PreRankItemInfo > prerank_items_info_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::mmp::ModelNameInfo > prerank_model_infos_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr trace_id_;
  ::aml::public_idl::feature_landing::BaseInfo* base_info_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2ffeature_5flanding_5fin_2eproto;
};
// -------------------------------------------------------------------

class FeatureSampleV2_ContextFeaturesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FeatureSampleV2_ContextFeaturesEntry_DoNotUse, 
    std::string, ::abc::recommend_plt::fmp::Feature,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FeatureSampleV2_ContextFeaturesEntry_DoNotUse, 
    std::string, ::abc::recommend_plt::fmp::Feature,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  FeatureSampleV2_ContextFeaturesEntry_DoNotUse();
  FeatureSampleV2_ContextFeaturesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const FeatureSampleV2_ContextFeaturesEntry_DoNotUse& other);
  static const FeatureSampleV2_ContextFeaturesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FeatureSampleV2_ContextFeaturesEntry_DoNotUse*>(&_FeatureSampleV2_ContextFeaturesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "aml.public_idl.feature_landing.FeatureSampleV2.ContextFeaturesEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto);
    return ::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto.file_level_metadata[9];
  }

  public:
};

// -------------------------------------------------------------------

class FeatureSampleV2_ItemFeaturesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FeatureSampleV2_ItemFeaturesEntry_DoNotUse, 
    std::string, ::abc::recommend_plt::fmp::Feature,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FeatureSampleV2_ItemFeaturesEntry_DoNotUse, 
    std::string, ::abc::recommend_plt::fmp::Feature,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  FeatureSampleV2_ItemFeaturesEntry_DoNotUse();
  FeatureSampleV2_ItemFeaturesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const FeatureSampleV2_ItemFeaturesEntry_DoNotUse& other);
  static const FeatureSampleV2_ItemFeaturesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FeatureSampleV2_ItemFeaturesEntry_DoNotUse*>(&_FeatureSampleV2_ItemFeaturesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "aml.public_idl.feature_landing.FeatureSampleV2.ItemFeaturesEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto);
    return ::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto.file_level_metadata[10];
  }

  public:
};

// -------------------------------------------------------------------

class FeatureSampleV2 :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:aml.public_idl.feature_landing.FeatureSampleV2) */ {
 public:
  FeatureSampleV2();
  virtual ~FeatureSampleV2();

  FeatureSampleV2(const FeatureSampleV2& from);
  FeatureSampleV2(FeatureSampleV2&& from) noexcept
    : FeatureSampleV2() {
    *this = ::std::move(from);
  }

  inline FeatureSampleV2& operator=(const FeatureSampleV2& from) {
    CopyFrom(from);
    return *this;
  }
  inline FeatureSampleV2& operator=(FeatureSampleV2&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const FeatureSampleV2& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FeatureSampleV2* internal_default_instance() {
    return reinterpret_cast<const FeatureSampleV2*>(
               &_FeatureSampleV2_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  void UnsafeArenaSwap(FeatureSampleV2* other);
  void Swap(FeatureSampleV2* other);
  friend void swap(FeatureSampleV2& a, FeatureSampleV2& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline FeatureSampleV2* New() const final {
    return CreateMaybeMessage<FeatureSampleV2>(nullptr);
  }

  FeatureSampleV2* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FeatureSampleV2>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const FeatureSampleV2& from);
  void MergeFrom(const FeatureSampleV2& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FeatureSampleV2* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "aml.public_idl.feature_landing.FeatureSampleV2";
  }
  protected:
  explicit FeatureSampleV2(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto);
    return ::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // repeated int64 item_ids = 3;
  int item_ids_size() const;
  void clear_item_ids();
  static const int kItemIdsFieldNumber = 3;
  ::PROTOBUF_NAMESPACE_ID::int64 item_ids(int index) const;
  void set_item_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value);
  void add_item_ids(::PROTOBUF_NAMESPACE_ID::int64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
      item_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
      mutable_item_ids();

  // map<string, .abc.recommend_plt.fmp.Feature> context_features = 10;
  int context_features_size() const;
  void clear_context_features();
  static const int kContextFeaturesFieldNumber = 10;
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >&
      context_features() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >*
      mutable_context_features();

  // map<string, .abc.recommend_plt.fmp.Feature> item_features = 11;
  int item_features_size() const;
  void clear_item_features();
  static const int kItemFeaturesFieldNumber = 11;
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >&
      item_features() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >*
      mutable_item_features();

  // repeated .aml.public_idl.feature_landing.ItemSourceLogInfo item_source_log_info = 12;
  int item_source_log_info_size() const;
  void clear_item_source_log_info();
  static const int kItemSourceLogInfoFieldNumber = 12;
  ::aml::public_idl::feature_landing::ItemSourceLogInfo* mutable_item_source_log_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::ItemSourceLogInfo >*
      mutable_item_source_log_info();
  const ::aml::public_idl::feature_landing::ItemSourceLogInfo& item_source_log_info(int index) const;
  ::aml::public_idl::feature_landing::ItemSourceLogInfo* add_item_source_log_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::ItemSourceLogInfo >&
      item_source_log_info() const;

  // string trace_id = 1;
  void clear_trace_id();
  static const int kTraceIdFieldNumber = 1;
  const std::string& trace_id() const;
  void set_trace_id(const std::string& value);
  void set_trace_id(std::string&& value);
  void set_trace_id(const char* value);
  void set_trace_id(const char* value, size_t size);
  std::string* mutable_trace_id();
  std::string* release_trace_id();
  void set_allocated_trace_id(std::string* trace_id);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_trace_id();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_trace_id(
      std::string* trace_id);

  // .aml.public_idl.feature_landing.BaseInfo base_info = 2;
  bool has_base_info() const;
  void clear_base_info();
  static const int kBaseInfoFieldNumber = 2;
  const ::aml::public_idl::feature_landing::BaseInfo& base_info() const;
  ::aml::public_idl::feature_landing::BaseInfo* release_base_info();
  ::aml::public_idl::feature_landing::BaseInfo* mutable_base_info();
  void set_allocated_base_info(::aml::public_idl::feature_landing::BaseInfo* base_info);
  void unsafe_arena_set_allocated_base_info(
      ::aml::public_idl::feature_landing::BaseInfo* base_info);
  ::aml::public_idl::feature_landing::BaseInfo* unsafe_arena_release_base_info();

  // @@protoc_insertion_point(class_scope:aml.public_idl.feature_landing.FeatureSampleV2)
 private:
  class HasBitSetters;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 > item_ids_;
  mutable std::atomic<int> _item_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      FeatureSampleV2_ContextFeaturesEntry_DoNotUse,
      std::string, ::abc::recommend_plt::fmp::Feature,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > context_features_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      FeatureSampleV2_ItemFeaturesEntry_DoNotUse,
      std::string, ::abc::recommend_plt::fmp::Feature,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > item_features_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::ItemSourceLogInfo > item_source_log_info_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr trace_id_;
  ::aml::public_idl::feature_landing::BaseInfo* base_info_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2ffeature_5flanding_5fin_2eproto;
};
// -------------------------------------------------------------------

class AmlFeatureSample :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:aml.public_idl.feature_landing.AmlFeatureSample) */ {
 public:
  AmlFeatureSample();
  virtual ~AmlFeatureSample();

  AmlFeatureSample(const AmlFeatureSample& from);
  AmlFeatureSample(AmlFeatureSample&& from) noexcept
    : AmlFeatureSample() {
    *this = ::std::move(from);
  }

  inline AmlFeatureSample& operator=(const AmlFeatureSample& from) {
    CopyFrom(from);
    return *this;
  }
  inline AmlFeatureSample& operator=(AmlFeatureSample&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const AmlFeatureSample& default_instance();

  enum FeatureSampleCase {
    kFeatureSampleV1 = 2,
    kFeatureSampleV2 = 3,
    FEATURE_SAMPLE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AmlFeatureSample* internal_default_instance() {
    return reinterpret_cast<const AmlFeatureSample*>(
               &_AmlFeatureSample_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  void UnsafeArenaSwap(AmlFeatureSample* other);
  void Swap(AmlFeatureSample* other);
  friend void swap(AmlFeatureSample& a, AmlFeatureSample& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline AmlFeatureSample* New() const final {
    return CreateMaybeMessage<AmlFeatureSample>(nullptr);
  }

  AmlFeatureSample* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AmlFeatureSample>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const AmlFeatureSample& from);
  void MergeFrom(const AmlFeatureSample& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AmlFeatureSample* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "aml.public_idl.feature_landing.AmlFeatureSample";
  }
  protected:
  explicit AmlFeatureSample(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto);
    return ::descriptor_table_proto_2ffeature_5flanding_5fin_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .aml.public_idl.feature_landing.FeatureSampleProtol protol_version = 1;
  void clear_protol_version();
  static const int kProtolVersionFieldNumber = 1;
  ::aml::public_idl::feature_landing::FeatureSampleProtol protol_version() const;
  void set_protol_version(::aml::public_idl::feature_landing::FeatureSampleProtol value);

  // .aml.public_idl.feature_landing.FeatureSampleV1 feature_sample_v1 = 2;
  bool has_feature_sample_v1() const;
  void clear_feature_sample_v1();
  static const int kFeatureSampleV1FieldNumber = 2;
  const ::aml::public_idl::feature_landing::FeatureSampleV1& feature_sample_v1() const;
  ::aml::public_idl::feature_landing::FeatureSampleV1* release_feature_sample_v1();
  ::aml::public_idl::feature_landing::FeatureSampleV1* mutable_feature_sample_v1();
  void set_allocated_feature_sample_v1(::aml::public_idl::feature_landing::FeatureSampleV1* feature_sample_v1);
  void unsafe_arena_set_allocated_feature_sample_v1(
      ::aml::public_idl::feature_landing::FeatureSampleV1* feature_sample_v1);
  ::aml::public_idl::feature_landing::FeatureSampleV1* unsafe_arena_release_feature_sample_v1();

  // .aml.public_idl.feature_landing.FeatureSampleV2 feature_sample_v2 = 3;
  bool has_feature_sample_v2() const;
  void clear_feature_sample_v2();
  static const int kFeatureSampleV2FieldNumber = 3;
  const ::aml::public_idl::feature_landing::FeatureSampleV2& feature_sample_v2() const;
  ::aml::public_idl::feature_landing::FeatureSampleV2* release_feature_sample_v2();
  ::aml::public_idl::feature_landing::FeatureSampleV2* mutable_feature_sample_v2();
  void set_allocated_feature_sample_v2(::aml::public_idl::feature_landing::FeatureSampleV2* feature_sample_v2);
  void unsafe_arena_set_allocated_feature_sample_v2(
      ::aml::public_idl::feature_landing::FeatureSampleV2* feature_sample_v2);
  ::aml::public_idl::feature_landing::FeatureSampleV2* unsafe_arena_release_feature_sample_v2();

  void clear_feature_sample();
  FeatureSampleCase feature_sample_case() const;
  // @@protoc_insertion_point(class_scope:aml.public_idl.feature_landing.AmlFeatureSample)
 private:
  class HasBitSetters;
  void set_has_feature_sample_v1();
  void set_has_feature_sample_v2();

  inline bool has_feature_sample() const;
  inline void clear_has_feature_sample();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int protol_version_;
  union FeatureSampleUnion {
    FeatureSampleUnion() {}
    ::aml::public_idl::feature_landing::FeatureSampleV1* feature_sample_v1_;
    ::aml::public_idl::feature_landing::FeatureSampleV2* feature_sample_v2_;
  } feature_sample_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_proto_2ffeature_5flanding_5fin_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ItemInfo

// int64 item_id = 1;
inline void ItemInfo::clear_item_id() {
  item_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ItemInfo::item_id() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.ItemInfo.item_id)
  return item_id_;
}
inline void ItemInfo::set_item_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  item_id_ = value;
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.ItemInfo.item_id)
}

// .abc.recommend_plt.mmp.ItemModelScore item_rank_score = 2;
inline bool ItemInfo::has_item_rank_score() const {
  return this != internal_default_instance() && item_rank_score_ != nullptr;
}
inline const ::abc::recommend_plt::mmp::ItemModelScore& ItemInfo::item_rank_score() const {
  const ::abc::recommend_plt::mmp::ItemModelScore* p = item_rank_score_;
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.ItemInfo.item_rank_score)
  return p != nullptr ? *p : *reinterpret_cast<const ::abc::recommend_plt::mmp::ItemModelScore*>(
      &::abc::recommend_plt::mmp::_ItemModelScore_default_instance_);
}
inline ::abc::recommend_plt::mmp::ItemModelScore* ItemInfo::release_item_rank_score() {
  // @@protoc_insertion_point(field_release:aml.public_idl.feature_landing.ItemInfo.item_rank_score)
  
  ::abc::recommend_plt::mmp::ItemModelScore* temp = item_rank_score_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  item_rank_score_ = nullptr;
  return temp;
}
inline ::abc::recommend_plt::mmp::ItemModelScore* ItemInfo::unsafe_arena_release_item_rank_score() {
  // @@protoc_insertion_point(field_unsafe_arena_release:aml.public_idl.feature_landing.ItemInfo.item_rank_score)
  
  ::abc::recommend_plt::mmp::ItemModelScore* temp = item_rank_score_;
  item_rank_score_ = nullptr;
  return temp;
}
inline ::abc::recommend_plt::mmp::ItemModelScore* ItemInfo::mutable_item_rank_score() {
  
  if (item_rank_score_ == nullptr) {
    auto* p = CreateMaybeMessage<::abc::recommend_plt::mmp::ItemModelScore>(GetArenaNoVirtual());
    item_rank_score_ = p;
  }
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.ItemInfo.item_rank_score)
  return item_rank_score_;
}
inline void ItemInfo::set_allocated_item_rank_score(::abc::recommend_plt::mmp::ItemModelScore* item_rank_score) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(item_rank_score_);
  }
  if (item_rank_score) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(item_rank_score)->GetArena();
    if (message_arena != submessage_arena) {
      item_rank_score = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, item_rank_score, submessage_arena);
    }
    
  } else {
    
  }
  item_rank_score_ = item_rank_score;
  // @@protoc_insertion_point(field_set_allocated:aml.public_idl.feature_landing.ItemInfo.item_rank_score)
}

// -------------------------------------------------------------------

// PreRankItemInfo

// int64 item_id = 1;
inline void PreRankItemInfo::clear_item_id() {
  item_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 PreRankItemInfo::item_id() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.PreRankItemInfo.item_id)
  return item_id_;
}
inline void PreRankItemInfo::set_item_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  item_id_ = value;
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.PreRankItemInfo.item_id)
}

// .abc.recommend_plt.mmp.ItemModelScore item_prerank_score = 2;
inline bool PreRankItemInfo::has_item_prerank_score() const {
  return this != internal_default_instance() && item_prerank_score_ != nullptr;
}
inline const ::abc::recommend_plt::mmp::ItemModelScore& PreRankItemInfo::item_prerank_score() const {
  const ::abc::recommend_plt::mmp::ItemModelScore* p = item_prerank_score_;
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.PreRankItemInfo.item_prerank_score)
  return p != nullptr ? *p : *reinterpret_cast<const ::abc::recommend_plt::mmp::ItemModelScore*>(
      &::abc::recommend_plt::mmp::_ItemModelScore_default_instance_);
}
inline ::abc::recommend_plt::mmp::ItemModelScore* PreRankItemInfo::release_item_prerank_score() {
  // @@protoc_insertion_point(field_release:aml.public_idl.feature_landing.PreRankItemInfo.item_prerank_score)
  
  ::abc::recommend_plt::mmp::ItemModelScore* temp = item_prerank_score_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  item_prerank_score_ = nullptr;
  return temp;
}
inline ::abc::recommend_plt::mmp::ItemModelScore* PreRankItemInfo::unsafe_arena_release_item_prerank_score() {
  // @@protoc_insertion_point(field_unsafe_arena_release:aml.public_idl.feature_landing.PreRankItemInfo.item_prerank_score)
  
  ::abc::recommend_plt::mmp::ItemModelScore* temp = item_prerank_score_;
  item_prerank_score_ = nullptr;
  return temp;
}
inline ::abc::recommend_plt::mmp::ItemModelScore* PreRankItemInfo::mutable_item_prerank_score() {
  
  if (item_prerank_score_ == nullptr) {
    auto* p = CreateMaybeMessage<::abc::recommend_plt::mmp::ItemModelScore>(GetArenaNoVirtual());
    item_prerank_score_ = p;
  }
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.PreRankItemInfo.item_prerank_score)
  return item_prerank_score_;
}
inline void PreRankItemInfo::set_allocated_item_prerank_score(::abc::recommend_plt::mmp::ItemModelScore* item_prerank_score) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(item_prerank_score_);
  }
  if (item_prerank_score) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(item_prerank_score)->GetArena();
    if (message_arena != submessage_arena) {
      item_prerank_score = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, item_prerank_score, submessage_arena);
    }
    
  } else {
    
  }
  item_prerank_score_ = item_prerank_score;
  // @@protoc_insertion_point(field_set_allocated:aml.public_idl.feature_landing.PreRankItemInfo.item_prerank_score)
}

// -------------------------------------------------------------------

// BaseInfo

// string mid = 1;
inline void BaseInfo::clear_mid() {
  mid_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& BaseInfo::mid() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.BaseInfo.mid)
  return mid_.Get();
}
inline void BaseInfo::set_mid(const std::string& value) {
  
  mid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.BaseInfo.mid)
}
inline void BaseInfo::set_mid(std::string&& value) {
  
  mid_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:aml.public_idl.feature_landing.BaseInfo.mid)
}
inline void BaseInfo::set_mid(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  mid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:aml.public_idl.feature_landing.BaseInfo.mid)
}
inline void BaseInfo::set_mid(const char* value,
    size_t size) {
  
  mid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:aml.public_idl.feature_landing.BaseInfo.mid)
}
inline std::string* BaseInfo::mutable_mid() {
  
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.BaseInfo.mid)
  return mid_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* BaseInfo::release_mid() {
  // @@protoc_insertion_point(field_release:aml.public_idl.feature_landing.BaseInfo.mid)
  
  return mid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void BaseInfo::set_allocated_mid(std::string* mid) {
  if (mid != nullptr) {
    
  } else {
    
  }
  mid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), mid,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:aml.public_idl.feature_landing.BaseInfo.mid)
}
inline std::string* BaseInfo::unsafe_arena_release_mid() {
  // @@protoc_insertion_point(field_unsafe_arena_release:aml.public_idl.feature_landing.BaseInfo.mid)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return mid_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void BaseInfo::unsafe_arena_set_allocated_mid(
    std::string* mid) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (mid != nullptr) {
    
  } else {
    
  }
  mid_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      mid, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:aml.public_idl.feature_landing.BaseInfo.mid)
}

// string ugid = 2;
inline void BaseInfo::clear_ugid() {
  ugid_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& BaseInfo::ugid() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.BaseInfo.ugid)
  return ugid_.Get();
}
inline void BaseInfo::set_ugid(const std::string& value) {
  
  ugid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.BaseInfo.ugid)
}
inline void BaseInfo::set_ugid(std::string&& value) {
  
  ugid_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:aml.public_idl.feature_landing.BaseInfo.ugid)
}
inline void BaseInfo::set_ugid(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  ugid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:aml.public_idl.feature_landing.BaseInfo.ugid)
}
inline void BaseInfo::set_ugid(const char* value,
    size_t size) {
  
  ugid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:aml.public_idl.feature_landing.BaseInfo.ugid)
}
inline std::string* BaseInfo::mutable_ugid() {
  
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.BaseInfo.ugid)
  return ugid_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* BaseInfo::release_ugid() {
  // @@protoc_insertion_point(field_release:aml.public_idl.feature_landing.BaseInfo.ugid)
  
  return ugid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void BaseInfo::set_allocated_ugid(std::string* ugid) {
  if (ugid != nullptr) {
    
  } else {
    
  }
  ugid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ugid,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:aml.public_idl.feature_landing.BaseInfo.ugid)
}
inline std::string* BaseInfo::unsafe_arena_release_ugid() {
  // @@protoc_insertion_point(field_unsafe_arena_release:aml.public_idl.feature_landing.BaseInfo.ugid)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return ugid_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void BaseInfo::unsafe_arena_set_allocated_ugid(
    std::string* ugid) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (ugid != nullptr) {
    
  } else {
    
  }
  ugid_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ugid, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:aml.public_idl.feature_landing.BaseInfo.ugid)
}

// string cookie_id = 3;
inline void BaseInfo::clear_cookie_id() {
  cookie_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& BaseInfo::cookie_id() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.BaseInfo.cookie_id)
  return cookie_id_.Get();
}
inline void BaseInfo::set_cookie_id(const std::string& value) {
  
  cookie_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.BaseInfo.cookie_id)
}
inline void BaseInfo::set_cookie_id(std::string&& value) {
  
  cookie_id_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:aml.public_idl.feature_landing.BaseInfo.cookie_id)
}
inline void BaseInfo::set_cookie_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  cookie_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:aml.public_idl.feature_landing.BaseInfo.cookie_id)
}
inline void BaseInfo::set_cookie_id(const char* value,
    size_t size) {
  
  cookie_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:aml.public_idl.feature_landing.BaseInfo.cookie_id)
}
inline std::string* BaseInfo::mutable_cookie_id() {
  
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.BaseInfo.cookie_id)
  return cookie_id_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* BaseInfo::release_cookie_id() {
  // @@protoc_insertion_point(field_release:aml.public_idl.feature_landing.BaseInfo.cookie_id)
  
  return cookie_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void BaseInfo::set_allocated_cookie_id(std::string* cookie_id) {
  if (cookie_id != nullptr) {
    
  } else {
    
  }
  cookie_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), cookie_id,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:aml.public_idl.feature_landing.BaseInfo.cookie_id)
}
inline std::string* BaseInfo::unsafe_arena_release_cookie_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:aml.public_idl.feature_landing.BaseInfo.cookie_id)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return cookie_id_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void BaseInfo::unsafe_arena_set_allocated_cookie_id(
    std::string* cookie_id) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (cookie_id != nullptr) {
    
  } else {
    
  }
  cookie_id_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      cookie_id, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:aml.public_idl.feature_landing.BaseInfo.cookie_id)
}

// string device_id = 4;
inline void BaseInfo::clear_device_id() {
  device_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& BaseInfo::device_id() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.BaseInfo.device_id)
  return device_id_.Get();
}
inline void BaseInfo::set_device_id(const std::string& value) {
  
  device_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.BaseInfo.device_id)
}
inline void BaseInfo::set_device_id(std::string&& value) {
  
  device_id_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:aml.public_idl.feature_landing.BaseInfo.device_id)
}
inline void BaseInfo::set_device_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  device_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:aml.public_idl.feature_landing.BaseInfo.device_id)
}
inline void BaseInfo::set_device_id(const char* value,
    size_t size) {
  
  device_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:aml.public_idl.feature_landing.BaseInfo.device_id)
}
inline std::string* BaseInfo::mutable_device_id() {
  
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.BaseInfo.device_id)
  return device_id_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* BaseInfo::release_device_id() {
  // @@protoc_insertion_point(field_release:aml.public_idl.feature_landing.BaseInfo.device_id)
  
  return device_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void BaseInfo::set_allocated_device_id(std::string* device_id) {
  if (device_id != nullptr) {
    
  } else {
    
  }
  device_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device_id,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:aml.public_idl.feature_landing.BaseInfo.device_id)
}
inline std::string* BaseInfo::unsafe_arena_release_device_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:aml.public_idl.feature_landing.BaseInfo.device_id)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return device_id_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void BaseInfo::unsafe_arena_set_allocated_device_id(
    std::string* device_id) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (device_id != nullptr) {
    
  } else {
    
  }
  device_id_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      device_id, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:aml.public_idl.feature_landing.BaseInfo.device_id)
}

// string country = 5;
inline void BaseInfo::clear_country() {
  country_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& BaseInfo::country() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.BaseInfo.country)
  return country_.Get();
}
inline void BaseInfo::set_country(const std::string& value) {
  
  country_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.BaseInfo.country)
}
inline void BaseInfo::set_country(std::string&& value) {
  
  country_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:aml.public_idl.feature_landing.BaseInfo.country)
}
inline void BaseInfo::set_country(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  country_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:aml.public_idl.feature_landing.BaseInfo.country)
}
inline void BaseInfo::set_country(const char* value,
    size_t size) {
  
  country_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:aml.public_idl.feature_landing.BaseInfo.country)
}
inline std::string* BaseInfo::mutable_country() {
  
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.BaseInfo.country)
  return country_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* BaseInfo::release_country() {
  // @@protoc_insertion_point(field_release:aml.public_idl.feature_landing.BaseInfo.country)
  
  return country_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void BaseInfo::set_allocated_country(std::string* country) {
  if (country != nullptr) {
    
  } else {
    
  }
  country_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), country,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:aml.public_idl.feature_landing.BaseInfo.country)
}
inline std::string* BaseInfo::unsafe_arena_release_country() {
  // @@protoc_insertion_point(field_unsafe_arena_release:aml.public_idl.feature_landing.BaseInfo.country)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return country_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void BaseInfo::unsafe_arena_set_allocated_country(
    std::string* country) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (country != nullptr) {
    
  } else {
    
  }
  country_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      country, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:aml.public_idl.feature_landing.BaseInfo.country)
}

// string item_brand_id = 6;
inline void BaseInfo::clear_item_brand_id() {
  item_brand_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& BaseInfo::item_brand_id() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.BaseInfo.item_brand_id)
  return item_brand_id_.Get();
}
inline void BaseInfo::set_item_brand_id(const std::string& value) {
  
  item_brand_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.BaseInfo.item_brand_id)
}
inline void BaseInfo::set_item_brand_id(std::string&& value) {
  
  item_brand_id_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:aml.public_idl.feature_landing.BaseInfo.item_brand_id)
}
inline void BaseInfo::set_item_brand_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  item_brand_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:aml.public_idl.feature_landing.BaseInfo.item_brand_id)
}
inline void BaseInfo::set_item_brand_id(const char* value,
    size_t size) {
  
  item_brand_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:aml.public_idl.feature_landing.BaseInfo.item_brand_id)
}
inline std::string* BaseInfo::mutable_item_brand_id() {
  
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.BaseInfo.item_brand_id)
  return item_brand_id_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* BaseInfo::release_item_brand_id() {
  // @@protoc_insertion_point(field_release:aml.public_idl.feature_landing.BaseInfo.item_brand_id)
  
  return item_brand_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void BaseInfo::set_allocated_item_brand_id(std::string* item_brand_id) {
  if (item_brand_id != nullptr) {
    
  } else {
    
  }
  item_brand_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), item_brand_id,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:aml.public_idl.feature_landing.BaseInfo.item_brand_id)
}
inline std::string* BaseInfo::unsafe_arena_release_item_brand_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:aml.public_idl.feature_landing.BaseInfo.item_brand_id)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return item_brand_id_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void BaseInfo::unsafe_arena_set_allocated_item_brand_id(
    std::string* item_brand_id) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (item_brand_id != nullptr) {
    
  } else {
    
  }
  item_brand_id_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      item_brand_id, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:aml.public_idl.feature_landing.BaseInfo.item_brand_id)
}

// string item_site_id = 7;
inline void BaseInfo::clear_item_site_id() {
  item_site_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& BaseInfo::item_site_id() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.BaseInfo.item_site_id)
  return item_site_id_.Get();
}
inline void BaseInfo::set_item_site_id(const std::string& value) {
  
  item_site_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.BaseInfo.item_site_id)
}
inline void BaseInfo::set_item_site_id(std::string&& value) {
  
  item_site_id_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:aml.public_idl.feature_landing.BaseInfo.item_site_id)
}
inline void BaseInfo::set_item_site_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  item_site_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:aml.public_idl.feature_landing.BaseInfo.item_site_id)
}
inline void BaseInfo::set_item_site_id(const char* value,
    size_t size) {
  
  item_site_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:aml.public_idl.feature_landing.BaseInfo.item_site_id)
}
inline std::string* BaseInfo::mutable_item_site_id() {
  
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.BaseInfo.item_site_id)
  return item_site_id_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* BaseInfo::release_item_site_id() {
  // @@protoc_insertion_point(field_release:aml.public_idl.feature_landing.BaseInfo.item_site_id)
  
  return item_site_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void BaseInfo::set_allocated_item_site_id(std::string* item_site_id) {
  if (item_site_id != nullptr) {
    
  } else {
    
  }
  item_site_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), item_site_id,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:aml.public_idl.feature_landing.BaseInfo.item_site_id)
}
inline std::string* BaseInfo::unsafe_arena_release_item_site_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:aml.public_idl.feature_landing.BaseInfo.item_site_id)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return item_site_id_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void BaseInfo::unsafe_arena_set_allocated_item_site_id(
    std::string* item_site_id) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (item_site_id != nullptr) {
    
  } else {
    
  }
  item_site_id_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      item_site_id, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:aml.public_idl.feature_landing.BaseInfo.item_site_id)
}

// int64 scene_id = 8;
inline void BaseInfo::clear_scene_id() {
  scene_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 BaseInfo::scene_id() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.BaseInfo.scene_id)
  return scene_id_;
}
inline void BaseInfo::set_scene_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  scene_id_ = value;
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.BaseInfo.scene_id)
}

// repeated int64 user_tag_ids = 9;
inline int BaseInfo::user_tag_ids_size() const {
  return user_tag_ids_.size();
}
inline void BaseInfo::clear_user_tag_ids() {
  user_tag_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 BaseInfo::user_tag_ids(int index) const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.BaseInfo.user_tag_ids)
  return user_tag_ids_.Get(index);
}
inline void BaseInfo::set_user_tag_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  user_tag_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.BaseInfo.user_tag_ids)
}
inline void BaseInfo::add_user_tag_ids(::PROTOBUF_NAMESPACE_ID::int64 value) {
  user_tag_ids_.Add(value);
  // @@protoc_insertion_point(field_add:aml.public_idl.feature_landing.BaseInfo.user_tag_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
BaseInfo::user_tag_ids() const {
  // @@protoc_insertion_point(field_list:aml.public_idl.feature_landing.BaseInfo.user_tag_ids)
  return user_tag_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
BaseInfo::mutable_user_tag_ids() {
  // @@protoc_insertion_point(field_mutable_list:aml.public_idl.feature_landing.BaseInfo.user_tag_ids)
  return &user_tag_ids_;
}

// int32 item_type = 10;
inline void BaseInfo::clear_item_type() {
  item_type_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 BaseInfo::item_type() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.BaseInfo.item_type)
  return item_type_;
}
inline void BaseInfo::set_item_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  item_type_ = value;
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.BaseInfo.item_type)
}

// string user_site_id = 11;
inline void BaseInfo::clear_user_site_id() {
  user_site_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& BaseInfo::user_site_id() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.BaseInfo.user_site_id)
  return user_site_id_.Get();
}
inline void BaseInfo::set_user_site_id(const std::string& value) {
  
  user_site_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.BaseInfo.user_site_id)
}
inline void BaseInfo::set_user_site_id(std::string&& value) {
  
  user_site_id_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:aml.public_idl.feature_landing.BaseInfo.user_site_id)
}
inline void BaseInfo::set_user_site_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  user_site_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:aml.public_idl.feature_landing.BaseInfo.user_site_id)
}
inline void BaseInfo::set_user_site_id(const char* value,
    size_t size) {
  
  user_site_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:aml.public_idl.feature_landing.BaseInfo.user_site_id)
}
inline std::string* BaseInfo::mutable_user_site_id() {
  
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.BaseInfo.user_site_id)
  return user_site_id_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* BaseInfo::release_user_site_id() {
  // @@protoc_insertion_point(field_release:aml.public_idl.feature_landing.BaseInfo.user_site_id)
  
  return user_site_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void BaseInfo::set_allocated_user_site_id(std::string* user_site_id) {
  if (user_site_id != nullptr) {
    
  } else {
    
  }
  user_site_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), user_site_id,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:aml.public_idl.feature_landing.BaseInfo.user_site_id)
}
inline std::string* BaseInfo::unsafe_arena_release_user_site_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:aml.public_idl.feature_landing.BaseInfo.user_site_id)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return user_site_id_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void BaseInfo::unsafe_arena_set_allocated_user_site_id(
    std::string* user_site_id) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (user_site_id != nullptr) {
    
  } else {
    
  }
  user_site_id_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      user_site_id, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:aml.public_idl.feature_landing.BaseInfo.user_site_id)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ItemSourceLogInfo

// int32 item_source = 1;
inline void ItemSourceLogInfo::clear_item_source() {
  item_source_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ItemSourceLogInfo::item_source() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.ItemSourceLogInfo.item_source)
  return item_source_;
}
inline void ItemSourceLogInfo::set_item_source(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  item_source_ = value;
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.ItemSourceLogInfo.item_source)
}

// string item_site_id = 2;
inline void ItemSourceLogInfo::clear_item_site_id() {
  item_site_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& ItemSourceLogInfo::item_site_id() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.ItemSourceLogInfo.item_site_id)
  return item_site_id_.Get();
}
inline void ItemSourceLogInfo::set_item_site_id(const std::string& value) {
  
  item_site_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.ItemSourceLogInfo.item_site_id)
}
inline void ItemSourceLogInfo::set_item_site_id(std::string&& value) {
  
  item_site_id_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:aml.public_idl.feature_landing.ItemSourceLogInfo.item_site_id)
}
inline void ItemSourceLogInfo::set_item_site_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  item_site_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:aml.public_idl.feature_landing.ItemSourceLogInfo.item_site_id)
}
inline void ItemSourceLogInfo::set_item_site_id(const char* value,
    size_t size) {
  
  item_site_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:aml.public_idl.feature_landing.ItemSourceLogInfo.item_site_id)
}
inline std::string* ItemSourceLogInfo::mutable_item_site_id() {
  
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.ItemSourceLogInfo.item_site_id)
  return item_site_id_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* ItemSourceLogInfo::release_item_site_id() {
  // @@protoc_insertion_point(field_release:aml.public_idl.feature_landing.ItemSourceLogInfo.item_site_id)
  
  return item_site_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ItemSourceLogInfo::set_allocated_item_site_id(std::string* item_site_id) {
  if (item_site_id != nullptr) {
    
  } else {
    
  }
  item_site_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), item_site_id,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:aml.public_idl.feature_landing.ItemSourceLogInfo.item_site_id)
}
inline std::string* ItemSourceLogInfo::unsafe_arena_release_item_site_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:aml.public_idl.feature_landing.ItemSourceLogInfo.item_site_id)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return item_site_id_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ItemSourceLogInfo::unsafe_arena_set_allocated_item_site_id(
    std::string* item_site_id) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (item_site_id != nullptr) {
    
  } else {
    
  }
  item_site_id_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      item_site_id, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:aml.public_idl.feature_landing.ItemSourceLogInfo.item_site_id)
}

// string item_brand_id = 3;
inline void ItemSourceLogInfo::clear_item_brand_id() {
  item_brand_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& ItemSourceLogInfo::item_brand_id() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.ItemSourceLogInfo.item_brand_id)
  return item_brand_id_.Get();
}
inline void ItemSourceLogInfo::set_item_brand_id(const std::string& value) {
  
  item_brand_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.ItemSourceLogInfo.item_brand_id)
}
inline void ItemSourceLogInfo::set_item_brand_id(std::string&& value) {
  
  item_brand_id_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:aml.public_idl.feature_landing.ItemSourceLogInfo.item_brand_id)
}
inline void ItemSourceLogInfo::set_item_brand_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  item_brand_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:aml.public_idl.feature_landing.ItemSourceLogInfo.item_brand_id)
}
inline void ItemSourceLogInfo::set_item_brand_id(const char* value,
    size_t size) {
  
  item_brand_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:aml.public_idl.feature_landing.ItemSourceLogInfo.item_brand_id)
}
inline std::string* ItemSourceLogInfo::mutable_item_brand_id() {
  
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.ItemSourceLogInfo.item_brand_id)
  return item_brand_id_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* ItemSourceLogInfo::release_item_brand_id() {
  // @@protoc_insertion_point(field_release:aml.public_idl.feature_landing.ItemSourceLogInfo.item_brand_id)
  
  return item_brand_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ItemSourceLogInfo::set_allocated_item_brand_id(std::string* item_brand_id) {
  if (item_brand_id != nullptr) {
    
  } else {
    
  }
  item_brand_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), item_brand_id,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:aml.public_idl.feature_landing.ItemSourceLogInfo.item_brand_id)
}
inline std::string* ItemSourceLogInfo::unsafe_arena_release_item_brand_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:aml.public_idl.feature_landing.ItemSourceLogInfo.item_brand_id)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return item_brand_id_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ItemSourceLogInfo::unsafe_arena_set_allocated_item_brand_id(
    std::string* item_brand_id) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (item_brand_id != nullptr) {
    
  } else {
    
  }
  item_brand_id_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      item_brand_id, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:aml.public_idl.feature_landing.ItemSourceLogInfo.item_brand_id)
}

// string item_country = 4;
inline void ItemSourceLogInfo::clear_item_country() {
  item_country_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& ItemSourceLogInfo::item_country() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.ItemSourceLogInfo.item_country)
  return item_country_.Get();
}
inline void ItemSourceLogInfo::set_item_country(const std::string& value) {
  
  item_country_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.ItemSourceLogInfo.item_country)
}
inline void ItemSourceLogInfo::set_item_country(std::string&& value) {
  
  item_country_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:aml.public_idl.feature_landing.ItemSourceLogInfo.item_country)
}
inline void ItemSourceLogInfo::set_item_country(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  item_country_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:aml.public_idl.feature_landing.ItemSourceLogInfo.item_country)
}
inline void ItemSourceLogInfo::set_item_country(const char* value,
    size_t size) {
  
  item_country_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:aml.public_idl.feature_landing.ItemSourceLogInfo.item_country)
}
inline std::string* ItemSourceLogInfo::mutable_item_country() {
  
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.ItemSourceLogInfo.item_country)
  return item_country_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* ItemSourceLogInfo::release_item_country() {
  // @@protoc_insertion_point(field_release:aml.public_idl.feature_landing.ItemSourceLogInfo.item_country)
  
  return item_country_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ItemSourceLogInfo::set_allocated_item_country(std::string* item_country) {
  if (item_country != nullptr) {
    
  } else {
    
  }
  item_country_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), item_country,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:aml.public_idl.feature_landing.ItemSourceLogInfo.item_country)
}
inline std::string* ItemSourceLogInfo::unsafe_arena_release_item_country() {
  // @@protoc_insertion_point(field_unsafe_arena_release:aml.public_idl.feature_landing.ItemSourceLogInfo.item_country)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return item_country_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ItemSourceLogInfo::unsafe_arena_set_allocated_item_country(
    std::string* item_country) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (item_country != nullptr) {
    
  } else {
    
  }
  item_country_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      item_country, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:aml.public_idl.feature_landing.ItemSourceLogInfo.item_country)
}

// string step_id = 5;
inline void ItemSourceLogInfo::clear_step_id() {
  step_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& ItemSourceLogInfo::step_id() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.ItemSourceLogInfo.step_id)
  return step_id_.Get();
}
inline void ItemSourceLogInfo::set_step_id(const std::string& value) {
  
  step_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.ItemSourceLogInfo.step_id)
}
inline void ItemSourceLogInfo::set_step_id(std::string&& value) {
  
  step_id_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:aml.public_idl.feature_landing.ItemSourceLogInfo.step_id)
}
inline void ItemSourceLogInfo::set_step_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  step_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:aml.public_idl.feature_landing.ItemSourceLogInfo.step_id)
}
inline void ItemSourceLogInfo::set_step_id(const char* value,
    size_t size) {
  
  step_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:aml.public_idl.feature_landing.ItemSourceLogInfo.step_id)
}
inline std::string* ItemSourceLogInfo::mutable_step_id() {
  
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.ItemSourceLogInfo.step_id)
  return step_id_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* ItemSourceLogInfo::release_step_id() {
  // @@protoc_insertion_point(field_release:aml.public_idl.feature_landing.ItemSourceLogInfo.step_id)
  
  return step_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ItemSourceLogInfo::set_allocated_step_id(std::string* step_id) {
  if (step_id != nullptr) {
    
  } else {
    
  }
  step_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), step_id,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:aml.public_idl.feature_landing.ItemSourceLogInfo.step_id)
}
inline std::string* ItemSourceLogInfo::unsafe_arena_release_step_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:aml.public_idl.feature_landing.ItemSourceLogInfo.step_id)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return step_id_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ItemSourceLogInfo::unsafe_arena_set_allocated_step_id(
    std::string* step_id) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (step_id != nullptr) {
    
  } else {
    
  }
  step_id_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      step_id, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:aml.public_idl.feature_landing.ItemSourceLogInfo.step_id)
}

// repeated int64 item_ids = 8;
inline int ItemSourceLogInfo::item_ids_size() const {
  return item_ids_.size();
}
inline void ItemSourceLogInfo::clear_item_ids() {
  item_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 ItemSourceLogInfo::item_ids(int index) const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.ItemSourceLogInfo.item_ids)
  return item_ids_.Get(index);
}
inline void ItemSourceLogInfo::set_item_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  item_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.ItemSourceLogInfo.item_ids)
}
inline void ItemSourceLogInfo::add_item_ids(::PROTOBUF_NAMESPACE_ID::int64 value) {
  item_ids_.Add(value);
  // @@protoc_insertion_point(field_add:aml.public_idl.feature_landing.ItemSourceLogInfo.item_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
ItemSourceLogInfo::item_ids() const {
  // @@protoc_insertion_point(field_list:aml.public_idl.feature_landing.ItemSourceLogInfo.item_ids)
  return item_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
ItemSourceLogInfo::mutable_item_ids() {
  // @@protoc_insertion_point(field_mutable_list:aml.public_idl.feature_landing.ItemSourceLogInfo.item_ids)
  return &item_ids_;
}

// repeated .abc.recommend_plt.mmp.ModelNameInfo prerank_model_infos = 10;
inline int ItemSourceLogInfo::prerank_model_infos_size() const {
  return prerank_model_infos_.size();
}
inline ::abc::recommend_plt::mmp::ModelNameInfo* ItemSourceLogInfo::mutable_prerank_model_infos(int index) {
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.ItemSourceLogInfo.prerank_model_infos)
  return prerank_model_infos_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::mmp::ModelNameInfo >*
ItemSourceLogInfo::mutable_prerank_model_infos() {
  // @@protoc_insertion_point(field_mutable_list:aml.public_idl.feature_landing.ItemSourceLogInfo.prerank_model_infos)
  return &prerank_model_infos_;
}
inline const ::abc::recommend_plt::mmp::ModelNameInfo& ItemSourceLogInfo::prerank_model_infos(int index) const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.ItemSourceLogInfo.prerank_model_infos)
  return prerank_model_infos_.Get(index);
}
inline ::abc::recommend_plt::mmp::ModelNameInfo* ItemSourceLogInfo::add_prerank_model_infos() {
  // @@protoc_insertion_point(field_add:aml.public_idl.feature_landing.ItemSourceLogInfo.prerank_model_infos)
  return prerank_model_infos_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::mmp::ModelNameInfo >&
ItemSourceLogInfo::prerank_model_infos() const {
  // @@protoc_insertion_point(field_list:aml.public_idl.feature_landing.ItemSourceLogInfo.prerank_model_infos)
  return prerank_model_infos_;
}

// repeated .aml.public_idl.feature_landing.PreRankItemInfo prerank_items_info = 11;
inline int ItemSourceLogInfo::prerank_items_info_size() const {
  return prerank_items_info_.size();
}
inline void ItemSourceLogInfo::clear_prerank_items_info() {
  prerank_items_info_.Clear();
}
inline ::aml::public_idl::feature_landing::PreRankItemInfo* ItemSourceLogInfo::mutable_prerank_items_info(int index) {
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.ItemSourceLogInfo.prerank_items_info)
  return prerank_items_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::PreRankItemInfo >*
ItemSourceLogInfo::mutable_prerank_items_info() {
  // @@protoc_insertion_point(field_mutable_list:aml.public_idl.feature_landing.ItemSourceLogInfo.prerank_items_info)
  return &prerank_items_info_;
}
inline const ::aml::public_idl::feature_landing::PreRankItemInfo& ItemSourceLogInfo::prerank_items_info(int index) const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.ItemSourceLogInfo.prerank_items_info)
  return prerank_items_info_.Get(index);
}
inline ::aml::public_idl::feature_landing::PreRankItemInfo* ItemSourceLogInfo::add_prerank_items_info() {
  // @@protoc_insertion_point(field_add:aml.public_idl.feature_landing.ItemSourceLogInfo.prerank_items_info)
  return prerank_items_info_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::PreRankItemInfo >&
ItemSourceLogInfo::prerank_items_info() const {
  // @@protoc_insertion_point(field_list:aml.public_idl.feature_landing.ItemSourceLogInfo.prerank_items_info)
  return prerank_items_info_;
}

// repeated .abc.recommend_plt.mmp.ModelNameInfo rank_model_infos = 12;
inline int ItemSourceLogInfo::rank_model_infos_size() const {
  return rank_model_infos_.size();
}
inline ::abc::recommend_plt::mmp::ModelNameInfo* ItemSourceLogInfo::mutable_rank_model_infos(int index) {
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.ItemSourceLogInfo.rank_model_infos)
  return rank_model_infos_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::mmp::ModelNameInfo >*
ItemSourceLogInfo::mutable_rank_model_infos() {
  // @@protoc_insertion_point(field_mutable_list:aml.public_idl.feature_landing.ItemSourceLogInfo.rank_model_infos)
  return &rank_model_infos_;
}
inline const ::abc::recommend_plt::mmp::ModelNameInfo& ItemSourceLogInfo::rank_model_infos(int index) const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.ItemSourceLogInfo.rank_model_infos)
  return rank_model_infos_.Get(index);
}
inline ::abc::recommend_plt::mmp::ModelNameInfo* ItemSourceLogInfo::add_rank_model_infos() {
  // @@protoc_insertion_point(field_add:aml.public_idl.feature_landing.ItemSourceLogInfo.rank_model_infos)
  return rank_model_infos_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::mmp::ModelNameInfo >&
ItemSourceLogInfo::rank_model_infos() const {
  // @@protoc_insertion_point(field_list:aml.public_idl.feature_landing.ItemSourceLogInfo.rank_model_infos)
  return rank_model_infos_;
}

// repeated .aml.public_idl.feature_landing.ItemInfo rank_items_info = 13;
inline int ItemSourceLogInfo::rank_items_info_size() const {
  return rank_items_info_.size();
}
inline void ItemSourceLogInfo::clear_rank_items_info() {
  rank_items_info_.Clear();
}
inline ::aml::public_idl::feature_landing::ItemInfo* ItemSourceLogInfo::mutable_rank_items_info(int index) {
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.ItemSourceLogInfo.rank_items_info)
  return rank_items_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::ItemInfo >*
ItemSourceLogInfo::mutable_rank_items_info() {
  // @@protoc_insertion_point(field_mutable_list:aml.public_idl.feature_landing.ItemSourceLogInfo.rank_items_info)
  return &rank_items_info_;
}
inline const ::aml::public_idl::feature_landing::ItemInfo& ItemSourceLogInfo::rank_items_info(int index) const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.ItemSourceLogInfo.rank_items_info)
  return rank_items_info_.Get(index);
}
inline ::aml::public_idl::feature_landing::ItemInfo* ItemSourceLogInfo::add_rank_items_info() {
  // @@protoc_insertion_point(field_add:aml.public_idl.feature_landing.ItemSourceLogInfo.rank_items_info)
  return rank_items_info_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::ItemInfo >&
ItemSourceLogInfo::rank_items_info() const {
  // @@protoc_insertion_point(field_list:aml.public_idl.feature_landing.ItemSourceLogInfo.rank_items_info)
  return rank_items_info_;
}

// map<string, .abc.recommend_plt.fmp.Feature> context_features = 14;
inline int ItemSourceLogInfo::context_features_size() const {
  return context_features_.size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >&
ItemSourceLogInfo::context_features() const {
  // @@protoc_insertion_point(field_map:aml.public_idl.feature_landing.ItemSourceLogInfo.context_features)
  return context_features_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >*
ItemSourceLogInfo::mutable_context_features() {
  // @@protoc_insertion_point(field_mutable_map:aml.public_idl.feature_landing.ItemSourceLogInfo.context_features)
  return context_features_.MutableMap();
}

// map<string, .abc.recommend_plt.fmp.Feature> item_features = 15;
inline int ItemSourceLogInfo::item_features_size() const {
  return item_features_.size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >&
ItemSourceLogInfo::item_features() const {
  // @@protoc_insertion_point(field_map:aml.public_idl.feature_landing.ItemSourceLogInfo.item_features)
  return item_features_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >*
ItemSourceLogInfo::mutable_item_features() {
  // @@protoc_insertion_point(field_mutable_map:aml.public_idl.feature_landing.ItemSourceLogInfo.item_features)
  return item_features_.MutableMap();
}

// .abc.recommend_plt.fmp.AmlLandingExtraData extra_data = 20;
inline bool ItemSourceLogInfo::has_extra_data() const {
  return this != internal_default_instance() && extra_data_ != nullptr;
}
inline const ::abc::recommend_plt::fmp::AmlLandingExtraData& ItemSourceLogInfo::extra_data() const {
  const ::abc::recommend_plt::fmp::AmlLandingExtraData* p = extra_data_;
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.ItemSourceLogInfo.extra_data)
  return p != nullptr ? *p : *reinterpret_cast<const ::abc::recommend_plt::fmp::AmlLandingExtraData*>(
      &::abc::recommend_plt::fmp::_AmlLandingExtraData_default_instance_);
}
inline ::abc::recommend_plt::fmp::AmlLandingExtraData* ItemSourceLogInfo::release_extra_data() {
  // @@protoc_insertion_point(field_release:aml.public_idl.feature_landing.ItemSourceLogInfo.extra_data)
  
  ::abc::recommend_plt::fmp::AmlLandingExtraData* temp = extra_data_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  extra_data_ = nullptr;
  return temp;
}
inline ::abc::recommend_plt::fmp::AmlLandingExtraData* ItemSourceLogInfo::unsafe_arena_release_extra_data() {
  // @@protoc_insertion_point(field_unsafe_arena_release:aml.public_idl.feature_landing.ItemSourceLogInfo.extra_data)
  
  ::abc::recommend_plt::fmp::AmlLandingExtraData* temp = extra_data_;
  extra_data_ = nullptr;
  return temp;
}
inline ::abc::recommend_plt::fmp::AmlLandingExtraData* ItemSourceLogInfo::mutable_extra_data() {
  
  if (extra_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::abc::recommend_plt::fmp::AmlLandingExtraData>(GetArenaNoVirtual());
    extra_data_ = p;
  }
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.ItemSourceLogInfo.extra_data)
  return extra_data_;
}
inline void ItemSourceLogInfo::set_allocated_extra_data(::abc::recommend_plt::fmp::AmlLandingExtraData* extra_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(extra_data_);
  }
  if (extra_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(extra_data)->GetArena();
    if (message_arena != submessage_arena) {
      extra_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, extra_data, submessage_arena);
    }
    
  } else {
    
  }
  extra_data_ = extra_data;
  // @@protoc_insertion_point(field_set_allocated:aml.public_idl.feature_landing.ItemSourceLogInfo.extra_data)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// FeatureSampleV1

// string trace_id = 1;
inline void FeatureSampleV1::clear_trace_id() {
  trace_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& FeatureSampleV1::trace_id() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.FeatureSampleV1.trace_id)
  return trace_id_.Get();
}
inline void FeatureSampleV1::set_trace_id(const std::string& value) {
  
  trace_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.FeatureSampleV1.trace_id)
}
inline void FeatureSampleV1::set_trace_id(std::string&& value) {
  
  trace_id_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:aml.public_idl.feature_landing.FeatureSampleV1.trace_id)
}
inline void FeatureSampleV1::set_trace_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  trace_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:aml.public_idl.feature_landing.FeatureSampleV1.trace_id)
}
inline void FeatureSampleV1::set_trace_id(const char* value,
    size_t size) {
  
  trace_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:aml.public_idl.feature_landing.FeatureSampleV1.trace_id)
}
inline std::string* FeatureSampleV1::mutable_trace_id() {
  
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.FeatureSampleV1.trace_id)
  return trace_id_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* FeatureSampleV1::release_trace_id() {
  // @@protoc_insertion_point(field_release:aml.public_idl.feature_landing.FeatureSampleV1.trace_id)
  
  return trace_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void FeatureSampleV1::set_allocated_trace_id(std::string* trace_id) {
  if (trace_id != nullptr) {
    
  } else {
    
  }
  trace_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), trace_id,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:aml.public_idl.feature_landing.FeatureSampleV1.trace_id)
}
inline std::string* FeatureSampleV1::unsafe_arena_release_trace_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:aml.public_idl.feature_landing.FeatureSampleV1.trace_id)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return trace_id_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void FeatureSampleV1::unsafe_arena_set_allocated_trace_id(
    std::string* trace_id) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (trace_id != nullptr) {
    
  } else {
    
  }
  trace_id_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      trace_id, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:aml.public_idl.feature_landing.FeatureSampleV1.trace_id)
}

// repeated int64 item_ids = 2;
inline int FeatureSampleV1::item_ids_size() const {
  return item_ids_.size();
}
inline void FeatureSampleV1::clear_item_ids() {
  item_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 FeatureSampleV1::item_ids(int index) const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.FeatureSampleV1.item_ids)
  return item_ids_.Get(index);
}
inline void FeatureSampleV1::set_item_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  item_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.FeatureSampleV1.item_ids)
}
inline void FeatureSampleV1::add_item_ids(::PROTOBUF_NAMESPACE_ID::int64 value) {
  item_ids_.Add(value);
  // @@protoc_insertion_point(field_add:aml.public_idl.feature_landing.FeatureSampleV1.item_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
FeatureSampleV1::item_ids() const {
  // @@protoc_insertion_point(field_list:aml.public_idl.feature_landing.FeatureSampleV1.item_ids)
  return item_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
FeatureSampleV1::mutable_item_ids() {
  // @@protoc_insertion_point(field_mutable_list:aml.public_idl.feature_landing.FeatureSampleV1.item_ids)
  return &item_ids_;
}

// .aml.public_idl.feature_landing.BaseInfo base_info = 3;
inline bool FeatureSampleV1::has_base_info() const {
  return this != internal_default_instance() && base_info_ != nullptr;
}
inline void FeatureSampleV1::clear_base_info() {
  if (GetArenaNoVirtual() == nullptr && base_info_ != nullptr) {
    delete base_info_;
  }
  base_info_ = nullptr;
}
inline const ::aml::public_idl::feature_landing::BaseInfo& FeatureSampleV1::base_info() const {
  const ::aml::public_idl::feature_landing::BaseInfo* p = base_info_;
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.FeatureSampleV1.base_info)
  return p != nullptr ? *p : *reinterpret_cast<const ::aml::public_idl::feature_landing::BaseInfo*>(
      &::aml::public_idl::feature_landing::_BaseInfo_default_instance_);
}
inline ::aml::public_idl::feature_landing::BaseInfo* FeatureSampleV1::release_base_info() {
  // @@protoc_insertion_point(field_release:aml.public_idl.feature_landing.FeatureSampleV1.base_info)
  
  ::aml::public_idl::feature_landing::BaseInfo* temp = base_info_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  base_info_ = nullptr;
  return temp;
}
inline ::aml::public_idl::feature_landing::BaseInfo* FeatureSampleV1::unsafe_arena_release_base_info() {
  // @@protoc_insertion_point(field_unsafe_arena_release:aml.public_idl.feature_landing.FeatureSampleV1.base_info)
  
  ::aml::public_idl::feature_landing::BaseInfo* temp = base_info_;
  base_info_ = nullptr;
  return temp;
}
inline ::aml::public_idl::feature_landing::BaseInfo* FeatureSampleV1::mutable_base_info() {
  
  if (base_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::aml::public_idl::feature_landing::BaseInfo>(GetArenaNoVirtual());
    base_info_ = p;
  }
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.FeatureSampleV1.base_info)
  return base_info_;
}
inline void FeatureSampleV1::set_allocated_base_info(::aml::public_idl::feature_landing::BaseInfo* base_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete base_info_;
  }
  if (base_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(base_info);
    if (message_arena != submessage_arena) {
      base_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, base_info, submessage_arena);
    }
    
  } else {
    
  }
  base_info_ = base_info;
  // @@protoc_insertion_point(field_set_allocated:aml.public_idl.feature_landing.FeatureSampleV1.base_info)
}

// repeated .aml.public_idl.feature_landing.ItemInfo items_info = 4;
inline int FeatureSampleV1::items_info_size() const {
  return items_info_.size();
}
inline void FeatureSampleV1::clear_items_info() {
  items_info_.Clear();
}
inline ::aml::public_idl::feature_landing::ItemInfo* FeatureSampleV1::mutable_items_info(int index) {
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.FeatureSampleV1.items_info)
  return items_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::ItemInfo >*
FeatureSampleV1::mutable_items_info() {
  // @@protoc_insertion_point(field_mutable_list:aml.public_idl.feature_landing.FeatureSampleV1.items_info)
  return &items_info_;
}
inline const ::aml::public_idl::feature_landing::ItemInfo& FeatureSampleV1::items_info(int index) const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.FeatureSampleV1.items_info)
  return items_info_.Get(index);
}
inline ::aml::public_idl::feature_landing::ItemInfo* FeatureSampleV1::add_items_info() {
  // @@protoc_insertion_point(field_add:aml.public_idl.feature_landing.FeatureSampleV1.items_info)
  return items_info_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::ItemInfo >&
FeatureSampleV1::items_info() const {
  // @@protoc_insertion_point(field_list:aml.public_idl.feature_landing.FeatureSampleV1.items_info)
  return items_info_;
}

// repeated .abc.recommend_plt.mmp.ModelNameInfo model_infos = 5;
inline int FeatureSampleV1::model_infos_size() const {
  return model_infos_.size();
}
inline ::abc::recommend_plt::mmp::ModelNameInfo* FeatureSampleV1::mutable_model_infos(int index) {
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.FeatureSampleV1.model_infos)
  return model_infos_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::mmp::ModelNameInfo >*
FeatureSampleV1::mutable_model_infos() {
  // @@protoc_insertion_point(field_mutable_list:aml.public_idl.feature_landing.FeatureSampleV1.model_infos)
  return &model_infos_;
}
inline const ::abc::recommend_plt::mmp::ModelNameInfo& FeatureSampleV1::model_infos(int index) const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.FeatureSampleV1.model_infos)
  return model_infos_.Get(index);
}
inline ::abc::recommend_plt::mmp::ModelNameInfo* FeatureSampleV1::add_model_infos() {
  // @@protoc_insertion_point(field_add:aml.public_idl.feature_landing.FeatureSampleV1.model_infos)
  return model_infos_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::mmp::ModelNameInfo >&
FeatureSampleV1::model_infos() const {
  // @@protoc_insertion_point(field_list:aml.public_idl.feature_landing.FeatureSampleV1.model_infos)
  return model_infos_;
}

// map<string, .abc.recommend_plt.fmp.Feature> context_features = 6;
inline int FeatureSampleV1::context_features_size() const {
  return context_features_.size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >&
FeatureSampleV1::context_features() const {
  // @@protoc_insertion_point(field_map:aml.public_idl.feature_landing.FeatureSampleV1.context_features)
  return context_features_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >*
FeatureSampleV1::mutable_context_features() {
  // @@protoc_insertion_point(field_mutable_map:aml.public_idl.feature_landing.FeatureSampleV1.context_features)
  return context_features_.MutableMap();
}

// map<string, .abc.recommend_plt.fmp.Feature> item_features = 7;
inline int FeatureSampleV1::item_features_size() const {
  return item_features_.size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >&
FeatureSampleV1::item_features() const {
  // @@protoc_insertion_point(field_map:aml.public_idl.feature_landing.FeatureSampleV1.item_features)
  return item_features_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >*
FeatureSampleV1::mutable_item_features() {
  // @@protoc_insertion_point(field_mutable_map:aml.public_idl.feature_landing.FeatureSampleV1.item_features)
  return item_features_.MutableMap();
}

// repeated .aml.public_idl.feature_landing.PreRankItemInfo prerank_items_info = 8;
inline int FeatureSampleV1::prerank_items_info_size() const {
  return prerank_items_info_.size();
}
inline void FeatureSampleV1::clear_prerank_items_info() {
  prerank_items_info_.Clear();
}
inline ::aml::public_idl::feature_landing::PreRankItemInfo* FeatureSampleV1::mutable_prerank_items_info(int index) {
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.FeatureSampleV1.prerank_items_info)
  return prerank_items_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::PreRankItemInfo >*
FeatureSampleV1::mutable_prerank_items_info() {
  // @@protoc_insertion_point(field_mutable_list:aml.public_idl.feature_landing.FeatureSampleV1.prerank_items_info)
  return &prerank_items_info_;
}
inline const ::aml::public_idl::feature_landing::PreRankItemInfo& FeatureSampleV1::prerank_items_info(int index) const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.FeatureSampleV1.prerank_items_info)
  return prerank_items_info_.Get(index);
}
inline ::aml::public_idl::feature_landing::PreRankItemInfo* FeatureSampleV1::add_prerank_items_info() {
  // @@protoc_insertion_point(field_add:aml.public_idl.feature_landing.FeatureSampleV1.prerank_items_info)
  return prerank_items_info_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::PreRankItemInfo >&
FeatureSampleV1::prerank_items_info() const {
  // @@protoc_insertion_point(field_list:aml.public_idl.feature_landing.FeatureSampleV1.prerank_items_info)
  return prerank_items_info_;
}

// repeated .abc.recommend_plt.mmp.ModelNameInfo prerank_model_infos = 9;
inline int FeatureSampleV1::prerank_model_infos_size() const {
  return prerank_model_infos_.size();
}
inline ::abc::recommend_plt::mmp::ModelNameInfo* FeatureSampleV1::mutable_prerank_model_infos(int index) {
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.FeatureSampleV1.prerank_model_infos)
  return prerank_model_infos_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::mmp::ModelNameInfo >*
FeatureSampleV1::mutable_prerank_model_infos() {
  // @@protoc_insertion_point(field_mutable_list:aml.public_idl.feature_landing.FeatureSampleV1.prerank_model_infos)
  return &prerank_model_infos_;
}
inline const ::abc::recommend_plt::mmp::ModelNameInfo& FeatureSampleV1::prerank_model_infos(int index) const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.FeatureSampleV1.prerank_model_infos)
  return prerank_model_infos_.Get(index);
}
inline ::abc::recommend_plt::mmp::ModelNameInfo* FeatureSampleV1::add_prerank_model_infos() {
  // @@protoc_insertion_point(field_add:aml.public_idl.feature_landing.FeatureSampleV1.prerank_model_infos)
  return prerank_model_infos_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::abc::recommend_plt::mmp::ModelNameInfo >&
FeatureSampleV1::prerank_model_infos() const {
  // @@protoc_insertion_point(field_list:aml.public_idl.feature_landing.FeatureSampleV1.prerank_model_infos)
  return prerank_model_infos_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// FeatureSampleV2

// string trace_id = 1;
inline void FeatureSampleV2::clear_trace_id() {
  trace_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& FeatureSampleV2::trace_id() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.FeatureSampleV2.trace_id)
  return trace_id_.Get();
}
inline void FeatureSampleV2::set_trace_id(const std::string& value) {
  
  trace_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.FeatureSampleV2.trace_id)
}
inline void FeatureSampleV2::set_trace_id(std::string&& value) {
  
  trace_id_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:aml.public_idl.feature_landing.FeatureSampleV2.trace_id)
}
inline void FeatureSampleV2::set_trace_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  trace_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:aml.public_idl.feature_landing.FeatureSampleV2.trace_id)
}
inline void FeatureSampleV2::set_trace_id(const char* value,
    size_t size) {
  
  trace_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:aml.public_idl.feature_landing.FeatureSampleV2.trace_id)
}
inline std::string* FeatureSampleV2::mutable_trace_id() {
  
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.FeatureSampleV2.trace_id)
  return trace_id_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* FeatureSampleV2::release_trace_id() {
  // @@protoc_insertion_point(field_release:aml.public_idl.feature_landing.FeatureSampleV2.trace_id)
  
  return trace_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void FeatureSampleV2::set_allocated_trace_id(std::string* trace_id) {
  if (trace_id != nullptr) {
    
  } else {
    
  }
  trace_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), trace_id,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:aml.public_idl.feature_landing.FeatureSampleV2.trace_id)
}
inline std::string* FeatureSampleV2::unsafe_arena_release_trace_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:aml.public_idl.feature_landing.FeatureSampleV2.trace_id)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return trace_id_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void FeatureSampleV2::unsafe_arena_set_allocated_trace_id(
    std::string* trace_id) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (trace_id != nullptr) {
    
  } else {
    
  }
  trace_id_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      trace_id, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:aml.public_idl.feature_landing.FeatureSampleV2.trace_id)
}

// .aml.public_idl.feature_landing.BaseInfo base_info = 2;
inline bool FeatureSampleV2::has_base_info() const {
  return this != internal_default_instance() && base_info_ != nullptr;
}
inline void FeatureSampleV2::clear_base_info() {
  if (GetArenaNoVirtual() == nullptr && base_info_ != nullptr) {
    delete base_info_;
  }
  base_info_ = nullptr;
}
inline const ::aml::public_idl::feature_landing::BaseInfo& FeatureSampleV2::base_info() const {
  const ::aml::public_idl::feature_landing::BaseInfo* p = base_info_;
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.FeatureSampleV2.base_info)
  return p != nullptr ? *p : *reinterpret_cast<const ::aml::public_idl::feature_landing::BaseInfo*>(
      &::aml::public_idl::feature_landing::_BaseInfo_default_instance_);
}
inline ::aml::public_idl::feature_landing::BaseInfo* FeatureSampleV2::release_base_info() {
  // @@protoc_insertion_point(field_release:aml.public_idl.feature_landing.FeatureSampleV2.base_info)
  
  ::aml::public_idl::feature_landing::BaseInfo* temp = base_info_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  base_info_ = nullptr;
  return temp;
}
inline ::aml::public_idl::feature_landing::BaseInfo* FeatureSampleV2::unsafe_arena_release_base_info() {
  // @@protoc_insertion_point(field_unsafe_arena_release:aml.public_idl.feature_landing.FeatureSampleV2.base_info)
  
  ::aml::public_idl::feature_landing::BaseInfo* temp = base_info_;
  base_info_ = nullptr;
  return temp;
}
inline ::aml::public_idl::feature_landing::BaseInfo* FeatureSampleV2::mutable_base_info() {
  
  if (base_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::aml::public_idl::feature_landing::BaseInfo>(GetArenaNoVirtual());
    base_info_ = p;
  }
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.FeatureSampleV2.base_info)
  return base_info_;
}
inline void FeatureSampleV2::set_allocated_base_info(::aml::public_idl::feature_landing::BaseInfo* base_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete base_info_;
  }
  if (base_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(base_info);
    if (message_arena != submessage_arena) {
      base_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, base_info, submessage_arena);
    }
    
  } else {
    
  }
  base_info_ = base_info;
  // @@protoc_insertion_point(field_set_allocated:aml.public_idl.feature_landing.FeatureSampleV2.base_info)
}

// repeated int64 item_ids = 3;
inline int FeatureSampleV2::item_ids_size() const {
  return item_ids_.size();
}
inline void FeatureSampleV2::clear_item_ids() {
  item_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int64 FeatureSampleV2::item_ids(int index) const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.FeatureSampleV2.item_ids)
  return item_ids_.Get(index);
}
inline void FeatureSampleV2::set_item_ids(int index, ::PROTOBUF_NAMESPACE_ID::int64 value) {
  item_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.FeatureSampleV2.item_ids)
}
inline void FeatureSampleV2::add_item_ids(::PROTOBUF_NAMESPACE_ID::int64 value) {
  item_ids_.Add(value);
  // @@protoc_insertion_point(field_add:aml.public_idl.feature_landing.FeatureSampleV2.item_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >&
FeatureSampleV2::item_ids() const {
  // @@protoc_insertion_point(field_list:aml.public_idl.feature_landing.FeatureSampleV2.item_ids)
  return item_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int64 >*
FeatureSampleV2::mutable_item_ids() {
  // @@protoc_insertion_point(field_mutable_list:aml.public_idl.feature_landing.FeatureSampleV2.item_ids)
  return &item_ids_;
}

// map<string, .abc.recommend_plt.fmp.Feature> context_features = 10;
inline int FeatureSampleV2::context_features_size() const {
  return context_features_.size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >&
FeatureSampleV2::context_features() const {
  // @@protoc_insertion_point(field_map:aml.public_idl.feature_landing.FeatureSampleV2.context_features)
  return context_features_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >*
FeatureSampleV2::mutable_context_features() {
  // @@protoc_insertion_point(field_mutable_map:aml.public_idl.feature_landing.FeatureSampleV2.context_features)
  return context_features_.MutableMap();
}

// map<string, .abc.recommend_plt.fmp.Feature> item_features = 11;
inline int FeatureSampleV2::item_features_size() const {
  return item_features_.size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >&
FeatureSampleV2::item_features() const {
  // @@protoc_insertion_point(field_map:aml.public_idl.feature_landing.FeatureSampleV2.item_features)
  return item_features_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::abc::recommend_plt::fmp::Feature >*
FeatureSampleV2::mutable_item_features() {
  // @@protoc_insertion_point(field_mutable_map:aml.public_idl.feature_landing.FeatureSampleV2.item_features)
  return item_features_.MutableMap();
}

// repeated .aml.public_idl.feature_landing.ItemSourceLogInfo item_source_log_info = 12;
inline int FeatureSampleV2::item_source_log_info_size() const {
  return item_source_log_info_.size();
}
inline void FeatureSampleV2::clear_item_source_log_info() {
  item_source_log_info_.Clear();
}
inline ::aml::public_idl::feature_landing::ItemSourceLogInfo* FeatureSampleV2::mutable_item_source_log_info(int index) {
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.FeatureSampleV2.item_source_log_info)
  return item_source_log_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::ItemSourceLogInfo >*
FeatureSampleV2::mutable_item_source_log_info() {
  // @@protoc_insertion_point(field_mutable_list:aml.public_idl.feature_landing.FeatureSampleV2.item_source_log_info)
  return &item_source_log_info_;
}
inline const ::aml::public_idl::feature_landing::ItemSourceLogInfo& FeatureSampleV2::item_source_log_info(int index) const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.FeatureSampleV2.item_source_log_info)
  return item_source_log_info_.Get(index);
}
inline ::aml::public_idl::feature_landing::ItemSourceLogInfo* FeatureSampleV2::add_item_source_log_info() {
  // @@protoc_insertion_point(field_add:aml.public_idl.feature_landing.FeatureSampleV2.item_source_log_info)
  return item_source_log_info_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::aml::public_idl::feature_landing::ItemSourceLogInfo >&
FeatureSampleV2::item_source_log_info() const {
  // @@protoc_insertion_point(field_list:aml.public_idl.feature_landing.FeatureSampleV2.item_source_log_info)
  return item_source_log_info_;
}

// -------------------------------------------------------------------

// AmlFeatureSample

// .aml.public_idl.feature_landing.FeatureSampleProtol protol_version = 1;
inline void AmlFeatureSample::clear_protol_version() {
  protol_version_ = 0;
}
inline ::aml::public_idl::feature_landing::FeatureSampleProtol AmlFeatureSample::protol_version() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.AmlFeatureSample.protol_version)
  return static_cast< ::aml::public_idl::feature_landing::FeatureSampleProtol >(protol_version_);
}
inline void AmlFeatureSample::set_protol_version(::aml::public_idl::feature_landing::FeatureSampleProtol value) {
  
  protol_version_ = value;
  // @@protoc_insertion_point(field_set:aml.public_idl.feature_landing.AmlFeatureSample.protol_version)
}

// .aml.public_idl.feature_landing.FeatureSampleV1 feature_sample_v1 = 2;
inline bool AmlFeatureSample::has_feature_sample_v1() const {
  return feature_sample_case() == kFeatureSampleV1;
}
inline void AmlFeatureSample::set_has_feature_sample_v1() {
  _oneof_case_[0] = kFeatureSampleV1;
}
inline void AmlFeatureSample::clear_feature_sample_v1() {
  if (has_feature_sample_v1()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete feature_sample_.feature_sample_v1_;
    }
    clear_has_feature_sample();
  }
}
inline ::aml::public_idl::feature_landing::FeatureSampleV1* AmlFeatureSample::release_feature_sample_v1() {
  // @@protoc_insertion_point(field_release:aml.public_idl.feature_landing.AmlFeatureSample.feature_sample_v1)
  if (has_feature_sample_v1()) {
    clear_has_feature_sample();
      ::aml::public_idl::feature_landing::FeatureSampleV1* temp = feature_sample_.feature_sample_v1_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    feature_sample_.feature_sample_v1_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::aml::public_idl::feature_landing::FeatureSampleV1& AmlFeatureSample::feature_sample_v1() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.AmlFeatureSample.feature_sample_v1)
  return has_feature_sample_v1()
      ? *feature_sample_.feature_sample_v1_
      : *reinterpret_cast< ::aml::public_idl::feature_landing::FeatureSampleV1*>(&::aml::public_idl::feature_landing::_FeatureSampleV1_default_instance_);
}
inline ::aml::public_idl::feature_landing::FeatureSampleV1* AmlFeatureSample::unsafe_arena_release_feature_sample_v1() {
  // @@protoc_insertion_point(field_unsafe_arena_release:aml.public_idl.feature_landing.AmlFeatureSample.feature_sample_v1)
  if (has_feature_sample_v1()) {
    clear_has_feature_sample();
    ::aml::public_idl::feature_landing::FeatureSampleV1* temp = feature_sample_.feature_sample_v1_;
    feature_sample_.feature_sample_v1_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AmlFeatureSample::unsafe_arena_set_allocated_feature_sample_v1(::aml::public_idl::feature_landing::FeatureSampleV1* feature_sample_v1) {
  clear_feature_sample();
  if (feature_sample_v1) {
    set_has_feature_sample_v1();
    feature_sample_.feature_sample_v1_ = feature_sample_v1;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:aml.public_idl.feature_landing.AmlFeatureSample.feature_sample_v1)
}
inline ::aml::public_idl::feature_landing::FeatureSampleV1* AmlFeatureSample::mutable_feature_sample_v1() {
  if (!has_feature_sample_v1()) {
    clear_feature_sample();
    set_has_feature_sample_v1();
    feature_sample_.feature_sample_v1_ = CreateMaybeMessage< ::aml::public_idl::feature_landing::FeatureSampleV1 >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.AmlFeatureSample.feature_sample_v1)
  return feature_sample_.feature_sample_v1_;
}

// .aml.public_idl.feature_landing.FeatureSampleV2 feature_sample_v2 = 3;
inline bool AmlFeatureSample::has_feature_sample_v2() const {
  return feature_sample_case() == kFeatureSampleV2;
}
inline void AmlFeatureSample::set_has_feature_sample_v2() {
  _oneof_case_[0] = kFeatureSampleV2;
}
inline void AmlFeatureSample::clear_feature_sample_v2() {
  if (has_feature_sample_v2()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete feature_sample_.feature_sample_v2_;
    }
    clear_has_feature_sample();
  }
}
inline ::aml::public_idl::feature_landing::FeatureSampleV2* AmlFeatureSample::release_feature_sample_v2() {
  // @@protoc_insertion_point(field_release:aml.public_idl.feature_landing.AmlFeatureSample.feature_sample_v2)
  if (has_feature_sample_v2()) {
    clear_has_feature_sample();
      ::aml::public_idl::feature_landing::FeatureSampleV2* temp = feature_sample_.feature_sample_v2_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    feature_sample_.feature_sample_v2_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::aml::public_idl::feature_landing::FeatureSampleV2& AmlFeatureSample::feature_sample_v2() const {
  // @@protoc_insertion_point(field_get:aml.public_idl.feature_landing.AmlFeatureSample.feature_sample_v2)
  return has_feature_sample_v2()
      ? *feature_sample_.feature_sample_v2_
      : *reinterpret_cast< ::aml::public_idl::feature_landing::FeatureSampleV2*>(&::aml::public_idl::feature_landing::_FeatureSampleV2_default_instance_);
}
inline ::aml::public_idl::feature_landing::FeatureSampleV2* AmlFeatureSample::unsafe_arena_release_feature_sample_v2() {
  // @@protoc_insertion_point(field_unsafe_arena_release:aml.public_idl.feature_landing.AmlFeatureSample.feature_sample_v2)
  if (has_feature_sample_v2()) {
    clear_has_feature_sample();
    ::aml::public_idl::feature_landing::FeatureSampleV2* temp = feature_sample_.feature_sample_v2_;
    feature_sample_.feature_sample_v2_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AmlFeatureSample::unsafe_arena_set_allocated_feature_sample_v2(::aml::public_idl::feature_landing::FeatureSampleV2* feature_sample_v2) {
  clear_feature_sample();
  if (feature_sample_v2) {
    set_has_feature_sample_v2();
    feature_sample_.feature_sample_v2_ = feature_sample_v2;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:aml.public_idl.feature_landing.AmlFeatureSample.feature_sample_v2)
}
inline ::aml::public_idl::feature_landing::FeatureSampleV2* AmlFeatureSample::mutable_feature_sample_v2() {
  if (!has_feature_sample_v2()) {
    clear_feature_sample();
    set_has_feature_sample_v2();
    feature_sample_.feature_sample_v2_ = CreateMaybeMessage< ::aml::public_idl::feature_landing::FeatureSampleV2 >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:aml.public_idl.feature_landing.AmlFeatureSample.feature_sample_v2)
  return feature_sample_.feature_sample_v2_;
}

inline bool AmlFeatureSample::has_feature_sample() const {
  return feature_sample_case() != FEATURE_SAMPLE_NOT_SET;
}
inline void AmlFeatureSample::clear_has_feature_sample() {
  _oneof_case_[0] = FEATURE_SAMPLE_NOT_SET;
}
inline AmlFeatureSample::FeatureSampleCase AmlFeatureSample::feature_sample_case() const {
  return AmlFeatureSample::FeatureSampleCase(_oneof_case_[0]);
}
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace feature_landing
}  // namespace public_idl
}  // namespace aml

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::aml::public_idl::feature_landing::FeatureSampleProtol> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::aml::public_idl::feature_landing::FeatureSampleProtol>() {
  return ::aml::public_idl::feature_landing::FeatureSampleProtol_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_proto_2ffeature_5flanding_5fin_2eproto
