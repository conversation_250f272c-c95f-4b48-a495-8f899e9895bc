// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/recommend_dmp.proto

#include "proto/recommend_dmp.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fdmp_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_Tag_proto_2frecommend_5fdmp_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fdmp_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_TagValue_proto_2frecommend_5fdmp_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fdmp_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_UserKey_proto_2frecommend_5fdmp_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fdmp_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_UserProfile_proto_2frecommend_5fdmp_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_proto_2frecommend_5fdmp_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_UserProfileBatch_proto_2frecommend_5fdmp_2eproto;
namespace abc {
namespace recommend_plt {
namespace dmp {
class UserKeyDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<UserKey> _instance;
} _UserKey_default_instance_;
class TagValueDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TagValue> _instance;
} _TagValue_default_instance_;
class TagDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<Tag> _instance;
} _Tag_default_instance_;
class UserProfileDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<UserProfile> _instance;
} _UserProfile_default_instance_;
class UserProfileRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<UserProfileRequest> _instance;
} _UserProfileRequest_default_instance_;
class UserProfileRequestBatchDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<UserProfileRequestBatch> _instance;
} _UserProfileRequestBatch_default_instance_;
class UserProfileBatchDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<UserProfileBatch> _instance;
} _UserProfileBatch_default_instance_;
class UserProfileResponseBatchDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<UserProfileResponseBatch> _instance;
} _UserProfileResponseBatch_default_instance_;
class UserProfileResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<UserProfileResponse> _instance;
} _UserProfileResponse_default_instance_;
}  // namespace dmp
}  // namespace recommend_plt
}  // namespace abc
static void InitDefaultsscc_info_Tag_proto_2frecommend_5fdmp_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::dmp::_Tag_default_instance_;
    new (ptr) ::abc::recommend_plt::dmp::Tag();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::dmp::Tag::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_Tag_proto_2frecommend_5fdmp_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsscc_info_Tag_proto_2frecommend_5fdmp_2eproto}, {
      &scc_info_TagValue_proto_2frecommend_5fdmp_2eproto.base,}};

static void InitDefaultsscc_info_TagValue_proto_2frecommend_5fdmp_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::dmp::_TagValue_default_instance_;
    new (ptr) ::abc::recommend_plt::dmp::TagValue();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::dmp::TagValue::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_TagValue_proto_2frecommend_5fdmp_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsscc_info_TagValue_proto_2frecommend_5fdmp_2eproto}, {}};

static void InitDefaultsscc_info_UserKey_proto_2frecommend_5fdmp_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::dmp::_UserKey_default_instance_;
    new (ptr) ::abc::recommend_plt::dmp::UserKey();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::dmp::UserKey::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_UserKey_proto_2frecommend_5fdmp_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsscc_info_UserKey_proto_2frecommend_5fdmp_2eproto}, {}};

static void InitDefaultsscc_info_UserProfile_proto_2frecommend_5fdmp_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::dmp::_UserProfile_default_instance_;
    new (ptr) ::abc::recommend_plt::dmp::UserProfile();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::dmp::UserProfile::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_UserProfile_proto_2frecommend_5fdmp_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsscc_info_UserProfile_proto_2frecommend_5fdmp_2eproto}, {
      &scc_info_Tag_proto_2frecommend_5fdmp_2eproto.base,}};

static void InitDefaultsscc_info_UserProfileBatch_proto_2frecommend_5fdmp_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::dmp::_UserProfileBatch_default_instance_;
    new (ptr) ::abc::recommend_plt::dmp::UserProfileBatch();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::dmp::UserProfileBatch::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_UserProfileBatch_proto_2frecommend_5fdmp_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsscc_info_UserProfileBatch_proto_2frecommend_5fdmp_2eproto}, {
      &scc_info_UserKey_proto_2frecommend_5fdmp_2eproto.base,
      &scc_info_UserProfile_proto_2frecommend_5fdmp_2eproto.base,}};

static void InitDefaultsscc_info_UserProfileRequest_proto_2frecommend_5fdmp_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::dmp::_UserProfileRequest_default_instance_;
    new (ptr) ::abc::recommend_plt::dmp::UserProfileRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::dmp::UserProfileRequest::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_UserProfileRequest_proto_2frecommend_5fdmp_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsscc_info_UserProfileRequest_proto_2frecommend_5fdmp_2eproto}, {
      &scc_info_UserKey_proto_2frecommend_5fdmp_2eproto.base,}};

static void InitDefaultsscc_info_UserProfileRequestBatch_proto_2frecommend_5fdmp_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::dmp::_UserProfileRequestBatch_default_instance_;
    new (ptr) ::abc::recommend_plt::dmp::UserProfileRequestBatch();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::dmp::UserProfileRequestBatch::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_UserProfileRequestBatch_proto_2frecommend_5fdmp_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsscc_info_UserProfileRequestBatch_proto_2frecommend_5fdmp_2eproto}, {
      &scc_info_UserKey_proto_2frecommend_5fdmp_2eproto.base,}};

static void InitDefaultsscc_info_UserProfileResponse_proto_2frecommend_5fdmp_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::dmp::_UserProfileResponse_default_instance_;
    new (ptr) ::abc::recommend_plt::dmp::UserProfileResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::dmp::UserProfileResponse::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_UserProfileResponse_proto_2frecommend_5fdmp_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsscc_info_UserProfileResponse_proto_2frecommend_5fdmp_2eproto}, {
      &scc_info_UserProfile_proto_2frecommend_5fdmp_2eproto.base,}};

static void InitDefaultsscc_info_UserProfileResponseBatch_proto_2frecommend_5fdmp_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::abc::recommend_plt::dmp::_UserProfileResponseBatch_default_instance_;
    new (ptr) ::abc::recommend_plt::dmp::UserProfileResponseBatch();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::abc::recommend_plt::dmp::UserProfileResponseBatch::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_UserProfileResponseBatch_proto_2frecommend_5fdmp_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsscc_info_UserProfileResponseBatch_proto_2frecommend_5fdmp_2eproto}, {
      &scc_info_UserProfileBatch_proto_2frecommend_5fdmp_2eproto.base,}};

static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_proto_2frecommend_5fdmp_2eproto[9];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_proto_2frecommend_5fdmp_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_proto_2frecommend_5fdmp_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_proto_2frecommend_5fdmp_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserKey, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserKey, member_id_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserKey, device_id_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserKey, cookie_id_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserKey, ugid_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserKey, region_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::TagValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::TagValue, values_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::Tag, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::Tag, hkey_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::Tag, value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserProfile, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserProfile, tags_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserProfileRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserProfileRequest, user_key_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserProfileRequest, source_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserProfileRequest, timeout_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserProfileRequestBatch, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserProfileRequestBatch, user_key_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserProfileRequestBatch, source_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserProfileBatch, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserProfileBatch, user_key_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserProfileBatch, user_profile_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserProfileBatch, ugid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserProfileResponseBatch, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserProfileResponseBatch, user_profile_batch_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserProfileResponseBatch, status_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserProfileResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserProfileResponse, user_profile_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserProfileResponse, status_),
  PROTOBUF_FIELD_OFFSET(::abc::recommend_plt::dmp::UserProfileResponse, ugid_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::abc::recommend_plt::dmp::UserKey)},
  { 10, -1, sizeof(::abc::recommend_plt::dmp::TagValue)},
  { 16, -1, sizeof(::abc::recommend_plt::dmp::Tag)},
  { 23, -1, sizeof(::abc::recommend_plt::dmp::UserProfile)},
  { 29, -1, sizeof(::abc::recommend_plt::dmp::UserProfileRequest)},
  { 37, -1, sizeof(::abc::recommend_plt::dmp::UserProfileRequestBatch)},
  { 44, -1, sizeof(::abc::recommend_plt::dmp::UserProfileBatch)},
  { 52, -1, sizeof(::abc::recommend_plt::dmp::UserProfileResponseBatch)},
  { 59, -1, sizeof(::abc::recommend_plt::dmp::UserProfileResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::dmp::_UserKey_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::dmp::_TagValue_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::dmp::_Tag_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::dmp::_UserProfile_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::dmp::_UserProfileRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::dmp::_UserProfileRequestBatch_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::dmp::_UserProfileBatch_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::dmp::_UserProfileResponseBatch_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::abc::recommend_plt::dmp::_UserProfileResponse_default_instance_),
};

const char descriptor_table_protodef_proto_2frecommend_5fdmp_2eproto[] =
  "\n\031proto/recommend_dmp.proto\022\025abc.recomme"
  "nd_plt.dmp\"`\n\007UserKey\022\021\n\tmember_id\030\001 \001(\t"
  "\022\021\n\tdevice_id\030\002 \001(\t\022\021\n\tcookie_id\030\003 \001(\t\022\014"
  "\n\004ugid\030\004 \001(\t\022\016\n\006region\030\005 \001(\t\"\032\n\010TagValue"
  "\022\016\n\006values\030\001 \003(\003\"C\n\003Tag\022\014\n\004hkey\030\001 \001(\005\022.\n"
  "\005value\030\002 \001(\0132\037.abc.recommend_plt.dmp.Tag"
  "Value\"7\n\013UserProfile\022(\n\004tags\030\001 \003(\0132\032.abc"
  ".recommend_plt.dmp.Tag\"g\n\022UserProfileReq"
  "uest\0220\n\010user_key\030\001 \001(\0132\036.abc.recommend_p"
  "lt.dmp.UserKey\022\016\n\006source\030\002 \001(\t\022\017\n\007timeou"
  "t\030\003 \001(\005\"[\n\027UserProfileRequestBatch\0220\n\010us"
  "er_key\030\001 \003(\0132\036.abc.recommend_plt.dmp.Use"
  "rKey\022\016\n\006source\030\002 \001(\t\"\214\001\n\020UserProfileBatc"
  "h\0220\n\010user_key\030\001 \001(\0132\036.abc.recommend_plt."
  "dmp.UserKey\0228\n\014user_profile\030\002 \001(\0132\".abc."
  "recommend_plt.dmp.UserProfile\022\014\n\004ugid\030\003 "
  "\001(\t\"\243\002\n\030UserProfileResponseBatch\022C\n\022user"
  "_profile_batch\030\001 \003(\0132\'.abc.recommend_plt"
  ".dmp.UserProfileBatch\022N\n\006status\030\002 \001(\0162>."
  "abc.recommend_plt.dmp.UserProfileRespons"
  "eBatch.ResponseStatus\"r\n\016ResponseStatus\022"
  "\013\n\007SUCCESS\020\000\022\023\n\017PARAMETER_EMPTY\020\001\022\t\n\005ERR"
  "OR\020\002\022\r\n\tNOT_FOUND\020\003\022\021\n\rREDIS_TIMEOUT\020\004\022\021"
  "\n\rPARTIAL_ERROR\020\005\"\211\002\n\023UserProfileRespons"
  "e\0228\n\014user_profile\030\001 \001(\0132\".abc.recommend_"
  "plt.dmp.UserProfile\022I\n\006status\030\002 \001(\01629.ab"
  "c.recommend_plt.dmp.UserProfileResponse."
  "ResponseStatus\022\014\n\004ugid\030\003 \001(\t\"_\n\016Response"
  "Status\022\013\n\007SUCCESS\020\000\022\023\n\017PARAMETER_EMPTY\020\001"
  "\022\t\n\005ERROR\020\002\022\r\n\tNOT_FOUND\020\003\022\021\n\rREDIS_TIME"
  "OUT\020\0042\371\001\n\022UserProfileService\022i\n\016GetUserP"
  "rofile\022).abc.recommend_plt.dmp.UserProfi"
  "leRequest\032*.abc.recommend_plt.dmp.UserPr"
  "ofileResponse\"\000\022x\n\023GetUserProfileBatch\022."
  ".abc.recommend_plt.dmp.UserProfileReques"
  "tBatch\032/.abc.recommend_plt.dmp.UserProfi"
  "leResponseBatch\"\000B\036\n\031com.shein.abc.dmp.s"
  "ervice\370\001\001b\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_proto_2frecommend_5fdmp_2eproto_deps[1] = {
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_proto_2frecommend_5fdmp_2eproto_sccs[9] = {
  &scc_info_Tag_proto_2frecommend_5fdmp_2eproto.base,
  &scc_info_TagValue_proto_2frecommend_5fdmp_2eproto.base,
  &scc_info_UserKey_proto_2frecommend_5fdmp_2eproto.base,
  &scc_info_UserProfile_proto_2frecommend_5fdmp_2eproto.base,
  &scc_info_UserProfileBatch_proto_2frecommend_5fdmp_2eproto.base,
  &scc_info_UserProfileRequest_proto_2frecommend_5fdmp_2eproto.base,
  &scc_info_UserProfileRequestBatch_proto_2frecommend_5fdmp_2eproto.base,
  &scc_info_UserProfileResponse_proto_2frecommend_5fdmp_2eproto.base,
  &scc_info_UserProfileResponseBatch_proto_2frecommend_5fdmp_2eproto.base,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_proto_2frecommend_5fdmp_2eproto_once;
static bool descriptor_table_proto_2frecommend_5fdmp_2eproto_initialized = false;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2frecommend_5fdmp_2eproto = {
  &descriptor_table_proto_2frecommend_5fdmp_2eproto_initialized, descriptor_table_protodef_proto_2frecommend_5fdmp_2eproto, "proto/recommend_dmp.proto", 1497,
  &descriptor_table_proto_2frecommend_5fdmp_2eproto_once, descriptor_table_proto_2frecommend_5fdmp_2eproto_sccs, descriptor_table_proto_2frecommend_5fdmp_2eproto_deps, 9, 0,
  schemas, file_default_instances, TableStruct_proto_2frecommend_5fdmp_2eproto::offsets,
  file_level_metadata_proto_2frecommend_5fdmp_2eproto, 9, file_level_enum_descriptors_proto_2frecommend_5fdmp_2eproto, file_level_service_descriptors_proto_2frecommend_5fdmp_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_proto_2frecommend_5fdmp_2eproto = (  ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_proto_2frecommend_5fdmp_2eproto), true);
namespace abc {
namespace recommend_plt {
namespace dmp {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* UserProfileResponseBatch_ResponseStatus_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_proto_2frecommend_5fdmp_2eproto);
  return file_level_enum_descriptors_proto_2frecommend_5fdmp_2eproto[0];
}
bool UserProfileResponseBatch_ResponseStatus_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr UserProfileResponseBatch_ResponseStatus UserProfileResponseBatch::SUCCESS;
constexpr UserProfileResponseBatch_ResponseStatus UserProfileResponseBatch::PARAMETER_EMPTY;
constexpr UserProfileResponseBatch_ResponseStatus UserProfileResponseBatch::ERROR;
constexpr UserProfileResponseBatch_ResponseStatus UserProfileResponseBatch::NOT_FOUND;
constexpr UserProfileResponseBatch_ResponseStatus UserProfileResponseBatch::REDIS_TIMEOUT;
constexpr UserProfileResponseBatch_ResponseStatus UserProfileResponseBatch::PARTIAL_ERROR;
constexpr UserProfileResponseBatch_ResponseStatus UserProfileResponseBatch::ResponseStatus_MIN;
constexpr UserProfileResponseBatch_ResponseStatus UserProfileResponseBatch::ResponseStatus_MAX;
constexpr int UserProfileResponseBatch::ResponseStatus_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* UserProfileResponse_ResponseStatus_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_proto_2frecommend_5fdmp_2eproto);
  return file_level_enum_descriptors_proto_2frecommend_5fdmp_2eproto[1];
}
bool UserProfileResponse_ResponseStatus_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr UserProfileResponse_ResponseStatus UserProfileResponse::SUCCESS;
constexpr UserProfileResponse_ResponseStatus UserProfileResponse::PARAMETER_EMPTY;
constexpr UserProfileResponse_ResponseStatus UserProfileResponse::ERROR;
constexpr UserProfileResponse_ResponseStatus UserProfileResponse::NOT_FOUND;
constexpr UserProfileResponse_ResponseStatus UserProfileResponse::REDIS_TIMEOUT;
constexpr UserProfileResponse_ResponseStatus UserProfileResponse::ResponseStatus_MIN;
constexpr UserProfileResponse_ResponseStatus UserProfileResponse::ResponseStatus_MAX;
constexpr int UserProfileResponse::ResponseStatus_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)

// ===================================================================

void UserKey::InitAsDefaultInstance() {
}
class UserKey::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int UserKey::kMemberIdFieldNumber;
const int UserKey::kDeviceIdFieldNumber;
const int UserKey::kCookieIdFieldNumber;
const int UserKey::kUgidFieldNumber;
const int UserKey::kRegionFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

UserKey::UserKey()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.dmp.UserKey)
}
UserKey::UserKey(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
  _internal_metadata_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:abc.recommend_plt.dmp.UserKey)
}
UserKey::UserKey(const UserKey& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  member_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.member_id().size() > 0) {
    member_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.member_id(),
      GetArenaNoVirtual());
  }
  device_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.device_id().size() > 0) {
    device_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.device_id(),
      GetArenaNoVirtual());
  }
  cookie_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.cookie_id().size() > 0) {
    cookie_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.cookie_id(),
      GetArenaNoVirtual());
  }
  ugid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.ugid().size() > 0) {
    ugid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.ugid(),
      GetArenaNoVirtual());
  }
  region_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.region().size() > 0) {
    region_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.region(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.dmp.UserKey)
}

void UserKey::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_UserKey_proto_2frecommend_5fdmp_2eproto.base);
  member_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  device_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  cookie_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ugid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  region_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

UserKey::~UserKey() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.dmp.UserKey)
  SharedDtor();
}

void UserKey::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == nullptr);
  member_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  device_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  cookie_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ugid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  region_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void UserKey::ArenaDtor(void* object) {
  UserKey* _this = reinterpret_cast< UserKey* >(object);
  (void)_this;
}
void UserKey::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UserKey::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const UserKey& UserKey::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_UserKey_proto_2frecommend_5fdmp_2eproto.base);
  return *internal_default_instance();
}


void UserKey::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.dmp.UserKey)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  member_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  device_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  cookie_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ugid_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  region_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* UserKey::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArenaNoVirtual(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string member_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_member_id(), ptr, ctx, "abc.recommend_plt.dmp.UserKey.member_id");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string device_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_device_id(), ptr, ctx, "abc.recommend_plt.dmp.UserKey.device_id");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string cookie_id = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_cookie_id(), ptr, ctx, "abc.recommend_plt.dmp.UserKey.cookie_id");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string ugid = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_ugid(), ptr, ctx, "abc.recommend_plt.dmp.UserKey.ugid");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string region = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_region(), ptr, ctx, "abc.recommend_plt.dmp.UserKey.region");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool UserKey::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.dmp.UserKey)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string member_id = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_member_id()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->member_id().data(), static_cast<int>(this->member_id().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.dmp.UserKey.member_id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string device_id = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_device_id()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->device_id().data(), static_cast<int>(this->device_id().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.dmp.UserKey.device_id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string cookie_id = 3;
      case 3: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (26 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_cookie_id()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->cookie_id().data(), static_cast<int>(this->cookie_id().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.dmp.UserKey.cookie_id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string ugid = 4;
      case 4: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (34 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_ugid()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->ugid().data(), static_cast<int>(this->ugid().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.dmp.UserKey.ugid"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string region = 5;
      case 5: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (42 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_region()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->region().data(), static_cast<int>(this->region().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.dmp.UserKey.region"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.dmp.UserKey)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.dmp.UserKey)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void UserKey::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.dmp.UserKey)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string member_id = 1;
  if (this->member_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->member_id().data(), static_cast<int>(this->member_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.dmp.UserKey.member_id");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->member_id(), output);
  }

  // string device_id = 2;
  if (this->device_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->device_id().data(), static_cast<int>(this->device_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.dmp.UserKey.device_id");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->device_id(), output);
  }

  // string cookie_id = 3;
  if (this->cookie_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->cookie_id().data(), static_cast<int>(this->cookie_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.dmp.UserKey.cookie_id");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->cookie_id(), output);
  }

  // string ugid = 4;
  if (this->ugid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->ugid().data(), static_cast<int>(this->ugid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.dmp.UserKey.ugid");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->ugid(), output);
  }

  // string region = 5;
  if (this->region().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->region().data(), static_cast<int>(this->region().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.dmp.UserKey.region");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->region(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.dmp.UserKey)
}

::PROTOBUF_NAMESPACE_ID::uint8* UserKey::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.dmp.UserKey)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string member_id = 1;
  if (this->member_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->member_id().data(), static_cast<int>(this->member_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.dmp.UserKey.member_id");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        1, this->member_id(), target);
  }

  // string device_id = 2;
  if (this->device_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->device_id().data(), static_cast<int>(this->device_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.dmp.UserKey.device_id");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        2, this->device_id(), target);
  }

  // string cookie_id = 3;
  if (this->cookie_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->cookie_id().data(), static_cast<int>(this->cookie_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.dmp.UserKey.cookie_id");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        3, this->cookie_id(), target);
  }

  // string ugid = 4;
  if (this->ugid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->ugid().data(), static_cast<int>(this->ugid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.dmp.UserKey.ugid");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        4, this->ugid(), target);
  }

  // string region = 5;
  if (this->region().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->region().data(), static_cast<int>(this->region().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.dmp.UserKey.region");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        5, this->region(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.dmp.UserKey)
  return target;
}

size_t UserKey::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.dmp.UserKey)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string member_id = 1;
  if (this->member_id().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->member_id());
  }

  // string device_id = 2;
  if (this->device_id().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->device_id());
  }

  // string cookie_id = 3;
  if (this->cookie_id().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->cookie_id());
  }

  // string ugid = 4;
  if (this->ugid().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->ugid());
  }

  // string region = 5;
  if (this->region().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->region());
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void UserKey::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.dmp.UserKey)
  GOOGLE_DCHECK_NE(&from, this);
  const UserKey* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<UserKey>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.dmp.UserKey)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.dmp.UserKey)
    MergeFrom(*source);
  }
}

void UserKey::MergeFrom(const UserKey& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.dmp.UserKey)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.member_id().size() > 0) {
    set_member_id(from.member_id());
  }
  if (from.device_id().size() > 0) {
    set_device_id(from.device_id());
  }
  if (from.cookie_id().size() > 0) {
    set_cookie_id(from.cookie_id());
  }
  if (from.ugid().size() > 0) {
    set_ugid(from.ugid());
  }
  if (from.region().size() > 0) {
    set_region(from.region());
  }
}

void UserKey::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.dmp.UserKey)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UserKey::CopyFrom(const UserKey& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.dmp.UserKey)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UserKey::IsInitialized() const {
  return true;
}

void UserKey::Swap(UserKey* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    UserKey* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == nullptr) {
      delete temp;
    }
  }
}
void UserKey::UnsafeArenaSwap(UserKey* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void UserKey::InternalSwap(UserKey* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  member_id_.Swap(&other->member_id_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  device_id_.Swap(&other->device_id_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  cookie_id_.Swap(&other->cookie_id_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  ugid_.Swap(&other->ugid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  region_.Swap(&other->region_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
}

::PROTOBUF_NAMESPACE_ID::Metadata UserKey::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void TagValue::InitAsDefaultInstance() {
}
class TagValue::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TagValue::kValuesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TagValue::TagValue()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.dmp.TagValue)
}
TagValue::TagValue(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
  _internal_metadata_(arena),
  values_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:abc.recommend_plt.dmp.TagValue)
}
TagValue::TagValue(const TagValue& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      values_(from.values_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.dmp.TagValue)
}

void TagValue::SharedCtor() {
}

TagValue::~TagValue() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.dmp.TagValue)
  SharedDtor();
}

void TagValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == nullptr);
}

void TagValue::ArenaDtor(void* object) {
  TagValue* _this = reinterpret_cast< TagValue* >(object);
  (void)_this;
}
void TagValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TagValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TagValue& TagValue::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TagValue_proto_2frecommend_5fdmp_2eproto.base);
  return *internal_default_instance();
}


void TagValue::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.dmp.TagValue)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  values_.Clear();
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* TagValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArenaNoVirtual(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated int64 values = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt64Parser(mutable_values(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8) {
          add_values(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool TagValue::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.dmp.TagValue)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated int64 values = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPackedPrimitive<
                   ::PROTOBUF_NAMESPACE_ID::int64, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_values())));
        } else if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (8 & 0xFF)) {
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::PROTOBUF_NAMESPACE_ID::int64, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64>(
                 1, 10u, input, this->mutable_values())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.dmp.TagValue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.dmp.TagValue)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void TagValue::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.dmp.TagValue)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int64 values = 1;
  if (this->values_size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteTag(1, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_values_cached_byte_size_.load(
        std::memory_order_relaxed));
  }
  for (int i = 0, n = this->values_size(); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64NoTag(
      this->values(i), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.dmp.TagValue)
}

::PROTOBUF_NAMESPACE_ID::uint8* TagValue::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.dmp.TagValue)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int64 values = 1;
  if (this->values_size() > 0) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteTagToArray(
      1,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream::WriteVarint32ToArray(
        _values_cached_byte_size_.load(std::memory_order_relaxed),
         target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->values_, target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.dmp.TagValue)
  return target;
}

size_t TagValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.dmp.TagValue)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated int64 values = 1;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int64Size(this->values_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _values_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TagValue::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.dmp.TagValue)
  GOOGLE_DCHECK_NE(&from, this);
  const TagValue* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TagValue>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.dmp.TagValue)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.dmp.TagValue)
    MergeFrom(*source);
  }
}

void TagValue::MergeFrom(const TagValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.dmp.TagValue)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  values_.MergeFrom(from.values_);
}

void TagValue::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.dmp.TagValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TagValue::CopyFrom(const TagValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.dmp.TagValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TagValue::IsInitialized() const {
  return true;
}

void TagValue::Swap(TagValue* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TagValue* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == nullptr) {
      delete temp;
    }
  }
}
void TagValue::UnsafeArenaSwap(TagValue* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TagValue::InternalSwap(TagValue* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  values_.InternalSwap(&other->values_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TagValue::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void Tag::InitAsDefaultInstance() {
  ::abc::recommend_plt::dmp::_Tag_default_instance_._instance.get_mutable()->value_ = const_cast< ::abc::recommend_plt::dmp::TagValue*>(
      ::abc::recommend_plt::dmp::TagValue::internal_default_instance());
}
class Tag::HasBitSetters {
 public:
  static const ::abc::recommend_plt::dmp::TagValue& value(const Tag* msg);
};

const ::abc::recommend_plt::dmp::TagValue&
Tag::HasBitSetters::value(const Tag* msg) {
  return *msg->value_;
}
void Tag::unsafe_arena_set_allocated_value(
    ::abc::recommend_plt::dmp::TagValue* value) {
  if (GetArenaNoVirtual() == nullptr) {
    delete value_;
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:abc.recommend_plt.dmp.Tag.value)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Tag::kHkeyFieldNumber;
const int Tag::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Tag::Tag()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.dmp.Tag)
}
Tag::Tag(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
  _internal_metadata_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:abc.recommend_plt.dmp.Tag)
}
Tag::Tag(const Tag& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_value()) {
    value_ = new ::abc::recommend_plt::dmp::TagValue(*from.value_);
  } else {
    value_ = nullptr;
  }
  hkey_ = from.hkey_;
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.dmp.Tag)
}

void Tag::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_Tag_proto_2frecommend_5fdmp_2eproto.base);
  ::memset(&value_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&hkey_) -
      reinterpret_cast<char*>(&value_)) + sizeof(hkey_));
}

Tag::~Tag() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.dmp.Tag)
  SharedDtor();
}

void Tag::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == nullptr);
  if (this != internal_default_instance()) delete value_;
}

void Tag::ArenaDtor(void* object) {
  Tag* _this = reinterpret_cast< Tag* >(object);
  (void)_this;
}
void Tag::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Tag::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const Tag& Tag::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_Tag_proto_2frecommend_5fdmp_2eproto.base);
  return *internal_default_instance();
}


void Tag::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.dmp.Tag)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
  hkey_ = 0;
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* Tag::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArenaNoVirtual(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // int32 hkey = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          hkey_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .abc.recommend_plt.dmp.TagValue value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(mutable_value(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool Tag::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.dmp.Tag)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 hkey = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (8 & 0xFF)) {

          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32>(
                 input, &hkey_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .abc.recommend_plt.dmp.TagValue value = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_value()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.dmp.Tag)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.dmp.Tag)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void Tag::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.dmp.Tag)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 hkey = 1;
  if (this->hkey() != 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32(1, this->hkey(), output);
  }

  // .abc.recommend_plt.dmp.TagValue value = 2;
  if (this->has_value()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, HasBitSetters::value(this), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.dmp.Tag)
}

::PROTOBUF_NAMESPACE_ID::uint8* Tag::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.dmp.Tag)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 hkey = 1;
  if (this->hkey() != 0) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->hkey(), target);
  }

  // .abc.recommend_plt.dmp.TagValue value = 2;
  if (this->has_value()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, HasBitSetters::value(this), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.dmp.Tag)
  return target;
}

size_t Tag::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.dmp.Tag)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .abc.recommend_plt.dmp.TagValue value = 2;
  if (this->has_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *value_);
  }

  // int32 hkey = 1;
  if (this->hkey() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->hkey());
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Tag::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.dmp.Tag)
  GOOGLE_DCHECK_NE(&from, this);
  const Tag* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<Tag>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.dmp.Tag)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.dmp.Tag)
    MergeFrom(*source);
  }
}

void Tag::MergeFrom(const Tag& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.dmp.Tag)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_value()) {
    mutable_value()->::abc::recommend_plt::dmp::TagValue::MergeFrom(from.value());
  }
  if (from.hkey() != 0) {
    set_hkey(from.hkey());
  }
}

void Tag::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.dmp.Tag)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Tag::CopyFrom(const Tag& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.dmp.Tag)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Tag::IsInitialized() const {
  return true;
}

void Tag::Swap(Tag* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    Tag* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == nullptr) {
      delete temp;
    }
  }
}
void Tag::UnsafeArenaSwap(Tag* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void Tag::InternalSwap(Tag* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(value_, other->value_);
  swap(hkey_, other->hkey_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Tag::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void UserProfile::InitAsDefaultInstance() {
}
class UserProfile::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int UserProfile::kTagsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

UserProfile::UserProfile()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.dmp.UserProfile)
}
UserProfile::UserProfile(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
  _internal_metadata_(arena),
  tags_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:abc.recommend_plt.dmp.UserProfile)
}
UserProfile::UserProfile(const UserProfile& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      tags_(from.tags_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.dmp.UserProfile)
}

void UserProfile::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_UserProfile_proto_2frecommend_5fdmp_2eproto.base);
}

UserProfile::~UserProfile() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.dmp.UserProfile)
  SharedDtor();
}

void UserProfile::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == nullptr);
}

void UserProfile::ArenaDtor(void* object) {
  UserProfile* _this = reinterpret_cast< UserProfile* >(object);
  (void)_this;
}
void UserProfile::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UserProfile::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const UserProfile& UserProfile::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_UserProfile_proto_2frecommend_5fdmp_2eproto.base);
  return *internal_default_instance();
}


void UserProfile::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.dmp.UserProfile)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  tags_.Clear();
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* UserProfile::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArenaNoVirtual(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .abc.recommend_plt.dmp.Tag tags = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(add_tags(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 10);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool UserProfile::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.dmp.UserProfile)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .abc.recommend_plt.dmp.Tag tags = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
                input, add_tags()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.dmp.UserProfile)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.dmp.UserProfile)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void UserProfile::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.dmp.UserProfile)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.dmp.Tag tags = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->tags_size()); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->tags(static_cast<int>(i)),
      output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.dmp.UserProfile)
}

::PROTOBUF_NAMESPACE_ID::uint8* UserProfile::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.dmp.UserProfile)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.dmp.Tag tags = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->tags_size()); i < n; i++) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->tags(static_cast<int>(i)), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.dmp.UserProfile)
  return target;
}

size_t UserProfile::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.dmp.UserProfile)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.dmp.Tag tags = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->tags_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          this->tags(static_cast<int>(i)));
    }
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void UserProfile::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.dmp.UserProfile)
  GOOGLE_DCHECK_NE(&from, this);
  const UserProfile* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<UserProfile>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.dmp.UserProfile)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.dmp.UserProfile)
    MergeFrom(*source);
  }
}

void UserProfile::MergeFrom(const UserProfile& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.dmp.UserProfile)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  tags_.MergeFrom(from.tags_);
}

void UserProfile::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.dmp.UserProfile)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UserProfile::CopyFrom(const UserProfile& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.dmp.UserProfile)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UserProfile::IsInitialized() const {
  return true;
}

void UserProfile::Swap(UserProfile* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    UserProfile* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == nullptr) {
      delete temp;
    }
  }
}
void UserProfile::UnsafeArenaSwap(UserProfile* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void UserProfile::InternalSwap(UserProfile* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  CastToBase(&tags_)->InternalSwap(CastToBase(&other->tags_));
}

::PROTOBUF_NAMESPACE_ID::Metadata UserProfile::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void UserProfileRequest::InitAsDefaultInstance() {
  ::abc::recommend_plt::dmp::_UserProfileRequest_default_instance_._instance.get_mutable()->user_key_ = const_cast< ::abc::recommend_plt::dmp::UserKey*>(
      ::abc::recommend_plt::dmp::UserKey::internal_default_instance());
}
class UserProfileRequest::HasBitSetters {
 public:
  static const ::abc::recommend_plt::dmp::UserKey& user_key(const UserProfileRequest* msg);
};

const ::abc::recommend_plt::dmp::UserKey&
UserProfileRequest::HasBitSetters::user_key(const UserProfileRequest* msg) {
  return *msg->user_key_;
}
void UserProfileRequest::unsafe_arena_set_allocated_user_key(
    ::abc::recommend_plt::dmp::UserKey* user_key) {
  if (GetArenaNoVirtual() == nullptr) {
    delete user_key_;
  }
  user_key_ = user_key;
  if (user_key) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:abc.recommend_plt.dmp.UserProfileRequest.user_key)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int UserProfileRequest::kUserKeyFieldNumber;
const int UserProfileRequest::kSourceFieldNumber;
const int UserProfileRequest::kTimeoutFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

UserProfileRequest::UserProfileRequest()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.dmp.UserProfileRequest)
}
UserProfileRequest::UserProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
  _internal_metadata_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:abc.recommend_plt.dmp.UserProfileRequest)
}
UserProfileRequest::UserProfileRequest(const UserProfileRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  source_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.source().size() > 0) {
    source_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.source(),
      GetArenaNoVirtual());
  }
  if (from.has_user_key()) {
    user_key_ = new ::abc::recommend_plt::dmp::UserKey(*from.user_key_);
  } else {
    user_key_ = nullptr;
  }
  timeout_ = from.timeout_;
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.dmp.UserProfileRequest)
}

void UserProfileRequest::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_UserProfileRequest_proto_2frecommend_5fdmp_2eproto.base);
  source_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&user_key_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&timeout_) -
      reinterpret_cast<char*>(&user_key_)) + sizeof(timeout_));
}

UserProfileRequest::~UserProfileRequest() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.dmp.UserProfileRequest)
  SharedDtor();
}

void UserProfileRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == nullptr);
  source_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete user_key_;
}

void UserProfileRequest::ArenaDtor(void* object) {
  UserProfileRequest* _this = reinterpret_cast< UserProfileRequest* >(object);
  (void)_this;
}
void UserProfileRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UserProfileRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const UserProfileRequest& UserProfileRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_UserProfileRequest_proto_2frecommend_5fdmp_2eproto.base);
  return *internal_default_instance();
}


void UserProfileRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.dmp.UserProfileRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  source_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == nullptr && user_key_ != nullptr) {
    delete user_key_;
  }
  user_key_ = nullptr;
  timeout_ = 0;
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* UserProfileRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArenaNoVirtual(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .abc.recommend_plt.dmp.UserKey user_key = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(mutable_user_key(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string source = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_source(), ptr, ctx, "abc.recommend_plt.dmp.UserProfileRequest.source");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 timeout = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          timeout_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool UserProfileRequest::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.dmp.UserProfileRequest)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .abc.recommend_plt.dmp.UserKey user_key = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_user_key()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string source = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_source()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->source().data(), static_cast<int>(this->source().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.dmp.UserProfileRequest.source"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 timeout = 3;
      case 3: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (24 & 0xFF)) {

          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   ::PROTOBUF_NAMESPACE_ID::int32, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32>(
                 input, &timeout_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.dmp.UserProfileRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.dmp.UserProfileRequest)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void UserProfileRequest::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.dmp.UserProfileRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .abc.recommend_plt.dmp.UserKey user_key = 1;
  if (this->has_user_key()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, HasBitSetters::user_key(this), output);
  }

  // string source = 2;
  if (this->source().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->source().data(), static_cast<int>(this->source().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.dmp.UserProfileRequest.source");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->source(), output);
  }

  // int32 timeout = 3;
  if (this->timeout() != 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32(3, this->timeout(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.dmp.UserProfileRequest)
}

::PROTOBUF_NAMESPACE_ID::uint8* UserProfileRequest::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.dmp.UserProfileRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .abc.recommend_plt.dmp.UserKey user_key = 1;
  if (this->has_user_key()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, HasBitSetters::user_key(this), target);
  }

  // string source = 2;
  if (this->source().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->source().data(), static_cast<int>(this->source().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.dmp.UserProfileRequest.source");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        2, this->source(), target);
  }

  // int32 timeout = 3;
  if (this->timeout() != 0) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->timeout(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.dmp.UserProfileRequest)
  return target;
}

size_t UserProfileRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.dmp.UserProfileRequest)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string source = 2;
  if (this->source().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->source());
  }

  // .abc.recommend_plt.dmp.UserKey user_key = 1;
  if (this->has_user_key()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *user_key_);
  }

  // int32 timeout = 3;
  if (this->timeout() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->timeout());
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void UserProfileRequest::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.dmp.UserProfileRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const UserProfileRequest* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<UserProfileRequest>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.dmp.UserProfileRequest)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.dmp.UserProfileRequest)
    MergeFrom(*source);
  }
}

void UserProfileRequest::MergeFrom(const UserProfileRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.dmp.UserProfileRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.source().size() > 0) {
    set_source(from.source());
  }
  if (from.has_user_key()) {
    mutable_user_key()->::abc::recommend_plt::dmp::UserKey::MergeFrom(from.user_key());
  }
  if (from.timeout() != 0) {
    set_timeout(from.timeout());
  }
}

void UserProfileRequest::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.dmp.UserProfileRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UserProfileRequest::CopyFrom(const UserProfileRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.dmp.UserProfileRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UserProfileRequest::IsInitialized() const {
  return true;
}

void UserProfileRequest::Swap(UserProfileRequest* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    UserProfileRequest* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == nullptr) {
      delete temp;
    }
  }
}
void UserProfileRequest::UnsafeArenaSwap(UserProfileRequest* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void UserProfileRequest::InternalSwap(UserProfileRequest* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  source_.Swap(&other->source_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(user_key_, other->user_key_);
  swap(timeout_, other->timeout_);
}

::PROTOBUF_NAMESPACE_ID::Metadata UserProfileRequest::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void UserProfileRequestBatch::InitAsDefaultInstance() {
}
class UserProfileRequestBatch::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int UserProfileRequestBatch::kUserKeyFieldNumber;
const int UserProfileRequestBatch::kSourceFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

UserProfileRequestBatch::UserProfileRequestBatch()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.dmp.UserProfileRequestBatch)
}
UserProfileRequestBatch::UserProfileRequestBatch(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
  _internal_metadata_(arena),
  user_key_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:abc.recommend_plt.dmp.UserProfileRequestBatch)
}
UserProfileRequestBatch::UserProfileRequestBatch(const UserProfileRequestBatch& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      user_key_(from.user_key_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  source_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.source().size() > 0) {
    source_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.source(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.dmp.UserProfileRequestBatch)
}

void UserProfileRequestBatch::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_UserProfileRequestBatch_proto_2frecommend_5fdmp_2eproto.base);
  source_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

UserProfileRequestBatch::~UserProfileRequestBatch() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.dmp.UserProfileRequestBatch)
  SharedDtor();
}

void UserProfileRequestBatch::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == nullptr);
  source_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void UserProfileRequestBatch::ArenaDtor(void* object) {
  UserProfileRequestBatch* _this = reinterpret_cast< UserProfileRequestBatch* >(object);
  (void)_this;
}
void UserProfileRequestBatch::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UserProfileRequestBatch::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const UserProfileRequestBatch& UserProfileRequestBatch::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_UserProfileRequestBatch_proto_2frecommend_5fdmp_2eproto.base);
  return *internal_default_instance();
}


void UserProfileRequestBatch::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.dmp.UserProfileRequestBatch)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  user_key_.Clear();
  source_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* UserProfileRequestBatch::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArenaNoVirtual(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .abc.recommend_plt.dmp.UserKey user_key = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(add_user_key(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 10);
        } else goto handle_unusual;
        continue;
      // string source = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_source(), ptr, ctx, "abc.recommend_plt.dmp.UserProfileRequestBatch.source");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool UserProfileRequestBatch::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.dmp.UserProfileRequestBatch)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .abc.recommend_plt.dmp.UserKey user_key = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
                input, add_user_key()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string source = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_source()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->source().data(), static_cast<int>(this->source().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.dmp.UserProfileRequestBatch.source"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.dmp.UserProfileRequestBatch)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.dmp.UserProfileRequestBatch)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void UserProfileRequestBatch::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.dmp.UserProfileRequestBatch)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.dmp.UserKey user_key = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->user_key_size()); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->user_key(static_cast<int>(i)),
      output);
  }

  // string source = 2;
  if (this->source().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->source().data(), static_cast<int>(this->source().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.dmp.UserProfileRequestBatch.source");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->source(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.dmp.UserProfileRequestBatch)
}

::PROTOBUF_NAMESPACE_ID::uint8* UserProfileRequestBatch::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.dmp.UserProfileRequestBatch)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.dmp.UserKey user_key = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->user_key_size()); i < n; i++) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->user_key(static_cast<int>(i)), target);
  }

  // string source = 2;
  if (this->source().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->source().data(), static_cast<int>(this->source().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.dmp.UserProfileRequestBatch.source");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        2, this->source(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.dmp.UserProfileRequestBatch)
  return target;
}

size_t UserProfileRequestBatch::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.dmp.UserProfileRequestBatch)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.dmp.UserKey user_key = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->user_key_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          this->user_key(static_cast<int>(i)));
    }
  }

  // string source = 2;
  if (this->source().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->source());
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void UserProfileRequestBatch::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.dmp.UserProfileRequestBatch)
  GOOGLE_DCHECK_NE(&from, this);
  const UserProfileRequestBatch* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<UserProfileRequestBatch>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.dmp.UserProfileRequestBatch)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.dmp.UserProfileRequestBatch)
    MergeFrom(*source);
  }
}

void UserProfileRequestBatch::MergeFrom(const UserProfileRequestBatch& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.dmp.UserProfileRequestBatch)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  user_key_.MergeFrom(from.user_key_);
  if (from.source().size() > 0) {
    set_source(from.source());
  }
}

void UserProfileRequestBatch::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.dmp.UserProfileRequestBatch)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UserProfileRequestBatch::CopyFrom(const UserProfileRequestBatch& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.dmp.UserProfileRequestBatch)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UserProfileRequestBatch::IsInitialized() const {
  return true;
}

void UserProfileRequestBatch::Swap(UserProfileRequestBatch* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    UserProfileRequestBatch* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == nullptr) {
      delete temp;
    }
  }
}
void UserProfileRequestBatch::UnsafeArenaSwap(UserProfileRequestBatch* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void UserProfileRequestBatch::InternalSwap(UserProfileRequestBatch* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  CastToBase(&user_key_)->InternalSwap(CastToBase(&other->user_key_));
  source_.Swap(&other->source_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
}

::PROTOBUF_NAMESPACE_ID::Metadata UserProfileRequestBatch::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void UserProfileBatch::InitAsDefaultInstance() {
  ::abc::recommend_plt::dmp::_UserProfileBatch_default_instance_._instance.get_mutable()->user_key_ = const_cast< ::abc::recommend_plt::dmp::UserKey*>(
      ::abc::recommend_plt::dmp::UserKey::internal_default_instance());
  ::abc::recommend_plt::dmp::_UserProfileBatch_default_instance_._instance.get_mutable()->user_profile_ = const_cast< ::abc::recommend_plt::dmp::UserProfile*>(
      ::abc::recommend_plt::dmp::UserProfile::internal_default_instance());
}
class UserProfileBatch::HasBitSetters {
 public:
  static const ::abc::recommend_plt::dmp::UserKey& user_key(const UserProfileBatch* msg);
  static const ::abc::recommend_plt::dmp::UserProfile& user_profile(const UserProfileBatch* msg);
};

const ::abc::recommend_plt::dmp::UserKey&
UserProfileBatch::HasBitSetters::user_key(const UserProfileBatch* msg) {
  return *msg->user_key_;
}
const ::abc::recommend_plt::dmp::UserProfile&
UserProfileBatch::HasBitSetters::user_profile(const UserProfileBatch* msg) {
  return *msg->user_profile_;
}
void UserProfileBatch::unsafe_arena_set_allocated_user_key(
    ::abc::recommend_plt::dmp::UserKey* user_key) {
  if (GetArenaNoVirtual() == nullptr) {
    delete user_key_;
  }
  user_key_ = user_key;
  if (user_key) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:abc.recommend_plt.dmp.UserProfileBatch.user_key)
}
void UserProfileBatch::unsafe_arena_set_allocated_user_profile(
    ::abc::recommend_plt::dmp::UserProfile* user_profile) {
  if (GetArenaNoVirtual() == nullptr) {
    delete user_profile_;
  }
  user_profile_ = user_profile;
  if (user_profile) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:abc.recommend_plt.dmp.UserProfileBatch.user_profile)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int UserProfileBatch::kUserKeyFieldNumber;
const int UserProfileBatch::kUserProfileFieldNumber;
const int UserProfileBatch::kUgidFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

UserProfileBatch::UserProfileBatch()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.dmp.UserProfileBatch)
}
UserProfileBatch::UserProfileBatch(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
  _internal_metadata_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:abc.recommend_plt.dmp.UserProfileBatch)
}
UserProfileBatch::UserProfileBatch(const UserProfileBatch& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ugid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.ugid().size() > 0) {
    ugid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.ugid(),
      GetArenaNoVirtual());
  }
  if (from.has_user_key()) {
    user_key_ = new ::abc::recommend_plt::dmp::UserKey(*from.user_key_);
  } else {
    user_key_ = nullptr;
  }
  if (from.has_user_profile()) {
    user_profile_ = new ::abc::recommend_plt::dmp::UserProfile(*from.user_profile_);
  } else {
    user_profile_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.dmp.UserProfileBatch)
}

void UserProfileBatch::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_UserProfileBatch_proto_2frecommend_5fdmp_2eproto.base);
  ugid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&user_key_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&user_profile_) -
      reinterpret_cast<char*>(&user_key_)) + sizeof(user_profile_));
}

UserProfileBatch::~UserProfileBatch() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.dmp.UserProfileBatch)
  SharedDtor();
}

void UserProfileBatch::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == nullptr);
  ugid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete user_key_;
  if (this != internal_default_instance()) delete user_profile_;
}

void UserProfileBatch::ArenaDtor(void* object) {
  UserProfileBatch* _this = reinterpret_cast< UserProfileBatch* >(object);
  (void)_this;
}
void UserProfileBatch::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UserProfileBatch::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const UserProfileBatch& UserProfileBatch::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_UserProfileBatch_proto_2frecommend_5fdmp_2eproto.base);
  return *internal_default_instance();
}


void UserProfileBatch::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.dmp.UserProfileBatch)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ugid_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == nullptr && user_key_ != nullptr) {
    delete user_key_;
  }
  user_key_ = nullptr;
  if (GetArenaNoVirtual() == nullptr && user_profile_ != nullptr) {
    delete user_profile_;
  }
  user_profile_ = nullptr;
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* UserProfileBatch::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArenaNoVirtual(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .abc.recommend_plt.dmp.UserKey user_key = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(mutable_user_key(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .abc.recommend_plt.dmp.UserProfile user_profile = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(mutable_user_profile(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string ugid = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_ugid(), ptr, ctx, "abc.recommend_plt.dmp.UserProfileBatch.ugid");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool UserProfileBatch::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.dmp.UserProfileBatch)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .abc.recommend_plt.dmp.UserKey user_key = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_user_key()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .abc.recommend_plt.dmp.UserProfile user_profile = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (18 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_user_profile()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string ugid = 3;
      case 3: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (26 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_ugid()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->ugid().data(), static_cast<int>(this->ugid().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.dmp.UserProfileBatch.ugid"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.dmp.UserProfileBatch)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.dmp.UserProfileBatch)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void UserProfileBatch::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.dmp.UserProfileBatch)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .abc.recommend_plt.dmp.UserKey user_key = 1;
  if (this->has_user_key()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, HasBitSetters::user_key(this), output);
  }

  // .abc.recommend_plt.dmp.UserProfile user_profile = 2;
  if (this->has_user_profile()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, HasBitSetters::user_profile(this), output);
  }

  // string ugid = 3;
  if (this->ugid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->ugid().data(), static_cast<int>(this->ugid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.dmp.UserProfileBatch.ugid");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->ugid(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.dmp.UserProfileBatch)
}

::PROTOBUF_NAMESPACE_ID::uint8* UserProfileBatch::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.dmp.UserProfileBatch)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .abc.recommend_plt.dmp.UserKey user_key = 1;
  if (this->has_user_key()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, HasBitSetters::user_key(this), target);
  }

  // .abc.recommend_plt.dmp.UserProfile user_profile = 2;
  if (this->has_user_profile()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, HasBitSetters::user_profile(this), target);
  }

  // string ugid = 3;
  if (this->ugid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->ugid().data(), static_cast<int>(this->ugid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.dmp.UserProfileBatch.ugid");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        3, this->ugid(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.dmp.UserProfileBatch)
  return target;
}

size_t UserProfileBatch::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.dmp.UserProfileBatch)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string ugid = 3;
  if (this->ugid().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->ugid());
  }

  // .abc.recommend_plt.dmp.UserKey user_key = 1;
  if (this->has_user_key()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *user_key_);
  }

  // .abc.recommend_plt.dmp.UserProfile user_profile = 2;
  if (this->has_user_profile()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *user_profile_);
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void UserProfileBatch::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.dmp.UserProfileBatch)
  GOOGLE_DCHECK_NE(&from, this);
  const UserProfileBatch* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<UserProfileBatch>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.dmp.UserProfileBatch)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.dmp.UserProfileBatch)
    MergeFrom(*source);
  }
}

void UserProfileBatch::MergeFrom(const UserProfileBatch& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.dmp.UserProfileBatch)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.ugid().size() > 0) {
    set_ugid(from.ugid());
  }
  if (from.has_user_key()) {
    mutable_user_key()->::abc::recommend_plt::dmp::UserKey::MergeFrom(from.user_key());
  }
  if (from.has_user_profile()) {
    mutable_user_profile()->::abc::recommend_plt::dmp::UserProfile::MergeFrom(from.user_profile());
  }
}

void UserProfileBatch::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.dmp.UserProfileBatch)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UserProfileBatch::CopyFrom(const UserProfileBatch& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.dmp.UserProfileBatch)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UserProfileBatch::IsInitialized() const {
  return true;
}

void UserProfileBatch::Swap(UserProfileBatch* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    UserProfileBatch* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == nullptr) {
      delete temp;
    }
  }
}
void UserProfileBatch::UnsafeArenaSwap(UserProfileBatch* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void UserProfileBatch::InternalSwap(UserProfileBatch* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  ugid_.Swap(&other->ugid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(user_key_, other->user_key_);
  swap(user_profile_, other->user_profile_);
}

::PROTOBUF_NAMESPACE_ID::Metadata UserProfileBatch::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void UserProfileResponseBatch::InitAsDefaultInstance() {
}
class UserProfileResponseBatch::HasBitSetters {
 public:
};

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int UserProfileResponseBatch::kUserProfileBatchFieldNumber;
const int UserProfileResponseBatch::kStatusFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

UserProfileResponseBatch::UserProfileResponseBatch()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.dmp.UserProfileResponseBatch)
}
UserProfileResponseBatch::UserProfileResponseBatch(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
  _internal_metadata_(arena),
  user_profile_batch_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:abc.recommend_plt.dmp.UserProfileResponseBatch)
}
UserProfileResponseBatch::UserProfileResponseBatch(const UserProfileResponseBatch& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr),
      user_profile_batch_(from.user_profile_batch_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  status_ = from.status_;
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.dmp.UserProfileResponseBatch)
}

void UserProfileResponseBatch::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_UserProfileResponseBatch_proto_2frecommend_5fdmp_2eproto.base);
  status_ = 0;
}

UserProfileResponseBatch::~UserProfileResponseBatch() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.dmp.UserProfileResponseBatch)
  SharedDtor();
}

void UserProfileResponseBatch::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == nullptr);
}

void UserProfileResponseBatch::ArenaDtor(void* object) {
  UserProfileResponseBatch* _this = reinterpret_cast< UserProfileResponseBatch* >(object);
  (void)_this;
}
void UserProfileResponseBatch::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UserProfileResponseBatch::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const UserProfileResponseBatch& UserProfileResponseBatch::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_UserProfileResponseBatch_proto_2frecommend_5fdmp_2eproto.base);
  return *internal_default_instance();
}


void UserProfileResponseBatch::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.dmp.UserProfileResponseBatch)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  user_profile_batch_.Clear();
  status_ = 0;
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* UserProfileResponseBatch::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArenaNoVirtual(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .abc.recommend_plt.dmp.UserProfileBatch user_profile_batch = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(add_user_profile_batch(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<::PROTOBUF_NAMESPACE_ID::uint8>(ptr) == 10);
        } else goto handle_unusual;
        continue;
      // .abc.recommend_plt.dmp.UserProfileResponseBatch.ResponseStatus status = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
          set_status(static_cast<::abc::recommend_plt::dmp::UserProfileResponseBatch_ResponseStatus>(val));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool UserProfileResponseBatch::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.dmp.UserProfileResponseBatch)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .abc.recommend_plt.dmp.UserProfileBatch user_profile_batch = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
                input, add_user_profile_batch()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .abc.recommend_plt.dmp.UserProfileResponseBatch.ResponseStatus status = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (16 & 0xFF)) {
          int value = 0;
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   int, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_status(static_cast< ::abc::recommend_plt::dmp::UserProfileResponseBatch_ResponseStatus >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.dmp.UserProfileResponseBatch)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.dmp.UserProfileResponseBatch)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void UserProfileResponseBatch::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.dmp.UserProfileResponseBatch)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.dmp.UserProfileBatch user_profile_batch = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->user_profile_batch_size()); i < n; i++) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->user_profile_batch(static_cast<int>(i)),
      output);
  }

  // .abc.recommend_plt.dmp.UserProfileResponseBatch.ResponseStatus status = 2;
  if (this->status() != 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnum(
      2, this->status(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.dmp.UserProfileResponseBatch)
}

::PROTOBUF_NAMESPACE_ID::uint8* UserProfileResponseBatch::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.dmp.UserProfileResponseBatch)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.dmp.UserProfileBatch user_profile_batch = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->user_profile_batch_size()); i < n; i++) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->user_profile_batch(static_cast<int>(i)), target);
  }

  // .abc.recommend_plt.dmp.UserProfileResponseBatch.ResponseStatus status = 2;
  if (this->status() != 0) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->status(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.dmp.UserProfileResponseBatch)
  return target;
}

size_t UserProfileResponseBatch::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.dmp.UserProfileResponseBatch)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .abc.recommend_plt.dmp.UserProfileBatch user_profile_batch = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->user_profile_batch_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          this->user_profile_batch(static_cast<int>(i)));
    }
  }

  // .abc.recommend_plt.dmp.UserProfileResponseBatch.ResponseStatus status = 2;
  if (this->status() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->status());
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void UserProfileResponseBatch::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.dmp.UserProfileResponseBatch)
  GOOGLE_DCHECK_NE(&from, this);
  const UserProfileResponseBatch* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<UserProfileResponseBatch>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.dmp.UserProfileResponseBatch)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.dmp.UserProfileResponseBatch)
    MergeFrom(*source);
  }
}

void UserProfileResponseBatch::MergeFrom(const UserProfileResponseBatch& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.dmp.UserProfileResponseBatch)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  user_profile_batch_.MergeFrom(from.user_profile_batch_);
  if (from.status() != 0) {
    set_status(from.status());
  }
}

void UserProfileResponseBatch::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.dmp.UserProfileResponseBatch)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UserProfileResponseBatch::CopyFrom(const UserProfileResponseBatch& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.dmp.UserProfileResponseBatch)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UserProfileResponseBatch::IsInitialized() const {
  return true;
}

void UserProfileResponseBatch::Swap(UserProfileResponseBatch* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    UserProfileResponseBatch* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == nullptr) {
      delete temp;
    }
  }
}
void UserProfileResponseBatch::UnsafeArenaSwap(UserProfileResponseBatch* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void UserProfileResponseBatch::InternalSwap(UserProfileResponseBatch* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  CastToBase(&user_profile_batch_)->InternalSwap(CastToBase(&other->user_profile_batch_));
  swap(status_, other->status_);
}

::PROTOBUF_NAMESPACE_ID::Metadata UserProfileResponseBatch::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void UserProfileResponse::InitAsDefaultInstance() {
  ::abc::recommend_plt::dmp::_UserProfileResponse_default_instance_._instance.get_mutable()->user_profile_ = const_cast< ::abc::recommend_plt::dmp::UserProfile*>(
      ::abc::recommend_plt::dmp::UserProfile::internal_default_instance());
}
class UserProfileResponse::HasBitSetters {
 public:
  static const ::abc::recommend_plt::dmp::UserProfile& user_profile(const UserProfileResponse* msg);
};

const ::abc::recommend_plt::dmp::UserProfile&
UserProfileResponse::HasBitSetters::user_profile(const UserProfileResponse* msg) {
  return *msg->user_profile_;
}
void UserProfileResponse::unsafe_arena_set_allocated_user_profile(
    ::abc::recommend_plt::dmp::UserProfile* user_profile) {
  if (GetArenaNoVirtual() == nullptr) {
    delete user_profile_;
  }
  user_profile_ = user_profile;
  if (user_profile) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:abc.recommend_plt.dmp.UserProfileResponse.user_profile)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int UserProfileResponse::kUserProfileFieldNumber;
const int UserProfileResponse::kStatusFieldNumber;
const int UserProfileResponse::kUgidFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

UserProfileResponse::UserProfileResponse()
  : ::PROTOBUF_NAMESPACE_ID::Message(), _internal_metadata_(nullptr) {
  SharedCtor();
  // @@protoc_insertion_point(constructor:abc.recommend_plt.dmp.UserProfileResponse)
}
UserProfileResponse::UserProfileResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
  _internal_metadata_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:abc.recommend_plt.dmp.UserProfileResponse)
}
UserProfileResponse::UserProfileResponse(const UserProfileResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _internal_metadata_(nullptr) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ugid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from.ugid().size() > 0) {
    ugid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from.ugid(),
      GetArenaNoVirtual());
  }
  if (from.has_user_profile()) {
    user_profile_ = new ::abc::recommend_plt::dmp::UserProfile(*from.user_profile_);
  } else {
    user_profile_ = nullptr;
  }
  status_ = from.status_;
  // @@protoc_insertion_point(copy_constructor:abc.recommend_plt.dmp.UserProfileResponse)
}

void UserProfileResponse::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_UserProfileResponse_proto_2frecommend_5fdmp_2eproto.base);
  ugid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&user_profile_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&status_) -
      reinterpret_cast<char*>(&user_profile_)) + sizeof(status_));
}

UserProfileResponse::~UserProfileResponse() {
  // @@protoc_insertion_point(destructor:abc.recommend_plt.dmp.UserProfileResponse)
  SharedDtor();
}

void UserProfileResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == nullptr);
  ugid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete user_profile_;
}

void UserProfileResponse::ArenaDtor(void* object) {
  UserProfileResponse* _this = reinterpret_cast< UserProfileResponse* >(object);
  (void)_this;
}
void UserProfileResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UserProfileResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const UserProfileResponse& UserProfileResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_UserProfileResponse_proto_2frecommend_5fdmp_2eproto.base);
  return *internal_default_instance();
}


void UserProfileResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:abc.recommend_plt.dmp.UserProfileResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ugid_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == nullptr && user_profile_ != nullptr) {
    delete user_profile_;
  }
  user_profile_ = nullptr;
  status_ = 0;
  _internal_metadata_.Clear();
}

#if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
const char* UserProfileResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArenaNoVirtual(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .abc.recommend_plt.dmp.UserProfile user_profile = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(mutable_user_profile(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .abc.recommend_plt.dmp.UserProfileResponse.ResponseStatus status = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint(&ptr);
          CHK_(ptr);
          set_status(static_cast<::abc::recommend_plt::dmp::UserProfileResponse_ResponseStatus>(val));
        } else goto handle_unusual;
        continue;
      // string ugid = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParserUTF8(mutable_ugid(), ptr, ctx, "abc.recommend_plt.dmp.UserProfileResponse.ugid");
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag, &_internal_metadata_, ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}
#else  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
bool UserProfileResponse::MergePartialFromCodedStream(
    ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!PROTOBUF_PREDICT_TRUE(EXPRESSION)) goto failure
  ::PROTOBUF_NAMESPACE_ID::uint32 tag;
  // @@protoc_insertion_point(parse_start:abc.recommend_plt.dmp.UserProfileResponse)
  for (;;) {
    ::std::pair<::PROTOBUF_NAMESPACE_ID::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .abc.recommend_plt.dmp.UserProfile user_profile = 1;
      case 1: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (10 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadMessage(
               input, mutable_user_profile()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .abc.recommend_plt.dmp.UserProfileResponse.ResponseStatus status = 2;
      case 2: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (16 & 0xFF)) {
          int value = 0;
          DO_((::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadPrimitive<
                   int, ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_status(static_cast< ::abc::recommend_plt::dmp::UserProfileResponse_ResponseStatus >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string ugid = 3;
      case 3: {
        if (static_cast< ::PROTOBUF_NAMESPACE_ID::uint8>(tag) == (26 & 0xFF)) {
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::ReadString(
                input, this->mutable_ugid()));
          DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
            this->ugid().data(), static_cast<int>(this->ugid().length()),
            ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE,
            "abc.recommend_plt.dmp.UserProfileResponse.ugid"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:abc.recommend_plt.dmp.UserProfileResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:abc.recommend_plt.dmp.UserProfileResponse)
  return false;
#undef DO_
}
#endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER

void UserProfileResponse::SerializeWithCachedSizes(
    ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:abc.recommend_plt.dmp.UserProfileResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .abc.recommend_plt.dmp.UserProfile user_profile = 1;
  if (this->has_user_profile()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, HasBitSetters::user_profile(this), output);
  }

  // .abc.recommend_plt.dmp.UserProfileResponse.ResponseStatus status = 2;
  if (this->status() != 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnum(
      2, this->status(), output);
  }

  // string ugid = 3;
  if (this->ugid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->ugid().data(), static_cast<int>(this->ugid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.dmp.UserProfileResponse.ugid");
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->ugid(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:abc.recommend_plt.dmp.UserProfileResponse)
}

::PROTOBUF_NAMESPACE_ID::uint8* UserProfileResponse::InternalSerializeWithCachedSizesToArray(
    ::PROTOBUF_NAMESPACE_ID::uint8* target) const {
  // @@protoc_insertion_point(serialize_to_array_start:abc.recommend_plt.dmp.UserProfileResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .abc.recommend_plt.dmp.UserProfile user_profile = 1;
  if (this->has_user_profile()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, HasBitSetters::user_profile(this), target);
  }

  // .abc.recommend_plt.dmp.UserProfileResponse.ResponseStatus status = 2;
  if (this->status() != 0) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->status(), target);
  }

  // string ugid = 3;
  if (this->ugid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->ugid().data(), static_cast<int>(this->ugid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "abc.recommend_plt.dmp.UserProfileResponse.ugid");
    target =
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteStringToArray(
        3, this->ugid(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:abc.recommend_plt.dmp.UserProfileResponse)
  return target;
}

size_t UserProfileResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:abc.recommend_plt.dmp.UserProfileResponse)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string ugid = 3;
  if (this->ugid().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->ugid());
  }

  // .abc.recommend_plt.dmp.UserProfile user_profile = 1;
  if (this->has_user_profile()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *user_profile_);
  }

  // .abc.recommend_plt.dmp.UserProfileResponse.ResponseStatus status = 2;
  if (this->status() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->status());
  }

  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void UserProfileResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:abc.recommend_plt.dmp.UserProfileResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const UserProfileResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<UserProfileResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:abc.recommend_plt.dmp.UserProfileResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:abc.recommend_plt.dmp.UserProfileResponse)
    MergeFrom(*source);
  }
}

void UserProfileResponse::MergeFrom(const UserProfileResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:abc.recommend_plt.dmp.UserProfileResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.ugid().size() > 0) {
    set_ugid(from.ugid());
  }
  if (from.has_user_profile()) {
    mutable_user_profile()->::abc::recommend_plt::dmp::UserProfile::MergeFrom(from.user_profile());
  }
  if (from.status() != 0) {
    set_status(from.status());
  }
}

void UserProfileResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:abc.recommend_plt.dmp.UserProfileResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UserProfileResponse::CopyFrom(const UserProfileResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:abc.recommend_plt.dmp.UserProfileResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UserProfileResponse::IsInitialized() const {
  return true;
}

void UserProfileResponse::Swap(UserProfileResponse* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    UserProfileResponse* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == nullptr) {
      delete temp;
    }
  }
}
void UserProfileResponse::UnsafeArenaSwap(UserProfileResponse* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void UserProfileResponse::InternalSwap(UserProfileResponse* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  ugid_.Swap(&other->ugid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(user_profile_, other->user_profile_);
  swap(status_, other->status_);
}

::PROTOBUF_NAMESPACE_ID::Metadata UserProfileResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace dmp
}  // namespace recommend_plt
}  // namespace abc
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::dmp::UserKey* Arena::CreateMaybeMessage< ::abc::recommend_plt::dmp::UserKey >(Arena* arena) {
  return Arena::CreateMessageInternal< ::abc::recommend_plt::dmp::UserKey >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::dmp::TagValue* Arena::CreateMaybeMessage< ::abc::recommend_plt::dmp::TagValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::abc::recommend_plt::dmp::TagValue >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::dmp::Tag* Arena::CreateMaybeMessage< ::abc::recommend_plt::dmp::Tag >(Arena* arena) {
  return Arena::CreateMessageInternal< ::abc::recommend_plt::dmp::Tag >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::dmp::UserProfile* Arena::CreateMaybeMessage< ::abc::recommend_plt::dmp::UserProfile >(Arena* arena) {
  return Arena::CreateMessageInternal< ::abc::recommend_plt::dmp::UserProfile >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::dmp::UserProfileRequest* Arena::CreateMaybeMessage< ::abc::recommend_plt::dmp::UserProfileRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::abc::recommend_plt::dmp::UserProfileRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::dmp::UserProfileRequestBatch* Arena::CreateMaybeMessage< ::abc::recommend_plt::dmp::UserProfileRequestBatch >(Arena* arena) {
  return Arena::CreateMessageInternal< ::abc::recommend_plt::dmp::UserProfileRequestBatch >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::dmp::UserProfileBatch* Arena::CreateMaybeMessage< ::abc::recommend_plt::dmp::UserProfileBatch >(Arena* arena) {
  return Arena::CreateMessageInternal< ::abc::recommend_plt::dmp::UserProfileBatch >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::dmp::UserProfileResponseBatch* Arena::CreateMaybeMessage< ::abc::recommend_plt::dmp::UserProfileResponseBatch >(Arena* arena) {
  return Arena::CreateMessageInternal< ::abc::recommend_plt::dmp::UserProfileResponseBatch >(arena);
}
template<> PROTOBUF_NOINLINE ::abc::recommend_plt::dmp::UserProfileResponse* Arena::CreateMaybeMessage< ::abc::recommend_plt::dmp::UserProfileResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::abc::recommend_plt::dmp::UserProfileResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
