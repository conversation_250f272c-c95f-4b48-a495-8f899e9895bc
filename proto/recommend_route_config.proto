syntax = "proto3";
package abc.recommend_plt.route;

message RouteRule {
    string service_name = 1;
    string app = 2; // idmp2
    // iosshsa 默认站点路由
    // 物料格式 iosshsa_1    site_uid:item_type
    // default 默认兜底路由策略 必须存在配置
    // ,逗号分割多个路由策略
    string route_rule = 3; 
}

message RouteInfoList {
    repeated RouteRule rule_info = 1;
}

message RouteParam {
    RouteInfoList route_info_list = 1;
}

message RouteConfig {
    string route_conf = 1; 
}