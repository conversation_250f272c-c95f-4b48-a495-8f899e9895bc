[{"directory": "/Users/<USER>/shein_project/rec-plt", "command": "clang++ -I. -Iproto -Iservice -Iservice/common -Iservice/strategy -Iservice/strategy/common -Iservice/strategy/executor -Iservice/strategy/functor -Iservice/strategy/src -Iservice/updater -Iutil -Ithirdparty -Ithirdparty/boost -Ithirdparty/grpc/include -Ithirdparty/gflags -Ithirdparty/glog -Ithirdparty/jsoncpp/include -Ithirdparty/rapidjson -Ithirdparty/prometheus -Ithirdparty/arrow/include -std=c++17 -g -O2 -Wno-sign-compare -Wno-overloaded-virtual -Wno-non-virtual-dtor -Werror=return-type -Werror=type-limits -Werror=return-local-addr -Werror=maybe-uninitialized -Wno-deprecated -c service/strategy/rec_strategy.cc", "file": "service/strategy/rec_strategy.cc"}, {"directory": "/Users/<USER>/shein_project/rec-plt", "command": "clang++ -I. -Iproto -Iservice -Iservice/common -Iservice/strategy -Iservice/strategy/common -Iservice/strategy/executor -Iservice/strategy/functor -Iservice/strategy/src -Iservice/updater -Iutil -Ithirdparty -Ithirdparty/boost -Ithirdparty/grpc/include -Ithirdparty/gflags -Ithirdparty/glog -Ithirdparty/jsoncpp/include -Ithirdparty/rapidjson -Ithirdparty/prometheus -Ithirdparty/arrow/include -std=c++17 -g -O2 -Wno-sign-compare -Wno-overloaded-virtual -Wno-non-virtual-dtor -Werror=return-type -Werror=type-limits -Werror=return-local-addr -Werror=maybe-uninitialized -Wno-deprecated -c service/strategy/src/strategy_svr_imp.cc", "file": "service/strategy/src/strategy_svr_imp.cc"}, {"directory": "/Users/<USER>/shein_project/rec-plt", "command": "clang++ -I. -Iproto -Iservice -Iservice/common -Iservice/strategy -Iservice/strategy/common -Iservice/strategy/executor -Iservice/strategy/functor -Iservice/strategy/src -Iservice/updater -Iutil -Ithirdparty -Ithirdparty/boost -Ithirdparty/grpc/include -Ithirdparty/gflags -Ithirdparty/glog -Ithirdparty/jsoncpp/include -Ithirdparty/rapidjson -Ithirdparty/prometheus -Ithirdparty/arrow/include -std=c++17 -g -O2 -Wno-sign-compare -Wno-overloaded-virtual -Wno-non-virtual-dtor -Werror=return-type -Werror=type-limits -Werror=return-local-addr -Werror=maybe-uninitialized -Wno-deprecated -c service/strategy/common/common_func.cc", "file": "service/strategy/common/common_func.cc"}, {"directory": "/Users/<USER>/shein_project/rec-plt", "command": "clang++ -I. -Iproto -Iservice -Iservice/common -Iservice/strategy -Iservice/strategy/common -Iservice/strategy/executor -Iservice/strategy/functor -Iservice/strategy/src -Iservice/updater -Iutil -Ithirdparty -Ithirdparty/boost -Ithirdparty/grpc/include -Ithirdparty/gflags -Ithirdparty/glog -Ithirdparty/jsoncpp/include -Ithirdparty/rapidjson -Ithirdparty/prometheus -Ithirdparty/arrow/include -std=c++17 -g -O2 -Wno-sign-compare -Wno-overloaded-virtual -Wno-non-virtual-dtor -Werror=return-type -Werror=type-limits -Werror=return-local-addr -Werror=maybe-uninitialized -Wno-deprecated -c service/strategy/executor/strategy_executor.cc", "file": "service/strategy/executor/strategy_executor.cc"}, {"directory": "/Users/<USER>/shein_project/rec-plt", "command": "clang++ -I. -Iproto -Iservice -Iservice/common -Iservice/strategy -Iservice/strategy/common -Iservice/strategy/executor -Iservice/strategy/functor -Iservice/strategy/src -Iservice/updater -Iutil -Ithirdparty -Ithirdparty/boost -Ithirdparty/grpc/include -Ithirdparty/gflags -Ithirdparty/glog -Ithirdparty/jsoncpp/include -Ithirdparty/rapidjson -Ithirdparty/prometheus -Ithirdparty/arrow/include -std=c++17 -g -O2 -Wno-sign-compare -Wno-overloaded-virtual -Wno-non-virtual-dtor -Werror=return-type -Werror=type-limits -Werror=return-local-addr -Werror=maybe-uninitialized -Wno-deprecated -c util/func.cc", "file": "util/func.cc"}, {"directory": "/Users/<USER>/shein_project/rec-plt", "command": "clang++ -I. -Iproto -Iservice -Iservice/common -Iservice/strategy -Iservice/strategy/common -Iservice/strategy/executor -Iservice/strategy/functor -Iservice/strategy/src -Iservice/updater -Iutil -Ithirdparty -Ithirdparty/boost -Ithirdparty/grpc/include -Ithirdparty/gflags -Ithirdparty/glog -Ithirdparty/jsoncpp/include -Ithirdparty/rapidjson -Ithirdparty/prometheus -Ithirdparty/arrow/include -std=c++17 -g -O2 -Wno-sign-compare -Wno-overloaded-virtual -Wno-non-virtual-dtor -Werror=return-type -Werror=type-limits -Werror=return-local-addr -Werror=maybe-uninitialized -Wno-deprecated -c util/logger.cc", "file": "util/logger.cc"}]