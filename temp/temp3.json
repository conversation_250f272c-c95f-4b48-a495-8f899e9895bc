{"dsl_score_fuse": "# ptr调整\n\n## 市价因子查询(埋点用) && 投放子场景 sub_tc_exp_scene\ntc_exp_scene_int = int(tc_exp_scene)\nsub_scenes = [2028, 2029, 2033] if scene_id == 131 else [tc_exp_scene_int]\n### ROI商品市价因子\nsub_tc_exp_scene_valid = ifnull(sub_tc_exp_scene, padding([tc_exp_scene_int], len(G_score), tc_exp_scene_int))\nkv_keys_mpr = concat_ws(\"`\", site_id, site_country, sub_scenes)\nroi_market_price_rate = kv_model(13423, kv_keys_mpr, \"0.0\", \"FLOAT\")\nroi_rate_map = to_map(sub_scenes, roi_market_price_rate)\nroi_market_price_rate_item = fetch(roi_rate_map, sub_tc_exp_scene_valid, 0) if roi_plan_goods > 0 else 0.0\n### AD商品市价因子\nad_sub_tc_exp_scene_valid = ifnull(ad_sub_tc_exp_scene, padding([tc_exp_scene_int], len(G_score), tc_exp_scene_int))\nad_market_price_rate = kv_model(13486, kv_keys_mpr, \"0.0\", \"FLOAT\")\nad_rate_map = to_map(sub_scenes, ad_market_price_rate)\nad_market_price_rate_item = fetch(ad_rate_map, ad_sub_tc_exp_scene_valid, 0) if ad_id > 0 else 0.0\n### 市价因子合并\nroi_market_price_rate_item = roi_market_price_rate_item if roi_plan_goods > 0 else ad_market_price_rate_item\n\n## 兼容空值\ndefault_int = padding([0], len(G_score), 0)\ndefault_int_one = padding([1], len(G_score), 1)\ndefault_float = padding([0.0], len(G_score), 0.0)\nroi_no_weight_valid = ifnull(roi_no_weight, default_int_one)\nad_no_weight_valid = ifnull(ad_no_weight, default_int_one)\npprice_valid = ifnull(pprice, default_float)\nad_pprice_valid = ifnull(ad_pprice, default_float)\nad_cvr_valid = ifnull(ad_cvr, default_float)\nad_ad_cvr_valid = ifnull(ad_ad_cvr, default_float)\nctr_valid = ifnull(ctr_cal, default_float)\ncommon_ad_cvr = ad_cvr_valid if roi_plan_goods > 0 else ad_ad_cvr_valid\ncommon_pprice = pprice_valid if roi_plan_goods > 0 else ad_pprice_valid\n## 调整roi_ptr\nroi_ptr = ifnull(ori_ptr, padding([0.0], len(G_score), 0.0))\nroi_ptr = 0.0 if roi_no_weight_valid > 0 else roi_ptr\n## 调整ad_ptr\nad_ptr = ifnull(ori_ad_ptr, padding([0.0], len(G_score), 0.0))\nad_ptr = 0.0 if ad_no_weight_valid > 0 else ad_ptr\n## 合并ptr\nptr = roi_ptr if roi_plan_goods > 0 else ad_ptr\nptr = ptr if G_score > 1e-6 else 0.0\nptr = ptr if ctr_cal > 0.0 else 0.0\nptr = ptr if common_ad_cvr > 0.0 and common_pprice > 0.0 else 0.0\nptr = ptr if roi_market_price_rate_item > 0 else 0.0\n\nweight = ptr + 1.0\nfinal_score = G_score * weight\n\n# 以下变量可解释顺序不可看\n## 按final_score排序\ndesc_sorted_idx = sort_by(List(final_score), 1)\ndesc_rank_score = take(G_score, desc_sorted_idx)\ndesc_item_id = take(item_id, desc_sorted_idx)\n## 计算修正分\ndesc_modified_score = make_orderly(desc_rank_score, 0.0, 1, 1)\ndesc_bill_tr = desc_modified_score / desc_rank_score - 1.0 if desc_rank_score > 1e-6 else 0.0\ndesc_bill_tr = desc_bill_tr if desc_bill_tr > 0.0 else 0.0\n\n# 还原modified_score供测试使用, 上线前删掉!!!!!!!!\nitem_id2modified_score_map = to_map(desc_item_id, desc_modified_score)\nmodified_score = fetch(item_id2modified_score_map, item_id)\n\n# 还原顺序\nitem_id2bill_tr_map = to_map(desc_item_id, desc_bill_tr)\nbill_tr = fetch(item_id2bill_tr_map, item_id)\nbill_tr = bill_tr if ptr > 0.0 else 0.0\n# bill_tr = bill_tr if bill_tr < ptr else ptr\n\n# 计算埋点（干预前后坑位变化）\nroi_rloc_before = copy(G_location)\nroi_rloc_after = rank(final_score, 1) - 1\n## 前端埋点\nroi_rloc_diff = concat_ws(\"_\", roi_rloc_before, roi_rloc_after)\nroi_rloc_diff = roi_rloc_diff if bill_tr > 0.0 else \"\"\n## 干预mpr埋点\nroi_market_price_rate_item = roi_market_price_rate_item if bill_tr > 0.0 else 0.0\n\n# ROI&AD埋点兼容\n## 上报ad埋点用\nad_mark = 1 if ad_id > 0 and bill_tr > 0.0 else 0\n## 上报roi埋点用\nroi_ad_plan_mark = roi_plan_goods if roi_plan_goods > 0 else ad_id\n## 人群命中埋点\nroi_crowd_target_valid = ifnull(roi_crowd_target, default_int)\nad_crowd_target_valid = ifnull(ad_crowd_target, default_int)\nroi_ad_crowd_target_mark = roi_crowd_target_valid if roi_plan_goods > 0 else ad_crowd_target_valid\n\n## 获取各个因子值\nacr_val = ad_cvr_valid if roi_plan_goods > 0 else ad_ad_cvr_valid if ad_id > 0 else 0.0\nppr_val = pprice_valid if roi_plan_goods > 0 else ad_pprice_valid if ad_id > 0 else 0.0\n\n## 计算乘积\necpm_val = 0.0\nif scene_id == 2028 or scene_id == 2029:  # 商详混品场景\n    ecpm_val = ctr_valid * acr_val * ppr_val * roi_market_price_rate_item * ptr\nelse:  # 其他场景\n    ecpm_val = ctr_valid * acr_val * ppr_val * roi_market_price_rate_item * bill_tr\n    \n## 计算ROI商品的均值\nroi_roi_market_price_rate_items = select(roi_market_price_rate_item, is_roi_item)\nroi_bill_trs = select(bill_tr, is_roi_item)\nroi_ecpm_vals = select(ecpm_val, is_roi_item)\n\nroi_avg_mpr = int(avg(roi_roi_market_price_rate_items) * 10000) if len(roi_roi_market_price_rate_items) > 0 else 0\nroi_avg_btr = int(avg(roi_bill_trs) * 10000) if len(roi_bill_trs) > 0 else 0\nroi_avg_ecpm = int(avg(roi_ecpm_vals) * 10000) if len(roi_ecpm_vals) > 0 else 0\n\n## 计算商业化广告的均值\nad_roi_market_price_rate_items = select(roi_market_price_rate_item, is_ad_item)\nad_bill_trs = select(bill_tr, is_ad_item)\nad_ecpm_vals = select(ecpm_val, is_ad_item)\n\nad_avg_mpr = int(avg(ad_roi_market_price_rate_items) * 10000) if len(ad_roi_market_price_rate_items) > 0 else 0\nad_avg_btr = int(avg(ad_bill_trs) * 10000) if len(ad_bill_trs) > 0 else 0\nad_avg_ecpm = int(avg(ad_ecpm_vals) * 10000) if len(ad_ecpm_vals) > 0 else 0\n\nfinal_score", "score_item_fea": "G_rank_score", "input_item_fea": ["ori_ptr", "ori_ad_ptr", "ad_pprice", "ad_ad_cvr", "ctr_cal", "pprice", "ad_cvr", "rt_take_rate", "item_id", "roi_no_weight", "ad_no_weight", "G_score", "G_location", "roi_plan_goods", "sub_tc_exp_scene", "ad_sub_tc_exp_scene", "ad_id", "roi_crowd_target", "ad_crowd_target"], "output_item_fea": ["bill_tr", "roi_market_price_rate_item", "roi_rloc_diff", "ad_mark", "roi_ad_plan_mark", "roi_ad_crowd_target_mark"], "report_item_fea": ["svr_mark:ptr", "svr_mark:bill_tr", "svr_mark:rt_take_rate", "svr_mark:roi_rloc_before", "svr_mark:roi_rloc_after", "svr_mark:roi_btr", "svr_mark:roi_ecpm", "svr_mark:ad_btr", "svr_mark:ad_ecpm"], "input_ctx_fea": ["item_source_req", "roi_market_price_rate", "tc_exp_scene", "site_country", "site_id", "scene_id"], "fg_options": {"enable_null_execution": true}}