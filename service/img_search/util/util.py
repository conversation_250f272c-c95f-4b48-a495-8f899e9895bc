#!/usr/bin/evn python
# _*_ coding:utf-8 _*_

#
# author: bluse.lee
# date: 2019.01.15
#
import os
import sys
import logging
import time
import json
import socket
import requests
import urllib.request
import ssl
import re
import socket
from hashlib import md5
import subprocess
from logging.handlers import TimedRotatingFileHandler
from configparser import ConfigParser
socket.setdefaulttimeout(10)
import util.config as cg
ssl._create_default_https_context = ssl._create_unverified_context
'''
#python 单例类
class singleton(object):
    def __new__(cls, *args, **kw):
        if not hasattr(cls, '_instance'):
            orig = super(Singleton, cls)
            cls._instance = orig.__new__(cls, *args, **kw)
        return cls._instance

class rec_Section(Singleton):
    section = 'prod'       #默认为prod
    base_path = ''         #项目的基准路径
    proto_data_path = ''   #项目的PB临时存放路径
    log_path = ''
'''
#公用接口 


def setup_log(log_name):
    # 创建logger对象。传入logger名字
    log_path = os.path.join('path', log_name)
    
    log_json_str = '{\"logTag\": \"img-index-svr\",\"appName\":\"recommend-plt\",\"timestamp\": \"%(asctime)s.%(msecs)04d\", \"level\":\"INFO\", \"host\": \"%(host_name)s\", \"ip\": \"%(host_ip)s\", \"message\":\"[%(asctime)s.%(msecs)04d %(filename)s %(lineno)d] %(message)s\"}'
    
    logging.basicConfig(filename = path + file_name, format = log_json_str, datefmt = '%Y-%m-%d+%H:%M:%S', filemode = 'w', level=logging.INFO)
    # 设置日志记录等级
    logger = logging.getLogger(log_name)
    logger.setLevel(logging.INFO)
    # interval 滚动周期，
    # when="MIDNIGHT", interval=1 表示每天0点为更新点，每天生成一个文件
    # backupCount  表示日志保存个数
    file_handler = TimedRotatingFileHandler(filename=log_path, when="MIDNIGHT", interval=1, backupCount=7)
    # filename="mylog" suffix设置，会生成文件名为mylog.2020-02-25.log
    file_handler.suffix = "%Y-%m-%d.log"
    # extMatch是编译好正则表达式，用于匹配日志文件名后缀
    # 需要注意的是suffix和extMatch一定要匹配的上，如果不匹配，过期日志不会被删除。
    file_handler.extMatch = re.compile(r"^\d{4}-\d{2}-\d{2}.log$")
    # 定义日志输出格式
    
    file_handler.setFormatter(logging.Formatter(log_json_str, datefmt = '%Y-%m-%d+%H:%M:%S'))
    logger.addHandler(file_handler)
    return logger



def get_host_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(('*******', 80))
        ip = s.getsockname()[0]
    finally:
        s.close()

    return ip

def get_config_param(para_section, param, param_config_file='param_conf.ini'):
    cfg = ConfigParser()
    assert param_config_file == 'param_conf.ini'
    
    path = sys.path[0]
    print(path)
    
    file_path = '/'+ path +'/'+ 'conf/' + param_config_file
    print(file_path)
    cfg.read(file_path)
    result = cfg.get(para_section, param)
    return result
    
def remove_file(filename):
    if os.path.exists(filename):
        os.remove(filename)
        return True
    return False

def rename_file(old_name,new_name):
    if os.path.exists(old_name):
        os.rename(old_name,new_name)
        return True
    else:
        return False

def create_path(path):
    if not os.path.isdir(path):
        os.mkdir(path)

def create_dir(path_dir):
    if not os.path.exists(path_dir):
        os.makedirs(path_dir)

def get_host_name():
    return socket.gethostname()
      
def config_logger(path, file_name, section):
    if section == 'prod':
        logging.basicConfig(filename = path+file_name,
                format   = '[%(asctime)s %(filename)s %(lineno)d] %(message)s',
                datefmt  = '%Y-%m-%d %A %H:%M:%S',
                filemode = 'w',
                level=logging.INFO)
    else:
        logging.basicConfig(filename = path+file_name,
                format   = '[%(asctime)s %(filename)s %(lineno)d] %(message)s',
                datefmt  = '%Y-%m-%d %A %H:%M:%S',
                filemode = 'w',
                level=logging.DEBUG)
                
#文件download 接口                
def url_download_file(url,path):
    try:
        opener=urllib.request.build_opener()
        opener.addheaders=[('User-Agent','Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1941.0 Safari/537.36')]
        urllib.request.install_opener(opener)
        urllib.request.urlretrieve(url, path)
    except (urllib.error.ContentTooShortError,  socket.timeout) as e:
        logging.error(e)
        return False
    except (socket.gaierror, urllib.error.URLError) as e:
        logging.error(e)
        return False
    except urllib.error.HTTPError as e:
        logging.error(e)
        return False
    except Exception as e:
        logging.error(e)
        return False
    except:
        return False
    return True

def s3_cp_to_local(path, file_name, section):
    if 'dev' == section:
        cmd = 'aws s3 cp ' + cg.dev_index_s3 + file_name + " " + path + file_name + "  --acl bucket-owner-full-control"
    else:
        cmd = 'aws s3 cp ' + cg.prod_index_s3 + file_name + " " + path + file_name + "  --acl bucket-owner-full-control"
    ret = subprocess.call(cmd, shell=True)

def isExistFile(file_name, section):
    if 'dev' == section:
        cmd = 'aws s3 ls ' + cg.dev_index_s3 + file_name + " | wc -l"
    elif 'prod' == section:
        cmd = 'aws s3 ls ' + cg.prod_index_s3 + file_name + " | wc -l"
    st = subprocess.getoutput(cmd)
    return st


def isExistImgFile(file_name):
    cmd = 'aws s3 ls ' + cg.img_data_path_s3 + file_name + " | wc -l"
    st = subprocess.getoutput(cmd)
    return st

def s3_cp_img_to_local(path, file_name):
    cmd = 'aws s3 cp ' + cg.img_data_path_s3 + file_name + " " + path + file_name + "  --acl bucket-owner-full-control"
    ret = subprocess.call(cmd, shell=True)

def local_cp_to_s3(file_path):
    cmd = 'aws s3 cp ' +  file_path + " " + cg.dev_index_s3 + "  --acl bucket-owner-full-control"
    ret = subprocess.call(cmd, shell=True)
    cmd = 'aws s3 cp ' +  file_path + " " + cg.prod_index_s3 + "  --acl bucket-owner-full-control"
    ret = subprocess.call(cmd, shell=True)

def local_sync_index_s3(index_path, cur_index_path):
    cmd = 'aws s3 sync ' + index_path + " "+ cg.dev_index_s3 + cur_index_path +  "  --acl bucket-owner-full-control"
    ret = subprocess.call(cmd, shell=True)
    cmd = 'aws s3 sync ' +  index_path + " " + cg.prod_index_s3 +  cur_index_path + "  --acl bucket-owner-full-control"
    ret = subprocess.call(cmd, shell=True)

def s3_sync_index_local(index_path, local_path, section):
    if 'dev' == section:
        cmd = 'aws s3 sync '  + cg.dev_index_s3 + index_path + " " + local_path + index_path + "  --acl bucket-owner-full-control"
    elif 'prod' == section:
        cmd = 'aws s3 sync '  + cg.prod_index_s3 + index_path + " " + local_path +  index_path + "  --acl bucket-owner-full-control"
    ret = subprocess.call(cmd, shell=True)

def s3_cp_model_local():
    command = 'aws s3 cp {} {}'.format(cg.model_path_s3 + cg.clothmodel, cg.model_path + cg.clothmodel)
    ret = os.system(command)
    time.sleep(20)

    command = 'aws s3 cp {} {}'.format(cg.model_path_s3 + cg.clothdettxt, cg.model_path + cg.clothdettxt)
    ret = os.system(command)
    time.sleep(20)

    command = 'aws s3 cp {} {}'.format(cg.model_path_s3 + cg.clothdetpth, cg.model_path + cg.clothdetpth)
    ret = os.system(command)   



def calFileMD5(file_path):
    if os.path.exists(file_path):
        m = md5()
        f = open(file_path, 'rb')
        while True:
            chunk = f.read(8192)
            if not chunk: 
                break
            m.update(chunk)
        f.close()
        return str(m.hexdigest())
    return ''

    
def write_data_2_file(file, data):
    result = True
    try:
        with open(file, "w") as f:
            f.write(data)
    except Exception as e:
        logging.error(e)
        result = False
    return result
    
def write_data_2__binary_file(file, data):
    result = True
    try:
        with open(file, "wb") as f:
            f.write(data)
    except Exception as e:
        logging.error(e)
        result = False
    return result
    
def alarmMessage(labels_dict, section):
    headers = {"Content-Type": "application/json"}
    url = 'http://pm-laserver-master.prod/api/v1.0/kafka/monitor_metrics'

    if section == 'dev':
        url = 'http://pm-laserver-master.prod.abc.sheincorp.cn/api/v1.0/kafka/monitor_metrics'
    t = time.time()

    alarm_data = dict()
    alarm_data['metric_name'] = 'Image_New_Search'
    alarm_data['labels'] = labels_dict
    alarm_data['value'] = 1
    alarm_data['timestamp'] = (int(round(t * 1000)))

    print (json.dumps(alarm_data))
    r = requests.post(url, data=json.dumps(alarm_data), headers=headers)
    logging.info("alarm report " + r.text)