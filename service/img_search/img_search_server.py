#!/usr/bin/env python
# -*- coding:utf-8 -*-

#
# author: leeviv
# date: 2019.04.11
#
from concurrent import futures
import sys
import os
import warnings
import logging
import sqlite3
import json
import datetime
import hashlib
import requests
import time
import shutil
import base64
import schedule
import numpy as np
import tornado.httpserver
import tornado.ioloop
import tornado.options
import tornado.web
import tornado.gen
import asyncio
import threading
import _thread
import re
import codecs
import grpc
sys.path.append('./abc/')
from rec_img_srch.api import ImgSearchRec_pb2
from rec_img_srch.api import ImgSearchRec_pb2_grpc
from google.protobuf.json_format import MessageToJson, MessageToDict
from tornado.concurrent import run_on_executor
from concurrent.futures import ThreadPoolExecutor 
from collections import defaultdict
from apscheduler.schedulers.blocking import BlockingScheduler
import util.util as util
import util.config as cg
from ImageRetrieval import ImageRetrievalObj
import prometheus as prometheus

_ONE_DAY_IN_SECONDS = 60 * 60 * 24

clothmodel =  cg.local_model_path + cg.clothmodel
clothdettxt =  cg.local_model_path + cg.clothdettxt
clothdetpth = cg.local_model_path + cg.clothdetpth
    
processor = ImageRetrievalObj(cloth_model = clothmodel, clothdettxt = clothdettxt, clothdetpth= clothdetpth)


class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.integer):
            return int(obj)
        return json.JSONEncoder.default(self, obj)


class ImgSearchService(ImgSearchRec_pb2_grpc.ImgSearchServiceServicer):
    def __init__(self):
        self.img_path = '/Data/search/'
        self.img_index = '/Data/index/'
        self.section = sys.argv[1]
    

    def download_Image(self, url, url_md5val):
        logging.info("Begin download image url:{0}".format(url))
        suffix = os.path.splitext(url)[-1]
        if suffix == '':
            suffix = '.jpg'

        img_name = self.img_path + url_md5val + suffix
        
        rt = util.url_download_file(url,img_name)
        
        if not rt:
            logging.error("image url:{0} download image failed".format(url))
        else:
            logging.info("image:{0} download success.".format(url))
        return rt, img_name
    
    def base64ConvertImg(self, base64_str):
        base64_md5val = hashlib.md5(str(base64_str).encode('utf-8')).hexdigest()
        imgdata = base64.b64decode(base64_str)
        img_name = self.img_path + base64_md5val + '.jpg'
        fw = open(img_name,'wb')
        fw.write(imgdata)
        fw.close()
        return True, img_name
    
    def getSkcIdBy(self, skc):
        items = (skc.split('^'))
        return items[0], items[1]
    

    def getImgSimInfos(self, SearchRequest, context):
        url = SearchRequest.url
        base64 = SearchRequest.base64
        set_id = SearchRequest.set_id
        site_id = SearchRequest.site_id
        num = SearchRequest.num
        loc = SearchRequest.loc
        bcode = False
        rt = True
        
        prometheus_cli = prometheus.PrometheusClient()
        prometheus_cli.incr("search_qps_total")

        response = ImgSearchRec_pb2.SearchResponse()

        if '' != url:
            url_md5val = hashlib.md5(url.encode('utf-8')).hexdigest()
        elif  '' != base64:
            bcode = True
        else:
            logging.error("Requset image url or base64 is null")
            response.code = ImgSearchRec_pb2.ErrorCode.ERROR_INNER_ERROR
            response.bodyInfo.extend([])
            return response
        
        site_id_set = set()
        items = site_id.strip().split(',')
        for item in items:
            site_id_set.add(item)

        folder = self.img_index

        folder += (set_id + '/')

        if not os.path.exists(folder):
            logging.error("this set_id:{0} not exists.".format(set_id))
            response.code = ImgSearchRec_pb2.ErrorCode.ERROR_INNER_ERROR
            response.bodyInfo.extend([])
            return response
        
        if not bcode:
            rt, img_name = self.download_Image(url, url_md5val)
        else:
            rt, img_name = self.base64ConvertImg(base64)
        
        if not rt:
            response.code = ImgSearchRec_pb2.ErrorCode.ERROR_IMG_DOWNLOAD_ERROR
            response.bodyInfo.extend([])
            prometheus_cli.incr("download_failed_total")
            return response
        
        indexpaths = []
        if '' == site_id:
            for fdir in os.listdir(folder):
                file_path = os.path.join(folder, fdir)
                if os.path.isdir(file_path):
                    indexpaths.append(file_path)
        else:
            for s_id in site_id_set:
                if '' == s_id:
                    continue
                index_path = folder + s_id + '/'
                indexpaths.append(index_path)


        start = time.time()
        results = []
        logging.info("enter search set_id:{0}".format(set_id))
        results = processor.Search(img_name, indexpaths, num)
        end = time.time()
        logging.info("img  search image time is {0}".format(str(end - start)))
        
        if end - start >= 3.0:
            prometheus_cli.incr("search_timeout_total")
        
        if len(results) == 0:
            logging.error("this url: {0} not similar iamge".format(url))
            response.code = ImgSearchRec_pb2.ErrorCode.ERROR_NO_RESULT
            response.bodyInfo.extend([])
            prometheus_cli.incr("return_null_total")
            return response
            
        start = time.time()
        #response.bodyInfo = []        
        for result in results:
            bodyInfos = ImgSearchRec_pb2.BodyInfo()
            for res in result:
                resInfo = ImgSearchRec_pb2.ResInfo()
                goods_id, goods_sn = self.getSkcIdBy(res['imageid'])
                resInfo.url = res['url']
                resInfo.score = res['score']
                resInfo.goods_id = goods_id
                resInfo.skc = goods_sn
                resInfo.searchcls = res['searchcls']
                resInfo.cls = res['cls']
                resInfo.searchbox.extend(res['searchbox'])
                resInfo.bbox.extend(res['bbox'])
                bodyInfos.resInfo.append(resInfo)
            response.bodyInfo.append(bodyInfos)
        response.code = ImgSearchRec_pb2.ErrorCode.ERROR_NONE
        end = time.time()
        logging.info("img fetal result data time is {0}".format(str(end - start)))
        return response

'''
def cpAndLoadSiteIndex(dst_index):
    cur_dt = (datetime.date.today()).strftime('%Y%m%d')
    src_index = '/idx/index_' + cur_dt + '/'
    if not os.path.exists(src_index):
        cur_dt = (datetime.date.today() - datetime.timedelta(days=1)).strftime('%Y%m%d')
        src_index = '/idx/index_' + cur_dt + '/'
    if not os.path.exists(src_index):
        return
   
    for fdir in os.listdir(src_index):
        src_site_path = os.path.join(src_index, fdir)
        faiss_file = os.path.join(src_site_path, 'index.faissindex')
        filelist_file = os.path.join(src_site_path, 'index.filelist')
        if os.path.exists(faiss_file) and os.path.exists(filelist_file):
            index_site_path = os.path.join(dst_index, fdir)
            if os.path.exists(index_site_path):
                shutil.rmtree(index_site_path)
            shutil.copytree(src_site_path, index_site_path)
    processor.LoadSetIndex(dst_index)


def copyAndLoadIndex(dst_index):
    cpAndLoadSiteIndex(dst_index)
    schedule.every().day.at("17:00").do(cpAndLoadSiteIndex, dst_index)
    while True:
        schedule.run_pending()
        time.sleep(1)
'''   

def cpAndLoadSiteIndex(dst_index, new_index_path):
    '''
    cur_dt = (datetime.date.today()).strftime('%Y%m%d')
    src_index = '/idx/index_' + cur_dt + '/'
    if not os.path.exists(src_index):
        cur_dt = (datetime.date.today() - datetime.timedelta(days=1)).strftime('%Y%m%d')
        src_index = '/idx/index_' + cur_dt + '/'
    '''
    src_index = cg.index_path + new_index_path
    if not os.path.exists(src_index):
        return
   
    for fdir in os.listdir(src_index):
        src_site_path = os.path.join(src_index, fdir)
        faiss_file = os.path.join(src_site_path, 'index.faissindex')
        filelist_file = os.path.join(src_site_path, 'index.filelist')
        if os.path.exists(faiss_file) and os.path.exists(filelist_file):
            index_site_path = os.path.join(dst_index, fdir)
            if os.path.exists(index_site_path):
                shutil.rmtree(index_site_path)
            time.sleep(10)
            shutil.copytree(src_site_path, index_site_path)
    processor.LoadSetIndex(dst_index)

def getNewIndexPath(tag_file):
    if os.path.exists(tag_file):
        fr = open(tag_file, 'r+')
        line = fr.readline()
        fr.close()
        return line.strip()
    return ''

def copyAndLoadIndex(dst_index):
    old_index_path = ''
    new_index_path = ''
    tag_file = cg.tag_folder + cg.tag_name
    while True:
        new_index_path = getNewIndexPath(tag_file)
        if old_index_path != new_index_path and '' != new_index_path:
            cpAndLoadSiteIndex(dst_index, new_index_path)
            old_index_path = new_index_path
        time.sleep(60*60)

def server():
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
    ImgSearchRec_pb2_grpc.add_ImgSearchServiceServicer_to_server(ImgSearchService(), server)
    server.add_insecure_port('0.0.0.0:50052')
    server.start()
    try:
        while True:
            time.sleep(_ONE_DAY_IN_SECONDS)
    except KeyboardInterrupt:
        server.stop(0)

def delImg(img_path):
    if os.path.exists(img_path):
        cur_dt = time.strftime("%Y%m%d", time.localtime())
        for fname in os.listdir(img_path):
            img_fname_path = os.path.join(img_path, fname)
            create_time = time.localtime(os.stat(img_fname_path).st_ctime)
            strftime = time.strftime('%Y%m%d', create_time)
            if strftime < cur_dt:
                os.remove(img_fname_path)



def delExpireImg(img_path):
    delImg(img_path)
    schedule.every().day.at("12:00").do(delImg, img_path)
    while True:
        schedule.run_pending()
        time.sleep(1)

def main(section):

    log_path = '/Data/log/' 
    
    util.create_dir(log_path)
    
    util.config_logger(log_path, 'img_search_service.log', section)
    
    logging.info("start img costume search service")
    
    incr_metric_list = ["search_qps_total", "download_failed_total", "return_null_total", "search_timeout_total"]
    prometheus_cli = prometheus.PrometheusClient()
    prometheus_cli.register_incr(incr_metric_list) 

    try:
        dst_index = '/Data/index/10/'
        util.create_dir(dst_index)
        _thread.start_new_thread(copyAndLoadIndex, (dst_index, ))
    except Exception as e:
        logging.error("Error: unable to start copyAndLoadIndex-thread")
        return

    try:
        img_path = '/Data/search/'
        _thread.start_new_thread(delExpireImg, (img_path, ))   #用来删除过期图片
    except Exception as e:
        logging.error("Error: unable to start delExpireImg-thread")
        return
    
    server()
    
    

if __name__ == "__main__":
    if len(sys.argv) < 2:
        warnings.warn("please input local/dev/prod parameter")
        sys.exit()
    
    section = sys.argv[1]
    main(section)
    

