// Copyright (c) 2024 <EMAIL>. All rights reserved.
// Author：<EMAIL>
// Time：2024-03-06

#ifndef STRATEGY_EXECUTOR_STRATEGY_CONTROLLER_H_
#define STRATEGY_EXECUTOR_STRATEGY_CONTROLLER_H_

#include <unordered_set>
#include <atomic>
#include <memory>
#include "service/strategy/common/common_def.h"

namespace abc {
namespace recommend_plt {
namespace strategy {

class Node;
class Functor;
class Accessor;
class StrategyExecutor;

class StrategyController {
  public:
    StrategyController(StrategyExecutor* executor) : executor_(executor) {};
    void Reset();
    bool Bind(Accessor*);
    bool AllowBuild(const Functor*);
    bool AllowRun(const std::shared_ptr<Node> node);
    bool AddRuntimeMsg();
    bool has_degrade() const { return has_degrade_.load(std::memory_order_relaxed); }

  private:
    StrategyExecutor* executor_{nullptr};
    Accessor* accessor_{nullptr};
    bool disable_all_{false};
    std::atomic<bool> has_degrade_{false};
    std::unordered_set<std::string> disable_strategy_ids_;
    std::unordered_set<std::string> disable_strategy_names_;
    std::unordered_set<std::string> disable_strategy_types_;
    std::unordered_set<std::string> disable_strategy_stages_;
    std::unordered_set<std::string> disable_strategy_prefixs_;
    std::array<bool, FunctorStage::MAX_STAGE> must_stages_;
    int64_t pm_type_{0};      // no reset
    int64_t degrade_type_{0}; // no reset
};

}  // namespace strategy
}  // namespace recommend_plt
}  // namespace abc

#endif
