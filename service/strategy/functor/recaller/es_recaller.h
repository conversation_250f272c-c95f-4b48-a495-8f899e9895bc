#ifndef _STRATEGY_FUNCTOR_ES_RECALLER_H_
#define _STRATEGY_FUNCTOR_ES_RECALLER_H_

#include "proto/recommend_strategy_param.pb.h"
#include "proto/recommend_srch_recall.pb.h"
#include "service/feature_generate/core/feature_generater.h"
#include "service/strategy/functor/recaller/recaller.h"
#include "service/strategy/executor/es_batch_helper.h"
#include <map>

namespace abc {
namespace recommend_plt {
namespace strategy {

using BucketResult = abc::recommend_plt::match::BucketResult;

class EsRecaller : public Recaller {
public:
    using ArgList = std::vector<std::string>;
    EsRecaller() {}
    virtual ~EsRecaller() {}

    virtual void Reset() override;
    virtual bool Init() override;
    virtual bool Bind(const std::string& strategy_id, const strategy::StrategyParam& param) override;
    void SetEsBatchHelper(EsBatchHelper* helper) { es_batch_helper_ = helper; }
    virtual bool PrepareRun(Accessor* accessor) override;
    virtual bool Run(Accessor* accessor) override;

    bool support_async() const noexcept override { return true; }
    FunctorType Type() const override { return FunctorType::ES_RECALLER; }

    bool has_degrade_run() const noexcept override { return true; }
    bool DegradeRun(Accessor*) override;

    bool ExplainReport(Accessor* accessor, std::vector<match::RecList> srch_recalls);

private:
    bool PackOutputFeature(const feature::FeatureVector<int64_t>* fdata, Accessor* accessor);
    void ReportFeature(const feature::FeaturePtr select_ptr, Accessor* accessor);
    std::vector<match::SrchRecall> BuildSrchRecall(Accessor* accessor);
    feature::FeaturePtr GetDslStepFeature(const std::string& fea_name);
    std::set<std::string> ParseQueryArgs(const std::string& query);
    std::vector<match::SrchRecall>
    FillQuery(Accessor* accessor, std::string& query,
                    std::map<std::string, ArgList>& arg_map);

    bool need_ctx_fg() const noexcept { return need_ctx_fg_; }
    void set_need_ctx_fg(bool need_ctx_fg) { need_ctx_fg_ = need_ctx_fg; }

    //ha3召回接口
    bool EnableHa3Switch(Accessor *accessor);
    bool RunHa3(Accessor *accessor);
    int32_t GetRecallType(const int32_t type);
    bool BuildHa3Request(Accessor* accessor, std::vector<match::SrchRecall>& reqs, const int64_t timeout);
    bool ProcessHa3Rsp(Accessor* accessor, std::vector<match::RecList>& rec_lists);
    void ResetContainers(int capacity);
    bool BuildParamReq(Accessor* accessor,std::string& query,std::vector<match::SrchRecall>& reqs,int64_t timeout, bool is_dynamic);
    bool AddRequest(Accessor *accessor, std::vector<match::SrchRecall> &requests, const std::string &sql);
    bool GetArgNames(const std::string& query, std::vector<std::string>& arg_names, const std::string& pre_arg);
    bool GenerateValCombinations(const std::unordered_map<std::string, std::vector<std::string>>& arg_map,
                    std::vector<std::string>& keys, std::vector<std::vector<std::string>>& values);
    bool BuildSql(std::string& sql, const std::string& arg_query, const std::vector<std::string>& arg_names,
                    const std::vector<std::string>& m_keys, const std::vector<std::string>& m_vals, const int64_t timeout, bool is_dynamic);
    bool MultiSql(Accessor* accessor, const std::string &query, std::vector<std::string> &multi_query);
    const feature::FeaturePtr GetArgFeaPtr(Accessor* accessor, const std::string& fea_name);
    bool mergeAggResult(std::vector<match::RecList> &srch_recalls, std::vector<match::RecList> &ret);  


private:
    EsBatchHelper* es_batch_helper_;
    std::string es_pool_id_;
    std::vector<std::string> required_fea_names_;
    int32_t recall_id_;
    bool need_ctx_fg_ = false;
    bool need_relation_report_ = false;
    std::vector<std::int64_t> item_ids_;
    std::vector<float> item_scores_;
    std::vector<std::int64_t> item_relation_ids_;
    bool enable_ha3_ = false;
};

}  // namespace strategy
}  // namespace recommend_plt
}  // namespace abc

#endif  // _STRATEGY_FUNCTOR_ES_RECALLER_H_
