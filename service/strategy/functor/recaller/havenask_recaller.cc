#include <regex>
#include <memory>
#include <cctype> 
#include <unordered_set>
#include "service/strategy/functor/recaller/havenask_recaller.h"
#include "service/feature/feature.h"
#include "service/strategy/common/common_def.h"
#include "service/updater/api/data_auto_api.h"
#include "util/printable.h"
#include "util/func.h"
#include "thirdparty/google/protobuf/util/json_util.h"
#include <boost/algorithm/string.hpp>

namespace abc {
namespace recommend_plt {
namespace strategy {

bool HavenaskRecaller::Init() {
    if (!InitDslHelper(ItemsList::ORIGIN_ITEMS, true)) {
        LOG_ERROR << "HavenaskRecaller dsl helper init error.";
        return false;
    }
    LOG_DEBUG << "HavenaskRecaller init success.";
    return true;
}

void HavenaskRecaller::Reset() {
    LOG_DEBUG << "HavenaskRecaller reset()";
    Recaller::Reset();
    required_fea_names_.clear();
    need_relation_report_ = false;
}

bool HavenaskRecaller::Bind(const std::string& strategy_id, const strategy::StrategyParam& param) {
    set_strategy_id(strategy_id);
    set_name(param.functor_name());
    SetStrategyParam(param);
    for (auto& ctx_fea : param.havenask_recaller_param().input_ctx_fea()) {
        SetRequiredCtxFeaName(ctx_fea);
        if (ctx_fea == param.havenask_recaller_param().relation_dim()) {
            need_relation_report_ = true;
        }
    }
    //设置输出特征：用于二跳构图
    SetOutputCtxFeaName(strategy_id);

    // 是否需要执行dsl
    if(param.havenask_recaller_param().dsl().size() <= 0) {
        dsl_helper()->set_need_dsl(false);
    }

    auto strategy_frags = util::Split(strategy_id, ':');
    if (strategy_frags.size() != 2) {
        LOG_ERROR << "strategy_id:" << strategy_id << " format error";
        return false;
    }
    auto& recall_id_str = strategy_frags[1];
    recall_id_ = atoi(strategy_frags[1].c_str());

    return true;
}


bool HavenaskRecaller::Run(Accessor* accessor) {
    auto& param = GetStrategyParam().havenask_recaller_param();

    if (IsExcludeStrategyIds(accessor)) {
        LOG_DEBUG << "strategy_id:" << strategy_id() << " exclude strategy";
        DegradeRun(accessor);
        return false;
    }
    if (!batch_helper_) {
        accessor->AddErrorReport(strategy_id(),"internal error: es_batch_helper not register", "internal error");
        GetCallback()();
        return true;
    }

    const auto timeout_ms = GetServiceTimeout(kServiceHavenask, 200);
    const auto t_ms = accessor->GetCtxFeature<int64_t>("ha3_recall_timeout", timeout_ms);
    const auto rt_ms = accessor->GetCtxFeature<int64_t>("ha3_recall_rt_timeout", timeout_ms);
    const auto timeout = GetControlParam<int64_t>(accessor, kServiceHavenaskTimeout, t_ms);
    const auto rt_timeout = GetControlParam<int64_t>(accessor, kServiceHavenaskRtTimeout, rt_ms);
    batch_helper_->SetTimeout(timeout);
    batch_helper_->SetRtTimeout(rt_timeout);

    std::vector<match::SrchRecall> srch_recalls;
    BuildSrchRecall(accessor, srch_recalls, rt_timeout);
    if(srch_recalls.empty()){
        accessor->AddErrorReport(strategy_id(),"srch_recalls size is empty", "srch_recalls_size_0");
        DegradeRun(accessor);
        return false;
    }
    
    batch_helper_->AsyncEsBatch(accessor->item_type(),
                                    param.enable_rt_search(),
                                    param.srch_recall_type(),
                                    recall_id_,
                                    srch_recalls,
    [this, accessor=accessor](std::vector<match::RecList>&& rec_lists) {
        ProcessRsp(accessor, rec_lists);
        GetCallback()();
    });
    return true;
}

bool HavenaskRecaller::ProcessRsp(Accessor* accessor, std::vector<match::RecList>& rec_lists){
    LOG_DEBUG << "batch_helper_ callback. rec_lists:" << up::ToString(rec_lists);

    auto& param = GetStrategyParam().havenask_recaller_param();
    item_ids_.clear();
    item_scores_.clear();
    item_relation_ids_.clear();
    int cfg_recall_num = recall_num();
    item_ids_.reserve(cfg_recall_num);
    item_scores_.reserve(cfg_recall_num);

    std::vector<int64_t> temp_relation_ids;
    int channel_cnt = rec_lists.size();
    int not_empty_channl_cnt = channel_cnt;
    if (param.balance_type() == BanlanceTypes::FULLAVERAGE) {
        for (auto& sr : rec_lists) {
            if (sr.items_size() == 0) {
                not_empty_channl_cnt -= 1;
            }
        }
    }
    int balance_cnt = cfg_recall_num;
    int fbalance_cnt = cfg_recall_num;
    if (not_empty_channl_cnt > 0) {
        fbalance_cnt = cfg_recall_num / not_empty_channl_cnt;
    }
    if (channel_cnt > 0) {
        balance_cnt = cfg_recall_num / channel_cnt;
    }
    int min_cnt = std::min(fbalance_cnt, balance_cnt);
    min_cnt = min_cnt>0 ? min_cnt:1; 

    std::unordered_set<int64_t> item_id_set;
    if (need_relation_report_ && !param.relation_dim().empty()) {
        auto& relation_dim_feature_ptr = accessor->GetCtxFeaturePtr(param.relation_dim(), strategy_id());
        if (relation_dim_feature_ptr != nullptr && relation_dim_feature_ptr->type() == fmp::FeatureValueType::LIST_INT64) {
            auto fdata = static_cast<const feature::FeatureVector<int64_t>*>(relation_dim_feature_ptr.get());
            for (const auto& data : fdata->data()) {
                temp_relation_ids.emplace_back(data);
            }
        }
    }
    // 输出 item_list 检索结果
    int recall_key_item_num = min_cnt;
    if (param.recall_key_item_num_limit() > 0) {
        recall_key_item_num = std::min(min_cnt, param.recall_key_item_num_limit());
    }

    //校验extra_ctx_feature是否有数据
    bool has_extra_ctx_feature = false;

    for (size_t sr_idx = 0; sr_idx < rec_lists.size(); ++sr_idx) {
        int recall_key_item_cnt = 0;
        auto& sr = rec_lists[sr_idx];
        if(!sr.extra_ctx_feature().empty()){
            has_extra_ctx_feature = true;
        }
        for (size_t i = 0; i < sr.items_size() && i < min_cnt; ++i) {
            const auto& item = sr.items(i);
            if (recall_key_item_cnt >= recall_key_item_num) {
                break;
            }
            if (!disable_deduplicate()) {
                if (item_id_set.find(item.id()) != item_id_set.end()) {
                    continue;
                }
                item_id_set.insert(item.id());
                recall_key_item_cnt++;
            }
            item_ids_.push_back(item.id());
            item_scores_.push_back(item.score());
            if (sr_idx < temp_relation_ids.size()) {
                item_relation_ids_.push_back(temp_relation_ids[sr_idx]);
            }
        }
    }

    int32_t recall_type = param.srch_recall_type();
    //bypass和agg结果report
    if (recall_type==6 || has_extra_ctx_feature) {
        accessor->set_srch_recall_type(recall_type);
        if(recall_type==6 && param.spell_sql_mode()==2){
            std::vector<match::RecList> ret;
            mergeAggResult(rec_lists, ret);
            accessor->set_srch_recalls(std::move(ret)); 
        }else {
            accessor->set_srch_recalls(std::move(rec_lists)); 
        }
    }

    // 可解释上报
    if (accessor->debug_mode()) {
        //accessor->AddCtxExplain(strategy_id(), "rec_lists:", up::ToString(accessor->srch_recalls()));
        ExplainReport(accessor, accessor->srch_recalls());
    }

    LOG_DEBUG << " item_list:" << up::ToString(item_ids_) << " item_score:" << up::ToString(item_scores_);
    accessor->AddRecallItemFeature(strategy_id(), kFeaGHa3DisableFilter, FeatureFactory::CreateVector<int64_t>(item_ids_.size(), 1));
    accessor->AddRecallItem(strategy_id(), item_ids_, item_scores_);
    accessor->AddHitFeature(strategy_id(), item_ids_.size(), 1);
    //accessor->AddRecallItemFeature(strategy_id(), "keys", feature::FeatureFactory::CreateVector<std::string>(item_keys_));
    if (!item_relation_ids_.empty()) {
        auto relation_fea = FeatureFactory::CreateVector(std::move(item_relation_ids_));
        accessor->AddRecallItemFeature(strategy_id(), kFeaGRelationId, relation_fea);
    }
    if (accessor->svr_mark_full_mode()) {
        accessor->AddCtxSvrMarkV2(strategy_id(), "recall_items", feature::FeatureFactory::CreateVector(
            item_ids_
        ));
    }

    return true;
}


bool HavenaskRecaller::BuildSrchRecall(Accessor* accessor, std::vector<match::SrchRecall>& src_recalls, const int64_t timeout) {
    auto& param = GetStrategyParam().havenask_recaller_param();
    LOG_DEBUG << "dsl recaller run. param:" << param.ShortDebugString();

    if (dsl_helper()->need_dsl() && dsl_helper()->dsl_name_size() <= 0) {
        accessor->AddErrorReport(strategy_id(), "dsl_name is empty", "dsl_empty");
        return false;
    }

    std::string query = param.recall_sql();
    if(query.empty()){
        accessor->AddErrorReport(strategy_id(), "recall_sql is empty", "recall_sql_empty");
        return false;
    }
    
    //嵌套特征替换
    int spell_mode = param.spell_sql_mode();
    int nest_num = 5;
    while(nest_num>0 && spell_mode!=2){
        std::vector<std::string> exp_names;
        GetArgNames(query, exp_names, "&{");
        if(exp_names.empty()) break;
        for (auto& exp_name : exp_names) {
            auto exp_val_ptr = GetArgFeaPtr(accessor,exp_name);
            if (exp_val_ptr && exp_val_ptr->type() == fmp::STRING) {
                const std::string& exp_val = CastFeature<fmp::STRING>(exp_val_ptr)->data();
                query = util::Replace(query, "&{" + exp_name + "}", exp_val); 
            }
        }
        nest_num--;
    }

    if(spell_mode ==1){
        //dsl单sql方式
        match::SrchRecall srch_recall;
        FillSrchRecallReq(query, srch_recall);
        src_recalls.emplace_back(std::move(srch_recall));
    }else if(spell_mode==2){
        //dsl 多sql请求方式
        std::vector<std::string> multi_query;
        multi_query.reserve(6);
        MultiSql(accessor, query, multi_query);
        for(const std::string &sql:multi_query){
            match::SrchRecall srch_recall;
            FillSrchRecallReq(sql, srch_recall);
            src_recalls.emplace_back(std::move(srch_recall));
        }
    }else{
        //动态参数 方式;  解析sql中的参数名:有序
        std::vector<std::string> arg_names;
        arg_names.reserve(10);
        GetArgNames(query, arg_names, "#{");

        std::unordered_map<std::string, std::vector<std::string>> arg_map;
        for (auto& arg_name : arg_names) {
            query = util::Replace(query, "#{" + arg_name + "}", "?");   //placeholder
            auto arg_val_ptr = GetArgFeaPtr(accessor, arg_name);
            arg_map[arg_name] = FeatureToStrVec(arg_val_ptr, 1024, false, true);
        }

        if(arg_names.empty()){
            match::SrchRecall srch_recall;
            std::vector<std::string> col;
            std::string sql = BuildSql(query, col, timeout);
            FillSrchRecallReq(sql, srch_recall);
            src_recalls.emplace_back(std::move(srch_recall));
        }else{
            std::vector<std::vector<std::string>> values;
            PlaceholderValue(arg_map, arg_names, values);
            for (auto& col : values) {
                match::SrchRecall srch_recall;
                std::string sql = BuildSql(query, col, timeout);
                FillSrchRecallReq(sql, srch_recall);
                src_recalls.emplace_back(std::move(srch_recall));
            }
        }
        
        accessor->AddCtxExplain(strategy_id(), "args", up::ToString(arg_map) + ", query="+query);   
    }

    if (src_recalls.empty()) {
        accessor->AddErrorReport(strategy_id(), "Construct Failed, query=" + query, "construct_failed");
    }
    
    accessor->AddCtxExplain(strategy_id(), "src_recalls:", up::ToString(src_recalls));

    return true;
}


bool HavenaskRecaller::GetArgNames(const std::string& query, std::vector<std::string>& arg_names, const std::string& pre_arg) {
    size_t start_pos = 0;
    size_t found_pos = query.find(pre_arg, start_pos);
    while (found_pos != std::string::npos) {
        size_t end_pos = query.find("}", found_pos);
        if (end_pos != std::string::npos) {
            // Extract the substring between "#{ and }"
            std::string found_res = query.substr(found_pos + 2, end_pos - found_pos - 2);
            start_pos = end_pos + 1;
            found_pos = query.find(pre_arg, start_pos);

            if (util::isNumericStr(found_res)) {
                pool_id_ = found_res;
            }else{
                arg_names.emplace_back(std::move(found_res));
            }
        } else {
            break;
        }
    }
    return true;
}


bool HavenaskRecaller::FillSrchRecallReq(const std::string& sql, match::SrchRecall &srch_recall){
    auto& param = GetStrategyParam().havenask_recaller_param();

    srch_recall.set_es_sql(sql);

    srch_recall.mutable_context_map()->insert({pool_id_, ""});
    srch_recall.set_srch_recall_id(recall_id_);
    //缓存过期时间
    int64_t expire_time = param.expired_time()>0?param.expired_time():86400;
    srch_recall.set_redis_expire_time(expire_time);
    
    //缓存刷新时间
    uint64_t refresh_time = param.refresh_time()>0?param.refresh_time():86400;
    srch_recall.set_interval_time(refresh_time);

    //缓存是否强制覆盖
    srch_recall.set_write_if_result_empty(param.enable_force_override());

    //归一化方法
    //srch_recall.set_norm_method(param.norm_method());

    //本地缓存
    srch_recall.set_is_enable_cache(param.enable_local_cache());

    return true;
}


//输入参数:需要按keys的顺序
        // keys = ["key1", "key2", "key3", "key4"]
        // arg_map = [
        //    "key1" => ["1"],
        //    "key2" => ["1", "2", "3"], <---- row
        //    "key3" => ["a", "b"],
        //    "key4" => ["iosshus"]
        //            .      ^
        //            .      |
        //            .      col
        // ]
//输出结果
        // values = [
        //     ["1", "1", "a", "iosshus"],
        //     ["1", "2", "b", "iosshus"]
        // ]
bool HavenaskRecaller::PlaceholderValue(std::unordered_map<std::string, std::vector<std::string>>& arg_map,
    const std::vector<std::string>& keys, std::vector<std::vector<std::string>>& values) {
    
    if(keys.empty()) {
        return false;
    }
    std::unordered_map<std::string, size_t> idxs;
    bool has_multi = false;
    while (true) {
        std::vector<std::string> col;
        col.reserve(keys.size());
        for (auto& arg_name : keys) {
            if(arg_map.find(arg_name) == arg_map.end()){
                continue;
            }
            auto& row = arg_map[arg_name];
            auto row_idx = idxs[arg_name];
            idxs[arg_name] += 1;
            if (row.size() == 0) {
                col.emplace_back("");
                continue;
            }
            if (row.size() == 1) {
                row_idx = 0;
            }
            if (row.size() > 1) {
                has_multi = true;
            }
            if (row_idx >= row.size()) {
                return true;
            }
            col.emplace_back(row[row_idx]);
        }
        values.emplace_back(std::move(col));
        if (!has_multi) {
            return true;
        }
    }
    return true;
}

std::string HavenaskRecaller::BuildSql(const std::string& query, const std::vector<std::string>& val_col, const int64_t timeout){
    
    std::string res = query;
    std::string kv_pair = "&&kvpair=databaseName:database;formatType:flatbuffers;iquan.plan.cache.enable:true;lackResultEnable:true";
    kv_pair = kv_pair+ ";timeout:"+std::to_string(timeout);

    if(!val_col.empty()){
        std::string dynamic_params = boost::algorithm::join(val_col, ",");
        kv_pair = kv_pair+ ";dynamic_params:[["+dynamic_params+"]]";
    }
    
    return res+kv_pair;
}

//dsl拼写多sql召回
//[sql1,sql2,sql3]
bool HavenaskRecaller::MultiSql(Accessor* accessor, const std::string &query, std::vector<std::string> &multi_query){
    std::vector<std::string> exp_names;
    GetArgNames(query, exp_names, "&{");
    if(exp_names.size() !=1 ){
        accessor->AddErrorReport(strategy_id(), "exp arg nums " + std::to_string(exp_names.size()) + ", expected 1");
        return false;
    }
    auto exp_val_ptr = GetArgFeaPtr(accessor,exp_names[0]);
    if (exp_val_ptr && exp_val_ptr->type() == fmp::LIST_STRING) {
        auto fea_vals = static_cast<FeatureVector<std::string>*>(exp_val_ptr.get());
        if(!fea_vals || fea_vals->data().size()<1) {
            accessor->AddErrorReport(strategy_id(), "fail to get exp_name:"+exp_names[0]);
            return false;
        }

        for(const auto &val: fea_vals->data()){
            std::string sql = val;
            int nest_num = 5;
            while(nest_num>0){
                std::vector<std::string> sub_exps;
                GetArgNames(sql, sub_exps, "&{");
                if(sub_exps.empty()){
                    multi_query.emplace_back(std::move(sql));
                    break;
                }
                for (auto& sub_exp : sub_exps) {
                    auto exp_ptr = GetArgFeaPtr(accessor, sub_exp);
                    if (exp_ptr && exp_ptr->type() == fmp::STRING) {
                        const std::string& exp_val = CastFeature<fmp::STRING>(exp_ptr)->data();
                        sql = util::Replace(sql, "&{" + sub_exp + "}", exp_val); 
                    }
                }
                nest_num--;
            }
        }
    }
    return true;
}

const feature::FeaturePtr HavenaskRecaller::GetArgFeaPtr(Accessor* accessor, const std::string& fea_name) {
    if (dsl_helper()->need_dsl() && dsl_helper()->dsl_name_size() > 0) {
        const auto& ctx_fea_name = dsl_helper()->dsl_name(0);
        auto fg_fea_name = ctx_fea_name + "-" + fea_name;
        auto fea_ptr = dsl_helper()->GetFeature(fg_fea_name);
        if (fea_ptr) {
            return fea_ptr;
        }
    }
    return accessor->GetCtxFeaturePtr(fea_name, strategy_id());
}

bool HavenaskRecaller::mergeAggResult(std::vector<match::RecList> &srch_recalls, std::vector<match::RecList> &ret){
    auto& param = GetStrategyParam().havenask_recaller_param();
    match::RecList rec_agg;
    size_t num = srch_recalls.size();
    if(num >0){
        rec_agg.set_cache_status(srch_recalls[0].cache_status());
    }
    for (size_t i= 0; i < num; i++) {
        auto& sr = srch_recalls[i];
        for (auto& kv : sr.aggr_map()) {
            (*rec_agg.mutable_aggr_map())[kv.first] = kv.second;
        }
    }
    ret.emplace_back(std::move(rec_agg));

    return true;
}


bool HavenaskRecaller::DegradeRun(Accessor* accessor) {
    auto& param = GetStrategyParam().havenask_recaller_param();
    std::vector<match::SrchRecall> srch_recalls;
    batch_helper_->AsyncEsBatch(accessor->item_type(),
                                   param.enable_rt_search(),
                                   param.srch_recall_type(),
                                   recall_id_, srch_recalls,
                                   [this, accessor](std::vector<match::RecList>&&) {
                                       GetCallback()();
                                   });
    return true;
}


bool HavenaskRecaller::ExplainReport(Accessor* accessor, std::vector<match::RecList> &srch_recalls) {
    std::vector<std::string> aggr_keys;
    std::vector<std::string> aggr_values;
    auto& param = GetStrategyParam().havenask_recaller_param();
    if (param.srch_recall_type() == 6) {
        int aggr_idx = 0;
        for (size_t sr_idx = 0; sr_idx < srch_recalls.size(); ++sr_idx) {
            auto& sr = srch_recalls[sr_idx];
            for (auto& aggr_pair : sr.aggr_map()) {
                const auto& metric_name = aggr_pair.first;
                const auto& buckets = aggr_pair.second.buckets();
                for (const auto& bucket_pair : buckets) {
                    int64_t fake_item_id = aggr_idx + 1;
                    aggr_idx += 1;
                    item_ids_.push_back(fake_item_id);
                    item_scores_.push_back(1);
        
                    aggr_keys.push_back(metric_name);
                    std::string buckets_json_string;
                    abc::recommend_plt::match::BucketResult buckets_message;
                    (*buckets_message.mutable_buckets())[bucket_pair.first] = bucket_pair.second;
                    google::protobuf::util::MessageToJsonString(buckets_message, &buckets_json_string);
                    aggr_values.push_back(buckets_json_string);
                }
            }
        }
        LOG_DEBUG << "aggr_keys:" << up::ToString(aggr_keys)
                << " aggr_values:" << up::ToString(aggr_values);
    }

    if (!aggr_keys.empty()) {
        auto aggr_keys_fea = FeatureFactory::CreateVector<std::string>(std::move(aggr_keys));
        auto aggr_values_fea = FeatureFactory::CreateVector<std::string>(std::move(aggr_values));
        accessor->AddRecallItemFeature(strategy_id(), "aggr_keys", aggr_keys_fea);
        accessor->AddRecallItemFeature(strategy_id(), "aggr_values", aggr_values_fea);
    }
    return true;
}

}  // namespace strategy
}  // namespace recommend_plt
}  // namespace abc
