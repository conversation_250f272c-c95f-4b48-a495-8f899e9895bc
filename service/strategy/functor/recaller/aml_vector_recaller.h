#pragma once

#include "proto/recommend_strategy_param.pb.h"
#include "proto/recommend_mmp.grpc.pb.h"
#include "proto/recommend_aml_vector.grpc.pb.h"
#include "service/feature_generate/core/feature_generater.h"
#include "service/strategy/functor/recaller/recaller.h"

namespace abc {
namespace recommend_plt {
namespace strategy {

namespace aml_recall = abc::aml_vector::recall;
class AmlVectorRecaller : public Recaller {
public:
    AmlVectorRecaller() {}
    virtual ~AmlVectorRecaller() {}

    virtual void Reset() override;
    virtual bool Init() override;
    virtual bool Bind(const std::string& strategy_id, const strategy::StrategyParam& param) override;
    bool support_async() const noexcept override { return true; }

    virtual bool Run(Accessor* accessor) override;

private:
    bool PackRequest(Accessor* accessor);
    bool PackTfParam(Accessor* accessor, const strategy::AmlVectorRecallerParam &param, mmp::TFModelRequest* tf_req);
    bool ProcessRsp(const grpc::Status& status, aml_recall::AMLRecallResponse* rsp, Accessor* accessor);
    bool AsyncAml(Accessor* accessor);
    bool TransferExpression(Accessor* accessor, const std::string& raw_exp, std::string &out_exp);
    bool FeatureToStr(Accessor* accessor, const feature::FeaturePtr &fptr, std::string &out);
    bool DeduplicationProcess(aml_recall::AMLRecallResponse* rsp, std::vector<int64_t> &item_ids, std::vector<float> &item_scores);
    bool DefaultProcess(aml_recall::AMLRecallResponse* rsp, std::vector<int64_t> &item_ids, std::vector<float> &item_scores);


private:
    aml_recall::AMLRecallRequest aml_req_;
    bool enable_dsl_;

};

}  // namespace strategy
}  // namespace recommend_plt
}  // namespace abc

