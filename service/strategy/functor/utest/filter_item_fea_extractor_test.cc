#include <memory>
#define private public
#include "proto/recommend_feature.pb.h"
#include "proto/recommend_fmp.pb.h"
#include "proto/recommend_fmp_param.pb.h"
#include "service/strategy/functor/extractor/filter_item_fea_extractor.h"
#include "service/updater/api/data_auto_api.h"
#include "thirdparty/gflags/gflags.h"
#include "thirdparty/google/protobuf/text_format.h"
#include "thirdparty/gtest/gtest.h"
#include "util/logger.h"

using namespace abc::recommend_plt;
using namespace abc::recommend_plt::strategy;
using namespace abc::recommend_plt::feature;

class FilterItemFeaExtractorTest : public testing::Test {
public:
    virtual void SetUp() override {
        filter_item.reset(new FilterItemFeaExtractor());
        filter_item->Init();
        accessor.reset(new Accessor());
        for (size_t i = 0; i < 5; i++) rec_msg.mutable_item_msg()->mutable_item_list()->add_items()->set_item_id(i);
        rec_msg.mutable_context_msg()->set_scene_id(4);
        rec_msg.mutable_context_msg()->set_site_id("iosshus");
        rec_msg.mutable_context_msg()->set_site_uid("iosshus");
        rec_msg.mutable_context_msg()->set_item_type(1);

        accessor->Bind(&rec_msg, &rec_msg_rsp);
        accessor->AddCtxFeature("trace_id", "abc123");
        accessor->AddCtxFeature("debug_mode", feature::FeatureFactory::CreateSingle<std::string>("1"));
        accessor->AddCtxFeature("cate_id", feature::FeatureFactory::CreateSingle<std::string>("2645,2445,7887,4991"));
        accessor->AddCtxFeature("user_2001", feature::FeatureFactory::CreateVector<int64_t>({13474763, 1702275881, 26868909, 1702275878, 14497500, 1702275851, 26017610, 1702275767, 22449556, 1702275748, 26165815, 1702275719}));
    }

    virtual void TearDown() override { filter_item.reset(); }

    static void SetUpTestCase() {
        // 日志初始化
        singleton<Logger>::get_mutable_instance().InitLogging();
        LOG_DEBUG << "FilterItemFeaExtractorTest SetUpTestCase..";
    }

    static void TearDownTestCase() {
        singleton<Logger>::get_mutable_instance().StopLogging();
        LOG_DEBUG << "FilterItemFeaExtractorTest TearDownTestCase..";
    }

public:
    std::unique_ptr<FilterItemFeaExtractor> filter_item;
    std::unique_ptr<Accessor> accessor;
    abc::recommend_plt::api2::RecMsg rec_msg;
    abc::recommend_plt::api2::RecMsg rec_msg_rsp;
};

TEST_F(FilterItemFeaExtractorTest, Test1) {
    std::string strategy_id = "rec_recall_prepare_filter:001";
    StrategyParam param;
    param.set_functor_name("rec_recall_prepare_filter");
    param.mutable_filter_item_fea_extractor_param()->set_op_type(1);
    param.mutable_filter_item_fea_extractor_param()->set_feature_group("rt_feed_trend_cate");
    param.mutable_filter_item_fea_extractor_param()->add_input_ctx_fea("cate_id");
    param.mutable_filter_item_fea_extractor_param()->add_output_item_fea("output1");
    bool ret = filter_item->Bind(strategy_id, param);
    ASSERT_TRUE(ret);
    ASSERT_TRUE(filter_item->GetStrategyParam().filter_item_fea_extractor_param().op_type() == 1);
    ASSERT_TRUE(filter_item->GetStrategyParam().filter_item_fea_extractor_param().feature_group() == "rt_feed_trend_cate");
}

TEST_F(FilterItemFeaExtractorTest, AndOpTypeTest) {
    std::unordered_map<std::string, std::vector<bool>> fea_map;
    fea_map["cate_2345"] = {1,0,1,1,0};
    fea_map["cate_2343"] = {0,0,1,1,0};
    fea_map["cate_2342"] = {1,0,1,1,1};
    std::vector<int64_t> output_fea;
    output_fea.resize(5, 1);
    filter_item->AndOpType(fea_map, output_fea);
    LOG_ERROR << util::ToString(output_fea);
    ASSERT_TRUE(output_fea.size() == 5);
    ASSERT_TRUE(output_fea[0] == 0);
    ASSERT_TRUE(output_fea[1] == 0);
    ASSERT_TRUE(output_fea[2] == 1);
    ASSERT_TRUE(output_fea[3] == 1);
    ASSERT_TRUE(output_fea[4] == 0);
}

TEST_F(FilterItemFeaExtractorTest, OrOpTypeTest) {
    std::unordered_map<std::string, std::vector<bool>> fea_map;
    fea_map["cate_2345"] = {1,0,1,1,0};
    fea_map["cate_2343"] = {0,0,1,1,0};
    fea_map["cate_2342"] = {1,0,1,1,1};
    std::vector<int64_t> output_fea;
    output_fea.resize(5, 0);
    filter_item->OrOpType(fea_map, output_fea);
    LOG_ERROR << util::ToString(output_fea);
    ASSERT_TRUE(output_fea.size() == 5);
    ASSERT_TRUE(output_fea[0] == 1);
    ASSERT_TRUE(output_fea[1] == 0);
    ASSERT_TRUE(output_fea[2] == 1);
    ASSERT_TRUE(output_fea[3] == 1);
    ASSERT_TRUE(output_fea[4] == 1);
}

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
