// Copyright (c) 2024 <EMAIL>. All rights reserved.
// Author：<EMAIL>
// Time：2024-04-11

#include <memory>

#include "service/strategy/functor/utest/common.h"
#include "service/strategy/functor/extractor/control_param_extractor.h"

class ControlParamExtractorTest : public CommonTest<ControlParamExtractor> {};

TEST_F(ControlParamExtractorTest, Run) {
    bool done{false};
    functor->SetCallback([&]() { done = true; });
    std::string strategy_id = "rec_recall_fmp_fea:17188";
    strategy::StrategyParam param;
    model::DataAutoApi::get_mutable_instance().GetStrategyParam(strategy_id, &param);
    accessor->AddCtxFeature("user_2001", feature::FeatureFactory::CreateVector<int64_t>({1,2,3,4}));
    accessor->AddCtxFeature("user_2002", feature::FeatureFactory::CreateVector<int64_t>({1,2,3,4}));
    accessor->AddCtxFeature("user_2003", feature::FeatureFactory::CreateVector<int64_t>({1,2,3,4}));
    ASSERT_TRUE(functor->Bind(strategy_id, param));
    EXPECT_TRUE(functor->Run(accessor.get()));
    ASSERT_TRUE(done);
}
