#include <memory>

#include "proto/recommend_feature.pb.h"
#include "proto/recommend_fmp.pb.h"
#include "proto/recommend_fmp_param.pb.h"
#include "service/strategy/functor/arranger/first_lte_insert_arranger.h"
#include "service/updater/api/data_auto_api.h"
#include "thirdparty/gflags/gflags.h"
#include "thirdparty/google/protobuf/text_format.h"
#include "thirdparty/gtest/gtest.h"
#include "util/logger.h"

using namespace abc::recommend_plt;
using namespace abc::recommend_plt::strategy;
using namespace abc::recommend_plt::feature;

DEFINE_string(conf, "", "updater config file path");


static const int kItemSize = 20;
class FirstLteArrangerTest : public testing::Test {
public:
    virtual void SetUp() override {
        first_lte_insert_arranger.reset(new FirstLteInsertArranger());
        first_lte_insert_arranger->Init();
        accessor.reset(new Accessor());
        for (size_t i = 1; i <= kItemSize; i++) {
            rec_msg.mutable_item_msg()->mutable_item_list()->add_items()->set_item_id(i);
        }
        rec_msg.mutable_context_msg()->set_scene_id(4);
        rec_msg.mutable_context_msg()->set_site_id("iosshus");
        rec_msg.mutable_context_msg()->set_site_uid("iosshus");
        rec_msg.mutable_context_msg()->set_item_type(1);

        accessor->Bind(&rec_msg, &rec_rsp);
        accessor->AddCtxFeature("trace_id", "abc123");
    }

    virtual void TearDown() override { first_lte_insert_arranger.reset(); }

    static void SetUpTestCase() {
        // 日志初始化
        singleton<Logger>::get_mutable_instance().InitLogging();
        // 初始化配置文件
        LOG_DEBUG << "updater conf file:" << FLAGS_conf;
        abc::recommend_plt::updater::FileHandlerConf updater_conf_file;
        updater_conf_file.set_data_id(0);
        updater_conf_file.set_watch_file(FLAGS_conf);
        if (abc::recommend_plt::model::DataAutoApi::get_mutable_instance().Init(updater_conf_file, true) == false) {
            LOG_ERROR << "init updater config error," << updater_conf_file.watch_file();
            return;
        }
        LOG_DEBUG << "SelectSwapTest SetUpTestCase..";
    }

    static void TearDownTestCase() {
        singleton<Logger>::get_mutable_instance().StopLogging();
        LOG_DEBUG << "SelectSwapTest TearDownTestCase..";
    }

public:
    std::unique_ptr<FirstLteInsertArranger> first_lte_insert_arranger;
    std::unique_ptr<Accessor> accessor;
    abc::recommend_plt::api2::RecMsg rec_msg, rec_rsp;
};

TEST_F(FirstLteArrangerTest, TestFirstLte) {
    // 正常参数
    {
    std::string strategy_id = "rec_rerank_firstlte:0001";
    StrategyParam param;
    if (!model::DataAutoApi::get_mutable_instance().GetStrategyParam(strategy_id, &param)) {
        LOG_ERROR << "not found conf of strategy_id:" << strategy_id;
        return;
    }
    if (!first_lte_insert_arranger->Bind(strategy_id, param)) return;

    std::vector<int64_t> G_location;
    for (int i= 0; i < kItemSize; ++i) G_location.push_back(i);
    accessor->AddItemFeature("G_location", feature::FeatureFactory::CreateVector<int64_t>(std::move(G_location)));
    accessor->AddItemFeature("test_feature", feature::FeatureFactory::CreateVector<int64_t>(
        {1,2,1,0,2,1,1,2,0,2,  1,2,1,0,2,1,1,2,0,2}
    ));
    accessor->AddItemFeature("compare_feature", feature::FeatureFactory::CreateVector<float>(
        {3.0, 7.0, 4.0, 1.0, 6.0, 0.5, 5.0, 3.0, 1.0, 1.0,    3.0, 7.0, 4.0, 1.0, 6.0, 0.5, 5.0, 3.0, 1.0, 1.0}
    ));
    accessor->AddItemFeature("group_feature", feature::FeatureFactory::CreateVector<int64_t>(
        {1, 1, 1, 1, 1, 1, 1, 1, 1, 1,    2, 2, 2, 2, 2, 2, 2, 2, 2, 2}
    ));

    bool ret = first_lte_insert_arranger->Run(accessor.get());
    ASSERT_TRUE(ret);

    auto& after_arranged_item = accessor->ArrangedItems();
    for (size_t i = 0; i < after_arranged_item.size(); i++) {
        LOG_DEBUG << "Arranged item_id=" << after_arranged_item[i]->item_id;
    }
    auto fea_g = accessor->GetOriginItemFeaturePtr("G_location");
    LOG_DEBUG << fea_g->ToString();
    }
}

// cmd: ./build64_release/service/strategy/first_lte_insert_arranger_test --conf=service/strategy/functor/utest/strategy_test.pb.tag
int main(int argc, char** argv) {
    if (argc != 2) {
        std::cerr << "please add flags:--conf=${updater_conf_file}" << std::endl;
        return -1;
    }
    google::ParseCommandLineFlags(&argc, &argv, true);
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
