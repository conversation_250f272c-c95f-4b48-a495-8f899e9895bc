#include <bits/stdc++.h>

#include "proto/recommend_feature.pb.h"
#include "proto/recommend_fmp.pb.h"
#include "proto/recommend_fmp_param.pb.h"
#include "service/strategy/functor/arranger/insert_arranger.h"
#include "service/strategy/functor/arranger/top_arranger.h"
#include "service/strategy/common/common_def.h"
#include "service/updater/api/data_auto_api.h"
#include "service/updater/handler/strategy_param_handler.h"
#include "thirdparty/gflags/gflags.h"
#include "thirdparty/google/protobuf/text_format.h"
#include "thirdparty/gtest/gtest.h"
#include "util/logger.h"

using namespace abc::recommend_plt;
using namespace abc::recommend_plt::strategy;
using namespace abc::recommend_plt::feature;

DEFINE_string(conf, "", "updater config file path");


/*
  test cases:
	通过 1 - 正常 01 数组测试
	通过 2 - 正常 item_id 测试 (带有 begin_pos 和 end_pos 限制)
	通过 3 - 有重复 item_id 的 01 数组测试
	通过 4 - 有重复 item_id 的 item_id 测试
	通过 5 - 置顶测试 (G_top)
	通过 6 - 定坑测试 (fixed_loc)
	通过 7 - 不能插坑测试 (filter, bottom, disable_insert)
	通过 8 - cmp_val_score 测试，坑位顺延
	通过 9 - 拦截器件 开发 && 测试
    通过 10 - 多链路插坑测试——基础
    通过 11 - 多链路插坑测试——进阶
    通过 12 - 插坑前排序
    通过 13 - 插坑后分组排序
    addition - dsl 异常输出测试 (single, float_list, string)
    addition - 可解释 && 埋点 && 监控补充
*/

#define AddItems(rec_msg, n) {for (int i = 1; i <= n; ++i) rec_msg.mutable_item_msg()->mutable_item_list()->add_items()->set_item_id(i);}


class BucketInsertTest : public testing::Test {
public:
    virtual void SetUp() override {
        SetUpArtificial(false);
    }

    void SetUpArtificial(bool repeated_item) {
        rec_msg.Clear();
        random_engine.seed(time(nullptr));
        bucket_insert_arranger.reset(new InsertArranger());
        bucket_insert_arranger->Init();
        top_insert_arranger.reset(new TopArranger());
        top_insert_arranger->Init();
        accessor.reset(new Accessor());
        AddItems(rec_msg, 50);
        if (repeated_item) AddItems(rec_msg, 50);
        rec_msg.mutable_context_msg()->set_scene_id(4);
        rec_msg.mutable_context_msg()->set_site_id("iosshus");
        rec_msg.mutable_context_msg()->set_site_uid("iosshus");
        rec_msg.mutable_context_msg()->set_item_type(1);

        std::vector<std::string> strategy_ids = {};
        accessor->Bind(&rec_msg, &rsp_msg, strategy_ids);
        accessor->set_error_log_enable(true);
        accessor->AddCtxFeature("trace_id", "abc123");
    }
    
    void SetUpArtificial2(bool repeated_item) {
        rec_msg.Clear();
        random_engine.seed(time(nullptr));
        bucket_insert_arranger.reset(new InsertArranger());
        bucket_insert_arranger->Init();
        top_insert_arranger.reset(new TopArranger());
        top_insert_arranger->Init();
        accessor.reset(new Accessor());
        AddItems(rec_msg, 100);
        if (repeated_item) AddItems(rec_msg, 100);
        rec_msg.mutable_context_msg()->set_scene_id(4);
        rec_msg.mutable_context_msg()->set_site_id("iosshus");
        rec_msg.mutable_context_msg()->set_site_uid("iosshus");
        rec_msg.mutable_context_msg()->set_item_type(1);

        std::vector<std::string> strategy_ids = {};
        accessor->Bind(&rec_msg, &rsp_msg, strategy_ids);
        accessor->set_error_log_enable(true);
        accessor->AddCtxFeature("trace_id", "abc123");
    }

    virtual void TearDown() override { bucket_insert_arranger.reset(); }

    static void SetUpTestCase() {
        // 日志初始化
        singleton<Logger>::get_mutable_instance().InitLogging("test", "localhost", "127.0.0.1", "./log/", "./log/", 10, 0);
        // 初始化配置文件
        LOG_DEBUG << "updater conf file:" << FLAGS_conf;
        abc::recommend_plt::updater::FileHandlerConf updater_conf_file;
        updater_conf_file.set_data_id(0);
        updater_conf_file.set_watch_file(FLAGS_conf);
        if (abc::recommend_plt::model::DataAutoApi::get_mutable_instance().Init(updater_conf_file, true) == false) {
            LOG_ERROR << "init updater config error," << updater_conf_file.watch_file();
            return;
        }
        LOG_DEBUG << "BucketInsertArranger SetUpTestCase..";
    }

    static void TearDownTestCase() {
        singleton<Logger>::get_mutable_instance().StopLogging();
        LOG_DEBUG << "BucketInsertArranger TearDownTestCase..";
    }

    int64_t GetFeaValInt(Accessor* accessor, Item* item, const feature::FeaturePtr& fea) {
        auto fea_view = accessor->GetFeatureInt64View(item, fea);
        auto fea_val = fea_view.empty() ? 0 : fea_view[0];
        return fea_val;
    }

public:
    std::unique_ptr<InsertArranger> bucket_insert_arranger;
    std::unique_ptr<TopArranger> top_insert_arranger;
    std::unique_ptr<Accessor> accessor;
    abc::recommend_plt::api2::RecMsg rec_msg;
    abc::recommend_plt::api2::RecMsg rsp_msg;
    
    std::default_random_engine random_engine;
    std::uniform_int_distribution<int> random = std::uniform_int_distribution<int>(0, 1000);
    std::uniform_real_distribution<double> random2 = std::uniform_real_distribution<double>(0.0, 1.0);
};

// 正常 01 数组测试
TEST_F(BucketInsertTest, BucketInsert_TEST1) {
    std::string strategy_id = "rec_rerank_insert:1";
    StrategyParam param;
    if (!model::DataAutoApi::get_mutable_instance().GetStrategyParam(strategy_id, &param)) {
        LOG_ERROR << "not found conf of strategy_id:" << strategy_id;
        return;
    }
    if (!bucket_insert_arranger->Bind(strategy_id, param)) return;

    auto& origin_items = accessor->OriginItems();
    auto items_size = origin_items.size();
    std::vector<int64_t> G_last_cate_id(items_size);
    std::vector<int64_t> G_location(items_size);
    for (int i = 0; i < items_size; ++i) {
        G_last_cate_id[i] = (i < items_size / 2) ? random(random_engine) : 1727;
        G_location[i] = i;
        LOG_DEBUG << "origin item i=" << i << ", item_id=" << origin_items[i]->item_id << ", last_cate_id=" << G_last_cate_id[i];
    }
    accessor->AddItemFeature(kFeaGLocation, feature::FeatureFactory::CreateVector(G_location));
    accessor->AddItemFeature("last_cate_id", feature::FeatureFactory::CreateVector<int64_t>(G_last_cate_id));
    bool ret = bucket_insert_arranger->Run(accessor.get());
    ASSERT_TRUE(ret);
    auto& arranged_items = accessor->ArrangedItems();
    for (int i = 0; i < arranged_items.size(); ++i) {
        LOG_DEBUG << "topn item i=" << i << ", item_id = " << arranged_items[i]->item_id;
    }
}

// 正常 item_id 测试 (带有 begin_pos 和 end_pos 限制)
TEST_F(BucketInsertTest, BucketInsert_TEST2) {
    std::string strategy_id = "rec_rerank_insert:2";
    StrategyParam param;
    if (!model::DataAutoApi::get_mutable_instance().GetStrategyParam(strategy_id, &param)) {
        LOG_ERROR << "not found conf of strategy_id:" << strategy_id;
        return;
    }
    if (!bucket_insert_arranger->Bind(strategy_id, param)) return;

    auto& origin_items = accessor->OriginItems();
    auto items_size = origin_items.size();
    std::vector<int64_t> G_last_cate_id(items_size);
    std::vector<int64_t> G_location(items_size);
    for (int i = 0; i < items_size; ++i) {
        G_last_cate_id[i] = (i < items_size / 2) ? random(random_engine) : 1727;
        G_location[i] = i;
        LOG_DEBUG << "origin item i=" << i << ", item_id=" << origin_items[i]->item_id << ", last_cate_id=" << G_last_cate_id[i];
    }
    accessor->AddItemFeature(kFeaGLocation, feature::FeatureFactory::CreateVector(G_location));
    accessor->AddItemFeature("last_cate_id", feature::FeatureFactory::CreateVector<int64_t>(G_last_cate_id));
    bool ret = bucket_insert_arranger->Run(accessor.get());
    ASSERT_TRUE(ret);
    auto& arranged_items = accessor->ArrangedItems();
    for (int i = 0; i < arranged_items.size(); ++i) {
        LOG_DEBUG << "topn item i=" << i << ", item_id = " << arranged_items[i]->item_id;
    }
}

// 有重复 item_id 的 01 数组测试
TEST_F(BucketInsertTest, BucketInsert_TEST3) {
    SetUpArtificial(true);
    std::string strategy_id = "rec_rerank_insert:3";
    StrategyParam param;
    if (!model::DataAutoApi::get_mutable_instance().GetStrategyParam(strategy_id, &param)) {
        LOG_ERROR << "not found conf of strategy_id:" << strategy_id;
        return;
    }
    if (!bucket_insert_arranger->Bind(strategy_id, param)) return;

    auto& origin_items = accessor->OriginItems();
    auto items_size = origin_items.size();
    std::vector<int64_t> G_last_cate_id(items_size);
    std::vector<int64_t> G_location(items_size);
    std::vector<int64_t> G_repeated(items_size);
    for (int i = 0; i < items_size; ++i) {
        G_last_cate_id[i] = (i < items_size / 2) ? random(random_engine) : 1727;
        G_repeated[i] = (i < items_size / 2) ? 0 : 1;
        G_location[i] = i;
        LOG_DEBUG << "origin item i=" << i << ", item_id=" << origin_items[i]->item_id << ", last_cate_id=" << G_last_cate_id[i];
    }
    accessor->AddItemFeature(kFeaGLocation, feature::FeatureFactory::CreateVector(std::move(G_location)));
    accessor->AddItemFeature("last_cate_id", feature::FeatureFactory::CreateVector(std::move(G_last_cate_id)));
    accessor->AddItemFeature("G_repeated", feature::FeatureFactory::CreateVector(std::move(G_repeated)));
    bool ret = bucket_insert_arranger->Run(accessor.get());
    ASSERT_TRUE(ret);
    auto& arranged_items = accessor->ArrangedItems();
    auto repeated_ = accessor->GetOriginItemFeaturePtr("G_repeated");
    for (int i = 0; i < arranged_items.size(); ++i) {
        LOG_DEBUG << "topn item i=" << i << ", item_id=" << arranged_items[i]->item_id
                  << ", repeated_field=" << GetFeaValInt(accessor.get(), arranged_items[i], repeated_);
    }
}

// 有重复 item_id 的 item_id 测试
TEST_F(BucketInsertTest, BucketInsert_TEST4) {
    SetUpArtificial(true);
    std::string strategy_id = "rec_rerank_insert:4";
    StrategyParam param;
    if (!model::DataAutoApi::get_mutable_instance().GetStrategyParam(strategy_id, &param)) {
        LOG_ERROR << "not found conf of strategy_id:" << strategy_id;
        return;
    }
    if (!bucket_insert_arranger->Bind(strategy_id, param)) return;

    auto& origin_items = accessor->OriginItems();
    auto items_size = origin_items.size();
    std::vector<int64_t> G_last_cate_id(items_size);
    std::vector<int64_t> G_location(items_size);
    std::vector<int64_t> G_repeated(items_size);
    for (int i = 0; i < items_size; ++i) {
        G_last_cate_id[i] = (i < items_size / 2) ? random(random_engine) : 1727;
        G_repeated[i] = (i < items_size / 2) ? 0 : 1;
        G_location[i] = i;
        LOG_DEBUG << "origin item i=" << i << ", item_id=" << origin_items[i]->item_id << ", last_cate_id=" << G_last_cate_id[i];
    }
    accessor->AddItemFeature(kFeaGLocation, feature::FeatureFactory::CreateVector(std::move(G_location)));
    accessor->AddItemFeature("last_cate_id", feature::FeatureFactory::CreateVector(std::move(G_last_cate_id)));
    accessor->AddItemFeature("G_repeated", feature::FeatureFactory::CreateVector(std::move(G_repeated)));
    bool ret = bucket_insert_arranger->Run(accessor.get());
    ASSERT_TRUE(ret);
    auto& arranged_items = accessor->ArrangedItems();
    auto repeated_ = accessor->GetOriginItemFeaturePtr("G_repeated");
    for (int i = 0; i < arranged_items.size(); ++i) {
        LOG_DEBUG << "topn item i=" << i << ", item_id=" << arranged_items[i]->item_id
                  << ", repeated_field=" << GetFeaValInt(accessor.get(), arranged_items[i], repeated_);
    }
}

// 置顶测试 (G_top)
TEST_F(BucketInsertTest, BucketInsert_TEST5) {
    std::string strategy_id = "rec_rerank_top:5";
    StrategyParam param;
    if (!model::DataAutoApi::get_mutable_instance().GetStrategyParam(strategy_id, &param)) {
        LOG_ERROR << "not found conf of strategy_id:" << strategy_id;
        return;
    }
    if (!top_insert_arranger->Bind(strategy_id, param)) return;

    auto& origin_items = accessor->OriginItems();
    auto items_size = origin_items.size();
    std::vector<int64_t> G_last_cate_id(items_size);
    std::vector<int64_t> G_location(items_size);
    for (int i = 0; i < items_size; ++i) {
        G_last_cate_id[i] = (i < items_size / 2) ? random(random_engine) : 1727;
        G_location[i] = i;
        LOG_DEBUG << "origin item i=" << i << ", item_id=" << origin_items[i]->item_id << ", last_cate_id=" << G_last_cate_id[i];
    }
    accessor->AddItemFeature(kFeaGLocation, feature::FeatureFactory::CreateVector(std::move(G_location)));
    accessor->AddItemFeature("last_cate_id", feature::FeatureFactory::CreateVector(std::move(G_last_cate_id)));
    bool ret = top_insert_arranger->Run(accessor.get());
    ASSERT_TRUE(ret);
    auto& arranged_items = accessor->ArrangedItems();
    for (int i = 0; i < arranged_items.size(); ++i) {
        LOG_DEBUG << "topn item i=" << i << ", item_id=" << arranged_items[i]->item_id;
    }

    // 测试：置顶后是否会被插坑挤掉
    strategy_id = "rec_rerank_insert:1";
    if (!model::DataAutoApi::get_mutable_instance().GetStrategyParam(strategy_id, &param)) {
        LOG_ERROR << "not found conf of strategy_id:" << strategy_id;
        return;
    }
    if (!bucket_insert_arranger->Bind(strategy_id, param)) return;
    ret = bucket_insert_arranger->Run(accessor.get());
    ASSERT_TRUE(ret);
    auto& arranged_items2 = accessor->ArrangedItems();
    for (int i = 0; i < arranged_items2.size(); ++i) {
        LOG_DEBUG << "topn item i=" << i << ", item_id=" << arranged_items2[i]->item_id;
    }

}


// 定坑测试（G_fixed_loc）
TEST_F(BucketInsertTest, BucketInsert_TEST6) {
    std::string strategy_id = "rec_rerank_insert:1";
    StrategyParam param;
    if (!model::DataAutoApi::get_mutable_instance().GetStrategyParam(strategy_id, &param)) {
        LOG_ERROR << "not found conf of strategy_id:" << strategy_id;
        return;
    }
    param.mutable_insert_arranger_param()->set_fixed_loc_flag(true); // 定坑
    if (!bucket_insert_arranger->Bind(strategy_id, param)) return;

    auto& origin_items = accessor->OriginItems();
    auto items_size = origin_items.size();
    std::vector<int64_t> G_last_cate_id(items_size);
    std::vector<int64_t> G_location(items_size);
    for (int i = 0; i < items_size; ++i) {
        G_last_cate_id[i] = (i < items_size / 2) ? random(random_engine) : 1727;
        G_location[i] = i;
        LOG_DEBUG << "origin item i=" << i << ", item_id=" << origin_items[i]->item_id << ", last_cate_id=" << G_last_cate_id[i];
    }
    accessor->AddItemFeature(kFeaGLocation, feature::FeatureFactory::CreateVector(G_location));
    accessor->AddItemFeature("last_cate_id", feature::FeatureFactory::CreateVector<int64_t>(G_last_cate_id));
    bool ret = bucket_insert_arranger->Run(accessor.get());
    ASSERT_TRUE(ret);
    auto& arranged_items = accessor->ArrangedItems();
    for (int i = 0; i < arranged_items.size(); ++i) {
        LOG_DEBUG << "topn item i=" << i << ", item_id = " << arranged_items[i]->item_id;
    }

    // 测试：定坑后是否会被其它挤掉
    strategy_id = "rec_rerank_insert:12";
    for (int i = 0; i < items_size; ++i) {
        G_last_cate_id[i] = (i < items_size / 2) ? random(random_engine) : 1727;
    }
    accessor->AddItemFeature("last_cate_id", feature::FeatureFactory::CreateVector<int64_t>(G_last_cate_id), arranged_items);
    bucket_insert_arranger->Reset();
    if (!model::DataAutoApi::get_mutable_instance().GetStrategyParam(strategy_id, &param)) {
        LOG_ERROR << "not found conf of strategy_id:" << strategy_id;
        return;
    }
    if (!bucket_insert_arranger->Bind(strategy_id, param)) return;
    ret = bucket_insert_arranger->Run(accessor.get());
    ASSERT_TRUE(ret);
    auto& arranged_items2 = accessor->ArrangedItems();
    for (int i = 0; i < arranged_items2.size(); ++i) {
        LOG_DEBUG << "topn item i=" << i << ", item_id = " << arranged_items2[i]->item_id;
    }
}

// 不能插坑测试 (filter, bottom, disable_insert)
TEST_F(BucketInsertTest, BucketInsert_TEST7) {
    std::string strategy_id = "rec_rerank_insert:1";
    StrategyParam param;
    if (!model::DataAutoApi::get_mutable_instance().GetStrategyParam(strategy_id, &param)) {
        LOG_ERROR << "not found conf of strategy_id:" << strategy_id;
        return;
    }
    param.mutable_insert_arranger_param()->set_max_insert_num(1000); // 解除最大生效限制
    if (!bucket_insert_arranger->Bind(strategy_id, param)) return;

    auto& origin_items = accessor->OriginItems();
    auto items_size = origin_items.size();
    std::vector<int64_t> G_last_cate_id(items_size);
    std::vector<int64_t> G_location(items_size);
    std::vector<int64_t> G_disable_insert(items_size);
    for (int i = 0; i < items_size; ++i) {
        G_last_cate_id[i] = (i < items_size / 2) ? random(random_engine) : 1727;
        G_location[i] = i;
        G_disable_insert[i] = (i < 3 * items_size / 4) ? 0 : 1;
        LOG_DEBUG << "origin item i=" << i << ", item_id=" << origin_items[i]->item_id << ", last_cate_id=" << G_last_cate_id[i];
    }
    accessor->AddItemFeature(kFeaGLocation, feature::FeatureFactory::CreateVector(G_location));
    accessor->AddItemFeature(kFeaGDisableInsert, feature::FeatureFactory::CreateVector(G_disable_insert));
    accessor->AddItemFeature("last_cate_id", feature::FeatureFactory::CreateVector<int64_t>(G_last_cate_id));
    bool ret = bucket_insert_arranger->Run(accessor.get());
    ASSERT_TRUE(ret);
    auto& arranged_items = accessor->ArrangedItems();
    for (int i = 0; i < arranged_items.size(); ++i) {
        LOG_DEBUG << "topn item i=" << i << ", item_id = " << arranged_items[i]->item_id;
    }
}


// cmp_val_score 测试
TEST_F(BucketInsertTest, BucketInsert_TEST8) {
    std::string strategy_id = "rec_rerank_insert:1";
    StrategyParam param;
    if (!model::DataAutoApi::get_mutable_instance().GetStrategyParam(strategy_id, &param)) {
        LOG_ERROR << "not found conf of strategy_id:" << strategy_id;
        return;
    }
    param.mutable_insert_arranger_param()->set_cmp_score_fea(kFeaGScore);
    if (!bucket_insert_arranger->Bind(strategy_id, param)) return;

    auto& origin_items = accessor->OriginItems();
    auto items_size = origin_items.size();
    std::vector<int64_t> G_last_cate_id(items_size);
    std::vector<int64_t> G_location(items_size);
    std::vector<float> G_score(items_size);
    int mid = items_size / 2;
    for (int i = 0; i < items_size; ++i) {
        G_last_cate_id[i] = (i < mid) ? random(random_engine) : 1727;
        G_score[i] = (i < mid) ? float(items_size - i) : float(items_size - i + mid - 10);
        G_location[i] = i;
        LOG_DEBUG << "origin item i=" << i << ", item_id=" << origin_items[i]->item_id << ", last_cate_id=" << G_last_cate_id[i];
    }
    accessor->AddItemFeature(kFeaGLocation, feature::FeatureFactory::CreateVector(G_location));
    accessor->AddItemFeature("last_cate_id", feature::FeatureFactory::CreateVector(G_last_cate_id));
    accessor->AddItemFeature(kFeaGScore, feature::FeatureFactory::CreateVector(G_score));
    bool ret = bucket_insert_arranger->Run(accessor.get());
    ASSERT_TRUE(ret);
    auto& arranged_items = accessor->ArrangedItems();
    for (int i = 0; i < arranged_items.size(); ++i) {
        LOG_DEBUG << "topn item i=" << i << ", item_id = " << arranged_items[i]->item_id;
    }
}

// 插入特征测试
TEST_F(BucketInsertTest, BucketInsert_TEST9) {
    std::string strategy_id = "rec_rerank_insert:9";
    StrategyParam param;
    if (!model::DataAutoApi::get_mutable_instance().GetStrategyParam(strategy_id, &param)) {
        LOG_ERROR << "not found conf of strategy_id:" << strategy_id;
        return;
    }
    param.mutable_insert_arranger_param()->set_cmp_score_fea(kFeaGScore);
    if (!bucket_insert_arranger->Bind(strategy_id, param)) return;

    auto& origin_items = accessor->OriginItems();
    auto items_size = origin_items.size();
    std::vector<int64_t> G_last_cate_id(items_size);
    std::vector<int64_t> G_location(items_size);
    int mid = items_size / 2;
    for (int i = 0; i < items_size; ++i) {
        G_last_cate_id[i] = (i < mid) ? random(random_engine) : 1727;
        G_location[i] = i;
        LOG_DEBUG << "origin item i=" << i << ", item_id=" << origin_items[i]->item_id << ", last_cate_id=" << G_last_cate_id[i];
    }
    accessor->AddItemFeature(kFeaGLocation, feature::FeatureFactory::CreateVector(G_location));
    accessor->AddItemFeature("last_cate_id", feature::FeatureFactory::CreateVector(G_last_cate_id));
    bool ret = bucket_insert_arranger->Run(accessor.get());
    ASSERT_TRUE(ret);
    auto& arranged_items = accessor->ArrangedItems();
    for (int i = 0; i < arranged_items.size(); ++i) {
        LOG_DEBUG << "topn item i=" << i << ", item_id = " << arranged_items[i]->item_id;
    }
}


// 多链路插坑测试
TEST_F(BucketInsertTest, BucketInsert_TEST10) {
    SetUpArtificial(true);
    std::string strategy_id = "rec_rerank_insert:10";
    StrategyParam param;
    if (!model::DataAutoApi::get_mutable_instance().GetStrategyParam(strategy_id, &param)) {
        LOG_ERROR << "not found conf of strategy_id:" << strategy_id;
        return;
    }
    param.mutable_insert_arranger_param()->set_cmp_score_fea(kFeaGScore);
    if (!bucket_insert_arranger->Bind(strategy_id, param)) return;

    auto& origin_items = accessor->OriginItems();
    auto items_size = origin_items.size();
    std::vector<int64_t> G_last_cate_id(items_size);
    std::vector<int64_t> G_location(items_size);
    std::vector<int64_t> item_source(items_size);
    std::vector<float> G_score(items_size);
    int mid = items_size / 2;
    int count1 = 0;
    int count2 = 0;
    for (int i = 0; i < items_size; ++i) {
        if (i < items_size / 3) {
            item_source[i] = 0;
            G_score[i] = 0.2;
        } else if (i < items_size * 2 / 3) {
            item_source[i] = 1;
            if (count1++ < 5) G_score[i] = 0.5;
            else G_score[i] = 1.0;
        } else {
            item_source[i] = 2;
            if (count2++ < 5) G_score[i] = 1.0;
            else G_score[i] = 0.5;
        }
        G_last_cate_id[i] = (i < mid) ? random(random_engine) : 1727;
        G_location[i] = i;
        LOG_DEBUG << "origin item i=" << i << ", item_id=" << origin_items[i]->item_id << ", last_cate_id=" << G_last_cate_id[i];
    }
    accessor->AddItemFeature(kFeaGLocation, feature::FeatureFactory::CreateVector(G_location));
    accessor->AddItemFeature("last_cate_id", feature::FeatureFactory::CreateVector(G_last_cate_id));
    accessor->AddItemFeature("item_source", feature::FeatureFactory::CreateVector(item_source));
    accessor->AddItemFeature("G_score", feature::FeatureFactory::CreateVector(G_score));
    bool ret = bucket_insert_arranger->Run(accessor.get());
    ASSERT_TRUE(ret);
    auto& arranged_items = accessor->ArrangedItems();
    for (int i = 0; i < arranged_items.size(); ++i) {
        LOG_DEBUG << "topn item i=" << i << ", item_id = " << arranged_items[i]->item_id;
    }
}

// 多链路插坑测试
TEST_F(BucketInsertTest, BucketInsert_TEST11) {
    SetUpArtificial2(false);
    std::string strategy_id = "rec_rerank_insert:11";
    StrategyParam param;
    if (!model::DataAutoApi::get_mutable_instance().GetStrategyParam(strategy_id, &param)) {
        LOG_ERROR << "not found conf of strategy_id:" << strategy_id;
        return;
    }
    param.mutable_insert_arranger_param()->set_cmp_score_fea(kFeaGScore);
    if (!bucket_insert_arranger->Bind(strategy_id, param)) return;

    auto& origin_items = accessor->OriginItems();
    auto items_size = origin_items.size();
    std::vector<int64_t> G_last_cate_id(items_size);
    std::vector<int64_t> G_location(items_size);
    std::vector<int64_t> item_source(items_size);
    std::vector<float> G_score(items_size);
    std::vector<int64_t> G_layer(items_size);
    std::vector<float> location_val_score{0.01, 0.02, 0.03, 0.04, 0.05, 0.06, 0.07, 0.08, 0.1, 0.11, 0.13, 0.15, 0.17, 0.19, 0.33};
    int mid = items_size / 2;
    int count1 = 0;
    int count2 = 0;
    for (int i = 0; i < items_size; ++i) {
        if (i < 40) {
            item_source[i] = 0;
            G_score[i] = 0.2;
            G_layer[i] = i < 20 ? 1 : 2;
        } else if (40 <= i && i < 80) {
            item_source[i] = 1;
            G_layer[i] = i < 46 ? 1 : 2;
            if (count1++ < 5) G_score[i] = 0.5;
            else G_score[i] = 1.0;
        } else {
            item_source[i] = 2;
            G_layer[i] = i < 83 ? 1 : 2;
            if (count2++ < 5) G_score[i] = 1.0;
            else G_score[i] = 0.5;
        }
        G_last_cate_id[i] = (i < mid) ? random(random_engine) : 1727;
        G_location[i] = i;
        LOG_DEBUG << "origin item i=" << i << ", item_id=" << origin_items[i]->item_id << ", last_cate_id=" << G_last_cate_id[i];
    }
    accessor->AddItemFeature(kFeaGLocation, feature::FeatureFactory::CreateVector(G_location));
    accessor->AddItemFeature("last_cate_id", feature::FeatureFactory::CreateVector(G_last_cate_id));
    accessor->AddItemFeature("item_source", feature::FeatureFactory::CreateVector(item_source));
    accessor->AddItemFeature("G_score", feature::FeatureFactory::CreateVector(G_score));
    accessor->AddItemFeature("G_layer", feature::FeatureFactory::CreateVector(G_layer));
    accessor->AddCtxFeature("location_val_score", feature::FeatureFactory::CreateVector(location_val_score));
    bool ret = bucket_insert_arranger->Run(accessor.get());
    ASSERT_TRUE(ret);
    auto& arranged_items = accessor->ArrangedItems();
    auto fea_filter = accessor->GetOriginItemFeaturePtr(kFeaGFilter);
    auto fea_source = accessor->GetOriginItemFeaturePtr("item_source");
    for (int i = 0; i < arranged_items.size(); ++i) {
        auto item = arranged_items[i];
        auto filter_view = accessor->GetFeatureInt64View(item, fea_filter);
        auto source_view = accessor->GetFeatureInt64View(item, fea_source);
        auto filter = filter_view.empty() ? 0 : filter_view[0];
        auto source = source_view.empty() ? 0 : source_view[0];
        LOG_DEBUG << "topn item i=" << i << ", item_id = " << item->item_id << ", filter=" << filter << ", item_source=" << source;
    }
}


// 插坑前排序测试
TEST_F(BucketInsertTest, BucketInsert_TEST12) {
    std::string strategy_id = "rec_rerank_insert:13";
    StrategyParam param;
    if (!model::DataAutoApi::get_mutable_instance().GetStrategyParam(strategy_id, &param)) {
        LOG_ERROR << "not found conf of strategy_id:" << strategy_id;
        return;
    }
    if (!bucket_insert_arranger->Bind(strategy_id, param)) return;

    auto& origin_items = accessor->OriginItems();
    auto items_size = origin_items.size();
    std::vector<int64_t> G_last_cate_id(items_size);
    std::vector<int64_t> G_location(items_size);
    for (int i = 0; i < items_size; ++i) {
        G_last_cate_id[i] = (i < items_size / 2) ? 0 : random(random_engine);
        G_location[i] = i;
        LOG_DEBUG << "origin item i=" << i << ", item_id=" << origin_items[i]->item_id << ", last_cate_id=" << G_last_cate_id[i];
    }
    accessor->AddItemFeature(kFeaGLocation, feature::FeatureFactory::CreateVector(G_location));
    accessor->AddItemFeature("last_cate_id", feature::FeatureFactory::CreateVector<int64_t>(G_last_cate_id));
    bool ret = bucket_insert_arranger->Run(accessor.get());
    ASSERT_TRUE(ret);
    auto& arranged_items = accessor->ArrangedItems();
    for (int i = 0; i < arranged_items.size(); ++i) {
        LOG_DEBUG << "topn item i=" << i << ", item_id = " << arranged_items[i]->item_id;
    }
}

// 插坑后排序测试
TEST_F(BucketInsertTest, BucketInsert_TEST13) {
    std::string strategy_id = "rec_rerank_insert:14";
    StrategyParam param;
    if (!model::DataAutoApi::get_mutable_instance().GetStrategyParam(strategy_id, &param)) {
        LOG_ERROR << "not found conf of strategy_id:" << strategy_id;
        return;
    }
    if (!bucket_insert_arranger->Bind(strategy_id, param)) return;

    auto& origin_items = accessor->OriginItems();
    auto items_size = origin_items.size();
    std::vector<int64_t> G_last_cate_id(items_size);
    std::vector<int64_t> G_location(items_size);
    for (int i = 0; i < items_size; ++i) {
        G_last_cate_id[i] = (i < items_size / 2) ? random(random_engine) : (rand() % 10000 + 10000);
        G_location[i] = i;
        LOG_DEBUG << "origin item i=" << i << ", item_id=" << origin_items[i]->item_id << ", last_cate_id=" << G_last_cate_id[i];
    }
    accessor->AddItemFeature(kFeaGLocation, feature::FeatureFactory::CreateVector(G_location));
    accessor->AddItemFeature("last_cate_id", feature::FeatureFactory::CreateVector<int64_t>(G_last_cate_id));
    bool ret = bucket_insert_arranger->Run(accessor.get());
    ASSERT_TRUE(ret);
    auto& arranged_items = accessor->ArrangedItems();
    for (int i = 0; i < arranged_items.size(); ++i) {
        LOG_DEBUG << "topn item i=" << i << ", item_id = " << arranged_items[i]->item_id;
    }
}


// 优先级插坑 + 比分
TEST_F(BucketInsertTest, BucketInsert_TEST14) {
    SetUpArtificial2(false);
    auto& origin_items = accessor->OriginItems();
    auto items_size = origin_items.size();
    std::vector<int64_t> G_location(items_size);
    std::vector<int64_t> item_source(items_size);
    std::vector<float> G_score(items_size);
    std::vector<float> mix_position_score(items_size, 13.5f);

    int batch_size = 5;
    auto sources_count = items_size / batch_size;

    int idx = 0, k = 0;
    for (; k < batch_size; ++k) {
        for (int i = 0; i < sources_count; ++i) {
            idx = k * sources_count + i;
            G_location[idx] = idx;
            item_source[idx] = k;
            G_score[idx] = k + (sources_count - i);
            LOG_INFO << "origin item i=" << idx << ", item_id=" << origin_items[idx]->item_id;
        }
    }
    while (idx + 1 < items_size) {
        G_location[idx] = idx;
        item_source[idx] = k;
        G_score[idx] = items_size - idx;
        LOG_INFO << "origin item i=" << idx << ", item_id=" << origin_items[idx]->item_id;
        idx++;
    }
    accessor->AddItemFeature(kFeaGLocation, feature::FeatureFactory::CreateVector(G_location));
    accessor->AddItemFeature("G_mix_item_source", feature::FeatureFactory::CreateVector(item_source));
    accessor->AddItemFeature("G_score", feature::FeatureFactory::CreateVector(G_score));
    accessor->AddCtxFeature("mix_position_score", feature::FeatureFactory::CreateVector(mix_position_score));
    
    std::string strategy_id = "rec_rerank_insert:26624";
    StrategyParam param;
    if (!model::DataAutoApi::get_mutable_instance().GetStrategyParam(strategy_id, &param)) {
        LOG_ERROR << "not found conf of strategy_id:" << strategy_id;
        return;
    }
    param.mutable_insert_arranger_param()->set_cmp_score_fea(kFeaGScore);
    if (!bucket_insert_arranger->Bind(strategy_id, param)) return;

    std::unordered_map<int64_t, std::vector<int64_t>> sc_priority_fea;
    std::random_device rd;
    std::mt19937 gen(rd());
    std::vector<int64_t> links{1,3,4};
    for (int i = 0; i < items_size; ++i) {
        std::shuffle(links.begin(), links.end(), gen);
        sc_priority_fea[i] = links;
    }
    feature::FeaturePtr sc_priority_fea_ptr = feature::FeatureFactory::CreateFeatureMap(sc_priority_fea);
    accessor->AddCtxFeature("sc_priority_fea", sc_priority_fea_ptr);
    LOG_INFO << "sc_priority_fea map:" << sc_priority_fea_ptr->DebugString();

    bool ret = bucket_insert_arranger->Run(accessor.get());
    ASSERT_TRUE(ret);
    auto& arranged_items = accessor->ArrangedItems();
    for (int i = 0; i < arranged_items.size(); ++i) {
        LOG_INFO << "topn item i=" << i << ", item_id = " << arranged_items[i]->item_id
                 << ", item_source=" << item_source[arranged_items[i]->idx]
                 << ", G_score=" << G_score[arranged_items[i]->idx];
    }
}


// 二次过滤
TEST_F(BucketInsertTest, BucketInsert_TEST15) {
    SetUpArtificial2(false);
    std::string strategy_id = "rec_rerank_insert:15";
    StrategyParam param;
    if (!model::DataAutoApi::get_mutable_instance().GetStrategyParam(strategy_id, &param)) {
        LOG_ERROR << "not found conf of strategy_id:" << strategy_id;
        return;
    }
    if (!bucket_insert_arranger->Bind(strategy_id, param)) return;

    auto& origin_items = accessor->OriginItems();
    auto items_size = origin_items.size();
    std::vector<int64_t> G_last_cate_id(items_size);
    std::vector<int64_t> G_location(items_size);
    std::vector<int64_t> G_item_source(items_size);
    std::vector<int64_t> G_td_id(items_size);
    for (int i = 0; i < items_size; ++i) {
        G_item_source[i] = rand() % 5;
        G_last_cate_id[i] = (i > 7 * items_size / 10) ? 1727 : 0;
        G_td_id[i] = rand() % 5 + 100;
        G_location[i] = i;
    }
    accessor->AddItemFeature(kFeaGLocation, feature::FeatureFactory::CreateVector(G_location));
    accessor->AddItemFeature("last_cate_id", feature::FeatureFactory::CreateVector<int64_t>(G_last_cate_id));
    accessor->AddItemFeature("G_mix_item_source", feature::FeatureFactory::CreateVector<int64_t>(G_item_source));
    accessor->AddItemFeature("td_id", feature::FeatureFactory::CreateVector<int64_t>(G_td_id));
    accessor->set_debug_mode(true);
    accessor->AddCtxFeature("task_abt_res", "100,0,101,1,102,1,103,0,104,2,105,1");
    accessor->AddCtxFeature("task_abt_color", "100,101:XXXXX|102:YYYYY|103,104:UUUUUU|105:KKKKKKK");
    bool is_effect = bucket_insert_arranger->IsStrategyEffect(accessor.get());
    bool ret = bucket_insert_arranger->Run(accessor.get());
    ASSERT_TRUE(ret);
    auto& arranged_items = accessor->ArrangedItems();
    for (int i = 0; i < arranged_items.size(); ++i) {
        LOG_DEBUG << "topn item i=" << i << ", item_id = " << arranged_items[i]->item_id;
    }
}


// cmd: ./build64_release/service/strategy/functor/utest/insert_arranger_test --conf=service/strategy/functor/utest/strategy_test.pb.tag
int main(int argc, char** argv) {
    if (argc != 2) {
        std::cerr << "please add flags:--conf=${updater_conf_file}" << std::endl;
        return -1;
    }
    google::ParseCommandLineFlags(&argc, &argv, true);
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
