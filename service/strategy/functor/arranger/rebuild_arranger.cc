// Copyright (c) 2023 <EMAIL>. All rights reserved.
// Author：<EMAIL>
// Time：2023-12-04

#include "service/strategy/functor/arranger/rebuild_arranger.h"
#include "util/logger.h"

namespace abc {
namespace recommend_plt {
namespace strategy {

bool RebuildArranger::Bind(const std::string& strategy_id, const StrategyParam& param) {
    return Functor::Bind(strategy_id, param);
}

bool RebuildArranger::Run(Accessor* accessor) {
    LOG_DEBUG << "RebuildArranger in";
    auto truncate_num = accessor->GetCtxFeature<int64_t>(kStrategyRebuildTruncateNum);
    auto reserve_flag = accessor->GetCtxFeature<int64_t>(kStrategyRebuildTruncateReserveFlag);
    truncate_num = GetControlParam<int64_t>(accessor, kOutputNum, truncate_num);
    reserve_flag = GetControlParam<int64_t>(accessor, "reserve_flag", reserve_flag);
    return accessor->Rebuild(truncate_num, reserve_flag, false, next_accessor_);
}

}  // namespace strategy
}  // namespace recommend_plt
}  // namespace abc
