#include "service/strategy/functor/arranger/window_shuffle_arranger.h"

#include "service/strategy/common/common_def.h"
#include "service/feature/feature.h"
#include "service/updater/api/data_auto_api.h"


namespace abc {
namespace recommend_plt {
namespace strategy {

using namespace feature;

const std::pair<std::string, int64_t> kEmptyPairStringInt64 = {"", 0};
const std::pair<std::string, int64_t> kCustomPairStringInt64 = {"fea_name", 1};

bool WindowShuffleArranger::Init() {
    if (!InitDslHelper(ItemsList::ARRANGED_ITEMS)) {
        LOG_ERROR << "WindowShuffleArranger dsl helper init error.";
        return false;
    }
    Reset();
    return true;
}

void WindowShuffleArranger::Reset() {
    Arranger::Reset();
    accessor_ = nullptr;
    is_at_most_mode_ = false;
    debug_mode_ = 0;
    shuffle_mode_ = ShuffleModeDefault;
    window_size_ = 0;
    input_size_ = 0;
    output_size_ = 0;
    begin_pos_ = 0;
    hit_count_ = 0;
    trace_id_.clear();
    item_info_vec_.clear();
    item_info_list_.clear();
    item_info_map_.clear();
    item_fea_vals_map_.clear();
    cur_window_fea_kinds_map_.clear();
    cur_window_fea_count_map_.clear();
    cur_window_item_count_map_.clear();
    fea_val_limit_map_.clear();
    prepared_item_list_map_.clear();
}

bool WindowShuffleArranger::Run(Accessor* accessor) {
    accessor_ = accessor;
    trace_id_ = accessor_->trace_id();
    debug_mode_ = accessor_->debug_mode();

    auto& param = GetStrategyParam().window_shuffle_arranger_param();
    auto& arranged_items = accessor_->ArrangedItems();
    auto item_num = arranged_items.size();
    if (item_num <= 2) {
        return true;
    }
    if (!ParseParam(arranged_items)) {
        return false;
    }

    DoShuffle();

    ArrangeByGLocation(arranged_items);

    ReportFeature();

    return true;
}

void WindowShuffleArranger::ArrangeByGLocation(const std::vector<Item*>& arranged_items) {
    size_t item_num = arranged_items.size();
    std::vector<Item*> result(item_num, nullptr);
    std::vector<int64_t> G_location(item_num, 0);
    auto item_info_list_it = item_info_list_.begin();
    for (size_t i = 0; i < item_num; ++i) {
        G_location[i] = i;
        if (i >= begin_pos_ &&  i < input_size_) {
            result[i] = (*item_info_list_it)->item;
            ++item_info_list_it;
        } else {
            result[i] = arranged_items[i];
        }
    }
    auto fea_G_location = FeatureFactory::CreateVector(std::move(G_location));
    accessor_->AddItemFeature(kFeaGLocation, fea_G_location, result);
    accessor_->ArrangeBy(kFeaGLocation);
}

bool WindowShuffleArranger::ParseParam(const std::vector<Item*>& arranged_items) {
    auto& param = GetStrategyParam().window_shuffle_arranger_param();
    if ((param.fea_args_size() > 0 && param.val_args_size() > 0) ||
        (param.fea_args_size() == 0 && param.val_args_size() == 0)) {
        LOG_ERROR << "window shuffle params error";
        return false;
    }

    int64_t item_num = arranged_items.size();
    if (param.begin_pos() > 0) {
        begin_pos_ = param.begin_pos() - 1; // 取起始坑位的索引
    }
    if (begin_pos_ >= item_num) {
        return false;
    }
    input_size_ = std::min(item_num, param.input_item_num() > 0 ? param.input_item_num() : item_num);
    output_size_ = std::min(item_num, param.output_item_num() > 0 ? param.output_item_num() : item_num);
    window_size_ = param.window_size();
    LOG_DEBUG << "item_num=" << item_num << ", input_size=" << input_size_ << ", output_size=" << output_size_
              << ", window_size=" << window_size_ << ", trace_id=" << trace_id_ << ", debug_mode=" << debug_mode_;

    if (input_size_ - begin_pos_ <= window_size_) {
        return false;
    }
    item_info_vec_.resize(input_size_, nullptr);
    item_info_map_.reserve(input_size_);
    for (size_t i = 0; i < input_size_; ++i) {
        item_info_vec_[i] = std::make_shared<ItemInfo>(i, i, false, false, arranged_items[i]);
        item_info_vec_[i]->item_id = item_info_vec_[i]->item->item_id;
        item_info_map_[item_info_vec_[i]->item_id] = item_info_vec_[i];
    }
    item_info_list_.insert(item_info_list_.begin(), item_info_vec_.begin() + begin_pos_, item_info_vec_.end());

    // 生成打散阶段不能被前置的数组(1表示不能被前置)
    GenerateDisableShuffleVec();

    if (param.fea_args_size() > 0) {
        ParseParamFeaArgs();
    } else {
        ParseParamValArgs();
    }

    // 执行dsl, 生成参与打散0/1数组(1表示不参与打散)
    if (dsl_helper()->need_dsl() && !ExecuteDsl()) {
        return false;
    }

    LOG_DEBUG << "shuffle_mode=" << shuffle_mode_ << ", item_info_list_size=" << item_info_list_.size() << ", output_size=" << output_size_;

    // 至少打散窗口大小需要大于商品数量
    if ((shuffle_mode_ == ShuffleModeAtLeastFeaVals ||
         shuffle_mode_ == ShuffleModeAtLeastFeaKinds) &&
         item_num <= window_size_) {
        return false;
    }
    return true;
}

void WindowShuffleArranger::ParseParamValArgs() {
    auto& param = GetStrategyParam().window_shuffle_arranger_param();

    // 判断是否至多打散模式
    for (const auto& val_args : param.val_args()) {
        if (val_args.each_kind_at_most_num() > 0) {
            is_at_most_mode_ = true;
            break;
        }
    }

    if (is_at_most_mode_) {
        shuffle_mode_ = ShuffleModeAtMostFeaVals;
    } else {
        shuffle_mode_ = ShuffleModeAtLeastFeaVals;
    }

    // 获取待打散的商品特征
    for (const auto& val_args : param.val_args()) {
        QueryItemFeaVals(val_args.item_fea());
        fea_zero_valid_flag_map_[val_args.item_fea()] = val_args.zero_valid_flag();
    }

    // 初始化fea_val_limit_map_
    for (const auto& val_args : param.val_args()) {
        int64_t limit = 0;
        if (is_at_most_mode_) {
            limit = std::max(val_args.each_kind_at_most_num(), 1);
        } else {
            limit = std::max(val_args.each_kind_at_least_num(), 0);
            limit = std::min(limit, window_size_);
        }

        for (int64_t fea_val : val_args.fea_val()) {
            if (fea_val_limit_map_.find({val_args.item_fea(), fea_val}) != fea_val_limit_map_.end()) {
                continue;
            }
            fea_val_limit_map_.insert({{val_args.item_fea(), fea_val}, limit});
        }
        // 通过上下文指定特征值
        if (!val_args.fea_val_from_ctx_fea().empty()) {
            auto ctx_fea = accessor_->GetCtxFeaturePtr(val_args.fea_val_from_ctx_fea());
            if (!ctx_fea) {
                accessor_->AddErrorReport(strategy_id(), "get context feature error, feature_name: " + val_args.fea_val_from_ctx_fea(), "input_not_found");
                continue;
            }
            switch (ctx_fea->type()) {
                case fmp::STRING: {
                    const std::string& str_data = feature::CastDefaultPadding<std::string>(ctx_fea);
                    std::vector<std::string> fields;
                    boost::split(fields, str_data, boost::is_any_of(","));
                    for (std::string& field : fields)  {
                        int64_t fea_val = atol(field.c_str());
                        if (fea_val > 0) { fea_val_limit_map_.insert({{val_args.item_fea(), fea_val}, limit}); }
                    }
                    break;
                }
                case fmp::INT64: {
                    int64_t data = feature::CastDefaultPadding<int64_t>(ctx_fea);
                    if (data > 0) { fea_val_limit_map_.insert({{val_args.item_fea(), data}, limit}); }
                    break;
                }
                case fmp::LIST_INT64: {
                    const std::vector<int64_t>& int_data = feature::CastDefaultPadding<std::vector<int64_t>>(ctx_fea);
                    for (int64_t fea_val : int_data) {
                        if (fea_val > 0) { fea_val_limit_map_.insert({{val_args.item_fea(), fea_val}, limit}); }
                    }
                    break;
                }
                case fmp::LIST_STRING: {
                    const std::vector<std::string>& str_data = feature::CastDefaultPadding<std::vector<std::string>>(ctx_fea);
                    for (const std::string& data : str_data) {
                        int64_t fea_val = atol(data.c_str());
                        if (fea_val > 0) { fea_val_limit_map_.insert({{val_args.item_fea(), fea_val}, limit}); }
                    }
                    break;
                }
                default:
                    LOG_ERROR << "not support feature_type: " << ctx_fea->type();
                    break;
            }
        }
    }
}

void WindowShuffleArranger::ParseParamFeaArgs() {
    auto& param = GetStrategyParam().window_shuffle_arranger_param();

    // 判断是否至多打散模式
    bool item_id_cmp_flag = false;
    for (const auto& fea_args : param.fea_args()) {
        if (fea_args.item_id_cmp_flag()) {
            item_id_cmp_flag = true;
        }
        if (fea_args.each_kind_at_most_num() > 0) {
            is_at_most_mode_ = true;
            break;
        }
    }
    if (is_at_most_mode_ && item_id_cmp_flag) {
        shuffle_mode_ = ShuffleModeAtMostFeaKindsItemId;
    } else if (is_at_most_mode_) {
        shuffle_mode_ = ShuffleModeAtMostFeaKinds;
    } else {
        shuffle_mode_ = ShuffleModeAtLeastFeaKinds;
    }

    // 获取待打散的商品特征
    for (const auto& fea_args : param.fea_args()) {
        QueryItemFeaVals(fea_args.item_fea());
        fea_zero_valid_flag_map_[fea_args.item_fea()] = fea_args.zero_valid_flag();
    }

    // 初始化fea_val_limit_map_
    for (const auto& fea_args : param.fea_args()) {
        int64_t limit = 0;
        if (is_at_most_mode_) {
            limit = std::max(fea_args.each_kind_at_most_num(), 1);
        } else {
            limit = std::max(fea_args.at_least_kind_num(), 0);
        }

        if (fea_val_limit_map_.find({fea_args.item_fea(), 0}) == fea_val_limit_map_.end()) {
            fea_val_limit_map_.insert({{fea_args.item_fea(), 0}, limit});
        }
    }

    // 特殊逻辑
    // 信息流场景的item_id为内容体id, 需要取内容体绑定的商品id作为item_id (为不引入新字段, 此处逻辑暂时先写死)
    std::string item_id_fea_name = "";
    switch (accessor_->scene_id()) {
        case 70:
            item_id_fea_name = "carry_tag_2013";
            break;
        case 205:
        case 206:
        case 207:
            item_id_fea_name = "G_relation_id";
            break;
        default:
            break;
    }
    if (shuffle_mode_ == ShuffleModeAtMostFeaKindsItemId && !item_id_fea_name.empty()) {
        auto goods_id_item_fea = accessor_->GetArrangedItemFeaturePtr(item_id_fea_name);
        if (goods_id_item_fea && goods_id_item_fea->type() == fmp::LIST_INT64) {
            auto fdata = static_cast<const feature::FeatureVector<int64_t>*>(goods_id_item_fea.get());
            if (fdata->data().size() < item_info_vec_.size()) {
                LOG_ERROR << "size not match, goods_id_fea_size: " << fdata->data().size() << ", item_id_size: " << item_info_vec_.size();
                return;
            }
            for (size_t i = 0; i < item_info_vec_.size(); ++i) {
                item_info_vec_[i]->item_id = fdata->data(i);
            }
        }
    }
}

void WindowShuffleArranger::QueryItemFeaVals(const std::string& fea_name) {
    auto item_fea = accessor_->GetArrangedItemFeaturePtr(fea_name);
    if (!item_fea) {
        return;
    }
    switch (item_fea->type()) {
        case fmp::LIST_INT64: {
            auto& data = feature::CastDefaultPadding<fmp::LIST_INT64>(item_fea);
            std::vector<std::vector<int64_t>> data2d(data.size());
            for (size_t i = 0; i < data.size(); ++i) {
                data2d[i] = {data[i]};
            }
            item_fea_vals_map_.insert({fea_name, data2d});
            break;
        }
        case fmp::LLIST_INT64: {
            auto& data2d = feature::CastDefaultPadding<fmp::LLIST_INT64>(item_fea);
            item_fea_vals_map_.insert({fea_name, data2d});
            break;
        }
        default:
            accessor_->AddErrorReport(strategy_id(), "Unsupported fea_type: " + 
                                      std::to_string(item_fea->type()) + ", fea_name: " + fea_name);
            break;
    }
}

void WindowShuffleArranger::MockFirstWindow() {
    // 构造至少打散备选集
    for (size_t i = begin_pos_; i < item_info_vec_.size(); ++i) {
        auto item_info = item_info_vec_[i];
        if (item_info->not_shuffle) {
            continue;
        }

        int64_t ori_loc = item_info->ori_loc;
        for (const auto& kv : item_fea_vals_map_) {
            const std::string& fea_name = kv.first;
            const std::vector<int64_t>& fea_vals = kv.second[ori_loc];
            if (fea_vals.empty()) {
                continue;
            }
            if (IsItemNotShuffleForFeature(item_info, fea_name)) {
                continue;
            }
            std::unordered_set<int64_t> unique_fea_val_set(fea_vals.begin(), fea_vals.end());
            std::pair<std::string, int64_t> key = {fea_name, 0};
            bool fea_zero_valid_flag = fea_zero_valid_flag_map_[fea_name];
            for (int64_t fea_val : fea_vals) {
                if ((!fea_zero_valid_flag && fea_val == 0) ||
                    (shuffle_mode_ == ShuffleModeAtLeastFeaVals &&
                     fea_val_limit_map_.find({fea_name, fea_val}) == fea_val_limit_map_.end())) {
                    continue;
                }
                key.second = fea_val;
                auto prepared_item_list_map_iter = prepared_item_list_map_.find(key);
                if (prepared_item_list_map_iter == prepared_item_list_map_.end()) {
                    prepared_item_list_map_.insert({key, {item_info}});
                } else {
                    prepared_item_list_map_iter->second.emplace_back(item_info);
                }
            }
        }
    }
    // 构造第1个窗口
    std::unordered_map<std::string, std::vector<ItemInfoPtr>> initial_item_info_vec_map;
    for (auto it = prepared_item_list_map_.begin(); it != prepared_item_list_map_.end(); ++it) {
        auto& initial_item_info_vec = initial_item_info_vec_map[it->first.first];
        auto& prepared_item_list = it->second;
        if (shuffle_mode_ == ShuffleModeAtLeastFeaVals) {
            std::copy(prepared_item_list.begin(), prepared_item_list.end(), std::back_inserter(initial_item_info_vec));
        } else {
            initial_item_info_vec.push_back(*(it->second.begin()));
        }
    }
    // 排序 & 去重
    std::map<int64_t, int64_t> item_ori_loc_count_map;
    for (auto it = initial_item_info_vec_map.begin(); it != initial_item_info_vec_map.end(); ++it) {
        auto& initial_item_info_vec = it->second;
        std::sort(initial_item_info_vec.begin(), initial_item_info_vec.end(), SortByItemOriLoc);
        auto unique_it = std::unique(initial_item_info_vec.begin(), initial_item_info_vec.end(), UniqueByItemOriLoc);
        initial_item_info_vec.erase(unique_it, initial_item_info_vec.end());

        for (const auto& item_info : initial_item_info_vec) {
            ++item_ori_loc_count_map[item_info->ori_loc];
        }
    }
    // 取备选集交集
    std::vector<ItemInfoPtr> first_window_item_info_vec;
    first_window_item_info_vec.reserve(window_size_);
    for (auto it = item_ori_loc_count_map.begin(); it != item_ori_loc_count_map.end(); ++it) {
        ItemInfoPtr cur_item_info = item_info_vec_[it->first];
        if (it->second < fea_val_limit_map_.size() ||
            (cur_item_info->disable_front && cur_item_info->ori_loc >= window_size_)) {
            continue;
        }
        LOG_DEBUG << "AddItemToWindow, ori_loc=" << it->first << ", item_id=" << cur_item_info->item_id;
        AddItemToWindow(cur_item_info);
        cur_item_info->is_used = true;
        first_window_item_info_vec.push_back(cur_item_info);
        const auto& res = CheckWindow();
        if (res.first.empty()) {
            break;
        }
    }
    // 找到满足条件的首个窗口数据
    while (true) {
        const auto& res = CheckWindow();
        if (res.first.empty() || first_window_item_info_vec.size() >= window_size_) {
            break;
        }
        bool found = false;
        for (auto item_info : initial_item_info_vec_map[res.first]) {
            if (!item_info->is_used) {
                LOG_DEBUG << "AddItemToWindow, ori_loc=" << item_info->ori_loc << ", item_id=" << item_info->ori_loc;
                AddItemToWindow(item_info);
                item_info->is_used = true;
                first_window_item_info_vec.push_back(item_info);
                found = true;
                break;
            }
        }
        if (!found) {
            break;
        }
    }
    // 补齐窗口
    for (size_t i = begin_pos_; i < begin_pos_ + window_size_ && i < item_info_vec_.size(); ++i) {
        if (first_window_item_info_vec.size() >= window_size_) {
            break;
        }
        if (item_info_vec_[i]->is_used) {
            continue;
        }
        LOG_DEBUG << "padding window, ori_loc=" << item_info_vec_[i]->ori_loc << ", item_id=" << item_info_vec_[i]->item_id;
        AddItemToWindow(item_info_vec_[i]);
        item_info_vec_[i]->is_used = true;
        first_window_item_info_vec.push_back(item_info_vec_[i]);
    }
    // 调整首个窗口商品顺序
    std::sort(first_window_item_info_vec.begin(), first_window_item_info_vec.end(), SortByItemOriLoc);
    for (int i = first_window_item_info_vec.size() - 1; i >= 0; --i) {
        item_info_list_.push_front(first_window_item_info_vec[i]);
        if (first_window_item_info_vec[i]->ori_loc > i + begin_pos_) {
            first_window_item_info_vec[i]->is_hit = true;
            ++hit_count_;
        }
        LOG_DEBUG << "first_window, i=" << i << ", item_id=" << first_window_item_info_vec[i]->item_id << ", ori_loc=" << first_window_item_info_vec[i]->ori_loc;
    }
    size_t del_count = 0;
    auto item_info_list_it = item_info_list_.begin();
    for (std::advance(item_info_list_it, window_size_); item_info_list_it != item_info_list_.end();) {
        if (!(*item_info_list_it)->is_used) {
            ++item_info_list_it;
            continue;
        }
        item_info_list_it = item_info_list_.erase(item_info_list_it);
        if (++del_count >= window_size_) {
            break;
        }
    }
}

void WindowShuffleArranger::DoShuffleAtLeastFeaKinds() {
    MockFirstWindow();
    ShffuleItemInfoList(window_size_);
}

void WindowShuffleArranger::DoShuffleAtMostFeaKinds() {
    ShffuleItemInfoList(0);
}

void WindowShuffleArranger::ShffuleItemInfoList(size_t begin_idx) {
    auto item_info_list_it = item_info_list_.begin();
    std::advance(item_info_list_it, begin_idx);
    auto window_left_it = item_info_list_.begin();
    size_t iter_count = begin_idx;
    for (; item_info_list_it != item_info_list_.end(); ++item_info_list_it) {
        if (iter_count++ >= output_size_) {
            break;
        }
        LOG_DEBUG << "----------------iter_count=" << iter_count << ", item_id=" << (*item_info_list_it)->item_id << ", ori_loc=" << (*item_info_list_it)->ori_loc;

        if (iter_count > window_size_) {
            DelItemToWindow(*window_left_it);
            LOG_DEBUG << "DEL item_id=" << (*window_left_it)->item_id;
            ++window_left_it;
        }

        if ((*item_info_list_it)->not_shuffle && is_at_most_mode_) {
            AddItemToWindow(*item_info_list_it);
            continue;
        }

        bool found = false;
        auto selected_it = item_info_list_it;
        while (selected_it != item_info_list_.end()) {
            ItemInfoPtr cur_item_info = *selected_it;
            if (selected_it != item_info_list_it && cur_item_info->disable_front && cur_item_info->ori_loc >= iter_count) {
                LOG_DEBUG << "disable_front, item_id=" << cur_item_info->item_id << ", ori_loc=" << cur_item_info->ori_loc;
                ++selected_it;
                continue;
            }
            const auto& check_res = CheckWindow(cur_item_info);
            if (!check_res.first.empty()) {
                ++selected_it;
                continue;
            }
            AddItemToWindow(cur_item_info);
            found = true;
            if (item_info_list_it != selected_it) {
                LOG_DEBUG << "cur_item_id=" << (*item_info_list_it)->item_id << ", ori_loc=" << (*item_info_list_it)->ori_loc << "; selected_item_id=" << cur_item_info->item_id << ", ori_loc=" << cur_item_info->ori_loc;
                item_info_list_.erase(selected_it);
                item_info_list_.insert(item_info_list_it, cur_item_info);
                --item_info_list_it;
                LOG_DEBUG << "after insert, cur_item_id=" << (*item_info_list_it)->item_id << ", ori_loc=" << (*item_info_list_it)->ori_loc;
                ++hit_count_;
                cur_item_info->is_hit = true;
            }
            break;
        }

        // 没有满足条件的item, 直接退出
        if (!found) {
            break;
        }
    }
}

void WindowShuffleArranger::DoShuffle() {
    switch (shuffle_mode_) {
        case ShuffleModeAtMostFeaVals:
        case ShuffleModeAtMostFeaKinds:
        case ShuffleModeAtMostFeaKindsItemId:
            DoShuffleAtMostFeaKinds();
            break;
        case ShuffleModeAtLeastFeaVals:
        case ShuffleModeAtLeastFeaKinds:
            DoShuffleAtLeastFeaKinds();
            break;
        default:
            LOG_ERROR << "unsupported shuffle mode: " << shuffle_mode_ << ", trace_id: " << trace_id_;
            break;
    }
}

void WindowShuffleArranger::UpdateWindow(int64_t idx_left, int64_t idx_right) {
    LOG_DEBUG << "UpdateWindow, idx_left: " << idx_left << ", idx_right: " << idx_right;

    if (idx_right < input_size_) {
        AddItemToWindow(item_info_vec_[idx_right]);
    }
    if (idx_left >= 0) {
        DelItemToWindow(item_info_vec_[idx_left]);
    }
}

void WindowShuffleArranger::AddItemToWindow(ItemInfoPtr item_info) {
    if (!item_info.get()) {
        LOG_ERROR << "item_info nullptr, item_num=" << item_info_list_.size() << ", trace_id=" << trace_id_;
        return;
    }
    int64_t ori_loc = item_info->ori_loc;
    if (item_info->not_shuffle) {
        return;
    }
    for (const auto& item_fea_vals_iter : item_fea_vals_map_) {
        const std::string& fea_name = item_fea_vals_iter.first;
        const auto& item_fea = item_fea_vals_iter.second;
        if (ori_loc >= item_fea.size() ||
            IsItemNotShuffleForFeature(item_info, fea_name)) {
            continue;
        }
        std::pair<std::string, int64_t> key = {fea_name, 0};
        bool fea_zero_valid_flag = fea_zero_valid_flag_map_[fea_name];
        for (int64_t fea_val : item_fea[ori_loc]) {
            if (!fea_zero_valid_flag && fea_val == 0) {
                continue;
            }
            key.second = fea_val;
            if ((shuffle_mode_ == ShuffleModeAtMostFeaVals || shuffle_mode_ == ShuffleModeAtLeastFeaVals)
                && fea_val_limit_map_.find(key) == fea_val_limit_map_.end()) {
                continue;
            }
            auto iter = cur_window_fea_count_map_.find(key);
            if (iter == cur_window_fea_count_map_.end()) {
                cur_window_fea_count_map_.insert({key, 1});
                cur_window_fea_kinds_map_[fea_name].insert(fea_val);
            } else {
                iter->second += 1;
            }
        }
    }

    if (shuffle_mode_ == ShuffleModeAtMostFeaKindsItemId) {
        auto iter = cur_window_item_count_map_.find(item_info->item_id);
        if (iter == cur_window_item_count_map_.end()) {
            cur_window_item_count_map_.insert({item_info->item_id, 1});
        } else {
            iter->second += 1;
        }
    }
}

void WindowShuffleArranger::DelItemToWindow(ItemInfoPtr item_info) {
    if (!item_info.get()) {
        LOG_ERROR << "item_info nullptr, item_num=" << item_info_list_.size() << ", trace_id=" << trace_id_;
        return;
    }
    int64_t ori_loc = item_info->ori_loc;
    if (item_info->not_shuffle) {
        return;
    }
    for (const auto& item_fea_vals_iter : item_fea_vals_map_) {
        const std::string& fea_name = item_fea_vals_iter.first;
        const auto& item_fea = item_fea_vals_iter.second;
        if (ori_loc >= item_fea.size() ||
            IsItemNotShuffleForFeature(item_info, fea_name)) {
            continue;
        }
        std::pair<std::string, int64_t> key = {fea_name, 0};
        bool fea_zero_valid_flag = fea_zero_valid_flag_map_[fea_name];
        for (int64_t fea_val : item_fea[ori_loc]) {
            if (!fea_zero_valid_flag && fea_val == 0) {
                continue;
            }
            auto iter = cur_window_fea_count_map_.find({fea_name, fea_val});
            if (iter == cur_window_fea_count_map_.end()) {
                continue;
            }
            iter->second -= 1;
            if (iter->second == 0) {
                cur_window_fea_count_map_.erase(iter);
                cur_window_fea_kinds_map_[fea_name].erase(fea_val);
            }
        }
    }

    if (shuffle_mode_ == ShuffleModeAtMostFeaKindsItemId) {
        auto iter = cur_window_item_count_map_.find(item_info->item_id);
        if (iter != cur_window_item_count_map_.end() && iter->second > 0) {
            iter->second -= 1;
        }
    }
}

const std::pair<std::string, int64_t>& WindowShuffleArranger::CheckWindow(ItemInfoPtr item_info) {
    switch (shuffle_mode_) {
        case ShuffleModeAtLeastFeaVals: {
            for (const auto& kv : fea_val_limit_map_) {
                const std::string& fea_name = kv.first.first;
                size_t limit = kv.second;
                auto iter = cur_window_fea_count_map_.find(kv.first);
                int64_t cur_count = iter == cur_window_fea_count_map_.end() ? 0 : iter->second;
                if (cur_count >= limit) {
                    continue;
                }
                // 不参与打散的无需检查特征
                if (!item_info || item_info->not_shuffle || IsItemNotShuffleForFeature(item_info, fea_name)) {
                    LOG_DEBUG << "fea_name: " << fea_name << ", limit: " << limit << ", cur_count: " << cur_count;
                    return kv.first;
                }
                auto fea_vals_iter = item_fea_vals_map_.find(fea_name);
                if (fea_vals_iter == item_fea_vals_map_.end() || item_info->ori_loc > fea_vals_iter->second.size()) {
                    break;
                }
                const std::vector<int64_t>& fea_vals = (fea_vals_iter->second)[item_info->ori_loc];
                bool fea_zero_valid_flag = fea_zero_valid_flag_map_[fea_name];
                for (int64_t fea_val : fea_vals) {
                    if ((!fea_zero_valid_flag && fea_val == 0) || fea_val != kv.first.second) {
                        continue;
                    }
                    ++cur_count;
                    break;
                }
                if (cur_count < limit) {
                    LOG_DEBUG << "fea_name: " << fea_name << ", limit: " << limit << ", cur_count: " << cur_count;
                    return kv.first;
                }
            }
            break;
        }
        case ShuffleModeAtMostFeaKindsItemId: {
            if (!item_info || cur_window_fea_count_map_.empty()) {
                break;
            }
            const std::string& fea_name = fea_val_limit_map_.begin()->first.first;
            int64_t limit_count = fea_val_limit_map_.begin()->second;

            // step1: 判断当前item_id是否在窗口内特征值里面出现过
            std::pair<std::string, int64_t> key = {fea_name, item_info->item_id};
            auto fea_iter = cur_window_fea_count_map_.find(key);
            if (fea_iter != cur_window_fea_count_map_.end() && fea_iter->second >= limit_count) {
                LOG_DEBUG << "fea_name: " << fea_name << ", fea_val: " << item_info->item_id << ", limit: " << limit_count << ", cur_count: " << fea_iter->second;
                return kCustomPairStringInt64;
            }

            // step2: 判断当前特征值是否在窗口内item_id里面出现过
            auto item_fea_vals_map_iter = item_fea_vals_map_.find(fea_name);
            if (item_fea_vals_map_iter == item_fea_vals_map_.end() ||
                item_info->ori_loc >= item_fea_vals_map_iter->second.size()) {
                break;
            }
            for (int64_t fea_val : item_fea_vals_map_iter->second[item_info->ori_loc]) {
                auto item_iter = cur_window_item_count_map_.find(fea_val);
                if (item_iter != cur_window_item_count_map_.end() && item_iter->second >= limit_count) {
                    LOG_DEBUG << "fea_name: " << fea_name << ", fea_val: " << fea_val << ", limit: " << limit_count << ", cur_count: " << item_iter->second;
                    return kCustomPairStringInt64;
                }
            }
            break;
        }
        case ShuffleModeAtMostFeaVals:
        case ShuffleModeAtMostFeaKinds: {
            if (!item_info || cur_window_fea_count_map_.empty()) {
                break;
            }
            for (const auto& kv : fea_val_limit_map_) {
                const std::string& fea_name = kv.first.first;
                auto fea_vals_iter = item_fea_vals_map_.find(fea_name);
                if (fea_vals_iter == item_fea_vals_map_.end() || item_info->ori_loc > fea_vals_iter->second.size()) {
                    break;
                }
                bool fea_zero_valid_flag = fea_zero_valid_flag_map_[fea_name];
                std::pair<std::string, int64_t> key = {fea_name, 0};
                const std::vector<int64_t>& fea_vals = (fea_vals_iter->second)[item_info->ori_loc];
                for (int64_t fea_val : fea_vals) {
                    if (!fea_zero_valid_flag && fea_val == 0) {
                        continue;
                    }
                    // 指定特征值打散模型需要校验特征值是否需要打散
                    if (shuffle_mode_ == ShuffleModeAtMostFeaVals && fea_val != kv.first.second) {
                        continue;
                    }
                    key.second = fea_val;
                    auto iter = cur_window_fea_count_map_.find(key);
                    int64_t cur_count = iter == cur_window_fea_count_map_.end() ? 0 : iter->second;
                    if (++cur_count > kv.second) {
                        LOG_DEBUG << "fea_name: " << fea_name << ", limit: " << kv.second << ", cur_count: " << cur_count;
                        return kCustomPairStringInt64;
                    }
                }
            }
            break;
        }
        case ShuffleModeAtLeastFeaKinds: {
            for (const auto& kv : fea_val_limit_map_) {
                const std::string& fea_name = kv.first.first;
                size_t limit = kv.second;
                const auto& cur_window_fea_kinds_set = cur_window_fea_kinds_map_[fea_name];
                size_t cur_count = cur_window_fea_kinds_set.size();
                if (cur_count >= limit) {
                    continue;
                }
                // 不参与打散的无需检查特征
                if (!item_info || item_info->not_shuffle || IsItemNotShuffleForFeature(item_info, fea_name)) {
                    LOG_DEBUG << "fea_name: " << fea_name << ", limit: " << limit << ", cur_count: " << cur_count;
                    return kv.first;
                }

                auto fea_vals_iter = item_fea_vals_map_.find(fea_name);
                if (fea_vals_iter == item_fea_vals_map_.end() || item_info->ori_loc > fea_vals_iter->second.size()) {
                    break;
                }
                const std::vector<int64_t>& fea_vals = (fea_vals_iter->second)[item_info->ori_loc];
                bool fea_zero_valid_flag = fea_zero_valid_flag_map_[fea_name];
                for (int64_t fea_val : fea_vals) {
                    if ((!fea_zero_valid_flag && fea_val == 0) ||
                        cur_window_fea_kinds_set.find(fea_val) != cur_window_fea_kinds_set.end()) {
                        continue;
                    }
                    if (++cur_count >= limit) {
                        break;
                    }
                }
                if (cur_count < limit) {
                    LOG_DEBUG << "fea_name: " << fea_name << ", limit: " << limit << ", cur_count: " << cur_count;
                    return kv.first;
                }
            }
            break;
        }
        default:
            LOG_ERROR << "unsupported shuffle mode: " << shuffle_mode_;
            break;
    }

    return kEmptyPairStringInt64;
}

void WindowShuffleArranger::GenerateDisableShuffleVec() {
    auto transform_func = [&](const std::vector<int64_t>& vec1, std::vector<ItemInfoPtr>* vec2) {
        if (vec1.size() < vec2->size()) {
            return;
        }
        for (size_t i = 0; i < vec2->size(); ++i) {
            (*vec2)[i]->disable_front |= bool(vec1[i]);
        }
    };
    auto disable_item_fea = accessor_->GetArrangedItemFeaturePtr(kFeaGDisableShuffleFront);
    if (disable_item_fea && disable_item_fea->type() == fmp::LIST_INT64) {
        auto fdata = static_cast<const feature::FeatureVector<int64_t>*>(disable_item_fea.get());
        transform_func(fdata->data(), &item_info_vec_);
    }

    const auto& param = GetStrategyParam().window_shuffle_arranger_param();
    for (const std::string& fea_name : param.disable_front_item_fea()) {
        disable_item_fea = accessor_->GetArrangedItemFeaturePtr(fea_name);
        if (disable_item_fea && disable_item_fea->type() == fmp::LIST_INT64) {
            auto fdata = static_cast<const feature::FeatureVector<int64_t>*>(disable_item_fea.get());
            transform_func(fdata->data(), &item_info_vec_);
        }
    }
}

bool WindowShuffleArranger::ExecuteDsl() {

    // 商品维度的不参与打散标识
    auto res = dsl_helper()->GetFeature(dsl_helper()->dsl_name(0));
    if (res) {
        switch (res->type()) {
            // dsl执行结果为多值时, 取值为1或true对应的item不参与打散
            case fmp::LIST_INT64: {
                auto fdata = feature::CastDefaultPadding<fmp::LIST_INT64>(res);
                size_t res_size = std::min(fdata.size(), item_info_vec_.size());
                for (size_t i = 0; i < res_size; ++i) { item_info_vec_[i]->not_shuffle = fdata[i]; }
                break;
            }
            case fmp::LIST_BOOL: {
                auto fdata = feature::CastDefaultPadding<fmp::LIST_BOOL>(res);
                size_t res_size = std::min(fdata.size(), item_info_vec_.size());
                for (size_t i = 0; i < res_size; ++i) { item_info_vec_[i]->not_shuffle = fdata[i]; }
                break;
            }
            // dsl执行结果为单值1或true时, 本次请求不打散
            case fmp::INT64: {
                auto fdata = feature::CastDefaultPadding<fmp::INT64>(res);
                if (fdata > 0) { return false; }
                break;
            }
            case fmp::BOOL: {
                auto fdata = feature::CastDefaultPadding<fmp::INT64>(res);
                if (fdata) { return false; }
                break;
            }
            default:
                LOG_ERROR << "not support feature type: " << res->type();
                break;
        }
    }

    // G_top, G_bottom, G_filter不参与打散
    std::vector<std::string> not_shuffle_fea_name_vec = {kFeaGTop, kFeaGBottom, kFeaGFilter};
    for (const auto& not_shuffle_fea_name : not_shuffle_fea_name_vec) {
        auto not_shuffle_item_fea = accessor_->GetArrangedItemFeaturePtr(not_shuffle_fea_name);
        if (not_shuffle_item_fea && not_shuffle_item_fea->type() == fmp::LIST_INT64) {
            auto fdata = static_cast<const feature::FeatureVector<int64_t>*>(not_shuffle_item_fea.get());
            if (fdata->data().size() < item_info_vec_.size()) {
                continue;
            }
            for (size_t i = 0; i < item_info_vec_.size(); ++i) {
                item_info_vec_[i]->not_shuffle |= bool(fdata->data(i));
            }
        }
    }

    // 打散特征维度的不参与打散标识
    for (const auto& kv : fea_val_limit_map_) {
        const std::string& shuffle_fea_name = kv.first.first;
        std::string dsl_fea_name = dsl_helper()->dsl_name(0) + "-NOT_SHUFFLE_" + shuffle_fea_name;
        res = dsl_helper()->GetFeature(dsl_fea_name);
        if (!res) {
            LOG_DEBUG << "feature result is nullptr, fea_name: " << dsl_fea_name;
            continue;
        }
        switch (res->type()) {
            case fmp::LIST_INT64: {
                auto fdata = feature::CastDefaultPadding<fmp::LIST_INT64>(res);
                size_t res_size = std::min(fdata.size(), item_info_vec_.size());
                for (size_t i = 0; i < res_size; ++i) {
                    (item_info_vec_[i]->fea_name_not_shuffle_map)[shuffle_fea_name] = fdata[i];
                }
                break;
            }
            case fmp::LIST_BOOL: {
                auto fdata = feature::CastDefaultPadding<fmp::LIST_BOOL>(res);
                size_t res_size = std::min(fdata.size(), item_info_vec_.size());
                for (size_t i = 0; i < res_size; ++i) {
                    (item_info_vec_[i]->fea_name_not_shuffle_map)[shuffle_fea_name] = fdata[i];
                }
                break;
            }
            default:
                LOG_ERROR << "not support feature type: " << res->type();
                break;
        }
    }

    return true;
}

void WindowShuffleArranger::ReportFeature() {
    const auto& param = GetStrategyParam().window_shuffle_arranger_param();
    // 可解释信息收集
    if (debug_mode_) {
        // DSL结果
        const auto& output_map = dsl_helper()->GetAllFeature();
        std::vector<Item*> before_arranged_items(item_info_vec_.size(), nullptr);
        for (size_t i = 0; i < item_info_vec_.size(); ++i) {
            before_arranged_items[i] = item_info_vec_[i]->item;
        }
        for (auto& it : output_map) {
            auto& fea_name = it.first;
            size_t fea_size = feature::FeatureSize(it.second);
            size_t item_size = accessor_->OriginItems().size();
            fmp::FeatureParamConfig conf;
            if ((fea_size != 0 && item_size != 0 && fea_size == item_size) ||
                (model::DataAutoApi::get_mutable_instance().GetStrategyFmpParamConfig(fea_name, &conf) &&
                 conf.feature_param().feature_type() == fmp::FEA_TYPE_ITEM)) {
                if (accessor_->GetOriginItemFeaturePtr(fea_name) == nullptr) {
                    accessor_->AddItemFeature(fea_name, it.second, before_arranged_items);
                }
                accessor_->AddItemExplain(strategy_id(), fea_name);
            } else {
                if (accessor_->GetCtxFeaturePtr(fea_name) == nullptr) accessor_->AddCtxFeature(fea_name, it.second);
                accessor_->AddCtxExplain(strategy_id(), fea_name);
            }
        }

        // 不参与打散、不能被前置、用于打散的特征
        std::vector<std::string> fea_name_vec = {kFeaGTop, kFeaGBottom, kFeaGFilter, kFeaGDisableShuffleFront};
        for (const auto& kv : fea_val_limit_map_) {
            fea_name_vec.push_back(kv.first.first);
        }
        for (const std::string& fea_name : param.disable_front_item_fea()) {
            fea_name_vec.push_back(fea_name);
        }
        for (const std::string& fea_name : fea_name_vec) {
            accessor_->AddItemExplain(strategy_id(), fea_name);
        }
    }

    // 覆盖度信息收集
    const auto& origin_items = accessor_->OriginItems();
    std::vector<int64_t> hit_item_feature(origin_items.size(), 0);
    for (size_t i = 0; i < origin_items.size(); ++i) {
        auto item_info_map_it = item_info_map_.find(origin_items[i]->item_id);
        if (item_info_map_it == item_info_map_.end()) {
            continue;
        }
        hit_item_feature[i] = item_info_map_it->second->is_hit;
    }
    accessor_->AddHitFeature(strategy_id(), FeatureFactory::CreateVector<int64_t>(std::move(hit_item_feature)));
    accessor_->AddHitFeature(strategy_id(), hit_count_, origin_items.size());
    accessor_->AddItemExplain(strategy_id(), strategy_id() + "_hit");
}

bool WindowShuffleArranger::Bind(const std::string& strategy_id, const strategy::StrategyParam& param) {
    set_strategy_id(strategy_id);
    set_name(param.functor_name());
    SetStrategyParam(param);
    const auto& arranger_param = param.window_shuffle_arranger_param();
    for (const auto& fea_args : arranger_param.fea_args()) {
        SetRequiredItemFeaName(fea_args.item_fea());
    }
    for (const auto& val_args : arranger_param.val_args()) {
        if (val_args.fea_val_from_ctx_fea().empty()) {
            SetRequiredItemFeaName(val_args.item_fea());
        }
    }
    for (const auto& item_fea : arranger_param.disable_front_item_fea()) {
        SetRequiredItemFeaName(item_fea);
    }
    for (const auto& item_fea : arranger_param.input_item_fea()) {
        SetRequiredItemFeaName(item_fea);
    }
    for (const auto& ctx_fea : arranger_param.input_ctx_fea()) {
        SetRequiredCtxFeaName(ctx_fea);
    }
    SetOutputItemFeaName(kFeaGLocation);

    if(arranger_param.dsl_filter().size() == 0) {
        dsl_helper()->set_need_dsl(false);
    }
    return true;
}


}  // namespace strategy
}  // namespace recommend_plt
}  // namespace abc
