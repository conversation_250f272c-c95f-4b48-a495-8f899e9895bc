#include "service/strategy/functor/arranger/dsl_boost_score_arranger.h"

#include "service/strategy/common/common_def.h"
#include "service/updater/api/data_auto_api.h"

namespace abc {
namespace recommend_plt {
namespace strategy {

bool DslBoostScoreArranger::Bind(const std::string& strategy_id, const strategy::StrategyParam& param) {
    set_strategy_id(strategy_id);
    set_name(param.functor_name());
    for (auto& ctx_fea : param.dsl_boost_score_arranger_param().input_ctx_fea()) {
        SetRequiredCtxFeaName(ctx_fea);
    }
    for (auto& item_fea : param.dsl_boost_score_arranger_param().input_item_fea()) {
        SetRequiredItemFeaName(item_fea);
    }

    // for temp compatible
    dsl_helper()->SetFgOptions(param.dsl_boost_score_arranger_param().fg_options());

    strategy::StrategyParam mapping_param(param);
    auto* score_arr_param = mapping_param.mutable_dsl_boost_arranger_param();
    // 映射信息到dsl_boost_arranger_param
    std::string score_item_fea = kFeaGScore;
    if (strategy_id.find(kPrerankStrategyPrefix) != std::string::npos) {
        score_item_fea = kFeaGPrerankScore;
    } else if (strategy_id.find(kRankStrategyPrefix) != std::string::npos) {
        score_item_fea = kFeaGRankScore;
    } else if (strategy_id.find(kRecallStrategyPrefix) != std::string::npos) {
        score_item_fea = kFeaGRecallScore;
    }
    SetOutputItemFeaName(score_item_fea);
    score_arr_param->set_score_item_fea(score_item_fea);
    score_arr_param->set_dsl_boost(param.dsl_boost_score_arranger_param().dsl_boost());
    score_arr_param->set_dsl_select(param.dsl_boost_score_arranger_param().dsl_select());
    score_arr_param->set_select_result(param.dsl_boost_score_arranger_param().select_result());
    score_arr_param->mutable_hit_item_fea()->CopyFrom(param.dsl_boost_score_arranger_param().hit_item_fea());
    score_arr_param->mutable_input_ctx_fea()->CopyFrom(param.dsl_boost_score_arranger_param().input_ctx_fea());
    score_arr_param->mutable_input_item_fea()->CopyFrom(param.dsl_boost_score_arranger_param().input_item_fea());
    score_arr_param->mutable_report_ctx_fea()->CopyFrom(param.dsl_boost_score_arranger_param().report_ctx_fea());
    score_arr_param->mutable_report_item_fea()->CopyFrom(param.dsl_boost_score_arranger_param().report_item_fea());
    score_arr_param->mutable_fg_options()->CopyFrom(param.dsl_boost_score_arranger_param().fg_options());
    SetStrategyParam(mapping_param);
    LOG_DEBUG << "DslBoostScoreArranger bind...";
    return true;
}

}  // namespace strategy
}  // namespace recommend_plt
}  // namespace abc
