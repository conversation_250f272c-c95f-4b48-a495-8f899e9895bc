#include "service/strategy/functor/arranger/dsl_filter_arranger.h"

#include <unordered_set>
#include "service/feature/feature.h"
#include "service/strategy/common/common_def.h"
#include "service/updater/api/data_auto_api.h"

namespace abc {
namespace recommend_plt {
namespace strategy {

bool DslFilterArranger::Init() {
    if(!InitDslHelper()) return false;
    LOG_DEBUG << "DslFilterArranger init success.";
    return true;
}

void DslFilterArranger::Reset() {
    Arranger::Reset();
    hit_count_ = 0;
    hit_fea_ = nullptr;
    hit_items_.clear();
    hit_items_.reserve(1000);
}

bool DslFilterArranger::Bind(const std::string& strategy_id, const strategy::StrategyParam& param) {
    Arranger::Bind(strategy_id, param);
    // for temp compatible
    dsl_helper()->SetFgOptions(param.dsl_filter_arranger_param().fg_options());
    for (auto& ctx_fea : param.dsl_filter_arranger_param().input_ctx_fea()) {
        SetRequiredCtxFeaName(ctx_fea);
    }
    for (auto& item_fea : param.dsl_filter_arranger_param().input_item_fea()) {
        SetRequiredItemFeaName(item_fea);
    }
    can_parallel_ = false;
    if (boost::contains(strategy_id, "_recall_filter:")
        && !GetRequiredItemFeaName().count(kFeaGFilter)
        && !GetRequiredItemFeaName().count(kFeaGBottom)) {
        can_parallel_ = true;
    }
    SetOutputItemFeaName(param.dsl_filter_arranger_param().remain_on_bottom() ? kFeaGBottom : kFeaGFilter);
    for (const auto& fea: param.dsl_filter_arranger_param().output_item_fea()) {
        SetOutputItemFeaName(fea);
    }
    return true;
}

bool DslFilterArranger::Run(Accessor* accessor) {
    auto& dsl_filter_param = GetStrategyParam().dsl_filter_arranger_param();
    auto& intput_items = accessor->OriginItems();
    LOG_DEBUG << "dsl filter arranger run. param:" << dsl_filter_param.ShortDebugString();
    if (dsl_helper()->dsl_name_size() <= 0) {
        LOG_ERROR << "strategy_id:" << strategy_id() << " dsl_name is empty.";
        return false;
    }
    if (intput_items.empty()) {
        LOG_DEBUG << "strategy_id:" << strategy_id() << " meet empty OriginItems.";
        return false;
    }

    // 1. 获取DSL结果&&筛选
    std::string output_name = CanParallel() ? (strategy_id() + "-") : "";
    output_name += dsl_filter_param.remain_on_bottom() ? kFeaGBottom : kFeaGFilter;
    feature::FeaturePtr output_ptr = accessor->GetOriginItemFeaturePtr(output_name);
    if (output_ptr) {
        LOG_DEBUG << "before filter find fea:" << output_ptr->ToString();
    }
    auto select_ptr = dsl_helper()->GetFeature(dsl_helper()->dsl_name(0));
    std::string err_msg;
    if (!OutputCheck(dsl_filter_param.select_result(), intput_items.size(), output_ptr, select_ptr, err_msg)) {
        accessor->AddErrorReport(strategy_id(), err_msg, "fg-output-error");
        return false;
    }
    switch (dsl_filter_param.select_result()) {
        case 1: {
            SelectByFlag(select_ptr, output_name, output_ptr);
            break;
        }
        case 2: {
            SelectByItem(select_ptr, intput_items, output_name, output_ptr, false);
            break;
        }
        case 3: {
            SelectByItem(select_ptr, intput_items, output_name, output_ptr, true);
            break;
        }
        default:
            break;
    }
    LOG_DEBUG << "filter output_ptr:" << output_ptr->ToString();
    // 2.数据回写&&排序
    if (!PackOutputFeature(output_name, output_ptr, accessor)) return false;
    // 3.上报收集
    ReportFeature(output_ptr, accessor);
    return true;
}

bool DslFilterArranger::OutputCheck(int64_t select_result, size_t item_size, const feature::FeaturePtr output_ptr,
                                    feature::FeaturePtr& select_ptr, std::string& err_msg) {
    if (!select_ptr) {
        err_msg = "select dsl output not found.";
        return false;
    }
    if (output_ptr && output_ptr->type() != fmp::LIST_INT64) {
        err_msg = "invalid output type:" + output_ptr->type_name();
        return false;
    }

    if (select_ptr->type() == fmp::INT64) {
        int64_t value = feature::CastDefaultPadding<fmp::INT64>(select_ptr);
        select_ptr = FeatureFactory::CreateVector<int64_t>({value});
    } else if (select_ptr->type() == fmp::BOOL) {
        uint8_t value = feature::CastDefaultPadding<fmp::BOOL>(select_ptr);
        select_ptr = FeatureFactory::CreateVector<uint8_t>({value});
    }

    if ((select_result == 1 && select_ptr->type() != fmp::LIST_INT64 && select_ptr->type() != fmp::LIST_BOOL) ||
        (select_result > 1 && select_ptr->type() != fmp::LIST_INT64)) {
        err_msg = "invalid select type:" + select_ptr->type_name();
        return false;
    }

    return true;
}

void DslFilterArranger::SelectByFlag(const feature::FeaturePtr select_ptr, const std::string& output_name,
                                     feature::FeaturePtr& output_ptr) {
    std::vector<int64_t> select;
    if (select_ptr->type() == fmp::LIST_INT64) {
        select = static_cast<FeatureVector<int64_t>*>(select_ptr.get())->data();
    } else {
        auto& bool_select = static_cast<const FeatureVector<uint8_t>*>(select_ptr.get())->data();
        for (auto v : bool_select) select.emplace_back(v);
    }
    if (output_ptr) {
        auto* output = static_cast<FeatureVector<int64_t>*>(output_ptr.get());
        for (size_t i = 0; i < output->Size() && i < select.size(); i++) {
            (*output)[i] |= select[i];
            hit_count_ += select[i];
        }
    } else {
        hit_count_ = std::accumulate(select.begin(), select.end(), 0);
        output_ptr = FeatureFactory::CreateVector<int64_t>(output_name, select);
    }
    hit_fea_ = FeatureFactory::CreateVector<int64_t>(strategy_id() + "_hit", std::move(select));
    LOG_DEBUG << "SelectByFlag output feature:" << output_ptr->ToString();
}

void DslFilterArranger::SelectByItem(const feature::FeaturePtr select_ptr, const std::vector<Item*>& items,
                                     const std::string& output_name, feature::FeaturePtr& output_ptr, bool reserve) {
    auto& item_id = static_cast<const FeatureVector<int64_t>*>(select_ptr.get())->data();
    std::unordered_set<int64_t> item_set(item_id.begin(), item_id.end());
    std::vector<int64_t> select(items.size(), 0);
    for (size_t i = 0; i < items.size(); i++) {
        size_t exist = item_set.count(items[i]->item_id);
        // 保留-> 不存在item集合的置1   过滤-> 存在item集合的置1
        if ((reserve && exist == 0) || (!reserve && exist == 1)) select[i] = 1;
    }
    if (output_ptr) {
        auto* output = static_cast<FeatureVector<int64_t>*>(output_ptr.get());
        for (size_t i = 0; i < output->Size() && i < select.size(); i++) {
            (*output)[i] |= select[i];
            hit_count_ += select[i];
        }
    } else {
        hit_count_ = std::accumulate(select.begin(), select.end(), 0);
        output_ptr = FeatureFactory::CreateVector<int64_t>(output_name, select);
    }
    hit_fea_ = FeatureFactory::CreateVector<int64_t>(strategy_id() + "_hit", std::move(select));
    LOG_DEBUG << "SelectByItem reserve:" << reserve << " output feature:" << output_ptr->ToString();
}

feature::FeaturePtr DslFilterArranger::GetDslStepFeature(const std::string& fea_name) {
    feature::FeaturePtr fea_ptr = nullptr;
    if ((fea_ptr = dsl_helper()->GetFeature(fea_name)) != nullptr ||
        (fea_ptr = dsl_helper()->GetFeature(dsl_helper()->dsl_name(0) + "-" + fea_name)) != nullptr) {
        return fea_ptr;
    }
    return nullptr;
}

bool DslFilterArranger::PackOutputFeature(const std::string& output_name, const feature::FeaturePtr output_ptr,
                                          Accessor* accessor) {
    accessor->AddItemFeature(output_name, output_ptr);
    auto& dsl_filter_param = GetStrategyParam().dsl_filter_arranger_param();
    // 回写特征, 优先dsl, 后原始特征
    for (const auto& fea_name: dsl_filter_param.output_item_fea()) {
        const auto dsl_fea = dsl_helper()->dsl_name(0) + "-" + fea_name;
        if (auto fea_ptr = dsl_helper()->GetFeature(dsl_fea)) {
            fea_ptr->set_feature_name(fea_name);
            accessor->AddItemFeature(fea_name, fea_ptr);
        } else if (auto fea_ptr = dsl_helper()->GetFeature(fea_name)) {
            fea_ptr->set_feature_name(fea_name);
            accessor->AddItemFeature(fea_name, fea_ptr);
        }
    }
    std::string err_msg;
    //根据filter排序
    auto loc_ptr = accessor->GetOriginItemFeaturePtr(kFeaGLocation);
    if (!loc_ptr) {
        err_msg = "G_location feature not found can't arranger";
        accessor->AddErrorReport(strategy_id(), err_msg, "g_loc-error");
        return false;
    }
    if (arrange_item() && !accessor->ArrangeBy(kFeaGLocation)) {
        err_msg = "dsl filter arranger item error";
        accessor->AddErrorReport(strategy_id(), err_msg, "arrange-error");
        return false;
    }
    return true;
}

// 上报信息记录
void DslFilterArranger::ReportFeature(const feature::FeaturePtr output_ptr, Accessor* accessor) {
    auto& dsl_filter_param = GetStrategyParam().dsl_filter_arranger_param();
    // 1. 可解释信息收集
    if (accessor->debug_mode()) {
        accessor->AddItemExplain(strategy_id(), kFeaGBottom);
        accessor->AddItemExplain(strategy_id(), kFeaGFilter);
        const auto& output_map = dsl_helper()->GetAllFeature();
        for (auto& it : output_map) {
            auto& fea_name = it.first;
            size_t fea_size = feature::FeatureSize(it.second);
            size_t item_size = accessor->OriginItems().size();
            fmp::FeatureParamConfig conf;
            if ((fea_size != 0 && item_size != 0 && fea_size == item_size) ||
                (model::DataAutoApi::get_mutable_instance().GetStrategyFmpParamConfig(fea_name, &conf) &&
                 conf.feature_param().feature_type() == fmp::FEA_TYPE_ITEM)) {
                if (accessor->GetOriginItemFeaturePtr(fea_name) == nullptr) accessor->AddItemFeature(fea_name, it.second);
                accessor->AddItemExplain(strategy_id(), fea_name);
            } else {
                if (accessor->GetCtxFeaturePtr(fea_name) == nullptr) accessor->AddCtxFeature(fea_name, it.second);
                accessor->AddCtxExplain(strategy_id(), fea_name);
            }
        }

        if (hit_fea_) {
            accessor->AddItemFeature(dsl_helper()->dsl_name(0), hit_fea_->Clone());
            accessor->AddItemExplain(strategy_id(), dsl_helper()->dsl_name(0));
        }
    }

    // 2. 埋点、监控信息收集
    for (auto& ctx_fea : dsl_filter_param.report_ctx_fea()) {
        std::string ori_fea_name;
        if (!ParseReportCtx(ctx_fea, ori_fea_name, accessor)) continue;
        feature::FeaturePtr ctx_fea_ptr = GetDslStepFeature(ori_fea_name);
        if (ctx_fea_ptr) {
            accessor->AddCtxFeature(ori_fea_name, ctx_fea_ptr);
        }
    }
    for (auto& item_fea : dsl_filter_param.report_item_fea()) {
        std::string ori_fea_name;
        if (!ParseReportItem(item_fea, ori_fea_name, accessor)) continue;
        feature::FeaturePtr item_fea_ptr = GetDslStepFeature(ori_fea_name);
        if (item_fea_ptr) {
            accessor->AddItemFeature(ori_fea_name, item_fea_ptr);
        }
    }

    // 3. 覆盖度信息收集
    if (output_ptr && hit_fea_) {
        auto& output = static_cast<const FeatureVector<int64_t>*>(output_ptr.get())->data();
        accessor->AddHitFeature(strategy_id(), hit_fea_);
        accessor->AddHitFeature(strategy_id(), hit_count_, output.size());
        accessor->AddItemExplain(strategy_id(), strategy_id() + "_hit");
        if (accessor->svr_mark_full_mode() && Stage() == FunctorStage::RECALL) {
            auto& origin_items = accessor->OriginItems();
            auto& hit_data = hit_fea_->As<fmp::LIST_INT64>()->data();
            for (size_t i = 0; i < hit_data.size() && i < origin_items.size(); ++i) {
                if (hit_data[i]) {
                    hit_items_.emplace_back(origin_items[i]->item_id);
                }
            }
            accessor->AddCtxSvrMarkV2(strategy_id(), "recall_filter", feature::FeatureFactory::CreateVector(
                hit_items_
            ));
        }
    }
}

}  // namespace strategy
}  // namespace recommend_plt
}  // namespace abc
