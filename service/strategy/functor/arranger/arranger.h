#ifndef _STRATEGY_FUNCTOR_ARRANGER_H_
#define _STRATEGY_FUNCTOR_ARRANGER_H_

#include "service/strategy/functor/functor.h"

namespace abc {
namespace recommend_plt {
namespace strategy {

class Arranger : public Functor {
public:
    <PERSON>rranger() {}
    virtual ~Arranger() {}

    virtual FunctorType Type() const override { return FunctorType::ARRANGER; }

    virtual void Reset() override { arrange_item_ = true; before_loc_ = nullptr; Functor::Reset(); }

    virtual bool Run(Accessor* accessor) override { return true; };
    
    void set_arrange_item(bool a) { arrange_item_ = a; }
    bool arrange_item() const noexcept { return arrange_item_; }
    const feature::FeaturePtr& before_loc() noexcept { return before_loc_; }

    virtual bool PrepareRun(Accessor* accessor) override {
        before_loc_ = accessor->GetOriginItemFeaturePtr(kFeaGLocation);
        return true;
    }
    
private:
    bool arrange_item_{true}; // 写完特征后是否调用ArrangeBy
    feature::FeaturePtr before_loc_; // 执行策略前的坑位信息
};
}  // namespace strategy
}  // namespace recommend_plt
}  // namespace abc

#endif  // _STRATEGY_FUNCTOR_ARRANGER_H_
