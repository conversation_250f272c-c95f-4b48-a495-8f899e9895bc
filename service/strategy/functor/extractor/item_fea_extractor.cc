// Copyright (c) 2023 <EMAIL>. All rights reserved.
// Author：<EMAIL>
// Time：2023-11-28

#include "service/strategy/functor/extractor/item_fea_extractor.h"
#include "proto/recommend_feature.pb.h"
#include "proto/recommend_strategy_param.pb.h"
#include "service/acc/item_feature_client.h"
#include "service/feature/feature_factory.h"
#include "service/strategy/common/common_func.h"
#include "service/strategy/common/common_def.h"
#include "service/strategy/executor/strategy_pool.h"
#include "util/func.h"

namespace abc {
namespace recommend_plt {
namespace strategy {

void ItemFeaExtractor::Reset() {
    Extractor::Reset();
    item_feature_client_->Reset();
    goods_fea_client_->Reset();
    item_feas_.clear();
    goods_feas_.clear();
    cnt_.store(0, std::memory_order_relaxed);
}

bool ItemFeaExtractor::Init() {
    item_feature_client_.reset(new acc::ItemFeatureClient);
    goods_fea_client_.reset(new acc::ItemFeatureClient);
    return true;
}

bool ItemFeaExtractor::Bind(const std::string& sid, const strategy::StrategyParam&) {
    set_strategy_id(sid);
    set_name("ItemFeaExtractor");
    return true;
}

bool ItemFeaExtractor::Run(Accessor* accessor) {
    if (accessor->OriginItems().empty()) {
        GetCallback()();
        return true;
    }
    acc::ItemFeatureClient::BaseInfo base_info;
    base_info.trace_id = accessor->trace_id();
    base_info.caller = strategy_id();
    base_info.site_uid = accessor->site_uid();
    base_info.scene_id = accessor->scene_id();
    base_info.item_type = accessor->item_type();
    base_info.debug_mode = accessor->debug_mode();
    base_info.is_new  = accessor->item_source() == common::ItemSource::NEW;
    base_info.dim_finder = [=](const std::string& k) {
        auto fea = accessor->GetCtxFeaturePtr(k, strategy_id());
        std::string res;
        std::function<bool(std::string,int)> f = [&](std::string v, int) {
                                                    res = v;
                                                    return true;
                                                 };
        VisitFeatureAsString(fea, std::move(f));
        return res;
    };
    std::vector<int64_t> item_ids;
    item_ids.reserve(accessor->OriginItems().size());
    for (const auto& item: accessor->OriginItems()) {
        item_ids.push_back(item->item_id);
    }
    auto goods_id_fea = accessor->GetOriginItemFeaturePtr<fmp::LIST_INT64>(kFeaGRelationId);
    auto relation_fea = accessor->GetOriginItemFeaturePtr<fmp::LLIST_INT64>(kFeaGRelationId);
    if (!goods_id_fea && relation_fea) {
        std::vector<int64_t> goods_ids(relation_fea->data().size(), 0);
        for (size_t i = 0; i < relation_fea->data().size(); ++i) {
            if (!relation_fea->data()[i].empty()) {
                goods_ids[i] = relation_fea->data()[i][0];
            }
        }
        goods_id_fea = feature::FeatureFactory::CreateFeature<fmp::LIST_INT64>(goods_ids);
    }
    for (const auto& fea: GetOutputItemFeaName()) {
        if (IsOriginItemFeature(fea)) {
            item_feas_.insert(fea);
        }
        if (goods_id_fea && boost::starts_with(fea, kFeedsGoodsPrefix)) {
            const auto goods_fea = fea.substr(kFeedsGoodsPrefix.size());
            if (IsOriginItemFeature(goods_fea)) {
                goods_feas_.insert(goods_fea);
            }
        }
    }
    LOG_DEBUG << "GetRequiredItemFeaName()=" << util::ToString(GetOutputItemFeaName())
              << ", item_valid:" << util::ToString(item_feas_)
              << ", goods_valid:" << util::ToString(goods_feas_);
    const auto tms = GetServiceTimeout(kServiceFmp, 50);
    const auto timeout = GetControlParam<int64_t>(accessor, kServiceFmpTimeout, tms);
    const auto batch_size = GetDynConf<int64_t>("item_feature_batch_items", 1000);
    auto RequestItemFeature = [&, accessor, this]() {
        if (item_feas_.empty()) {
            WhenAll(accessor);
            return;
        }
        item_feature_client_->Bind(base_info);
        item_feature_client_->SetItems(item_ids);
        item_feature_client_->AddFeature(std::vector<std::string>({item_feas_.begin(), item_feas_.end()}));
        item_feature_client_->SetTimeout(timeout);
        item_feature_client_->SetBatchItemSize(batch_size);
        item_feature_client_->Async(StrategyAsyncPool::get_mutable_instance().AsyncPool(),
                                    [accessor, this]() mutable {
                                        ProcessItemFeature(accessor);
                                        WhenAll(accessor);
                                    });
    };
    auto RequestGoodsFeature = [&, accessor, this]() {
        if (!goods_id_fea || goods_feas_.empty()) {
            WhenAll(accessor);
            return;
        }
        base_info.item_type = 1;
        goods_fea_client_->Bind(base_info);
        goods_fea_client_->SetItems(goods_id_fea->data());
        goods_fea_client_->AddFeature(std::vector<std::string>({goods_feas_.begin(), goods_feas_.end()}));
        goods_fea_client_->SetTimeout(timeout);
        goods_fea_client_->SetBatchItemSize(batch_size);
        goods_fea_client_->Async(StrategyAsyncPool::get_mutable_instance().AsyncPool(),
                                 [accessor, this]() mutable {
                                     ProcessGoodsFeature(accessor);
                                     WhenAll(accessor);
                                 });
    };
    RequestItemFeature();
    RequestGoodsFeature();

    return true;
}

void ItemFeaExtractor::ProcessItemFeature(Accessor* accessor) {
    for (const auto& fea_name: item_feas_) {
        auto fea = item_feature_client_->GetFeaturePtr(fea_name);
        if (!fea) {
            LOG_DEBUG << "not valid feature:" << fea_name;
            if (accessor->debug_mode()) {
                accessor->AddCtxExplain(strategy_id(), fea_name, "not found");
            }
            continue;
        }
        accessor->AddItemFeature(fea_name, fea);
    }
}

void ItemFeaExtractor::ProcessGoodsFeature(Accessor* accessor) {
    for (const auto& fea_name: goods_feas_) {
        auto fea = goods_fea_client_->GetFeaturePtr(fea_name);
        if (!fea) {
            LOG_DEBUG << "not valid feature:" << fea_name;
            if (accessor->debug_mode()) {
                accessor->AddCtxExplain(strategy_id(), kFeedsGoodsPrefix + fea_name, "not found");
            }
            continue;
        }
        accessor->AddItemFeature(kFeedsGoodsPrefix + fea_name, fea);
    }
}

void ItemFeaExtractor::WhenAll(Accessor* accessor) {
    cnt_.fetch_add(1, std::memory_order_relaxed);
    int cnt = 2;
    if (cnt_.compare_exchange_strong(cnt, cnt+1, std::memory_order_acq_rel)) {
        if (accessor->debug_mode()) {
            auto& msg = item_feature_client_->ExplainMsg();
            for (int i = 0; i < msg.size(); ++i) {
                for(int j = 0; j < msg[i].size(); ++j) {
                    accessor->AddCtxExplain(strategy_id(), util::Join("", "[", i, "][", j, "]"), msg[i][j]);
                }
            }
            if (!item_feature_client_->LocalExplainMsg().empty()) {
                accessor->AddCtxExplain(strategy_id(), "local_item", item_feature_client_->LocalExplainMsg());
            }
            auto& msg2 = goods_fea_client_->ExplainMsg();
            for (int i = 0; i < msg2.size(); ++i) {
                for(int j = 0; j < msg2[i].size(); ++j) {
                    accessor->AddCtxExplain(strategy_id(), util::Join("", "[", i, "][", j, "]_goods"), msg2[i][j]);
                }
            }
            if (!goods_fea_client_->LocalExplainMsg().empty()) {
                accessor->AddCtxExplain(strategy_id(), "local_goods", goods_fea_client_->LocalExplainMsg());
            }
        }
        GetCallback()();
    }
}

}  // namespace strategy
}  // namespace recommend_plt
}  // namespace abc
