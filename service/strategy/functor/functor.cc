#include "service/strategy/functor/functor.h"

#include "service/feature/feature.h"
#include "service/feature/feature_single.h"
#include "service/feature/feature_vector.h"
#include "service/strategy/common/common_def.h"
#include <unordered_set>

namespace abc {
namespace recommend_plt {
namespace strategy {

bool Functor::Bind(const std::string& strategy_id, const StrategyParam& param) {
    set_strategy_id(strategy_id);
    set_name(param.functor_name());
    SetStrategyParam(param);
    return true;
}

bool Functor::DslEffectReportFeature(Accessor* accessor) {
    // dsl中间特征上报
    const auto& fea_map = dsl_effect_generater_.GetFeatureMap();
    for (const auto& it : fea_map) {
        const auto& fea_name = it.first;
        fmp::FeatureParamConfig conf;
        if (model::DataAutoApi::get_mutable_instance().GetStrategyFmpParamConfig(fea_name, &conf) &&
            conf.feature_param().feature_type() == fmp::FEA_TYPE_ITEM) {
        } else {
            auto pos = fea_name.find('-');
            if (pos != std::string::npos) {
                auto name = fea_name.substr(pos + 1, fea_name.size());
                accessor->AddCtxFeature(fmt::format("{}_local-{}", strategy_id(), name), it.second);
                local_ctx_feature_.insert(name);
            }

            if (!accessor->debug_mode()) continue;
            if (accessor->GetCtxFeaturePtr(fea_name) == nullptr) accessor->AddCtxFeature(fea_name, it.second);
            accessor->AddCtxExplain(strategy_id(), fea_name);
        }
    }
    return true;
}

bool Functor::IsStrategyEffect(Accessor* accessor) {
    dsl_effect_name_.clear();
    dsl_effect_generater_.Reset();
    auto IsStrategyEffectImpl = [&](Accessor* accessor) {
        // 1. 读取配置
        if (!model::DataAutoApi::get_mutable_instance().GetStrategyEffectString(strategy_id(), &dsl_effect_name_)) {
            LOG_DEBUG << "strategy_id:" << strategy_id() << ", Get dsl effect null";
            return true;
        }
        if (dsl_effect_name_.empty()) {
            LOG_DEBUG << "strategy_id:" << strategy_id() << ", dsl effect name empty";
            return true;
        }

        // 2. 准备数据并执行 DSL
        this->PrepareDslEffectData(accessor);
        if (!dsl_effect_generater_.GenerateOnlyDsl()) {
            accessor->AddErrorReport(strategy_id(), dsl_effect_generater_.error_msg(), "dsl_effect_fg_error");
            return false;
        }

        // 3. 数据检查 && 返回处理
        auto feature_ptr = dsl_effect_generater_.GetFeature(dsl_effect_name_);
        if (feature_ptr == nullptr) {
            accessor->AddErrorReport(strategy_id(), "dsl effect select dsl output not found", "dsl_effect_select_ptr_not_found");
            return false;
        }
        if (feature_ptr->type() != fmp::INT64 && feature_ptr->type() != fmp::BOOL) {
            accessor->AddErrorReport(strategy_id(), "dsl effect invaid select type:" + feature_ptr->type_name(), "dsl_effect_invaild_select_type");
            return false;
        }
        int64_t is_effect = 0;
        if (feature_ptr->type() == fmp::INT64) {
            is_effect = static_cast<const feature::FeatureSingle<int64_t>*>(feature_ptr.get())->data();
        } else if (feature_ptr->type() == fmp::BOOL) {
            is_effect = static_cast<const feature::FeatureSingle<uint8_t>*>(feature_ptr.get())->data();
        }
        return is_effect > 0;
    };
    is_effect_ = IsStrategyEffectImpl(accessor);
    if (!is_effect_) {
        accessor->AddStrategyEffect(strategy_id(), is_effect_);
    }
    DslEffectReportFeature(accessor); // 可解释上报(中间过程 + 错误 + 是否生效)
    return is_effect_;
}

// 只支持基于上下文的生效条件控制，不支持基于item的
void Functor::PrepareDslEffectData(Accessor* accessor) {
    feature_generate::FGBaseInfo base_info("strategy", accessor->trace_id());
    base_info.debug_mode = accessor->debug_mode();
    base_info.strategy_mode = true;
    dsl_effect_generater_.Bind(base_info);
    fmp::FgOptions options;
    options.set_enable_null_execution(true);
    dsl_effect_generater_.SetFgOptions(options);
    // 输入特征设置
    for (auto& ctx_fea : GetRequiredCtxFeaName()) {
        auto ctx_fea_ptr = accessor->GetCtxFeaturePtr(ctx_fea);
        if (!ctx_fea_ptr) {
            LOG_DEBUG << "dsl effect required ctx feature:" << ctx_fea << " not found.";
            continue;
        }
        dsl_effect_generater_.AddRawFeature(ctx_fea, ctx_fea_ptr->Clone());
    }

    // 生成特征设置
    dsl_effect_generater_.AddFeature(dsl_effect_name_);
}

void Functor::SetAdditionCtxFeaName(const std::string& name) {
    addition_ctx_fea_name_set_.emplace(name);
    SetOutputCtxFeaName(name);
}

void Functor::SetAdditionItemFeaName(const std::string& name) {
    addition_item_fea_name_set_.emplace(name);
    SetOutputItemFeaName(name);
}

void Functor::SetOutputCtxFeaName(const std::string& name) {
    output_ctx_fea_name_set_.emplace(name);
}

void Functor::SetOutputItemFeaName(const std::string& name) {
    static std::unordered_set<std::string> relation_feas {
        kFeaGScore, kFeaGTruncate, kFeaGFilter, kFeaGBottom, kFeaGLayer, kFeaGTop,
        kFeaGPrerankScore, kFeaGPrerankLocation, kFeaGRankScore, kFeaGRankLocation
    };
    if (relation_feas.count(name)) {
        output_item_fea_name_set_.emplace(kFeaGLocation);
    } else {
        output_item_fea_name_set_.emplace(name);
    }
}

bool Functor::ParseReportCtx(const std::string& ctx_fea_name, std::string& ori_fea_name, Accessor* accessor) {
    if (ctx_fea_name.find(kSvrMarkPrefix) != std::string::npos) {
        ori_fea_name = ctx_fea_name.substr(kSvrMarkPrefix.size());
        accessor->AddCtxSvrMark(strategy_id(), ori_fea_name);
    } else if (ctx_fea_name.find(kRecMarkPrefix) != std::string::npos) {
        ori_fea_name = ctx_fea_name.substr(kRecMarkPrefix.size());
    } else if (ctx_fea_name.find(kPMAddPrefix) != std::string::npos) {
        ori_fea_name = ctx_fea_name.substr(kPMAddPrefix.size());
    } else if (ctx_fea_name.find(kKafakaPrefix) != std::string::npos) {
        ori_fea_name = ctx_fea_name.substr(kKafakaPrefix.size());
    } else {
        return false;
    }
    return true;
}

bool Functor::ProcessReportCtx(const std::string& ctx_fea_name, const std::string& ori_fea_name, feature::FeaturePtr ctx_fea_ptr,
                               Accessor* accessor) {
    if (!ctx_fea_ptr) {
        return false;
    }
    if (ctx_fea_name.find(kPMAddPrefix) != std::string::npos) {
        std::string monitor_fea_name = strategy_id() + "-" + ori_fea_name;
        accessor->AddCtxFeature(monitor_fea_name, ctx_fea_ptr);
        accessor->AddCtxMonitorV2(strategy_id(), monitor_fea_name);
    } else {
        accessor->AddCtxFeature(ori_fea_name, ctx_fea_ptr);
    }
    return true;
}

bool Functor::ParseReportItem(const std::string& item_fea_name, std::string& ori_fea_name, Accessor* accessor) {
    if (item_fea_name.find(kSvrMarkPrefix) != std::string::npos) {
        ori_fea_name = item_fea_name.substr(kSvrMarkPrefix.size());
        accessor->AddItemSvrMark(strategy_id(), ori_fea_name);
    } else if (item_fea_name.find(kRecMarkPrefix) != std::string::npos) {
        ori_fea_name = item_fea_name.substr(kRecMarkPrefix.size());
    } else {
        return false;
    }
    return true;
}

}  // namespace strategy
}  // namespace recommend_plt
}  // namespace abc
