#ifndef __FUNCTION_SOBOL_RANDOM_H__
#define __FUNCTION_SOBOL_RANDOM_H__

#include <memory>
#include <random>

#include "service/feature/feature.h"
#include "service/feature/feature_single.h"
#include "service/feature/feature_vector.h"
#include "service/feature/feature_list.h"
#include "service/feature_generate/function/function.h"
#include "service/feature_generate/type/datatype_traits.h"
#include "util/random_sequence.h"

using namespace abc::recommend_plt::feature;

namespace abc {
namespace recommend_plt {
namespace feature_generate {

/**
 * @brief 低差异随机数获取(Low-Discrepancy Sequences), 序列已提前生成好，随机获取其中一段
 *      @args[0] 最小值
 *      @args[1] 最大值
 *      @args[2] 数量
 *      @args[3] 随机数种子(可选)
 *
 * @example
 *    1. sobol_random(0, 5, 10) -> [1, 2, 3, 0, 0, 4, 5, 2, 4, 5]
 */
class SobolRandomFunction : public IFunction {
public:
    virtual FeaturePtr ExecuteImpl(const Arguments& args, std::string& err_msg) override {
        if (args.size() < 3 || args.size() > 4 || has_nullptr(args)) {
            err_msg = "func:sobol_random() invalid args num: " + std::to_string(args.size());
            return nullptr;
        }
        bool has_seed = (args.size() == 4);
        if (!isValidType(args)) {
            err_msg = "func:sobol_random() invalid args[0]: " + args[0]->type_name() + " args[1]:" + args[1]->type_name()
                    + " args[2]:" + args[2]->type_name() + (has_seed ? ("args[3]: " + args[3]->type_name()) : "");
            return nullptr;
        }

        if (args[0]->type() == fmp::INT64) {
            return genRandomSequence<int64_t>(args, has_seed, err_msg);
        } else if (args[0]->type() == fmp::FLOAT32) {
            return genRandomSequence<float>(args, has_seed, err_msg);
        }

        return nullptr;
    }

private:
    bool isValidType(const Arguments& args) {
        if (args[0]->type() != args[1]->type() || args[2]->type() != fmp::INT64) return false;
        if (args[0]->type() != fmp::INT64 && args[0]->type() != fmp::FLOAT32) return false;
        if (args.size() == 4 && args[3]->type() != fmp::INT64) return false;
        return true;
    }

    template <class T>
    feature::FeaturePtr genRandomSequence(const Arguments& args, bool has_seed, std::string& err_msg) {
        T low = static_cast<FeatureSingle<T>*>(args[0].get())->data();
        T high = static_cast<FeatureSingle<T>*>(args[1].get())->data();
        int64_t num = static_cast<FeatureSingle<int64_t>*>(args[2].get())->data();
        T diff = high - low;
        if (diff < T() || num <= 0) { 
            return nullptr;
        }

        // 获取低差异序列
        auto res = FeatureFactory::CreateVector<T>(num, low);
        const std::vector<double>& seq = singleton<RandomSequenceGenerater>::get_mutable_instance().GetSequence();
        if (seq.empty()) {
            err_msg = "func:sobol_random() get sequence empty";
            return nullptr;
        }

        // 获取低差异序列随机起始遍历位置，并循环遍历
        int64_t seed = (has_seed ? args[3]->As<fmp::INT64>()->data() : 0);
        std::mt19937 gen(seed);
        typename std::conditional<std::is_same<T, int64_t>::value, std::uniform_int_distribution<int64_t>, std::uniform_real_distribution<float>>::type dist(0, seq.size());
        size_t seq_idx = dist(gen);
        size_t res_idx = 0;
        while (res_idx < num) {
            if (seq_idx >= seq.size()) {
                seq_idx = 0;
            }
            res->data()[res_idx] = static_cast<T>(seq[seq_idx] * diff + low);
            ++res_idx;
            ++seq_idx;
        }
        return res;
    }
};


}  // namespace feature_generate
}  // namespace recommend_plt
}  // namespace abc

#endif  // __FUNCTION_SOBOL_RANDOM_H__
