#ifndef __FUNCTION_FACTORY_H__
#define __FUNCTION_FACTORY_H__

#include <unordered_map>

#include "service/feature_generate/function/function.h"
#include <boost/serialization/singleton.hpp>

namespace abc
{
namespace recommend_plt
{
namespace feature_generate
{

using ParameterTypes = std::vector<fmp::FeatureValueType>;

class FunctorAllocateHandlers : public boost::serialization::singleton<FunctorAllocateHandlers> {
public:
    FunctorAllocateHandlers() = default;
    void Init();
    void Register(const std::string& function_name, std::function<FunctionPtr()> func);
    FunctionPtr Get(const std::string& function_name) const;
private:
    void RegisterBasicFunctions();
    void RegisterAggrFunctions();
    void RegisterAlgorithmFunctions();
    void RegisterBucktingFunctions();
    void RegisterMathFunctions();
    void RegisterStringFunctions();
    void RegisterOtherFunctions();
    void RegisterNullFunctions();
    std::unordered_map<std::string, std::function<FunctionPtr()>> init_func_map;

};

class FunctionFactory {
public:
    struct FunctionAttribute {
        ParameterTypes parameters;
        fmp::FeatureValueType result_type;
    };
    using FunctionAttributePtr = std::shared_ptr<FunctionAttribute>;
    FunctionFactory() = default;
    void Register(const std::string& function_name, FunctionPtr function);
    void RegisterDeclaration(const std::string& function_name, const ParameterTypes& parameter_types, fmp::FeatureValueType result_type);
    void Init();
    void Reset();

    FunctionPtr Get(const std::string& function_name)
    {
        auto func_it = functions.find(function_name);
        if (func_it != functions.end()) {
            return func_it->second;
        }
        FunctionPtr func = FunctorAllocateHandlers::get_mutable_instance().Get(function_name);
        if (func) {
            functions.insert({function_name, func});
            return func;
        }
        return nullptr;
    }

private:
    std::unordered_map<std::string, FunctionPtr> functions;
    std::unordered_multimap<std::string, FunctionAttributePtr> declarations;
};

}
}
}


#endif // __FUNCTION_FACTORY_H__
