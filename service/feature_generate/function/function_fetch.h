#ifndef _FUNCTION_FETCH_H_
#define _FUNCTION_FETCH_H_

#include <string>
#include <memory>

#include "service/feature/feature.h"
#include "service/feature/feature_map.h"
#include "service/feature/feature_single.h"
#include "service/feature/feature_vector.h"
#include "service/feature_generate/function/function.h"

using namespace abc::recommend_plt::feature;

namespace abc {
namespace recommend_plt {
namespace feature_generate {

/**
 * @brief FetchFunction Implement
 *      fetch(map, vector, default)
 *      case: fetch({1:2,3:4,6:4}, [1,2,3], -1)
 *             => [2,-1,4]
 *      fetch(map, vector<vector>, default)
 *      case: fetch({1:2, 3:4, 5:6}. [[1,2],[3,4],[5,6,7,8],[9,10]], -1)
 *             => [[2,-1],[4,-1],[6,-1,-1,-1],[-1,-1]]
 */
class FetchFunction : public IFunction {
public:

    template<typename K, typename V>
    FeaturePtr MapSubscript(const IFeature* map_untyped, const IFeature* key_untyped, V dft_val) {
        const auto* map_ptr = static_cast<const FeatureMap<K, V>*>(map_untyped);
        const auto* key_ptr = static_cast<const FeatureVector<K>*>(key_untyped);
        auto res = FeatureFactory::CreateVector<V>(key_ptr->Size(), dft_val);
        for (size_t i = 0; i < key_ptr->Size(); ++i) {
            (*res)[i] =  map_ptr->get((*key_ptr)[i], dft_val);
        }
        return res;
    }

    template<typename K, typename V>
    FeaturePtr MapSubscriptLLIST(const IFeature* map_untyped, const IFeature* key_untyped, V dft_val) {
        const auto* map_ptr = static_cast<const FeatureMap<K, V>*>(map_untyped);
        const auto* key_ptr = static_cast<const FeatureVector<std::vector<K>>*>(key_untyped);
        auto res = FeatureFactory::CreateVector<std::vector<V>>(key_ptr->Size(), std::vector<V>());
        for (size_t idx = 0; idx < key_ptr->data().size(); ++idx) {
            auto& vec = res->data()[idx];
            const auto& key_vec = key_ptr->data()[idx];
            vec.resize(key_vec.size());
            for (size_t j = 0; j < key_vec.size(); ++j) {
                vec[j] = map_ptr->get(key_vec[j], dft_val);
            }
        }
        return res;
    }

    virtual FeaturePtr ExecuteImpl(const Arguments& args, std::string& err_msg) override {
        FeaturePtr res = nullptr;
        // except args:
        // subscirpt op: Feature<T>, single<int64_t>
        if (args.size() < 2 || has_nullptr(args)) {
            ErrorMsg( "func:fetch() invalid args num: " + std::to_string(args.size()));
            return nullptr;
        }

        const IFeature* feature_untyped = args[0].get();
        const IFeature* args_untyped = args[1].get();
        int64_t dft_val = 0;
        std::string dft_val_str = "";
        if (args.size() == 3) {
            if (args[2]->type() == fmp::INT64) {
                dft_val = static_cast<const FeatureSingle<int64_t>*>(args[2].get())->data();
            } else if (args[2]->type() == fmp::STRING) {
                dft_val_str = static_cast<const FeatureSingle<std::string>*>(args[2].get())->data();
            }
        } 

        if (args[0]->type() == fmp::MAP_INT64_INT64 && (args[1]->type() == fmp::LIST_INT64 || args[1]->type() == fmp::LLIST_INT64)) {
            if (args[1]->type() == fmp::LIST_INT64) {
                return MapSubscript<int64_t, int64_t>(feature_untyped, args_untyped, dft_val);
            } else {
                return MapSubscriptLLIST<int64_t, int64_t>(feature_untyped, args_untyped, dft_val);
            }
        } else if (args[0]->type() == fmp::MAP_INT64_FLOAT32 && (args[1]->type() == fmp::LIST_INT64 || args[1]->type() == fmp::LLIST_INT64)) {
            if (args[1]->type() == fmp::LIST_INT64) {
                return MapSubscript<int64_t, float>(feature_untyped, args_untyped, dft_val);
            } else {
                return MapSubscriptLLIST<int64_t, float>(feature_untyped, args_untyped, dft_val);
            }
        } else if (args[0]->type() == fmp::MAP_INT64_STRING && (args[1]->type() == fmp::LIST_INT64 || args[1]->type() == fmp::LLIST_INT64)) {
            if (args[1]->type() == fmp::LIST_INT64) {
                return MapSubscript<int64_t, std::string>(feature_untyped, args_untyped, dft_val_str);
            } else {
                return MapSubscriptLLIST<int64_t, std::string>(feature_untyped, args_untyped, dft_val_str);
            }
        } else if (args[0]->type() == fmp::MAP_INT64_LIST_INT64 && args[1]->type() == fmp::LIST_INT64) {
            return MapSubscript<int64_t, typename EnumToType<fmp::LIST_INT64>::type>(feature_untyped, args_untyped, {});
        } else {
            ErrorMsg( "func:fetch() invalid type args[0]:" + args[0]->type_name() + " args[1]:" + args[1]->type_name());
        }
        return nullptr;
    }
};

} // namespace feature_generate
} // namespace recommend_plt
} // namespace abc


#endif /* _FUNCTION_FETCH_H_ */
