#ifndef __FUNCTION_UNION_H__
#define __FUNCTION_UNION_H__

#include <string>
#include <memory>

#include "service/feature/feature.h"
#include "service/feature/feature_single.h"
#include "service/feature/feature_vector.h"
#include "service/feature_generate/function/function.h"

using namespace abc::recommend_plt::feature;

namespace abc {
namespace recommend_plt {
namespace feature_generate {

/**
 * @brief IFunction Interface
 *
 */
class UnionFunction : public IFunction {
public:
    template<typename A>
    FeaturePtr UnionAll(const Arguments& args_untyped) {
        auto res = FeatureFactory::CreateVector<A>();
        for (const auto& arg : args_untyped) {
            if (arg->type() == fmp::LIST_INT64 || arg->type() == fmp::LIST_FLOAT32 || arg->type() == fmp::LIST_BOOL ||
                arg->type() == fmp::LIST_STRING) {
                const auto* arg_typed = dynamic_cast<const FeatureVector<A>*>(arg.get());
                res->Extend(arg_typed->data());
            } else if (arg->type() == fmp::INT64 || arg->type() == fmp::FLOAT32 || arg->type() == fmp::BOOL ||
                       arg->type() == fmp::STRING) {
                const auto* arg_typed = dynamic_cast<const FeatureSingle<A>*>(arg.get());
                res->Append(*arg_typed);
            }
        }
        return res;
    }

    virtual FeaturePtr ExecuteImpl(const Arguments& args, std::string& err_msg) override {
        if (args.size() < 1 || has_nullptr(args)) {
            ErrorMsg( "func:union() invalid args num: " + std::to_string(args.size()));
            return nullptr;
        }
        if (args[0]->type() == fmp::INT64 || args[0]->type() == fmp::LIST_INT64) {
            return UnionAll<int64_t>(args);
        } else if (args[0]->type() == fmp::FLOAT32 || args[0]->type() == fmp::LIST_FLOAT32) {
            return UnionAll<float>(args);
        } else if (args[0]->type() == fmp::STRING || args[0]->type() == fmp::LIST_STRING) {
            return UnionAll<std::string>(args);
        } else if (args[0]->type() == fmp::BOOL || args[0]->type() == fmp::LIST_BOOL) {
            return UnionAll<uint8_t>(args);
        } else {
            ErrorMsg( "func:union() invalid args type: " + args[0]->type_name());
        }
        return nullptr;
    }
};

} // namespace feature_generate
} // namespace recommend_plt
} // namespace abc

#endif // __FUNCTION_UNION_H__
