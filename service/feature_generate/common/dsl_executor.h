// Copyright (c) 2023 <EMAIL>. All rights reserved.
// Author：<EMAIL>
// Time：2023-07-28

#ifndef ACC_DSL_EXECUTOR_H_
#define ACC_DSL_EXECUTOR_H_

#include <map>
#include <memory>
#include <type_traits>
#include <unordered_map>
#include <vector>
#include <string>

#include "proto/recommend_dmp.pb.h"
#include "proto/recommend_fmp.grpc.pb.h"
#include "proto/recommend_fmp.pb.h"
#include "proto/recommend_fmp_param.pb.h"
#include "service/common/async_pool.h"
#include "service/acc/item_feature_client.h"
#include "service/feature_generate/executor/plan_executor.h"
#include "service/feature/feature.h"
#include "service/feature/feature_single.h"
#include "service/feature/feature_vector.h"
#include "util/func.h"

using namespace abc::recommend_plt::feature;

namespace abc {
namespace recommend_plt {
namespace feature_generate {

class DSLExecutor {
  public:
    DSLExecutor();

    struct BaseInfo {
        std::string trace_id;
        std::string caller;
        std::string site_uid;
        int64_t item_type = 0;
        bool debug_mode = false;
        const std::map<std::string, std::string>* dim_map = nullptr;
    };
    
    bool Bind(const BaseInfo& base_info);
    bool Reset();
    
    bool SetItemIds(const std::set<int64_t>& item_ids);
    bool SetItemIds(const std::vector<int64_t>& item_ids);
    bool SetFeature(const std::map<std::string, std::string>& ctxs);
    bool SetFeature(const dmp::UserProfile& user_profile);
    bool SetFeature(const fmp::Feature* feature);
    bool SetFeature(fmp::Feature&& feature);

    bool ExpectFeature(const std::string& feature_name);

    bool GetFeature(const std::string& fea, fmp::Feature* res) const noexcept;
    const FeaturePtr GetFeaturePtr(const std::string& fea) const noexcept;
    const std::vector<int64_t>& GetItemIds() const noexcept { return item_ids_; };
    const fmp::FeatureParamConfigList* FeatureParamConfigList() const noexcept {
        return &fmp_param_conf_;
    }
    
    feature_generate::PlanExecutor* plan_executor() { return executor_.get(); }

    using AsyncCb = std::function<void()>;
    template<typename P, typename = typename std::enable_if<service_comm::IsAsyncPool<P>::value>::type>
    bool Async(P* pool, AsyncCb&& f); // async

  private:
    bool GetDependentItemFeature();
    bool UpdateDependentFeature();
    bool GenerateDstFeture();

    const fmp::FeatureParamConfig* GetFeatureParamConfig(const std::string&) const;
    FeaturePtr GetFeatureFromItemFeature(const std::vector<int64_t>& item_ids,
                                         const std::string& feature) const;

  private:
    BaseInfo base_info_;
    int fmp_param_update_times_;
    fmp::FeatureParamConfigList fmp_param_conf_;
    std::unique_ptr<acc::ItemFeatureClient> client_;
    std::unique_ptr<feature_generate::PlanExecutor> executor_;
    std::vector<int64_t>  item_ids_;
    std::set<int64_t>     dep_item_ids_;
    std::set<std::string> dep_item_features_;
    std::set<std::string> dst_features_;
    std::unordered_map<std::string, std::string> cache_feature_map_;
    std::unordered_map<std::string, FeaturePtr> dst_feature_map_;
};


template<typename P, typename>
bool DSLExecutor::Async(P* pool, AsyncCb&& cb) {
    GetDependentItemFeature();
    acc::ItemFeatureClient::BaseInfo base_info;
    base_info.caller = base_info_.caller;
    base_info.trace_id = base_info_.trace_id;
    base_info.site_uid = base_info_.site_uid;
    base_info.item_type = base_info_.item_type;
    base_info.debug_mode = base_info_.debug_mode;
    base_info.dim_map = base_info_.dim_map;
    client_->Bind(base_info);
    client_->SetItems(dep_item_ids_);
    client_->AddFeature(std::vector<std::string>(dep_item_features_.begin(),
                                                 dep_item_features_.end()));
    client_->Async(pool, [this, cb=std::move(cb)]() {
        UpdateDependentFeature();
        GenerateDstFeture();
        cb();
    });
    return true;
}

}  // namespace acc
}  // namespace recommend_plt
}  // namespace abc

#endif
