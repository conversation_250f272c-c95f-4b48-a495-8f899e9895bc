#include "thirdparty/gtest/gtest.h"
#include "service/feature_generate/executor/plan_executor.h"
#include "thirdparty/google/protobuf/text_format.h"
#include "service/feature/feature_vector.h"
#include "service/feature_generate/function/function_if.h"



using namespace abc::recommend_plt;
using namespace abc::recommend_plt::feature_generate;

std::string err_msg;

TEST(IfFunction, FunctionSimpleRemove) {
    auto pred = FeatureFactory::CreateVector<uint8_t>({true, false, false, true});

    auto int_val1 = FeatureFactory::CreateVector<int64_t>({1,2,3,4});
    auto int_val2 = FeatureFactory::CreateVector<int64_t>({5,6,7,8});
    auto int_val3 = FeatureFactory::CreateSingle<int64_t>(100);

    auto float_val1 = FeatureFactory::CreateVector<float>({1.0,2.0,3.0,4.0});
    auto float_val2 = FeatureFactory::CreateVector<float>({5.0,6.0,7.0,8.0});
    auto float_val3 = FeatureFactory::CreateSingle<float>(100.0);

    auto str_val1 = FeatureFactory::CreateVector<std::string>({"H", "e", "l", "l"});
    auto str_val2 = FeatureFactory::CreateVector<std::string>({"W", "o", "r", "l"});
    auto str_val3 = FeatureFactory::CreateSingle<std::string>("d");

    IfFunction f;

    // vector<int>-vector<int>
    std::vector<FeaturePtr> args {pred, int_val1, int_val2};
    FeaturePtr res_untyped = f.ExecuteImpl(args, err_msg);
    EXPECT_NE(res_untyped, nullptr);
    auto res1 = dynamic_cast<FeatureVector<int64_t>*>(res_untyped.get());
    std::cout << "output1: " <<  res1->ToString() << std::endl;
    EXPECT_EQ(res1->ToString(), "name:,type:LIST_INT64,size:4,vers:,data:[1, 6, 7, 4]");

    // vector<int>-single<int>
    args = {pred, int_val1, int_val3};
    res_untyped = f.ExecuteImpl(args, err_msg);
    EXPECT_NE(res_untyped, nullptr);
    res1 = dynamic_cast<FeatureVector<int64_t>*>(res_untyped.get());
    std::cout << "output2: " <<  res1->ToString() << std::endl;
    EXPECT_EQ(res1->ToString(), "name:,type:LIST_INT64,size:4,vers:,data:[1, 100, 100, 4]");

    // vector<float>-vector<float>
    args = {pred, float_val1, float_val2};
    res_untyped = f.ExecuteImpl(args, err_msg);
    EXPECT_NE(res_untyped, nullptr);
    auto res2 = dynamic_cast<FeatureVector<float>*>(res_untyped.get());
    std::cout << "output3: " <<  res2->ToString() << std::endl;
    EXPECT_EQ(res2->ToString(), "name:,type:LIST_FLOAT32,size:4,vers:,data:[1.000000, 6.000000, 7.000000, 4.000000]");

    // vector<float>-single<float>
    args = {pred, float_val1, float_val3};
    res_untyped = f.ExecuteImpl(args, err_msg);
    EXPECT_NE(res_untyped, nullptr);
    res2 = dynamic_cast<FeatureVector<float>*>(res_untyped.get());
    std::cout << "output4: " <<  res2->ToString() << std::endl;
    EXPECT_EQ(res2->ToString(), "name:,type:LIST_FLOAT32,size:4,vers:,data:[1.000000, 100.000000, 100.000000, 4.000000]");

    // vector<string>-vector<string>
    args = {pred, str_val1, str_val2};
    res_untyped = f.ExecuteImpl(args, err_msg);
    EXPECT_NE(res_untyped, nullptr);
    auto res3 = dynamic_cast<FeatureVector<std::string>*>(res_untyped.get());
    std::cout << "output5: " <<  res3->ToString() << std::endl;
    EXPECT_EQ(res3->ToString(), "name:,type:LIST_STRING,size:4,vers:,data:[\"H\", \"o\", \"r\", \"l\"]");

    // vector<string>-single<string>
    args = {pred, str_val1, str_val3};
    res_untyped = f.ExecuteImpl(args, err_msg);
    EXPECT_NE(res_untyped, nullptr);
    res3 = dynamic_cast<FeatureVector<std::string>*>(res_untyped.get());
    std::cout << "output6: " <<  res3->ToString() << std::endl;
    EXPECT_EQ(res3->ToString(), "name:,type:LIST_STRING,size:4,vers:,data:[\"H\", \"d\", \"d\", \"l\"]");

    // single<string>-vector<string>
    args = {pred, str_val3, str_val1};
    res_untyped = f.ExecuteImpl(args, err_msg);
    EXPECT_NE(res_untyped, nullptr);
    res3 = dynamic_cast<FeatureVector<std::string>*>(res_untyped.get());
    std::cout << "output6: " <<  res3->ToString() << std::endl;
    EXPECT_EQ(res3->ToString(), "name:,type:LIST_STRING,size:4,vers:,data:[\"d\", \"e\", \"l\", \"d\"]");
}