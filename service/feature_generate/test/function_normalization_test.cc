#include "service/feature_generate/executor/plan_executor.h"
#include "service/feature/feature.h"
#include "service/feature/feature_vector.h"
#include "service/feature_generate/function/function_normalization.h"
#include "thirdparty/google/protobuf/text_format.h"
#include "thirdparty/gtest/gtest.h"
#include "util/date_time.h"

using namespace abc::recommend_plt;
using namespace abc::recommend_plt::feature_generate;
using namespace abc::recommend_plt::feature;

std::string norm_err_msg;

TEST(MinMaxNormFunctionTest, Basic0) {
    MinMaxNormFunction f;

    auto a1 = FeatureFactory::CreateVector<int64_t>({2, 3, 6, 1, 4, 5});
    auto a2 = FeatureFactory::CreateVector<float>({1.0, 4.0, -2.5, 3.5});
    auto a3 = FeatureFactory::CreateVector<int64_t>({10});
    auto a4 = FeatureFactory::CreateVector<int64_t>({10,10,10});

    LOG_DEBUG << "minmax_norm begin test.";
    std::vector<FeaturePtr> args{a1};
    FeaturePtr res = f.ExecuteImpl(args, norm_err_msg);
    auto* res_typed1 = static_cast<FeatureVector<float>*>(res.get());
    EXPECT_NE(res, nullptr);
    std::vector<float> out1 = {0.200000,0.400000,1.000000,0.000000,0.600000,0.800000};
    for (size_t i = 0; i < res_typed1->Size(); i++) {
        ASSERT_NEAR((*res_typed1)[i], out1[i], 0.00001f);
    }
    std::cout << "output:" << res->ToString() << std::endl;

    args = {a2};
    res = f.ExecuteImpl(args, norm_err_msg);
    auto* res_typed2 = static_cast<FeatureVector<float>*>(res.get());
    EXPECT_NE(res, nullptr);
    std::vector<float> out2 = {0.538462,1.000000,0.000000,0.923077};
    for (size_t i = 0; i < res_typed2->Size(); i++) {
        ASSERT_NEAR((*res_typed2)[i], out2[i], 0.00001f);
    }
    std::cout << "output:" << res->ToString() << std::endl;

    // size==1
    args = {a3};
    res = f.ExecuteImpl(args, norm_err_msg);
    auto* res_typed3 = static_cast<FeatureVector<int64_t>*>(res.get());
    EXPECT_NE(res, nullptr);
    std::cout << "output:" << res->ToString() << std::endl;

    // min==max
    args = {a4};
    res = f.ExecuteImpl(args, norm_err_msg);
    auto* res_typed4 = static_cast<FeatureVector<int64_t>*>(res.get());
    EXPECT_NE(res, nullptr);
    std::cout << "output:" << res->ToString() << std::endl;

}

TEST(ZscoreNormFunctionTest, Basic0) {
    ZscoreNormFunction f;

    auto a1 = FeatureFactory::CreateVector<int64_t>({1, 2, 3, 4, 5, 6});
    auto a2 = FeatureFactory::CreateVector<float>({1.0, -2.5, 3.5, 4.0});
    auto a3 = FeatureFactory::CreateVector<std::string>({"1.0", "2.0"});
    auto a4 = FeatureFactory::CreateVector<int64_t>({10});
    auto a5 = FeatureFactory::CreateVector<float>({1.0});
    auto a6 = FeatureFactory::CreateVector<float>({1.0, 1.0, 1.0});

    LOG_DEBUG << "zscore_norm begin test.";
    std::vector<FeaturePtr> args{a1};
    FeaturePtr res = f.ExecuteImpl(args, norm_err_msg);
    auto* res_typed1 = static_cast<FeatureVector<float>*>(res.get());
    EXPECT_NE(res, nullptr);
    std::vector<float> out1 = {-1.463846, -0.878308, -0.292769, 0.292769, 0.878308, 1.463846};
    for (size_t i = 0; i < res_typed1->Size(); i++) {
        ASSERT_NEAR((*res_typed1)[i], out1[i], 0.00001f);
    }
    std::cout << "output1:" << res->ToString() << std::endl;

    args = {a2};
    res = f.ExecuteImpl(args, norm_err_msg);
    auto* res_typed2 = static_cast<FeatureVector<float>*>(res.get());
    EXPECT_NE(res, nullptr);
    std::vector<float> out2 = {-0.194257, -1.554056, 0.777028, 0.971285};
    for (size_t i = 0; i < res_typed2->Size(); i++) {
        ASSERT_NEAR((*res_typed2)[i], out2[i], 0.00001f);
    }
    std::cout << "output2:" << res->ToString() << std::endl;

    // size = 1
    args = {a5};
    res = f.ExecuteImpl(args, norm_err_msg);
    EXPECT_NE(res, nullptr);
    auto* res_typed3 = static_cast<FeatureVector<float>*>(res.get());
    std::vector<float> out3 = {1.0};
    for (size_t i = 0; i < res_typed3->Size(); i++) {
        ASSERT_NEAR((*res_typed3)[i], out3[i], 0.00001f);
    }
    std::cout << "output3:" << res->ToString() << std::endl;

    // stdvep = 0
    args = {a6};
    res = f.ExecuteImpl(args, norm_err_msg);
    EXPECT_NE(res, nullptr);
    auto* res_typed4 = static_cast<FeatureVector<float>*>(res.get());
    std::vector<float> out4 = {1.0, 1.0, 1.0};
    for (size_t i = 0; i < res_typed4->Size(); i++) {
        ASSERT_NEAR((*res_typed4)[i], out4[i], 0.00001f);
    }
    std::cout << "output4:" << res->ToString() << std::endl;

}