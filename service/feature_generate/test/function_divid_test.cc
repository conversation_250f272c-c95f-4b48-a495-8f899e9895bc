#include "thirdparty/gtest/gtest.h"
#include "service/feature_generate/executor/plan_executor.h"
#include "thirdparty/google/protobuf/text_format.h"
#include "service/feature/feature_vector.h"
#include "service/feature_generate/function/function_divid.h"


using namespace abc::recommend_plt;
using namespace abc::recommend_plt::feature_generate;
using namespace abc::recommend_plt::feature;

std::string divid_err_msg;

TEST(FunctionDivid, FunctionDividPadding) {
    auto f1 = FeatureFactory::CreateVector<int64_t>({1, 2, 3, 4, 5});
    auto f2 = FeatureFactory::CreateSingle<int64_t>(2);
    auto f3 = FeatureFactory::CreateSingle<int64_t>(-1);
    auto f4 = FeatureFactory::CreateSingle<uint8_t>(true);
    auto f5 = FeatureFactory::CreateSingle<uint8_t>(false);

    std::vector<std::vector<int64_t>> res1 = {{1, 2}, {3, 4}, {5, -1}};
    std::vector<std::vector<int64_t>> res2 = {{1, 2}, {3, 4}, {5}};

    RunEnv env;
    DividFunction f;
    std::vector<FeaturePtr> args {f1, f2, f3, f4};
    FeaturePtr res = f.Execute(args, &env);
    f.PrintExecutionSummary();
    EXPECT_EQ(res->Size(), res1.size());
    // for (size_t i = 0; i < res1.size(); ++i) {
    //     for (size_t j = 0; j < res1[i].size(); ++j) {
    //         EXPECT_EQ((*res)[i][j], res1[i][j]);
    //     }
    // }

    args = {f1, f2, f3, f5};
    res = f.Execute(args, &env);
    f.PrintExecutionSummary();
    EXPECT_EQ(res->Size(), res2.size());
    // for (size_t i = 0; i < res2.size(); ++i) {
    //     for (size_t j = 0; j < res2[i].size(); ++j) {
    //         EXPECT_EQ((*res)[i][j], res2[i][j]);
    //     }
    // }
}

TEST(FunctionDivid, FunctionDividInt) {
    auto f1 = FeatureFactory::CreateVector<int64_t>({1, 2, 3, 4, 5});
    auto f2 = FeatureFactory::CreateSingle<int64_t>(2);
    auto f3 = FeatureFactory::CreateSingle<int64_t>(-1);

    RunEnv env;
    DividFunction f;
    std::vector<FeaturePtr> args {f1, f2};
    FeaturePtr res = f.Execute(args, &env);
    f.PrintExecutionSummary();

    args = {f1, f2, f3};
    res = f.Execute(args, &env);
    f.PrintExecutionSummary();
}

TEST(FunctionDivid, FunctionDividFloat) {
    auto f1 = FeatureFactory::CreateVector<float>({1.0, 2.0, 3.0, 4.0, 5});
    auto f2 = FeatureFactory::CreateSingle<int64_t>(2);
    auto f3 = FeatureFactory::CreateSingle<float>(3.0f);

    DividFunction f;
    RunEnv env;
    std::vector<FeaturePtr> args {f1, f2};
    FeaturePtr res = f.Execute(args, &env);
    f.PrintExecutionSummary();

    args = {f1, f2, f3};
    res = f.Execute(args, &env);
    f.PrintExecutionSummary();
}

TEST(FunctionDivid, FunctionDividString) {
    auto f1 = FeatureFactory::CreateVector<std::string>({"a", "b", "c", "d", "e"});
    auto f2 = FeatureFactory::CreateSingle<int64_t>(2);
    auto f3 = FeatureFactory::CreateSingle<std::string>("x");

    DividFunction f;
    RunEnv env;
    std::vector<FeaturePtr> args {f1, f2};
    FeaturePtr res = f.Execute(args, &env);
    f.PrintExecutionSummary();

    args = {f1, f2, f3};
    res = f.Execute(args, &env);
    f.PrintExecutionSummary();
}


TEST(FunctionDivid, FunctionDividFloat_ZERO_INPUT) {
    auto f1 = FeatureFactory::CreateVector<float>({1.0, 2.0, 3.0, 4.0, 5});
    auto f2 = FeatureFactory::CreateSingle<int64_t>(0);

    DividFunction f;
    RunEnv env;
    std::vector<FeaturePtr> args {f1, f2};
    FeaturePtr res = f.Execute(args, &env);
    f.PrintExecutionSummary();
}

TEST(FunctionDivid, FunctionDividFloat_WITH_DFT_VAL) {
    auto f1 = FeatureFactory::CreateVector<int64_t>({1, 2, 3, 4, 5});
    auto f2 = FeatureFactory::CreateSingle<int64_t>(3);
    auto f3 = FeatureFactory::CreateSingle<int64_t>(-1);

    DividFunction f;
    RunEnv env;
    std::vector<FeaturePtr> args {f1, f2, f3};
    FeaturePtr res = f.Execute(args, &env);
    f.PrintExecutionSummary();
}

TEST(FunctionDivid, BadInput) {
    auto f1 = FeatureFactory::CreateVector<int64_t>({1, 2, 3, 4, 5});
    auto f2 = FeatureFactory::CreateFeature2<fmp::STRING>({"abc"});

    DividFunction f;
    RunEnv env;
    std::vector<FeaturePtr> args {f1, f2};
    FeaturePtr res = f.Execute(args, &env);
    f.PrintExecutionSummary();
}
