#include "thirdparty/gtest/gtest.h"
#include "service/feature/feature.h"
#include "service/feature_generate/executor/plan_executor.h"
#include "thirdparty/google/protobuf/text_format.h"
#include "service/feature/feature_vector.h"
#include "service/feature_generate/function/function_shale.h"

using namespace abc::recommend_plt;
using namespace abc::recommend_plt::feature_generate;
using namespace abc::recommend_plt::feature;

std::string slice_err_msg;

/* feature = [1, 2, 3, 4, 5]
 *        feature[:-1] = [1, 2, 3, 4]
 *        feature[100:0] = []
 *        feature[:-10] = []
 */

TEST(ShaleFunctionTest, Basic0) {
    ShaleFunction f;

    auto alpha     = FeatureFactory::CreateVector<float>({0.1, 0.2, 0.3, 0.4, 0.5, 0.25});
    auto zeta      = FeatureFactory::CreateVector<float>({0.2, 0.3, 0.6, 0.1, 0.1, 0.7});
    auto theta     = FeatureFactory::CreateVector<float>({0.4, 0.7, 0.8, 0.2, 0.3, 0.5});
    auto V         = FeatureFactory::CreateVector<float>({0.3, 0.9, 0.5, 0.1, 0.4, 0.2});
    auto max_iter  = FeatureFactory::CreateSingle<int64_t>(50);
    auto tolerance = FeatureFactory::CreateSingle<float>(1e-2);

    std::vector<FeaturePtr> args {alpha, zeta, theta};
    std::string err_msg;
    FeaturePtr res = f.ExecuteImpl(args, err_msg);
    EXPECT_EQ(res, nullptr);

    args.push_back(V);
    res = f.ExecuteImpl(args, err_msg);
    EXPECT_NE(res, nullptr);
    auto res_typed = dynamic_cast<FeatureVector<float>*>(res.get());
    EXPECT_EQ(res_typed->Size(), alpha->Size() + 1);
    std::vector<float> expect_res = {0, 0, 0.197157, 0, 0, 0.802843, 0.578863};
    for (size_t i = 0; i < expect_res.size(); ++i) {
        EXPECT_FLOAT_EQ(expect_res[i], res_typed->data(i));
    }

    args.push_back(max_iter);
    res = f.ExecuteImpl(args, err_msg);
    EXPECT_NE(res, nullptr);
    res_typed = dynamic_cast<FeatureVector<float>*>(res.get());
    EXPECT_EQ(res_typed->Size(), alpha->Size() + 1);
    expect_res = {0, 0, 0.197157, 0, 0, 0.802843, 0.578863};
    for (size_t i = 0; i < expect_res.size(); ++i) {
        EXPECT_FLOAT_EQ(expect_res[i], res_typed->data(i));
    }

    args.push_back(tolerance);
    res = f.ExecuteImpl(args, err_msg);
    EXPECT_NE(res, nullptr);
    res_typed = dynamic_cast<FeatureVector<float>*>(res.get());
    EXPECT_EQ(res_typed->Size(), alpha->Size() + 1);
    expect_res = {0, 0, 0.18945324, 0, 0, 0.810547, 0.57578129};
    for (size_t i = 0; i < expect_res.size(); ++i) {
        EXPECT_FLOAT_EQ(expect_res[i], res_typed->data(i));
    }
}
