// Copyright (c) 2022 <EMAIL>. All rights reserved.
// Author：<EMAIL>
// Time：2022-11-30
#include "service/feature_generate/executor/plan_executor.h"
#include "service/feature/feature.h"
#include "service/feature/feature_vector.h"
#include "service/feature_generate/function/function_union.h"
#include "service/feature_generate/operator/arithmetic_operator.h"
#include "service/feature_generate/operator/logical_operator.h"

#include "thirdparty/gtest/gtest.h"
#include "util/func.h"
#include <memory>

using namespace abc::recommend_plt;
using namespace abc::recommend_plt::feature_generate;
using namespace abc::recommend_plt::feature;

std::string union_err_msg;

TEST(UnionFunctionTest, SingleArgs) {
    UnionFunction f;

    auto a1 = FeatureFactory::CreateSingle<int64_t>({0});
    auto a2 = FeatureFactory::CreateVector<int64_t>({1,2,3,4,5,6});
    auto a3 = FeatureFactory::CreateSingle<int64_t>({7});

    std::vector<FeaturePtr> args{a1,a2,a3};
    auto res = f.ExecuteImpl(args, union_err_msg);
    EXPECT_NE(res, nullptr);
    std::cout << "output:" << res->ToString() << std::endl;

    auto b1 = FeatureFactory::CreateSingle<float>({0.0});
    auto b2 = FeatureFactory::CreateVector<float>({1.1,2.2,3.3,4.4,5.5,6.6});
    auto b3 = FeatureFactory::CreateSingle<float>({7.7});
    args = {b1,b2,b3};
    res = f.ExecuteImpl(args, union_err_msg);
    EXPECT_NE(res, nullptr);
    std::cout << "output:" << res->ToString() << std::endl;

    auto c1 = FeatureFactory::CreateSingle<uint8_t>({0});
    auto c2 = FeatureFactory::CreateVector<uint8_t>({1,0,1,1});
    auto c3 = FeatureFactory::CreateSingle<uint8_t>({0});
    args = {c1,c2,c3};
    res = f.ExecuteImpl(args, union_err_msg);
    EXPECT_NE(res, nullptr);
    std::cout << "output:" << res->ToString() << std::endl;

}
