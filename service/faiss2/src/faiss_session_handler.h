#ifndef _FAISS_SESSION_HANDLER_H_
#define _FAISS_SESSION_HANDLER_H_

#include "service/common/session_handler.h"
#include "proto/recommend_health_detect.pb.h"
#include "proto/recommend_mmp.grpc.pb.h"

using namespace abc::recommend_plt::mmp;

namespace abc {
namespace recommend_plt {
namespace faiss {

class FaissSessionHandler: public service_comm::SessionHandler<FaissModelService::AsyncService> {
public:
    FaissSessionHandler(): SessionHandler() {}

    ~FaissSessionHandler() {}

    void InitContext(FaissModelService::AsyncService* service, grpc::ServerCompletionQueue* cq);

    void ReInitContext(FaissModelService::AsyncService* service, grpc::ServerCompletionQueue* cq);

    void Start();

public:
    std::unique_ptr<grpc::ServerAsyncResponseWriter<FaissModelResponse>> rsp_writer_;

    FaissModelRequest req_;

    FaissModelResponse rsp_;
    
    std::unique_ptr<grpc::ClientContext> cli_ctx_;
};

}
}
}

#endif
