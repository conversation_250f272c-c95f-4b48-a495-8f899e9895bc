#ifndef _SERVICE_FAISS_DEBUG_H
#define _SERVICE_FAISS_DEBUG_H

#include <iostream>
#include <vector>
#include <iterator>

// template<class T>
// std::ostream& operator<<(std::ostream& stream, const std::vector<T>& values)
// {
//     stream << "[ ";
//     copy( begin(values), end(values), std::ostream_iterator<T>(stream, " ") );
//     stream << ']';
//     return stream;
// }

#define TIME_BEGIN(WHO)  auto t1##WHO = std::chrono::steady_clock::now();
#define TIME_END(WHO)    auto t2##WHO = std::chrono::steady_clock::now(); \
                    double t_def##WHO = std::chrono::duration_cast<std::chrono::duration<double> >(t2##WHO - t1##WHO).count(); \
                    ;CONSOLE_LOG << #WHO << " execute consume " << t_def##WHO << "ms";

#endif //_SERVICE_FAISS_DEBUG_H
