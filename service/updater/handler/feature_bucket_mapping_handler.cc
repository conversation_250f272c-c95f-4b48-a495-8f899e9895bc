#include "service/updater/handler/feature_bucket_mapping_handler.h"

#include <fstream>
#include <string>
#include <vector>

#include "thirdparty/boost/algorithm/string.hpp"
#include "util/logger.h"


namespace abc {
namespace recommend_plt {
namespace updater {

FeatureBucketMappingHandler::FeatureBucketMappingHandler() {}

FeatureBucketMappingHandler::~FeatureBucketMappingHandler() {}

bool FeatureBucketMappingHandler::Update(const std::string& file) {
    std::ifstream fin(file, std::ios::in);
    if (!fin.is_open()) {
        LOG_ERROR << "open file failed, file = " << file;
        return false;
    }

    std::string header;
    std::getline(fin, header);

    std::string line;
    while (std::getline(fin, line)) {
        std::vector<std::string> data_vec;
        boost::algorithm::split(data_vec, line, boost::is_any_of(","));
        if (data_vec.size() != 4) {
            LOG_ERROR << "parse line error, expect field size to be equal to 4, but got " << data_vec.size()
                      << ", line = " << line;
            continue;
        }

        std::string key = "";
        std::string sep = "";
        for (size_t i = 0; i < data_vec.size() - 1; ++i) {
            key += sep + data_vec[i];
            sep = "-";
        }

        this->Set(key, data_vec[data_vec.size() - 1]);
    }

    fin.close();
    LOG_INFO << "FeatureBucketMappingHandler Update OK! file=" << file;
    return true;
}

}  // namespace updater
}  // namespace recommend_plt
}  // namespace abc

