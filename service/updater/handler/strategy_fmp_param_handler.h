#ifndef _SERVICE_UPDATER_STRATEGY_FMP_PARAM_HANDLER_H_
#define _SERVICE_UPDATER_STRATEGY_FMP_PARAM_HANDLER_H_
#include "service/updater/handler/file_handler.h"
#include "proto/recommend_fmp_param.pb.h"

namespace abc {
namespace recommend_plt {
namespace updater {

typedef ::google::protobuf::Message ProtoConfig;
class StrategyFmpParamHandler : public FileHandler
{
public:
    StrategyFmpParamHandler();
    virtual ~StrategyFmpParamHandler();
protected:
    virtual bool Update(const std::string& file);
private:
    int update_times_ = 0; 
    
};

}  // namespace updater
}  // namespace recommend_plt
}  // namespace abc


#endif  // _SERVICE_UPDATER_STRATEGY_FMP_PARAM_HANDLER_H_
