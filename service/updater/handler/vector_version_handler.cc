#include "service/updater/handler/files_handler.h"
#include "util/func.h"
#include "service/updater/handler/vector_version_handler.h"
#include "util/logger.h"

namespace abc {
namespace recommend_plt {
namespace updater {

bool
VectorVersionHandler::Update(const std::string& file) {

    const std::string& full_path = file;
    auto path_frags = util::Split2(file, '/');
    if (path_frags.size() == 0) {
        return false;
    }

    const std::string& filename = path_frags[path_frags.size() - 1];

    // e.g.: faiss_index-w2v1.0-iosshca-v1.dat.202206081305
    auto filename_frags = util::Split2(filename, '.');
    std::string filename_prefix = filename.substr(0, filename.find_last_of('.'));
    filename_prefix = filename_prefix.substr(0, filename_prefix.find_last_of('.'));

    auto model_name_frags = util::Split2(filename_prefix, '-');

    if (filename_frags.size() >= 3 && model_name_frags.size() >= 4) {
        std::string version = filename_frags[filename_frags.size()-1];
        std::string key = util::Join("-",model_name_frags[1], model_name_frags[2], model_name_frags[3]);

        LOG_INFO << "set [" << key << "] version to:" << version;
        this->Set(key, version);
    } else {
        LOG_ERROR << "file format error: " << file;
        return false;
    }

    return true;
}


}  // namespace updater
}  // namespace recommend_plt
}  // namespace abc


