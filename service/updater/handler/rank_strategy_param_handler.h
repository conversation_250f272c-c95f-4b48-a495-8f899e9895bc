#ifndef _SERVICE_UPDATER_RANK_STRATEGY_PARAM_HANDLER_H_
#define _SERVICE_UPDATER_RANK_STRATEGY_PARAM_HANDLER_H_

#include "service/updater/handler/file_handler.h"
#include "proto/recommend_mmp_param.pb.h"

namespace abc {
namespace recommend_plt {
namespace updater {

class RankStrategyParamHandler : public FileHandler
{
public:
    RankStrategyParamHandler();
    virtual ~RankStrategyParamHandler();
protected:
    virtual bool Update(const std::string& file);   
};

}  // namespace updater
}  // namespace recommend_plt
}  // namespace abc


#endif  // _SERVICE_UPDATER_RANK_STRATEGY_PARAM_HANDLER_H_
