#ifndef _PRERANK_PARAM_HANDLER_H_
#define _PRERANK_PARAM_HANDLER_H_


#include "service/updater/handler/files_handler.h"

namespace abc {
namespace recommend_plt {
namespace updater {

typedef ::google::protobuf::Message ProtoConfig;


class PrerankParamHandler: public FilesHandler
{
private:
    /* data */
public:
    PrerankParamHandler();
    ~PrerankParamHandler();
protected:
    virtual bool Update(const std::string& file);
};




}  // namespace updater
}  // namespace recommend_plt
}  // namespace abc



#endif /* _PRERANK_PARAM_HANDLER_H_ */
