// Copyright (c) 2024 <EMAIL>. All rights reserved.
// Author：<EMAIL>
// Time：2024-12-23

#include <filesystem>
#include <memory>
#include <unordered_map>
#include "thirdparty/gtest/gtest.h"
#include "arrow/io/api.h"
#include "parquet/arrow/writer.h"
#include "parquet/arrow/reader.h"
#include "service/feature/feature.h"
#include "service/feature/feature_vector.h"
#include "service/updater/handler/arrow_table.h"

using abc::recommend_plt::updater::ArrowTable;
using abc::recommend_plt::updater::IncreaseArrowTable;
using namespace abc::recommend_plt;
using namespace abc::recommend_plt::feature;

const std::string file_name = "goods.20241221";
const std::string file_name2 = "goods.20241222";

class ArrowTableTest : public testing::Test {
  public:
    void SetUp() override {
        table_ = std::make_unique<ArrowTable>();
        incr_table_ = std::make_unique<IncreaseArrowTable>();
    }

    static void SetUpTestCase() {
        arrow::Int64Builder idx_builder;
        arrow::Int64Builder val_builder;
        arrow::Int64Builder int_builder;
        arrow::ListBuilder list_builder(arrow::default_memory_pool(), std::make_shared<arrow::Int64Builder>());
        auto list_val_builder = static_cast<arrow::Int64Builder*>(list_builder.value_builder());
        for (int i = 0; i < 100000; ++i) {
            ASSERT_TRUE(idx_builder.Append(i).ok());
            ASSERT_TRUE(val_builder.Append(i % 100).ok());
            ASSERT_TRUE(int_builder.Append(i % 8).ok());
            list_builder.Append();
            if (i % 10) {
                list_val_builder->Append(i);
                list_val_builder->Append(i+1);
                list_val_builder->Append(i+2);
            } else if (i == 0) {
                list_val_builder->Append(1024);
                list_val_builder->Append(0);
                list_val_builder->Append(0);
            }
        }
        std::shared_ptr<arrow::Array> idx_array, val_array, list_array, int_array;
        ASSERT_TRUE(idx_builder.Finish(&idx_array).ok());
        ASSERT_TRUE(val_builder.Finish(&val_array).ok());
        ASSERT_TRUE(int_builder.Finish(&int_array).ok());
        ASSERT_TRUE(list_builder.Finish(&list_array).ok());
        std::shared_ptr<arrow::Field> idx_field = arrow::field("index", arrow::int64());
        std::shared_ptr<arrow::Field> int_field = arrow::field("fea", arrow::int64());
        std::shared_ptr<arrow::Field> aint_field = arrow::field("aint", arrow::int64());
        std::shared_ptr<arrow::Field> list_field = arrow::field("fea_list", arrow::list(arrow::int64()));
        auto table = arrow::Table::Make(arrow::schema({idx_field, int_field, list_field}),
                                        {idx_array, val_array, list_array});
        ASSERT_TRUE(bool(table));
        auto outfile = arrow::io::FileOutputStream::Open(file_name);
        ASSERT_TRUE(parquet::arrow::WriteTable(*table, arrow::default_memory_pool(), *outfile).ok());
        table = arrow::Table::Make(arrow::schema({idx_field, int_field, aint_field, list_field}),
                                                 {idx_array, val_array, int_array,  list_array});
        outfile = arrow::io::FileOutputStream::Open(file_name2);
        ASSERT_TRUE(parquet::arrow::WriteTable(*table, arrow::default_memory_pool(), *outfile).ok());
    }

    static void TearDownTestCase() {
        std::filesystem::remove(file_name);
    }

  public:
    std::shared_ptr<arrow::Table> table;
    std::unique_ptr<abc::recommend_plt::updater::ArrowTable> table_;
    std::unique_ptr<abc::recommend_plt::updater::IncreaseArrowTable> incr_table_;
};


TEST_F(ArrowTableTest, LoadData) {
    ASSERT_TRUE(table_->LoadData(file_name).ok());
}

TEST_F(ArrowTableTest, GetFeature) {
    ASSERT_TRUE(table_->LoadData(file_name).ok());
    std::vector<int64_t> keys, vals;
    for (int i = 0; i < 1000; ++i) {
        keys.emplace_back(i);
        vals.emplace_back(i % 100);
    }
    auto fea_ptr = CastFeature<fmp::LIST_INT64>(table_->GetFeature(keys, "fea"));
    ASSERT_TRUE(fea_ptr);
    for (int i = 0; i < 1000; ++i) {
        ASSERT_TRUE(vals[i] == fea_ptr->data(i));
    }
    auto list_ptr = CastFeature<fmp::LLIST_INT64>(table_->GetFeature(keys, "fea_list"));
    ASSERT_TRUE(list_ptr);
    std::cerr << list_ptr->ToString() << std::endl;
    for (int i = 1; i < 1000; ++i) {
        if (i % 10) {
            ASSERT_EQ(list_ptr->data(i).size(), 3);
            ASSERT_EQ(list_ptr->data(i)[0], i);
            ASSERT_EQ(list_ptr->data(i)[1], i+1);
            ASSERT_EQ(list_ptr->data(i)[2], i+2);
        } else {
            ASSERT_EQ(list_ptr->data(i).size(), 3);
            ASSERT_EQ(list_ptr->data(i)[0], 1024);
            ASSERT_EQ(list_ptr->data(i)[1], 1024);
            ASSERT_EQ(list_ptr->data(i)[2], 1024);
        }
    }
    std::vector<std::string> feas{"abc", "fea", "def", "fea_list"};
    std::unordered_map<std::string, FeaturePtr> result;
    std::unordered_map<std::string, int> fea_hits;
    ASSERT_TRUE(table_->GetFeature(keys, feas, result, fea_hits));
    ASSERT_EQ(result.size(), 2);
    {
        auto fea_ptr = CastFeature<fmp::LIST_INT64>(result["fea"]);
        ASSERT_TRUE(fea_ptr);
        for (int i = 0; i < 1000; ++i) {
            ASSERT_TRUE(vals[i] == fea_ptr->data(i));
        }
        auto list_ptr = CastFeature<fmp::LLIST_INT64>(result["fea_list"]);
        ASSERT_TRUE(list_ptr);
        for (int i = 1; i < 1000; ++i) {
            if (i % 10) {
                ASSERT_EQ(list_ptr->data(i).size(), 3);
                ASSERT_EQ(list_ptr->data(i)[0], i);
                ASSERT_EQ(list_ptr->data(i)[1], i+1);
                ASSERT_EQ(list_ptr->data(i)[2], i+2);
            } else {
                ASSERT_EQ(list_ptr->data(i).size(), 3);
                ASSERT_EQ(list_ptr->data(i)[0], 1024);
                ASSERT_EQ(list_ptr->data(i)[1], 1024);
                ASSERT_EQ(list_ptr->data(i)[2], 1024);
            }
        }
    }
    std::cerr << "fea_hits:" << util::ToString(fea_hits) << std::endl;
}

TEST_F(ArrowTableTest, IncreaseTable) {
    ASSERT_TRUE(incr_table_->LoadData(file_name).ok());
    IncreaseArrowTable other;
    other.Copy(*incr_table_);
    std::cerr << "incr_table:" << incr_table_->DebugString() << std::endl;
    std::vector<int64_t> keys;
    for (int i = 0; i < 10; ++i) {
        keys.push_back(i);
    }
    do {
        ASSERT_TRUE(incr_table_->LoadData(file_name2).ok());
        std::cerr << "incr_table:" << incr_table_->DebugString() << std::endl;
        std::vector<std::string> feas{"fea", "aint"};
        std::unordered_map<std::string, FeaturePtr> result;
        std::unordered_map<std::string, int> fea_hits;
        ASSERT_TRUE(incr_table_->GetFeature(keys, feas, result, fea_hits));
        for (const auto [name, fea]: result) {
            std::cerr << "fea:" << name << ", val:" << fea->DebugString() << std::endl;
        }
        sleep(1);
    } while (!incr_table_->LoadCompeleted());
    do {
        ASSERT_TRUE(incr_table_->LoadData(file_name).ok());
        std::cerr << "incr_table:" << incr_table_->DebugString() << std::endl;
        std::vector<std::string> feas{"fea", "aint"};
        std::unordered_map<std::string, FeaturePtr> result;
        std::unordered_map<std::string, int> fea_hits;
        ASSERT_TRUE(incr_table_->GetFeature(keys, feas, result, fea_hits));
        for (const auto [name, fea]: result) {
            std::cerr << "fea:" << name << ", val:" << fea->DebugString() << std::endl;
        }
        sleep(1);
    } while (!incr_table_->LoadCompeleted());
}
