cc_binary(
    name = 'rec_async_fmp',
    srcs = ['fmp_adapter_client_tool.cc'],
    deps = [
        '//thirdparty/gflags:gflags',
        '//thirdparty/grpc:grpc++',
        '//proto:recommend_fmp_api',
        '//thirdparty/grpc:grpc++',
        '//thirdparty/boost:boost_system',
        '//thirdparty/boost:boost_thread',
        '//proto:recommend_plt_proto',
        '//service/common:service_comm',
        '//service/updater:recommend_updater',
        '//util:util'
    ],
    optimize=['-O3','-Wall','-fPIC','-std=c++11']
)

# cc_binary(
#     name = 'item_tag_client_tools',
#     srcs = ['item_tag_client_tools.cc'],
#     deps = [
#         '//thirdparty/gflags:gflags',
#         '//thirdparty/grpc:grpc++',
#         '//proto:recommend_route_api',
#         '//util:util',
#         '//service/acc:acc',
#         '//service/updater:recommend_updater'
#     ]
# )