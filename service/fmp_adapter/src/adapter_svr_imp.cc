#include "service/fmp_adapter/src/adapter_svr_imp.h"
#include <iostream>
#include <thread>
#include <vector>

#include "service/common/config_fields.h"
#include "service/fmp_adapter/src/adapter_attribute.h"
#include "service/fmp_adapter/src/adapter_context.h"
#include "service/fmp_adapter/src/route_strategy.h"
#include "service/updater/api/data_auto_api.h"
#include "util/logger.h"

namespace abc {
namespace recommend_plt {
namespace item_feature {

void AdpaterSvrImp::Start(const std::string& svr_addr, const std::string& conf_file_tag, int thread_total) {
    if (thread_total <= 0) {
        thread_total = 4;
    }
    LOG_DEBUG << "thread total:" << thread_total;

    grpc::ServerBuilder builder;
    //设置grpc channel keepalive 间隔时间,详见：https://grpc.io/grpc/cpp/md_doc_keepalive.html
    builder.AddChannelArgument(GRPC_ARG_KEEPALIVE_TIME_MS, 3000);
    builder.AddChannelArgument(GRPC_ARG_KEEPALIVE_TIMEOUT_MS, 10000);
    builder.SetMaxSendMessageSize(20 * 1024 * 1024);  // 20M
    // builder.SetDefaultCompressionAlgorithm(GRPC_COMPRESS_GZIP);
    builder.AddListeningPort(svr_addr, grpc::InsecureServerCredentials());
    builder.RegisterService(&service_);

    cq_ = builder.AddCompletionQueue();
    server_ = builder.BuildAndStart();

    std::vector<std::thread> vec_thread;
    // init
    int total = kPoolSize;
    LOG_DEBUG << "pool size total:" << total;
    AdapterContext::get_mutable_instance().InitItemFeatureContext(&service_, cq_.get(), total);

    // 初始化主配置文件
    abc::recommend_plt::updater::FileHandlerConf updater_conf_file;
    updater_conf_file.set_data_id(0);
    updater_conf_file.set_watch_file(conf_file_tag);
    LOG_DEBUG << updater_conf_file.DebugString();

    if (model::DataAutoApi::get_mutable_instance().Init(updater_conf_file, true) == false) {
        LOG_ERROR << "init updater config error," << updater_conf_file.watch_file();
        return;
    }

    for (auto i = 0; i < thread_total; i++) {
        client_cq_queues_.emplace_back(new grpc::CompletionQueue);
        vec_thread.push_back(std::thread(std::bind(&AdpaterSvrImp::HandleItemFeatureResponse, this, i)));
    }

    RouteStrategy<ItemAsyncClient>::get_mutable_instance().Init();

    vec_thread.push_back(std::thread(std::bind(&AdpaterSvrImp::HandleUpdateModel, this)));

    for (auto i = 0; i < thread_total; i++) {
        vec_thread.push_back(std::thread(std::bind(&AdpaterSvrImp::HandleSrv, this, i)));
    }

    for (auto& p : vec_thread) {
        p.join();
    }
}

void AdpaterSvrImp::HandleSrv(int index) {
    LOG_DEBUG << "start worker thread, index:" << index;
    AdapterAttribute adapter_attr(&service_, cq_.get(), client_cq_queues_[index].get());
    adapter_attr.Init();
    adapter_attr.Run();
    LOG_DEBUG << "Exit worker thread";
}

void AdpaterSvrImp::HandleUpdateModel() {
    LOG_DEBUG << "start monitor recommend model";

    abc::recommend_plt::model::DataAutoApi::get_mutable_instance().WatchAndUpdate();

    LOG_DEBUG << "exit monitor recommend model";
}

void AdpaterSvrImp::HandleItemFeatureResponse(int index) {
    bool ok = false;

    gpr_timespec time;
    time.tv_sec = 0;
    time.tv_nsec = 5000;
    time.clock_type = GPR_TIMESPAN;
    while (true) {
        ItemFeatureHandler* item_handler = NULL;
       
        grpc::CompletionQueue::NextStatus nextStatus = this->client_cq_queues_[index]->AsyncNext((void**)&item_handler, &ok, time);

        if (nextStatus == grpc::CompletionQueue::GOT_EVENT) {
            if (item_handler != NULL) {
                // 不在乎是否超时
                item_handler->Finish();
            }
        }
    }
}
}  // namespace item_feature
}  // namespace recommend_plt
}  // namespace abc