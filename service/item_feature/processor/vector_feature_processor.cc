// Copyright 2023 shein
// Author <EMAIL>

#include "service/item_feature/processor/vector_feature_processor.h"

#include "service/updater/api/data_auto_api.h"
#include "util/func.h"
#include "util/logger.h"
#include "util/pmonitor.h"


using namespace abc::recommend_plt::model;

namespace abc {
namespace recommend_plt {
namespace item_feature {

bool VectorFeatureProcessor::Bind(const fmp::ItemFeatureRequest& req) {
    BaseProcessor::Bind(req);

    const auto& fea_group = req.feature_group(0);
    if (fea_group.compute_vector().has_float_list()) {
        size_t value_size = fea_group.compute_vector().float_list().value_size();
        user_emb_.resize(value_size, 0);
        for (size_t i = 0; i < value_size; ++i) {
            user_emb_[i] = fea_group.compute_vector().float_list().value(i) * 1e6;
        }
    }
    
    data_map_idx_ = -1;

    // 与cmp_item_id的emb交叉
    auto it = fea_group.dependent_feature().find("cmp_item_id_fea");
    if (it != fea_group.dependent_feature().end() 
        && it->second.has_bytes_list() && it->second.bytes_list().value_size() > 0) {
        std::string cmp_item_id = it->second.bytes_list().value(0);
        fmp::FeatureList feature_data;
        if (DataAutoApi::get_mutable_instance().GetItemFeatureVal(region() + "-" + cmp_item_id,
                                                                   &feature_data, data_map_idx_)) {
            user_emb_.resize(feature_data.value_size(), 0);
            for (size_t i = 0; i < feature_data.value_size(); ++i) {
                user_emb_[i] = feature_data.value().data()[i];
            }                                                          
        }
    }

    if (user_emb_.size() <= 0) {
        return false;
    }

    LOG_DEBUG << "Bind successfully, item_num: " << item_num() << ", region: " << region()
              << ", user_emb_size: " << user_emb_.size();

    return true;
}

bool VectorFeatureProcessor::Work(fmp::ItemFeatureResponse* rsp) {
    fmp::FeatureGroupData* feature_group_data = rsp->add_feature_group_data();
    feature_group_data->set_feature_group_name(fea_group());
    feature_group_data->mutable_feature_list()->Reserve(item_num() * 2);

    // header(目前只支持一个emb特征)
    fmp::FeatureMetaList* feature_meta_list = feature_group_data->mutable_feature_meta_list();
    if (!GetItemFeatureMeta(feature_meta_list)) {
        return false;
    }

    // 特征长度强制置为1
    feature_meta_list->mutable_feature_meta(0)->set_feature_size(1);

    // 版本统计，用于更新。
    if (!version().empty() && !fea_group().empty() && !region().empty()) {
        DataAutoApi::get_mutable_instance().UpdateItemFeatureVersion(fea_group() + "-" + region(), version());
        DataAutoApi::get_mutable_instance().GetDataMapIdx(version(), &data_map_idx_);
    }

    // 获取当前版本
    std::string cur_version;
    const std::string version_key = "version-" + fea_group() + "-" + region();
    if (DataAutoApi::get_mutable_instance().GetItemFeatureString(version_key, &cur_version, data_map_idx_)) {
        feature_group_data->set_version(cur_version);
    }

    // 初始化rsp特征数据大小
    for (size_t i = 0; i < item_num(); ++i) {
        feature_group_data->add_feature_list();
    }

    // 循环展开优化
    size_t step = 5;
    size_t item_idx = 0;
    size_t remainder = item_num() % step;
    for (; item_idx < item_num() - remainder; item_idx += step) {
        VectorQueryAndCal(feature_group_data->mutable_feature_list(item_idx), item_idx);
        VectorQueryAndCal(feature_group_data->mutable_feature_list(item_idx + 1), item_idx + 1);
        VectorQueryAndCal(feature_group_data->mutable_feature_list(item_idx + 2), item_idx + 2);
        VectorQueryAndCal(feature_group_data->mutable_feature_list(item_idx + 3), item_idx + 3);
        VectorQueryAndCal(feature_group_data->mutable_feature_list(item_idx + 4), item_idx + 4);
    }
    for (; item_idx < item_num(); ++item_idx) {
        VectorQueryAndCal(feature_group_data->mutable_feature_list(item_idx), item_idx);
    }

    return true;
}

void VectorFeatureProcessor::VectorQueryAndCal(fmp::FeatureList* feature_list, size_t item_idx) {
    fmp::FeatureList feature_data;
    if (!DataAutoApi::get_mutable_instance().GetItemFeatureVal(region() + "-" + std::to_string(item_id()[item_idx]),
                                                               &feature_data, data_map_idx_)) {
        return;
    }

    if (user_emb_.size() <= 0) {
        return;
    }

    if (feature_data.value_size() != user_emb_.size()) {
        LOG_ERROR << "item_emb_size not equal to user_emb_size, item_emb_size: " << feature_data.value_size()
                  << ", user_emb_size: " << user_emb_.size();
        return;
    }
    int64_t score = 0;
    switch (mode()) {
        case fmp::IF_MODE_VECTOR_COSINE:
            score = SigmoidCosine(feature_data.value().data(),
                                  user_emb_.data(),
                                  user_emb_.size());
            break;
        case fmp::IF_MODE_VECTOR_DOT:
            score = DotProduct(feature_data.value().data(),
                               user_emb_.data(),
                               user_emb_.size());
            break;
        default:
            break;
    }
    LOG_DEBUG << "score: " << score << ", item_id: " << item_id()[item_idx] << ", mode: " << mode();
    
    feature_list->add_value(score);

    return;
}

int64_t VectorFeatureProcessor::SigmoidCosine(const int64_t* v1, const int64_t* v2, int size) {
    return (int64_t)(util::Sigmoid32(util::Func::Cosine32(v1, v2, size)) * 1e6);
}

int64_t VectorFeatureProcessor::DotProduct(const int64_t* v1, const int64_t* v2, int size) {
    return (int64_t)(util::Func::DotProduct(v1, v2, size) * 1e-6);
}

}  // namespace item_feature
}  // namespace recommend_plt
}  // namespace abc
