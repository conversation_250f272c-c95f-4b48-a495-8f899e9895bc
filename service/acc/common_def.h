// Copyright 2018 shein
// Author <EMAIL>
// 常量定义

#ifndef _ACC_COMMON_DEF_H_
#define _ACC_COMMON_DEF_H_

#include <stdint.h>
#include <string.h>
#include <string>

namespace abc {
namespace recommend_plt {
namespace acc {

const int kMaxItemNum = 2500;
const int kMaxItemInBucketNum = 10000;
const int kMaxItemTagNum = 64;
const int kMaxItemTagValSize = 8;
const int kMaxQueryTraceNum = 16;
const int kMaxItemFeaNum = 64;
const std::string kEmptyString = "";

const float kFloatMutipleNum = 1000000.0;

#define F_VALUE_NULL -1
struct FValue {
    union {
        float real;
        int fixed;
        int64_t fixed64;
    } u;
    bool IsNull() const { return this->u.fixed == F_VALUE_NULL; }
    void SetNull() { this->u.fixed = F_VALUE_NULL; }
};

}  // namesapce acc
}  // namespace recommend_plt
}  // namespace abc

#endif  // _ACC_COMMON_DEF_H_

