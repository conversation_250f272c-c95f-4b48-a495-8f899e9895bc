#!/bin/bash
#####
#####sh ../sh/multi_line_comment.sh ranker
#####
svr=$1
sed -i '/^[ ]*LOG_DEBUG/{:a;s/.*/\/\/\0/;/\;[[:space:]]*$/!{n;ba}}' `find ../${svr} -name "*.cc"`
sed -i '/^[ ]*LOG_DEBUG/{:a;s/.*/\/\/\0/;/\;[[:space:]]*$/!{n;ba}}' `find ../${svr} -name "*.h"`
sed -i '/^[ ]*SPD_LOG_DEBUG/{:a;s/.*/\/\/\0/;/\;[[:space:]]*$/!{n;ba}}' `find ../${svr} -name "*.cc"`
sed -i '/^[ ]*SPD_LOG_DEBUG/{:a;s/.*/\/\/\0/;/\;[[:space:]]*$/!{n;ba}}' `find ../${svr} -name "*.h"`
