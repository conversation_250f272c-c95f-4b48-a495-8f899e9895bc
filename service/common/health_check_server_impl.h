#ifndef HEALTH_CHECK_SERVER_IMPL_H_
#define HEALTH_CHECK_SERVER_IMPL_H_

#include <map>
#include <mutex>

#include <grpcpp/grpcpp.h>
#include <grpcpp/server_context.h>
#include <grpcpp/support/status.h>

#include "proto/recommend_health_check.grpc.pb.h"

using grpc::Status;
using grpc::health::v1::HealthCheckRequest;
using grpc::health::v1::HealthCheckResponse;
using grpc::health::v1::Health;

namespace abc {
namespace recommend_plt {
namespace healthcheck {

// A sample sync implementation of the health checking service. This does the
// same thing as the default one.
class HealthCheckServiceImpl : public Health::AsyncService {
 public:
  Status Check(grpc::ServerContext* context,
               const HealthCheckRequest* request,
               HealthCheckResponse* response) override;
  Status Watch(grpc::ServerContext* context,
               const HealthCheckRequest* request,
               grpc::ServerWriter<HealthCheckResponse>* writer) override;
  void SetStatus(const std::string& service_name,
                 HealthCheckResponse::ServingStatus status);
  void SetAll(HealthCheckResponse::ServingStatus status);

  void Shutdown();

 private:
  std::mutex mu_;
  bool shutdown_ = false;
  std::map<const std::string, HealthCheckResponse::ServingStatus> status_map_;

};

}  // namespace healthcheck
}  // namespace recommend_plt
}//abc
#endif  // GRPC_TEST_CPP_END2END_TEST_HEALTH_CHECK_SERVICE_IMPL_H

