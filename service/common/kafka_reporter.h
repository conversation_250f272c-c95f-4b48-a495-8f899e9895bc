// Copyright (c) 2024 <EMAIL>. All rights reserved.
// Author：<EMAIL>
// Time：2024-03-19

#ifndef SERVICE_COMMON_KAFKA_REPORTER_H_
#define SERVICE_COMMON_KAFKA_REPORTER_H_

#include <algorithm>
#include <atomic>
#include <chrono>
#include <memory>
#include <queue>
#include <string>
#include <type_traits>
#include <unordered_map>
#include <unordered_set>
#include <vector>
#include "thirdparty/cppkafka/include/cppkafka/cppkafka.h"
#include "thirdparty/boost/serialization/singleton.hpp"
#include "thirdparty/boost/lockfree/queue.hpp"
#include "thirdparty/prometheus/summary.h"
#include "proto/recommend_strategy_report.pb.h"
#include "service/common/kafka_auth.h"
#include "service/common/service_utility.h"
#include "util/logger.h"


namespace abc {
namespace recommend_plt {
namespace service_comm {

class BasicKafkaReporterImpl {
  public:
    BasicKafkaReporterImpl() = default;
    virtual ~BasicKafkaReporterImpl() = default;
    bool Init(const std::string& name);
    bool IsEnable() const { return producer_ != nullptr; }
    bool PushKafka(std::string&& msg, const std::string& key = {});
    const std::string& name() const noexcept { return name_; }
    virtual bool Consume() = 0;

  private:
    std::string name_;
    std::unique_ptr<cppkafka::Producer> producer_{nullptr};
    std::unique_ptr<cppkafka::MessageBuilder> builder_{nullptr};
};

template<typename T>
class KafkaReporterImpl : public BasicKafkaReporterImpl {
  public:
    KafkaReporterImpl(int pool_size);
    virtual ~KafkaReporterImpl() = default;

    bool Push(T&& msg);
    bool Pop(T& msg);

  protected:
    std::vector<T> messages_;
    boost::lockfree::queue<int> pool_queue_;
    boost::lockfree::queue<int> work_queue_;
};

template<typename T>
KafkaReporterImpl<T>::KafkaReporterImpl(int pool_size) {
    messages_.resize(pool_size);
    for (int i = 0; i < pool_size; ++i) {
        pool_queue_.push(i);
    }
}

template<typename T>
bool KafkaReporterImpl<T>::Push(T&& msg) {
    int idx{0};
    if (!pool_queue_.pop(idx)) {
        LOG_DEBUG << "not valid resouce for kafka reporter:" << name();
        return false;
    }
    messages_[idx] = std::move(msg);
    work_queue_.push(idx);
    return true;
};

template<typename T>
bool KafkaReporterImpl<T>::Pop(T& msg) {
    int idx{0};
    if (!work_queue_.pop(idx)) {
        return false;
    }
    msg = std::move(messages_[idx]);
    pool_queue_.push(idx);
    return true;
};

class KafkaReporter : public KafkaReporterImpl<std::string> {
  public:
    KafkaReporter(int pool_size) : KafkaReporterImpl<std::string>(pool_size) {}

    bool Consume() override {
        std::string msg;
        if (!Pop(msg)) {
            return false;
        }
        return PushKafka(std::move(msg));
    }
};

class KVKafkaReporter : public KafkaReporterImpl<std::pair<std::string, std::string>> {
  public:
    KVKafkaReporter(int pool_size) : KafkaReporterImpl<std::pair<std::string, std::string>>(pool_size) {}

    bool Consume() override {
        std::pair<std::string, std::string> msg;
        if (!Pop(msg)) {
            return false;
        }
        return PushKafka(std::move(msg.second), msg.first);
    }
};

class StrategyMetricKafkaReporter : public KafkaReporterImpl<strategy::RecStrategyReport> {
  public:
    StrategyMetricKafkaReporter(int pool_size) : KafkaReporterImpl<strategy::RecStrategyReport>(pool_size) {}

    bool Consume() override;

    void SetDuration(int duration_second) { duration_second_ = duration_second; }
    void SetMaxPushSize(int size) { max_push_size_ = (size > 0 ? size : max_push_size_); }

  private:
    int duration_second_{15};
    int max_push_size_{512};
    std::chrono::time_point<std::chrono::system_clock> last_time_;
    std::unordered_map<uint64_t, strategy::RecStrategyReport::RecStrategyMetric> metric_map_;
    std::unordered_map<uint64_t, prometheus::Summary*> summary_quantiles_;
    std::vector<std::unique_ptr<prometheus::Summary>> summary_quantiles_vec_;
};

class KafkaReporterManager : public boost::serialization::singleton<KafkaReporterManager> {
  public:
    KafkaReporterManager() = default;
    ~KafkaReporterManager() {
        stop_.store(true, std::memory_order_relaxed);
        if (worker_) {
            worker_->join();
        }
    }

    template<typename T,
             typename = typename std::enable_if<std::is_base_of<BasicKafkaReporterImpl, T>::value>::type>
    bool InitReporter(const std::string& name, int queue_size = 8192) {
        if (reporters_.count(name)) {
            return false;
        }
        auto& reporter = reporters_[name];
        reporter.reset(new T(queue_size));
        return reporter->Init(name);
    }

    template<typename S, typename T>
    bool Push(const std::string& name, T&& msg) {
        auto iter = reporters_.find(name);
        if (iter == reporters_.end()) {
            return false;
        }
        auto& reporter = iter->second;
        auto real_reporter = static_cast<S*>(reporter.get());
        return real_reporter->Push(std::move(msg));
    }

    template<typename S>
    S* GetReporter(const std::string& name) {
        auto iter = reporters_.find(name);
        if (iter == reporters_.end()) {
            return nullptr;
        }
        auto& reporter = iter->second;
        return static_cast<S*>(reporter.get());
    }

    bool Run();

  private:
    std::atomic<bool> stop_{false};
    std::unique_ptr<std::thread> worker_;
    std::unordered_map<std::string, std::unique_ptr<BasicKafkaReporterImpl>> reporters_;
};

#define KAFKA_REPORTER_INIT(reporter, name) abc::recommend_plt::service_comm::KafkaReporterManager::get_mutable_instance().InitReporter<reporter>(name)
#define KAFKA_REPORTER_RUN() abc::recommend_plt::service_comm::KafkaReporterManager::get_mutable_instance().Run()
#define KAFKA_REPORTER_PUSH(name, msg) abc::recommend_plt::service_comm::KafkaReporterManager::get_mutable_instance().Push<abc::recommend_plt::service_comm::KafkaReporter>(name, msg)
#define KAFKA_REPORTER_RPUSH(name, reporter, msg) abc::recommend_plt::service_comm::KafkaReporterManager::get_mutable_instance().Push<reporter>(name, msg)
#define KAFKA_REPORTER_INSTANCE(name, reporter) abc::recommend_plt::service_comm::KafkaReporterManager::get_mutable_instance().GetReporter<reporter>(name)

}  // namespace service_comm
}  // namespace recommend_plt
}  // namespace abc

#endif
