#include "service/common/kafka_auth.h"
#include "service/common/curl_http.h"
#include "json/json.h"
#include "json/value.h"
#include "util/logger.h"

namespace abc{
namespace recommend_plt {
namespace service_comm {

bool KafkaAuth::GetKafkaSaslAuthInfo(const KafkaAuthInfo& kafka_auth_info, const std::string& url,  
                    std::string* broker_list, std::string* pwd) {
    CurlHttp curl_http;
    curl_http.Init(120);
    curl_http.SetHttpHeader("Content-Type: application/json"); 

    Json::Value root;
    root["aclAccount"] = kafka_auth_info.username;
    root["aclPwd"] = kafka_auth_info.token;
    root["clusterName"] = kafka_auth_info.cluster_name;
    root["accessType"] = "sdk";
    root["language"] = "c";
    std::string out = root.toStyledString();
    //rec_plt request pb database

    LOG_INFO << " url:" << url;
    resp_body_t rsp_data;
    rsp_data.buf_ = (char*)malloc(1); 
    rsp_data.size_ = 0;
    Json::Value rsp_obj;
    bool status = true;
    if(curl_http.PostRequest(url, out, &rsp_data)) {
        Json::Reader reader;
        std::string json_str(rsp_data.buf_, rsp_data.size_);
        LOG_INFO << "rsp data=" << json_str;
        if (!reader.parse(json_str, rsp_obj)) {
            status =  false;
        }
    } else {
        status = false;
    }

    if (status) {
        if (rsp_obj["code"].asString() != "0") {
            status = false;
        } else {
            *pwd = rsp_obj["info"]["aclPwd"].asString();
            *broker_list = rsp_obj["info"]["brokers"].asString();
        }
    }

    free(rsp_data.buf_);
    rsp_data.buf_ = NULL;
    return status;
}

bool KafkaAuth::GetKafkaSaslAuthInfo(const KafkaAuthInfo& kafka_auth_info, const std::string& url,  
                    std::string* broker_list, std::string* pwd, int& isAcl) {
    CurlHttp curl_http;
    curl_http.Init(60);
    curl_http.SetHttpHeader("Content-Type: application/json"); 

    Json::Value root;
    root["aclAccount"] = kafka_auth_info.username;
    root["aclPwd"] = kafka_auth_info.token;
    root["clusterName"] = kafka_auth_info.cluster_name;
    root["accessType"] = "sdk";
    root["language"] = "c";
    root["version"] = "1.0.0";
    
    std::string out = root.toStyledString();
    //rec_plt request pb database

    LOG_WARNING << " url:" << url ;
    resp_body_t rsp_data;
    rsp_data.buf_ = (char*)malloc(1); 
    rsp_data.size_ = 0;
    Json::Value rsp_obj;
    bool status = true;
    if(curl_http.PostRequest(url, out, &rsp_data)) {
        Json::Reader reader;
        std::string json_str(rsp_data.buf_, rsp_data.size_);
        std::cout << "rsp data=" << json_str << std::endl;
        if (!reader.parse(json_str, rsp_obj)) {
            status =  false;
        }
    } else {
        status = false;
    }

    if (status) {
        if (rsp_obj["code"].asString() != "0") {
            status = false;
        } else {
            LOG_WARNING << "GetKafkaSaslAuthInfo success";
            *pwd = rsp_obj["info"]["aclPwd"].asString();
            *broker_list = rsp_obj["info"]["brokers"].asString();
            isAcl = rsp_obj["info"]["aclMode"].asInt();
        }
    }

    if (rsp_data.buf_) {
        free(rsp_data.buf_);
        rsp_data.buf_ = NULL;
    }
    return status;
}

/* KafkaSaslAuth ��Ȩ�ӿ�
plat: ��������     dev : ��������kafka����Ҫ��Ȩ;   prod : ���ϻ���kafka��Ҫ��Ȩ
username: kafka ��Ȩ�û����������������ü�Ȩ
auth_token: kafka ��Ȩ��token 
cluster_name : kafka��ѡ�� ��Ⱥ����
auth_url: kafka��Ȩ�� url����ͬ������ͬ

return: config ����Ĭ�ϻ�������
 */
bool KafkaAuth::KafkaSaslAuth(const std::string& server_plat,
        const std::string& user_name,
        const std::string& auth_token,
        const std::string& cluster_name,
        const std::string& auth_url,
        cppkafka::Configuration* config) {

    LOG_INFO << " server_plat:" << server_plat << ", user_name: "<< user_name << ", auth_token: "
                << auth_token << ", cluster_name: " << cluster_name << ", auth_url: " << auth_url;

    int isAcl = 0;
    std::string auth_passwd = "";
    std::string brokers = "";
    if (server_plat == "prod") {
        std::string kafka_auth_url = auth_url + "/api/v1/sdk/access";
        KafkaAuthInfo auth_info;
        auth_info.username = user_name;
        auth_info.token = auth_token;
        auth_info.cluster_name = cluster_name;
        auth_info.version = "v1.0.0";

        int err_times = 0;
        while(true) {
            if (GetKafkaSaslAuthInfo(auth_info, kafka_auth_url, &brokers, &auth_passwd, isAcl)) {
                break;
            }

            // try 3 times
            if (++err_times == 3) {
                return false;
            }

            LOG_ERROR << "GetKafkaSaslAuthInfo error, url=" << kafka_auth_url << ",user_name=" << auth_info.username << ",token=" << auth_info.token;
            sleep(5);
        }
        
    } else {
        // dev : ��������kafka����Ҫ��Ȩ ����brokers
        // brokers = ""
    }
    
    LOG_WARNING << "brokers=" << brokers << ",password=" << auth_passwd;
    
    if (1 == isAcl) {
        *config = {
            { "metadata.broker.list", brokers },
            { "queue.buffering.max.messages", 3000 },
            { "queue.buffering.max.ms", 1 },
            { "request.required.acks", 0 },
            { "compression.codec", "lz4" },
            { "security.protocol", "sasl_plaintext"},
            { "sasl.mechanism", "SCRAM-SHA-512"},
            { "sasl.username", user_name},
            { "sasl.password", auth_passwd},
            {"message.max.bytes", 2 * 1024 * 1024}
        };
    } else {
        *config = {
            { "metadata.broker.list", brokers },
            { "queue.buffering.max.messages", 3000 },
            { "queue.buffering.max.ms", 1 },
            { "request.required.acks", 0 },
            { "compression.codec", "lz4" },
            {"message.max.bytes", 2 * 1024 * 1024}
        };
    }

    return true;
}

}
}
}
