cmake_minimum_required(VERSION 3.16)
project(rec_plt)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# Add compiler flags
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -O2 -Wno-sign-compare -Wno-overloaded-virtual -Wno-non-virtual-dtor -Wno-deprecated")

# Find required packages
find_package(Threads REQUIRED)

# 主要包含目录
include_directories(.)
include_directories(proto)
include_directories(service)
include_directories(util)
include_directories(thirdparty)
include_directories(thirdparty/boost)

# 收集所有源文件
file(GLOB_RECURSE SOURCES
    "service/*.cc"
    "service/*.cpp"
    "util/*.cc"
    "util/*.cpp"
)

# 排除测试文件
list(FILTER SOURCES EXCLUDE REGEX ".*test.*")
list(FILTER SOURCES EXCLUDE REGEX ".*_test\\.cc$")
list(FILTER SOURCES EXCLUDE REGEX ".*_benchmark\\.cc$")

# 创建主要的可执行文件
add_executable(rec_plt
    service/strategy/rec_strategy.cc
    ${SOURCES}
)

# 链接线程库
target_link_libraries(rec_plt Threads::Threads)
