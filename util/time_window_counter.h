// Copyright (c) 2024 <EMAIL>. All rights reserved.
// Author：shichang<PERSON>@shein.com
// Time：2024-09-03

#ifndef UTIL_TIME_WINDWOW_COUNTER_H_
#define UTIL_TIME_WINDWOW_COUNTER_H_

#include <chrono>
#include <mutex>
#include <vector>
#include <atomic>
#include "thirdparty/boost/thread/shared_mutex.hpp"


namespace abc {
namespace recommend_plt {
namespace util {

class TimeWindowCounter {
  public:
    TimeWindowCounter(uint32_t bucket_age, uint32_t bucket_count);

    void AddError(uint32_t error = 0);

    float ErrorRate();

  private:
    void Rotate();

  private:
    struct Counter {
      void reset() {
        boost::unique_lock<boost::shared_mutex> wl(locker);
        sum = 0;
        error = 0;
      }
      boost::shared_mutex locker;
      uint32_t sum;
      uint32_t error;
    };
    uint32_t rotation_interval_;
    std::vector<std::unique_ptr<Counter>> buckets_;
    std::atomic<uint32_t> current_bucket_;
    std::mutex mtx_;
    std::chrono::system_clock::time_point last_rotation_;
};

}  // namespace util
}  // namespace recommend_plt
}  // namespace abc

#endif
