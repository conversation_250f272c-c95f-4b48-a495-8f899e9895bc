#ifndef THREAD_SAFE_LRU_TWIN_CACHE_H
#define THREAD_SAFE_LRU_TWIN_CACHE_H

#include <atomic>
#include <boost/serialization/singleton.hpp>

#include "util/lru_cache.h"

namespace abc {
namespace recommend_plt {
namespace util {

const uint32_t DEFAULT_SWITCH_TS = 5;

template <typename key_t, typename value_t>
class LRUTwinCache
    : public boost::serialization::singleton<LRUTwinCache<key_t, value_t>> {
 public:
  LRUTwinCache();
  LRUTwinCache(uint32_t max_size, uint32_t expired_time, uint32_t switch_ts);
  LRUTwinCache(const LRUTwinCache&) = delete;
  ~LRUTwinCache();
  void set(const key_t& key, const value_t& value);
  bool get(const key_t& key, value_t& val);
  uint32_t size_1();
  uint32_t size_2();
  uint32_t get_cur();

 private:
  std::atomic<int> _cur;
  uint8_t _switch_flag;
  uint32_t _switch_ts;
  pthread_t _switch_thread;
  LRUCache<key_t, value_t> _lru1;
  LRUCache<key_t, value_t> _lru2;
  std::unordered_map<key_t, value_t> _message_map[2];
  std::mutex _mutex;

 private:
  uint8_t get_switch_flag();
  uint32_t get_switch_ts();
  void switch_cur();
  static void* _swtich(void* arg);
};

template <typename key_t, typename value_t>
void* LRUTwinCache<key_t, value_t>::_swtich(void* arg) {
  LRUTwinCache<key_t, value_t>* ptr =
      static_cast<LRUTwinCache<key_t, value_t>*>(arg);

  uint32_t switch_ts = ptr->get_switch_ts();
  uint8_t switch_flag = ptr->get_switch_flag();

  while (switch_flag) {
    sleep(switch_ts);
    ptr->switch_cur();
    switch_flag = ptr->get_switch_flag();
    switch_ts = ptr->get_switch_ts();
  }
  return nullptr;
}

template <typename key_t, typename value_t>
LRUTwinCache<key_t, value_t>::LRUTwinCache() : _switch_ts(DEFAULT_SWITCH_TS) {
  _cur = 0;
  _switch_flag = 1;
  pthread_create(&_switch_thread, nullptr, _swtich, static_cast<void*>(this));
}

template <typename key_t, typename value_t>
LRUTwinCache<key_t, value_t>::LRUTwinCache(uint32_t max_size,
                                           uint32_t expired_time,
                                           uint32_t switch_ts)
    : _switch_ts(switch_ts),
      _lru1(max_size, expired_time),
      _lru2(max_size, expired_time) {
  _cur = 0;
  _switch_flag = 1;
  pthread_create(&_switch_thread, nullptr, _swtich, static_cast<void*>(this));
}

template <typename key_t, typename value_t>
LRUTwinCache<key_t, value_t>::~LRUTwinCache() {
  _switch_flag = 0;
  pthread_join(_switch_thread, nullptr);
}

template <typename key_t, typename value_t>
bool LRUTwinCache<key_t, value_t>::get(const key_t& key, value_t& val) {
  if (_cur == 0) {
    return _lru1.get(key, val);
  }
  return _lru2.get(key, val);
}

template <typename key_t, typename value_t>
void LRUTwinCache<key_t, value_t>::set(const key_t& key, const value_t& val) {
  std::lock_guard<std::mutex> lock(_mutex);
  _message_map[_cur][key] = val;
}

template <typename key_t, typename value_t>
uint32_t LRUTwinCache<key_t, value_t>::size_1() {
  return _lru1.size();
}

template <typename key_t, typename value_t>
uint32_t LRUTwinCache<key_t, value_t>::size_2() {
  return _lru2.size();
}

template <typename key_t, typename value_t>
uint32_t LRUTwinCache<key_t, value_t>::get_cur() {
  return _cur;
}

template <typename key_t, typename value_t>
uint32_t LRUTwinCache<key_t, value_t>::get_switch_ts() {
  return _switch_ts;
}

template <typename key_t, typename value_t>
uint8_t LRUTwinCache<key_t, value_t>::get_switch_flag() {
  return _switch_flag;
}

template <typename key_t, typename value_t>
void LRUTwinCache<key_t, value_t>::switch_cur() {
  for (const auto& kv : _message_map[(_cur + 1) % 2]) {
    if (_cur == 0) {
      _lru2.put(kv.first, kv.second);
    } else {
      _lru1.put(kv.first, kv.second);
    }
    value_t tmp;
    if (!get(kv.first, tmp)) {
      std::lock_guard<std::mutex> lock(_mutex);
      _message_map[_cur][kv.first] = kv.second;
    }
  }
  _message_map[(_cur + 1) % 2].clear();
  _cur = (_cur + 1) % 2;
}

}  // namespace util
}  // namespace recommend_plt
}  // namespace abc
#endif
