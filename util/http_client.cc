// Copyright (c) 2023 <EMAIL>. All rights reserved.
// Author：<EMAIL>
// Time：2023-02-01

#include "util/http_client.h"
#include <atomic>
#include <boost/asio/error.hpp>
#include <boost/asio/io_context.hpp>
#include <boost/asio/strand.hpp>
#include <boost/beast/core/error.hpp>
#include <chrono>
#include <exception>
#include <future>

namespace abc {
namespace recommend_plt {
namespace util {

namespace beast = boost::beast;
namespace http = boost::beast::http;
using tcp = boost::asio::ip::tcp;


HttpResponse HttpGet(const char* host, const char* port,
                     const char* target, int timeout_ms) {
    boost::asio::io_context ioc;
    auto sess = std::make_shared<HttpSession>(ioc, host, port, timeout_ms);
    auto fut = sess->Get(target);
    ioc.run();
    fut.wait();
    return fut.get();
}
HttpResponse HttpPost(const char* host, const char* port,
                      const char* target, const char* data, int timeout_ms) {
    boost::asio::io_context ioc;
    auto sess = std::make_shared<HttpSession>(ioc, host, port, timeout_ms);
    auto fut = sess->Post(target, data);
    ioc.run();
    fut.wait();
    return fut.get();
}

HttpSession::HttpSession(boost::asio::io_context& ioc,
                         const char* host, const char* port,
                         int timeout_ms)
    : timeout_ms_(timeout_ms), host_(host), port_(port),
      resolver_(boost::asio::make_strand(ioc)),
      stream_(boost::asio::make_strand(ioc)) {
          stream_.expires_never();
      }

HttpSession::~HttpSession() {
    beast::error_code ec;
    stream_.socket().shutdown(tcp::socket::shutdown_both, ec);
}

void HttpSession::Reset() {
    res_.clear();
    req_.clear();
    promise_.reset(new std::promise<HttpResponse>());
    req_.keep_alive(true);
    for (const auto& p: header_) {
        req_.set(p.first, p.second);
    }
    buffer_.clear();
}

std::future<HttpResponse> HttpSession::Get(char const* target) {
    Reset();
    auto fut = promise_->get_future();
    req_.version(11);
    req_.method(http::verb::get);
    req_.target(target);
    req_.set(http::field::host, host_.c_str());
    req_.set(http::field::user_agent, BOOST_BEAST_VERSION_STRING);
    stream_.expires_after(std::chrono::milliseconds(timeout_ms_));
    if (stream_.socket().is_open()) {
        http::async_write(stream_, req_,
                beast::bind_front_handler(&HttpSession::OnWrite, shared_from_this()));
    } else {
        resolver_.async_resolve(host_.c_str(), port_.c_str(),
                beast::bind_front_handler(&HttpSession::OnResolve, shared_from_this()));
    }
    return fut;
}

std::future<HttpResponse> HttpSession::Post(const char* target, const char* data) {
    Reset();
    auto fut = promise_->get_future();
    req_.version(11);
    req_.method(http::verb::post);
    req_.target(target);
    req_.set(http::field::host, host_.c_str());
    req_.set(http::field::content_type, "application/json");
    req_.body() = data;
    req_.prepare_payload();
    stream_.expires_after(std::chrono::milliseconds(timeout_ms_));
    if (stream_.socket().is_open()) {
        http::async_write(stream_, req_,
                beast::bind_front_handler(&HttpSession::OnWrite, shared_from_this()));
    } else {
        resolver_.async_resolve(host_.c_str(), port_.c_str(),
            beast::bind_front_handler(&HttpSession::OnResolve, shared_from_this()));
    }
    return fut;
}

void HttpSession::OnResolve(boost::beast::error_code ec,
                            boost::asio::ip::tcp::resolver::results_type results) {
    if (ec) {
        promise_->set_value({ec, std::move(res_)});
        return;
    }
    // Set a timeout on the operation
    stream_.expires_after(std::chrono::milliseconds(timeout_ms_));
    // Make the connection on the IP address we get from a lookup
    stream_.async_connect(results, boost::beast::bind_front_handler(&HttpSession::OnConnect, shared_from_this()));
}

void HttpSession::OnConnect(boost::beast::error_code ec,
                            boost::asio::ip::tcp::resolver::results_type::endpoint_type) {
    if (ec) {
        if (ec != boost::asio::error::timed_out) {
            stream_.socket().close();
        }
        promise_->set_value({ec, std::move(res_)});
        return;
    }
    // Set a timeout on the operation
    stream_.expires_after(std::chrono::milliseconds(timeout_ms_));
    // Send the HTTP request to the remote host
    http::async_write(stream_, req_, beast::bind_front_handler(&HttpSession::OnWrite, shared_from_this()));
}

void HttpSession::OnWrite(boost::beast::error_code ec, std::size_t bytes_transferred) {
    boost::ignore_unused(bytes_transferred);
    if (ec) {
        stream_.socket().close();
        promise_->set_value({ec, std::move(res_)});
        return;
    }
    stream_.expires_after(std::chrono::milliseconds(timeout_ms_));
    // Receive the HTTP response
    http::async_read(stream_, buffer_, res_, beast::bind_front_handler(&HttpSession::OnRead, shared_from_this()));
}

void HttpSession::OnRead(boost::beast::error_code ec, std::size_t bytes_transferred) {
    boost::ignore_unused(bytes_transferred);
    if (ec != boost::asio::error::timed_out) {
        stream_.socket().close();
    }
    promise_->set_value({ec, std::move(res_)});
}

}  // namespace util
}  // namespace recommend_plt
}  // namespace abc
