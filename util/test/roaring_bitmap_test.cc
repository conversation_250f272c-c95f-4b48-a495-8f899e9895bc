
#include "thirdparty/gtest/gtest.h"
#include "util/roaring_bitmap.h"
#include "util/func.h"
#include <chrono>
#include <iostream>
#include <thread>
using namespace abc::recommend_plt::util;

class RBMTest : public testing::Test {
   public:
    void SetUp() {}
};


TEST_F(RBMTest, test0) {
    std::vector<uint32_t> idxs {1,2,3,4,5,6,8,10,20, 1000,65537,65538, 0x00201234};
    RoaringBitmap rbm;
    std::string res = rbm.Serialize(idxs);
    std::cout << RoaringBitmap::ToDebugString(res.data()) << std::endl;
    rbm.Interpret(res.data());
    std::cout << ToString(rbm.Search<int, int>({1,2,6, 7, 20, 21, 100, 65537, 1000})) << std::endl;
}
