
#include "thirdparty/gtest/gtest.h"
#include "util/proto_helper.h"
#include "util/tracing/tracing.h"
#include <chrono>
#include <thread>
using namespace abc::recommend_plt::util;

class TracingTest : public testing::Test {
   public:
    void SetUp() {}
};

int foo(int n) {
    TRACE_FUNC();  // 2. trace recursive
    if (n <= 2) {
        return 1;
    }
    return foo(n - 1) + foo(n - 2);
}

TEST_F(TracingTest, Basic1) {
    // 1. start trace
    StartTracing("./", 20, 2, 1024);


    std::this_thread::sleep_for(std::chrono::seconds{1});

    std::thread th2 ([=](){
        foo(20);
    });

    std::thread th ([=](){
        TRACE_FUNC();
        {
            TRACE_SCOPE("sleep"); // 3. trace scope
            std::this_thread::sleep_for(std::chrono::seconds{10});
        }
    });


    th.join();
    th2.join();

}
