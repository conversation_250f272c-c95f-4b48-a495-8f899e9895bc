#ifndef RP_TRACE_H
#define RP_TRACE_H

#include <unistd.h>

#include <string>
#include <map>
#include <set>
#include <queue>
#include <thread>
#include <chrono>

#include <boost/serialization/singleton.hpp>
#include "thirdparty/json/json.hpp"
#include "util/logger.h"
#include "util/http_client.h"


namespace abc {
namespace recommend_plt {
namespace util {

class RPTraceV2 : public boost::serialization::singleton<RPTraceV2> {
  public:
    RPTraceV2(): enable_flag_(false), mutex_(0), lock_(0), unlock_(1)  {}
    ~RPTraceV2() {
        stop_.store(true, std::memory_order_release);
        if (enable_flag_ && rp_thread_.joinable()) {
            rp_thread_.join();
        }
    }

    void Init(const std::string& host, int port, const std::string& target,
              const std::string& module_key, bool enable) {
        host_ = host;
        port_ = std::to_string(port);
        target_ = target;
        enable_flag_ = enable;
        module_key_ = module_key;
        if (enable) {
            rp_thread_ = std::thread([this]() { this->Start(); });
        }
    }
    
    void Init(const std::string& url, const std::string& module_key, bool enable) {
        auto sidx = url.find_first_of("/");
        target_ = url.substr(sidx);
        auto prestr = url.substr(0, sidx);
        sidx = prestr.find_last_of(":");
        port_ = prestr.substr(sidx+1);
        host_ = prestr.substr(0, sidx);
        enable_flag_ = enable;
        module_key_ = module_key;
        LOG_DEBUG << "host:" << host_ << ", port:" << port_ << ", target:" << target_;
        if (enable) {
            rp_thread_ = std::thread([this]() { this->Start(); });
        }
    }

    void Start() {
        LOG_INFO << "start RP Thread";
        boost::asio::io_context::work worker(io_ctx_);
        std::thread t([&]() { io_ctx_.run(); });
        // timeout set 1000ms
        http_session_ = std::make_shared<HttpSession>(io_ctx_, host_.c_str(),
                                                      port_.c_str(), 1000);
        http_session_->SetHeaders({{"rmf-caller", "abc-recplt"}});
        while (!stop_.load(std::memory_order_relaxed)) {
            std::string seq_no("");
            if (!this->Dequeue(&seq_no) || seq_no.empty()) {
                std::this_thread::sleep_for(std::chrono::milliseconds(50));
                continue;
            }
            this->Lock();
            auto iter = pool_map_.find(seq_no);
            if (iter != pool_map_.end()) {
                nlohmann::json req_json{{"modelKey", module_key_},
                                        {"traceIdWithStep", seq_no}};
                for (const auto& p: iter->second) {
                    req_json["explainData"][p.first] = p.second;
                }
                iter->second.clear();
                pool_map_.erase(iter);
                this->Unlock();
                
                const std::string& reqs = req_json.dump();
                auto f = http_session_->Post(target_.c_str(), reqs.c_str());
                f.wait();
                auto v = f.get();
                if (v.ec || v.resp.result_int() != 200) {
                    LOG_DEBUG << "rp_request: " << reqs;
                    LOG_DEBUG << "rp_response: ec[" << v.ec << "], resp:" << v.resp;
                }
                continue;
            }
            this->Unlock();
        }
        io_ctx_.stop();
        t.join();
    }

    void Add(const std::string& seq_no,
             const std::string& field_key,
             const std::string& val) {
        if (!enable_flag_ || field_key.empty()) {
            return;
        }
        this->Lock();
        if (!enable_seq_set_.count(seq_no)) {
            this->Unlock();
            return;
        }
        auto iter = pool_map_.find(seq_no);
        if (iter != pool_map_.end()) {
            auto it = iter->second.find(field_key);
            if (it == iter->second.end()) {
                iter->second.insert(std::pair<std::string, std::string>(field_key, val));
            } else {
                it->second += "\n" + val;
            }
        } else {
            pool_map_[seq_no].emplace(field_key, val);
        }
        this->Unlock();
    }


    void Push(const std::string& seq_no) {
        if (enable_flag_ == false) {
            return;
        }
        this->Lock();
        bool has_val = pool_map_.count(seq_no);
        this->Unlock();
        if (has_val) {
            this->Enqueue(seq_no);
        } else {
            this->Finish(seq_no);
        }
    }

    void Finish(const std::string& seq_no) {
        this->Lock();
        if (this->has(seq_no)) {
            enable_seq_set_.erase(seq_no);
        }
        this->Unlock();
    }

    bool IsEnable() const noexcept { return enable_flag_; }
    void Enable(bool enable) noexcept { enable_flag_ = enable; }
    void Enable(const std::string& seq_no, int debug_mode) {
        if (enable_flag_ && debug_mode == 1) {
            this->Lock();
            enable_seq_set_.insert(seq_no);
            this->Unlock();
        }
    }

  private:
    void Enqueue(const std::string& data) {
        this->Lock();
        if (rp_queue_.size() < 5000) {
            rp_queue_.push(data);
        }
        this->Unlock();
    }
    bool Dequeue(std::string* data) {
        bool rt = false;
        this->Lock();
        if (rp_queue_.size() > 0) {
            *data = rp_queue_.front();
            rp_queue_.pop();
            rt = true;
        }
        this->Unlock();
        return rt;
    }

    bool has(const std::string& key) {
        return enable_seq_set_.count(key) == 1;
    }
    //atomic operator lock
    void Lock() {
        while( !__sync_bool_compare_and_swap(&mutex_, lock_, 1) ) {
            usleep(10);
        }
    }
    //atomic operator unlock
    void Unlock() {
        __sync_bool_compare_and_swap(&mutex_, unlock_, 0) ;
    }

  private:
    bool enable_flag_{false};
    std::atomic<bool> stop_{false};
    std::string host_;
    std::string port_;
    std::string target_;
    std::string module_key_;
    std::set<std::string> enable_seq_set_;
    std::map<std::string, std::map<std::string, std::string>> pool_map_;
    std::queue<std::string> rp_queue_;
    std::thread rp_thread_;
    std::shared_ptr<util::HttpSession> http_session_;
    boost::asio::io_context io_ctx_;
    int mutex_;
    int lock_;
    int unlock_;
};


#define RP_INIT(url, module, enable) abc::recommend_plt::util::RPTraceV2::get_mutable_instance().Init(url, module, enable)
#define RP_ENABLE(seq_no, debug_mode) abc::recommend_plt::util::RPTraceV2::get_mutable_instance().Enable(seq_no, debug_mode)
#define RP_SET_ENABLE(enable) abc::recommend_plt::util::RPTraceV2::get_mutable_instance().Enable(enable)
#define RP_ADD(key, field_key, val) abc::recommend_plt::util::RPTraceV2::get_mutable_instance().Add(key, field_key, val);
#define RP_PUSH(key) abc::recommend_plt::util::RPTraceV2::get_mutable_instance().Push(key)
#define RP_ISENABLE() abc::recommend_plt::util::RPTraceV2::get_mutable_instance().IsEnable()
#define RP_FINISH(key) abc::recommend_plt::util::RPTraceV2::get_mutable_instance().Finish(key)
#define RP_ADD_IF(debug_mode, key, field_key, val) { if (debug_mode) { abc::recommend_plt::util::RPTraceV2::get_mutable_instance().Add(key, field_key, val); } }


}
}
}
#endif
