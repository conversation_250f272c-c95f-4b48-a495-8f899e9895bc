#ifndef _ROARING_BITMAP_H_
#define _ROARING_BITMAP_H_

#include <vector>
#include <unordered_map>
#include <string>
#include <cmath>
#include <algorithm>
#include <sstream>
#include <iostream>
#include <bitset>

class RoaringBitmap {
public:
    enum { ARRAY_CONTAINER, BITMAP_CONTAINER };
    static const int MAXSIZE_ARRAY_CONTAINER = 4096;

    struct BktInfo {
        uint16_t container_bytes;
        uint16_t container_type;
        uint32_t container_offset;
    };

    struct Header {
        uint16_t bkt_size;
        BktInfo bkts[0];
    };

public:
    RoaringBitmap(const void * interpret_addr = nullptr);

    void Interpret(const void* addr);

    template<typename ReqType, typename ResType>
    std::vector<ResType> Search(const std::vector<ReqType>& idxs);

    template<typename ReqType, typename ResType>
    std::vector<ResType> Search(const ReqType* idxs_addr, size_t idxs_num);

    std::string Serialize(const std::vector<uint32_t>& actived_idxs);

    static std::string ToDebugString(const void* addr);

    std::string ToDebugString() const {
        return ToDebugString(static_cast<const void*>(header_));
    }

    static std::string EmptyHeader() {
        std::string r;
        r.assign(sizeof(Header), 0);
        return r;
    }

private:
    std::string ToSortedArray(std::vector<uint16_t>& idxs);

    std::string ToBitMap(std::vector<uint16_t>& idxs);

    void SetBit(uint8_t& v, int idx);
    bool GetBit(const uint8_t& v, int idx);

    static const void* offbytes(const void* addr, int bytes);

private:
    const Header* header_;
};

template<typename ReqType, typename ResType>
std::vector<ResType> RoaringBitmap::Search(const std::vector<ReqType>& idxs) {
    return Search<ReqType, ResType>(idxs.data(), idxs.size());
}

template<typename ReqType, typename ResType>
std::vector<ResType> RoaringBitmap::Search(const ReqType* idxs_addr, size_t idxs_num) {
    std::vector<ResType> r(idxs_num);
    for (int i = 0; i < idxs_num; ++i) {
        uint32_t idx = idxs_addr[i];
        uint32_t bkt_idx = (idx & 0xFFFF0000) >> 16;
        if (bkt_idx >= header_->bkt_size) {
            r[i] = 0;
            continue;
        }
        auto& bkt_header = header_->bkts[bkt_idx];
        const void* container_addr = offbytes(reinterpret_cast<const char *>(header_), bkt_header.container_offset);
        if (bkt_header.container_type == ARRAY_CONTAINER) {
            auto* array = static_cast<const uint16_t*>(container_addr);
            r[i] = std::binary_search(array, array + (bkt_header.container_bytes / sizeof(uint16_t)), idx & 0xFFFF);
        } else if (bkt_header.container_type == BITMAP_CONTAINER) {
            auto* bytes = static_cast<const char*>(container_addr);
            int bytes_idx = (idx & 0xFFFF) / 8;
            int bit_idx = (idx & 7); // = idx % 8
            if (bytes_idx < bkt_header.container_bytes) {
                r[i] = GetBit(bytes[bytes_idx], bit_idx);
            }
        }
    }
    return r;
}

inline void RoaringBitmap::SetBit(uint8_t& v, int idx) {
    v |= 1 << (8 - idx - 1);
}
inline bool RoaringBitmap::GetBit(const uint8_t& v, int idx) {
    return v & 1 << (8 - idx - 1);
}

inline const void* RoaringBitmap::offbytes(const void* addr, int bytes) {
    return reinterpret_cast<const void*>(static_cast<const char*>(addr) + bytes);
}


#endif // _ROARING_BITMAP_H_
